(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6067],{285:(e,a,s)=>{"use strict";s.d(a,{$:()=>c});var r=s(5155);s(2115);var t=s(9708),i=s(2085),n=s(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:a,variant:s,size:i,asChild:c=!1,...l}=e,o=c?t.DX:"button";return(0,r.jsx)(o,{"data-slot":"button",className:(0,n.cn)(d({variant:s,size:i,className:a})),...l})}},646:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2049:(e,a,s)=>{"use strict";s.r(a),s.d(a,{default:()=>N});var r=s(5155),t=s(2115),i=s(5695),n=s(6874),d=s.n(n),c=s(6766),l=s(285),o=s(6126),u=s(6695),h=s(2346),x=s(5169),m=s(8564),v=s(9074),g=s(4186),p=s(5868),f=s(3717),j=s(7108),b=s(646),y=s(9434),k=s(6671);function N(){let e=(0,i.useParams)(),a=(0,i.useRouter)(),[s,n]=(0,t.useState)(null),[N,A]=(0,t.useState)(!0);if((0,t.useEffect)(()=>{let s=async()=>{try{let s=await fetch("/api/packages/".concat(e.id));if(s.ok){let e=await s.json();n(e)}else k.oR.error("Package not found"),a.push("/dashboard/packages")}catch(e){k.oR.error("Error fetching package"),a.push("/dashboard/packages")}finally{A(!1)}};e.id&&s()},[e.id,a]),N)return(0,r.jsx)("div",{children:"Loading..."});if(!s)return(0,r.jsx)("div",{children:"Package not found"});let P=e=>(0,r.jsx)(o.E,{variant:{ACTIVE:"default",INACTIVE:"secondary",ARCHIVED:"outline"}[e],children:e}),z=s.originalPrice&&s.originalPrice!==s.price;return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)(l.$,{variant:"ghost",size:"sm",onClick:()=>a.back(),children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,r.jsxs)("div",{children:[(0,r.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-2",children:[s.name,s.popular&&(0,r.jsx)(m.A,{className:"h-6 w-6 text-yellow-500 fill-current"})]}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),"Created ",(0,y.Yq)(s.createdAt)]}),s.duration&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),s.duration]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(p.A,{className:"h-4 w-4"}),s.price,z&&(0,r.jsx)("span",{className:"line-through text-muted-foreground ml-1",children:s.originalPrice})]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[P(s.status),s.category&&(0,r.jsx)(o.E,{variant:"outline",children:s.category}),(0,r.jsx)(l.$,{asChild:!0,children:(0,r.jsxs)(d(),{href:"/dashboard/packages/".concat(s.id,"/edit"),children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Edit"]})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[s.image&&(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-0",children:(0,r.jsx)("div",{className:"relative aspect-video w-full overflow-hidden rounded-lg",children:(0,r.jsx)(c.default,{src:s.image,alt:s.name,fill:!0,className:"object-cover"})})})}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{children:"Description"})}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:s.description})})]}),s.services.length>0&&(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{children:[(0,r.jsx)(u.ZB,{children:"Included Services"}),(0,r.jsx)(u.BT,{children:"Services included in this package"})]}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("ul",{className:"space-y-2",children:s.services.map((e,a)=>(0,r.jsxs)("li",{className:"flex items-start gap-2",children:[(0,r.jsx)(j.A,{className:"h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0"}),(0,r.jsx)("span",{children:e})]},a))})})]}),s.features.length>0&&(0,r.jsxs)(u.Zp,{children:[(0,r.jsxs)(u.aR,{children:[(0,r.jsx)(u.ZB,{children:"Package Features"}),(0,r.jsx)(u.BT,{children:"Additional features and benefits"})]}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("ul",{className:"space-y-2",children:s.features.map((e,a)=>(0,r.jsxs)("li",{className:"flex items-start gap-2",children:[(0,r.jsx)(b.A,{className:"h-5 w-5 text-green-500 mt-0.5 flex-shrink-0"}),(0,r.jsx)("span",{children:e})]},a))})})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{children:"Package Details"})}),(0,r.jsxs)(u.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{className:"text-sm font-medium",children:"Status"}),(0,r.jsx)("div",{className:"mt-1",children:P(s.status)})]}),s.category&&(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{className:"text-sm font-medium",children:"Category"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)(o.E,{variant:"outline",children:s.category})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{className:"text-sm font-medium",children:"Price"}),(0,r.jsxs)("div",{className:"mt-1 flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-lg font-semibold",children:s.price}),z&&(0,r.jsx)("span",{className:"text-sm text-muted-foreground line-through",children:s.originalPrice})]})]}),s.duration&&(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{className:"text-sm font-medium",children:"Duration"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:s.duration})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)(w,{className:"text-sm font-medium",children:"Popular Package"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)(o.E,{variant:s.popular?"default":"outline",children:s.popular?"Yes":"No"})})]}),(0,r.jsx)(h.w,{}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Created:"})," ",(0,y.Yq)(s.createdAt)]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Updated:"})," ",(0,y.Yq)(s.updatedAt)]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Slug:"})," ",(0,r.jsx)("code",{className:"text-xs bg-muted px-1 py-0.5 rounded",children:s.slug})]})]})]})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{children:"Quick Actions"})}),(0,r.jsxs)(u.Wu,{className:"space-y-2",children:[(0,r.jsx)(l.$,{asChild:!0,className:"w-full",children:(0,r.jsxs)(d(),{href:"/dashboard/packages/".concat(s.id,"/edit"),children:[(0,r.jsx)(f.A,{className:"h-4 w-4 mr-2"}),"Edit Package"]})}),(0,r.jsx)(l.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,r.jsx)(d(),{href:"/dashboard/packages",children:"View All Packages"})}),(0,r.jsx)(l.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,r.jsx)(d(),{href:"/dashboard/packages/new",children:"Create New Package"})})]})]})]})]})]})}function w(e){let{children:a,className:s}=e;return(0,r.jsx)("div",{className:s,children:a})}},2346:(e,a,s)=>{"use strict";s.d(a,{w:()=>n});var r=s(5155);s(2115);var t=s(7489),i=s(9434);function n(e){let{className:a,orientation:s="horizontal",decorative:n=!0,...d}=e;return(0,r.jsx)(t.b,{"data-slot":"separator",decorative:n,orientation:s,className:(0,i.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...d})}},3717:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4186:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},5169:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5695:(e,a,s)=>{"use strict";var r=s(8999);s.o(r,"redirect")&&s.d(a,{redirect:function(){return r.redirect}}),s.o(r,"useParams")&&s.d(a,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(a,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(a,{useRouter:function(){return r.useRouter}})},5868:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6126:(e,a,s)=>{"use strict";s.d(a,{E:()=>c});var r=s(5155);s(2115);var t=s(9708),i=s(2085),n=s(9434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:a,variant:s,asChild:i=!1,...c}=e,l=i?t.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(d({variant:s}),a),...c})}},6695:(e,a,s)=>{"use strict";s.d(a,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>n});var r=s(5155);s(2115);var t=s(9434);function i(e){let{className:a,...s}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,t.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...s})}function n(e){let{className:a,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,t.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...s})}function d(e){let{className:a,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,t.cn)("leading-none font-semibold",a),...s})}function c(e){let{className:a,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,t.cn)("text-muted-foreground text-sm",a),...s})}function l(e){let{className:a,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,t.cn)("px-6",a),...s})}},7108:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7489:(e,a,s)=>{"use strict";s.d(a,{b:()=>l});var r=s(2115),t=s(3655),i=s(5155),n="horizontal",d=["horizontal","vertical"],c=r.forwardRef((e,a)=>{var s;let{decorative:r,orientation:c=n,...l}=e,o=(s=c,d.includes(s))?c:n;return(0,i.jsx)(t.sG.div,{"data-orientation":o,...r?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...l,ref:a})});c.displayName="Separator";var l=c},8347:(e,a,s)=>{Promise.resolve().then(s.bind(s,2049))},8564:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9074:(e,a,s)=>{"use strict";s.d(a,{A:()=>r});let r=(0,s(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9434:(e,a,s)=>{"use strict";s.d(a,{EJ:()=>d,Yq:()=>n,cn:()=>i});var r=s(2596),t=s(9688);function i(){for(var e=arguments.length,a=Array(e),s=0;s<e;s++)a[s]=arguments[s];return(0,t.QP)((0,r.$)(a))}function n(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function d(e,a){return e.length<=a?e:e.substring(0,a).trim()+"..."}}},e=>{e.O(0,[5389,6671,651,6874,8441,5964,7358],()=>e(e.s=8347)),_N_E=e.O()}]);