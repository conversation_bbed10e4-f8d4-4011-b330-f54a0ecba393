"use strict";exports.id=86,exports.ids=[86],exports.modules={30474:(a,b,c)=>{c.d(b,{default:()=>e.a});var d=c(31261),e=c.n(d)},31261:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return i},getImageProps:function(){return h}});let d=c(14985),e=c(44953),f=c(46533),g=d._(c(1933));function h(a){let{props:b}=(0,e.getImgProps)(a,{defaultLoader:g.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[a,c]of Object.entries(b))void 0===c&&delete b[a];return{props:b}}let i=f.Image},51423:(a,b,c)=>{c.d(b,{I:()=>t});var d=c(39850),e=c(33465),f=c(61489),g=c(35536),h=c(73458),i=c(31212),j=class extends g.Q{constructor(a,b){super(),this.options=b,this.#a=a,this.#b=null,this.#c=(0,h.T)(),this.options.experimental_prefetchInRender||this.#c.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(b)}#a;#d=void 0;#e=void 0;#f=void 0;#g;#h;#c;#b;#i;#j;#k;#l;#m;#n;#o=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#d.addObserver(this),k(this.#d,this.options)?this.#p():this.updateResult(),this.#q())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return l(this.#d,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return l(this.#d,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#r(),this.#s(),this.#d.removeObserver(this)}setOptions(a){let b=this.options,c=this.#d;if(this.options=this.#a.defaultQueryOptions(a),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,i.Eh)(this.options.enabled,this.#d))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#t(),this.#d.setOptions(this.options),b._defaulted&&!(0,i.f8)(this.options,b)&&this.#a.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#d,observer:this});let d=this.hasListeners();d&&m(this.#d,c,this.options,b)&&this.#p(),this.updateResult(),d&&(this.#d!==c||(0,i.Eh)(this.options.enabled,this.#d)!==(0,i.Eh)(b.enabled,this.#d)||(0,i.d2)(this.options.staleTime,this.#d)!==(0,i.d2)(b.staleTime,this.#d))&&this.#u();let e=this.#v();d&&(this.#d!==c||(0,i.Eh)(this.options.enabled,this.#d)!==(0,i.Eh)(b.enabled,this.#d)||e!==this.#n)&&this.#w(e)}getOptimisticResult(a){var b,c;let d=this.#a.getQueryCache().build(this.#a,a),e=this.createResult(d,a);return b=this,c=e,(0,i.f8)(b.getCurrentResult(),c)||(this.#f=e,this.#h=this.options,this.#g=this.#d.state),e}getCurrentResult(){return this.#f}trackResult(a,b){return new Proxy(a,{get:(a,c)=>(this.trackProp(c),b?.(c),Reflect.get(a,c))})}trackProp(a){this.#o.add(a)}getCurrentQuery(){return this.#d}refetch({...a}={}){return this.fetch({...a})}fetchOptimistic(a){let b=this.#a.defaultQueryOptions(a),c=this.#a.getQueryCache().build(this.#a,b);return c.fetch().then(()=>this.createResult(c,b))}fetch(a){return this.#p({...a,cancelRefetch:a.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#f))}#p(a){this.#t();let b=this.#d.fetch(this.options,a);return a?.throwOnError||(b=b.catch(i.lQ)),b}#u(){this.#r();let a=(0,i.d2)(this.options.staleTime,this.#d);if(i.S$||this.#f.isStale||!(0,i.gn)(a))return;let b=(0,i.j3)(this.#f.dataUpdatedAt,a);this.#l=setTimeout(()=>{this.#f.isStale||this.updateResult()},b+1)}#v(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#d):this.options.refetchInterval)??!1}#w(a){this.#s(),this.#n=a,!i.S$&&!1!==(0,i.Eh)(this.options.enabled,this.#d)&&(0,i.gn)(this.#n)&&0!==this.#n&&(this.#m=setInterval(()=>{(this.options.refetchIntervalInBackground||d.m.isFocused())&&this.#p()},this.#n))}#q(){this.#u(),this.#w(this.#v())}#r(){this.#l&&(clearTimeout(this.#l),this.#l=void 0)}#s(){this.#m&&(clearInterval(this.#m),this.#m=void 0)}createResult(a,b){let c,d=this.#d,e=this.options,g=this.#f,j=this.#g,l=this.#h,o=a!==d?a.state:this.#e,{state:p}=a,q={...p},r=!1;if(b._optimisticResults){let c=this.hasListeners(),g=!c&&k(a,b),h=c&&m(a,d,b,e);(g||h)&&(q={...q,...(0,f.k)(p.data,a.options)}),"isRestoring"===b._optimisticResults&&(q.fetchStatus="idle")}let{error:s,errorUpdatedAt:t,status:u}=q;c=q.data;let v=!1;if(void 0!==b.placeholderData&&void 0===c&&"pending"===u){let a;g?.isPlaceholderData&&b.placeholderData===l?.placeholderData?(a=g.data,v=!0):a="function"==typeof b.placeholderData?b.placeholderData(this.#k?.state.data,this.#k):b.placeholderData,void 0!==a&&(u="success",c=(0,i.pl)(g?.data,a,b),r=!0)}if(b.select&&void 0!==c&&!v)if(g&&c===j?.data&&b.select===this.#i)c=this.#j;else try{this.#i=b.select,c=b.select(c),c=(0,i.pl)(g?.data,c,b),this.#j=c,this.#b=null}catch(a){this.#b=a}this.#b&&(s=this.#b,c=this.#j,t=Date.now(),u="error");let w="fetching"===q.fetchStatus,x="pending"===u,y="error"===u,z=x&&w,A=void 0!==c,B={status:u,fetchStatus:q.fetchStatus,isPending:x,isSuccess:"success"===u,isError:y,isInitialLoading:z,isLoading:z,data:c,dataUpdatedAt:q.dataUpdatedAt,error:s,errorUpdatedAt:t,failureCount:q.fetchFailureCount,failureReason:q.fetchFailureReason,errorUpdateCount:q.errorUpdateCount,isFetched:q.dataUpdateCount>0||q.errorUpdateCount>0,isFetchedAfterMount:q.dataUpdateCount>o.dataUpdateCount||q.errorUpdateCount>o.errorUpdateCount,isFetching:w,isRefetching:w&&!x,isLoadingError:y&&!A,isPaused:"paused"===q.fetchStatus,isPlaceholderData:r,isRefetchError:y&&A,isStale:n(a,b),refetch:this.refetch,promise:this.#c,isEnabled:!1!==(0,i.Eh)(b.enabled,a)};if(this.options.experimental_prefetchInRender){let b=a=>{"error"===B.status?a.reject(B.error):void 0!==B.data&&a.resolve(B.data)},c=()=>{b(this.#c=B.promise=(0,h.T)())},e=this.#c;switch(e.status){case"pending":a.queryHash===d.queryHash&&b(e);break;case"fulfilled":("error"===B.status||B.data!==e.value)&&c();break;case"rejected":("error"!==B.status||B.error!==e.reason)&&c()}}return B}updateResult(){let a=this.#f,b=this.createResult(this.#d,this.options);this.#g=this.#d.state,this.#h=this.options,void 0!==this.#g.data&&(this.#k=this.#d),(0,i.f8)(b,a)||(this.#f=b,this.#x({listeners:(()=>{if(!a)return!0;let{notifyOnChangeProps:b}=this.options,c="function"==typeof b?b():b;if("all"===c||!c&&!this.#o.size)return!0;let d=new Set(c??this.#o);return this.options.throwOnError&&d.add("error"),Object.keys(this.#f).some(b=>this.#f[b]!==a[b]&&d.has(b))})()}))}#t(){let a=this.#a.getQueryCache().build(this.#a,this.options);if(a===this.#d)return;let b=this.#d;this.#d=a,this.#e=a.state,this.hasListeners()&&(b?.removeObserver(this),a.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#q()}#x(a){e.jG.batch(()=>{a.listeners&&this.listeners.forEach(a=>{a(this.#f)}),this.#a.getQueryCache().notify({query:this.#d,type:"observerResultsUpdated"})})}};function k(a,b){return!1!==(0,i.Eh)(b.enabled,a)&&void 0===a.state.data&&("error"!==a.state.status||!1!==b.retryOnMount)||void 0!==a.state.data&&l(a,b,b.refetchOnMount)}function l(a,b,c){if(!1!==(0,i.Eh)(b.enabled,a)&&"static"!==(0,i.d2)(b.staleTime,a)){let d="function"==typeof c?c(a):c;return"always"===d||!1!==d&&n(a,b)}return!1}function m(a,b,c,d){return(a!==b||!1===(0,i.Eh)(d.enabled,a))&&(!c.suspense||"error"!==a.state.status)&&n(a,c)}function n(a,b){return!1!==(0,i.Eh)(b.enabled,a)&&a.isStaleByTime((0,i.d2)(b.staleTime,a))}var o=c(43210),p=c(8693);c(60687);var q=o.createContext(function(){let a=!1;return{clearReset:()=>{a=!1},reset:()=>{a=!0},isReset:()=>a}}()),r=o.createContext(!1);r.Provider;var s=(a,b,c)=>b.fetchOptimistic(a).catch(()=>{c.clearReset()});function t(a,b){return function(a,b,c){let d=o.useContext(r),f=o.useContext(q),g=(0,p.jE)(c),h=g.defaultQueryOptions(a);if(g.getDefaultOptions().queries?._experimental_beforeQuery?.(h),h._optimisticResults=d?"isRestoring":"optimistic",h.suspense){let a=a=>"static"===a?a:Math.max(a??1e3,1e3),b=h.staleTime;h.staleTime="function"==typeof b?(...c)=>a(b(...c)):a(b),"number"==typeof h.gcTime&&(h.gcTime=Math.max(h.gcTime,1e3))}(h.suspense||h.throwOnError||h.experimental_prefetchInRender)&&!f.isReset()&&(h.retryOnMount=!1),o.useEffect(()=>{f.clearReset()},[f]);let j=!g.getQueryCache().get(h.queryHash),[k]=o.useState(()=>new b(g,h)),l=k.getOptimisticResult(h),m=!d&&!1!==a.subscribed;if(o.useSyncExternalStore(o.useCallback(a=>{let b=m?k.subscribe(e.jG.batchCalls(a)):i.lQ;return k.updateResult(),b},[k,m]),()=>k.getCurrentResult(),()=>k.getCurrentResult()),o.useEffect(()=>{k.setOptions(h)},[h,k]),h?.suspense&&l.isPending)throw s(h,k,f);if((({result:a,errorResetBoundary:b,throwOnError:c,query:d,suspense:e})=>a.isError&&!b.isReset()&&!a.isFetching&&d&&(e&&void 0===a.data||(0,i.GU)(c,[a.error,d])))({result:l,errorResetBoundary:f,throwOnError:h.throwOnError,query:g.getQueryCache().get(h.queryHash),suspense:h.suspense}))throw l.error;if(g.getDefaultOptions().queries?._experimental_afterQuery?.(h,l),h.experimental_prefetchInRender&&!i.S$&&l.isLoading&&l.isFetching&&!d){let a=j?s(h,k,f):g.getQueryCache().get(h.queryHash)?.promise;a?.catch(i.lQ).finally(()=>{k.updateResult()})}return h.notifyOnChangeProps?l:k.trackResult(l)}(a,j,b)}}};