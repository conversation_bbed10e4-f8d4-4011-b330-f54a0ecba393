(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1431],{3557:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>b});var t=r(5155),l=r(2115),a=r(6874),n=r.n(a),i=r(285),c=r(2523),d=r(6126),o=r(5127),u=r(9409),h=r(6695),x=r(4616),j=r(7924),f=r(2657),g=r(3717),m=r(2525),p=r(9434),v=r(6671);function b(){let[e,s]=(0,l.useState)([]),[r,a]=(0,l.useState)([]),[b,y]=(0,l.useState)(!0),[N,A]=(0,l.useState)(""),[w,C]=(0,l.useState)("all"),[E,k]=(0,l.useState)("all"),[R,S]=(0,l.useState)(1),[_,P]=(0,l.useState)(1),L=async()=>{try{let e=new URLSearchParams({page:R.toString(),limit:"10",...N&&{search:N},...w&&"all"!==w&&{status:w},...E&&"all"!==E&&{categoryId:E}}),r=await fetch("/api/blogs?".concat(e));if(r.ok){let e=await r.json();s(e.blogs),P(e.pagination.pages)}else v.oR.error("Failed to fetch blogs")}catch(e){v.oR.error("Error fetching blogs")}finally{y(!1)}},B=async()=>{try{let e=await fetch("/api/categories");if(e.ok){let s=await e.json();a(s)}}catch(e){console.error("Error fetching categories:",e)}},D=async e=>{if(confirm("Are you sure you want to delete this blog post?"))try{(await fetch("/api/blogs/".concat(e),{method:"DELETE"})).ok?(v.oR.success("Blog deleted successfully"),L()):v.oR.error("Failed to delete blog")}catch(e){v.oR.error("Error deleting blog")}};return((0,l.useEffect)(()=>{L(),B()},[R,N,w,E]),b)?(0,t.jsx)("div",{children:"Loading..."}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Blog Posts"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Manage your blog posts and content"})]}),(0,t.jsx)(i.$,{asChild:!0,children:(0,t.jsxs)(n(),{href:"/dashboard/blogs/new",children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"New Post"]})})]}),(0,t.jsxs)(h.Zp,{children:[(0,t.jsxs)(h.aR,{children:[(0,t.jsx)(h.ZB,{children:"Filters"}),(0,t.jsx)(h.BT,{children:"Filter and search through your blog posts"})]}),(0,t.jsx)(h.Wu,{children:(0,t.jsxs)("div",{className:"flex gap-4",children:[(0,t.jsx)("div",{className:"flex-1",children:(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(j.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,t.jsx)(c.p,{placeholder:"Search posts...",value:N,onChange:e=>A(e.target.value),className:"pl-8"})]})}),(0,t.jsxs)(u.l6,{value:w,onValueChange:C,children:[(0,t.jsx)(u.bq,{className:"w-[180px]",children:(0,t.jsx)(u.yv,{placeholder:"All statuses"})}),(0,t.jsxs)(u.gC,{children:[(0,t.jsx)(u.eb,{value:"all",children:"All statuses"}),(0,t.jsx)(u.eb,{value:"DRAFT",children:"Draft"}),(0,t.jsx)(u.eb,{value:"PUBLISHED",children:"Published"}),(0,t.jsx)(u.eb,{value:"ARCHIVED",children:"Archived"})]})]}),(0,t.jsxs)(u.l6,{value:E,onValueChange:k,children:[(0,t.jsx)(u.bq,{className:"w-[180px]",children:(0,t.jsx)(u.yv,{placeholder:"All categories"})}),(0,t.jsxs)(u.gC,{children:[(0,t.jsx)(u.eb,{value:"all",children:"All categories"}),r.map(e=>(0,t.jsx)(u.eb,{value:e.id,children:e.name},e.id))]})]})]})})]}),(0,t.jsx)(h.Zp,{children:(0,t.jsxs)(o.XI,{children:[(0,t.jsx)(o.A0,{children:(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nd,{children:"Title"}),(0,t.jsx)(o.nd,{children:"Author"}),(0,t.jsx)(o.nd,{children:"Category"}),(0,t.jsx)(o.nd,{children:"Status"}),(0,t.jsx)(o.nd,{children:"Created"}),(0,t.jsx)(o.nd,{children:"Actions"})]})}),(0,t.jsx)(o.BF,{children:0===e.length?(0,t.jsx)(o.Hj,{children:(0,t.jsx)(o.nA,{colSpan:6,className:"text-center py-8",children:(0,t.jsxs)("div",{className:"text-muted-foreground",children:["No blog posts found.",(0,t.jsx)(n(),{href:"/dashboard/blogs/new",className:"text-primary hover:underline ml-1",children:"Create your first post"})]})})}):e.map(e=>{var s;return(0,t.jsxs)(o.Hj,{children:[(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{children:[(0,t.jsx)("div",{className:"font-medium",children:e.title}),e.featured&&(0,t.jsx)(d.E,{variant:"outline",className:"mt-1",children:"Featured"})]})}),(0,t.jsx)(o.nA,{children:e.author}),(0,t.jsx)(o.nA,{children:e.category?(0,t.jsx)(d.E,{variant:"outline",children:e.category.name}):(0,t.jsx)("span",{className:"text-muted-foreground",children:"No category"})}),(0,t.jsx)(o.nA,{children:(s=e.status,(0,t.jsx)(d.E,{variant:{DRAFT:"secondary",PUBLISHED:"default",ARCHIVED:"outline"}[s],children:s}))}),(0,t.jsx)(o.nA,{children:(0,p.Yq)(e.createdAt)}),(0,t.jsx)(o.nA,{children:(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[(0,t.jsx)(i.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,t.jsx)(n(),{href:"/dashboard/blogs/".concat(e.id),children:(0,t.jsx)(f.A,{className:"h-4 w-4"})})}),(0,t.jsx)(i.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,t.jsx)(n(),{href:"/dashboard/blogs/".concat(e.id,"/edit"),children:(0,t.jsx)(g.A,{className:"h-4 w-4"})})}),(0,t.jsx)(i.$,{variant:"ghost",size:"sm",onClick:()=>D(e.id),children:(0,t.jsx)(m.A,{className:"h-4 w-4"})})]})})]},e.id)})})]})}),_>1&&(0,t.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,t.jsx)(i.$,{variant:"outline",onClick:()=>S(R-1),disabled:1===R,children:"Previous"}),(0,t.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",R," of ",_]}),(0,t.jsx)(i.$,{variant:"outline",onClick:()=>S(R+1),disabled:R===_,children:"Next"})]})]})}},6654:(e,s,r)=>{"use strict";Object.defineProperty(s,"__esModule",{value:!0}),Object.defineProperty(s,"useMergedRef",{enumerable:!0,get:function(){return l}});let t=r(2115);function l(e,s){let r=(0,t.useRef)(null),l=(0,t.useRef)(null);return(0,t.useCallback)(t=>{if(null===t){let e=r.current;e&&(r.current=null,e());let s=l.current;s&&(l.current=null,s())}else e&&(r.current=a(e,t)),s&&(l.current=a(s,t))},[e,s])}function a(e,s){if("function"!=typeof e)return e.current=s,()=>{e.current=null};{let r=e(s);return"function"==typeof r?r:()=>e(null)}}("function"==typeof s.default||"object"==typeof s.default&&null!==s.default)&&void 0===s.default.__esModule&&(Object.defineProperty(s.default,"__esModule",{value:!0}),Object.assign(s.default,s),e.exports=s.default)},9904:(e,s,r)=>{Promise.resolve().then(r.bind(r,3557))},9946:(e,s,r)=>{"use strict";r.d(s,{A:()=>c});var t=r(2115);let l=e=>{let s=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,r)=>r?r.toUpperCase():s.toLowerCase());return s.charAt(0).toUpperCase()+s.slice(1)},a=function(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return s.filter((e,s,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===s).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,t.forwardRef)((e,s)=>{let{color:r="currentColor",size:l=24,strokeWidth:i=2,absoluteStrokeWidth:c,className:d="",children:o,iconNode:u,...h}=e;return(0,t.createElement)("svg",{ref:s,...n,width:l,height:l,stroke:r,strokeWidth:c?24*Number(i)/Number(l):i,className:a("lucide",d),...!o&&!(e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0})(h)&&{"aria-hidden":"true"},...h},[...u.map(e=>{let[s,r]=e;return(0,t.createElement)(s,r)}),...Array.isArray(o)?o:[o]])}),c=(e,s)=>{let r=(0,t.forwardRef)((r,n)=>{let{className:c,...d}=r;return(0,t.createElement)(i,{ref:n,iconNode:s,className:a("lucide-".concat(l(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),c),...d})});return r.displayName=l(e),r}}},e=>{e.O(0,[5389,6671,7536,7764,8062,6874,8064,8441,5964,7358],()=>e(e.s=9904)),_N_E=e.O()}]);