(()=>{var a={};a.id=606,a.ids=[606],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6211:(a,b,c)=>{"use strict";c.d(b,{A0:()=>g,BF:()=>h,Hj:()=>i,XI:()=>f,nA:()=>k,nd:()=>j});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"table-container",className:"relative w-full overflow-x-auto",children:(0,d.jsx)("table",{"data-slot":"table",className:(0,e.cn)("w-full caption-bottom text-sm",a),...b})})}function g({className:a,...b}){return(0,d.jsx)("thead",{"data-slot":"table-header",className:(0,e.cn)("[&_tr]:border-b",a),...b})}function h({className:a,...b}){return(0,d.jsx)("tbody",{"data-slot":"table-body",className:(0,e.cn)("[&_tr:last-child]:border-0",a),...b})}function i({className:a,...b}){return(0,d.jsx)("tr",{"data-slot":"table-row",className:(0,e.cn)("hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors",a),...b})}function j({className:a,...b}){return(0,d.jsx)("th",{"data-slot":"table-head",className:(0,e.cn)("text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...b})}function k({className:a,...b}){return(0,d.jsx)("td",{"data-slot":"table-cell",className:(0,e.cn)("p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]",a),...b})}},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},37798:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["testimonials",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,46122)),"C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali-cms\\src\\app\\dashboard\\testimonials\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,63144)),"C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali-cms\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali-cms\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali-cms\\src\\app\\dashboard\\testimonials\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/testimonials/page",pathname:"/dashboard/testimonials",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/testimonials/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},46122:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\anjali-portfolio\\\\anjali-cms\\\\src\\\\app\\\\dashboard\\\\testimonials\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali-cms\\src\\app\\dashboard\\testimonials\\page.tsx","default")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},83715:(a,b,c)=>{Promise.resolve().then(c.bind(c,46122))},83976:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>B});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(30474),i=c(29523),j=c(89667),k=c(96834),l=c(56896),m=c(6211),n=c(15079),o=c(21342),p=c(44493),q=c(64398),r=c(96474),s=c(99270),t=c(13964),u=c(11860),v=c(88233),w=c(13861),x=c(93661),y=c(63143),z=c(4780),A=c(52581);function B(){let[a,b]=(0,e.useState)([]),[c,f]=(0,e.useState)(!0),[B,C]=(0,e.useState)(""),[D,E]=(0,e.useState)("all"),[F,G]=(0,e.useState)(""),[H,I]=(0,e.useState)(1),[J,K]=(0,e.useState)(1),[L,M]=(0,e.useState)([]),N=async()=>{try{let a=new URLSearchParams({page:H.toString(),limit:"10",...B&&{search:B},...D&&"all"!==D&&{status:D},...F&&{service:F}}),c=await fetch(`/api/testimonials?${a}`);if(c.ok){let a=await c.json();b(a.testimonials),K(a.pagination.pages)}else A.oR.error("Failed to fetch testimonials")}catch(a){A.oR.error("Error fetching testimonials")}finally{f(!1)}},O=async a=>{if(confirm("Are you sure you want to delete this testimonial?"))try{(await fetch(`/api/testimonials/${a}`,{method:"DELETE"})).ok?(A.oR.success("Testimonial deleted successfully"),N()):A.oR.error("Failed to delete testimonial")}catch(a){A.oR.error("Error deleting testimonial")}},P=async(a,b)=>{try{(await fetch(`/api/testimonials/${a}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:b})})).ok?(A.oR.success(`Testimonial ${b.toLowerCase()} successfully`),N()):A.oR.error("Failed to update testimonial")}catch(a){A.oR.error("Error updating testimonial")}},Q=async a=>{if(0===L.length)return void A.oR.error("Please select testimonials first");if(confirm(`Are you sure you want to ${"approve"===a?"approve":"reject"===a?"reject":"delete"} ${L.length} testimonial(s)?`))try{let b=await fetch("/api/testimonials/bulk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({ids:L,action:a})});if(b.ok){let a=await b.json();A.oR.success(a.message),M([]),N()}else A.oR.error("Failed to perform bulk action")}catch(a){A.oR.error("Error performing bulk action")}};return c?(0,d.jsx)("div",{children:"Loading..."}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"Testimonials"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Manage client testimonials and reviews"})]}),(0,d.jsx)(i.$,{asChild:!0,children:(0,d.jsxs)(g(),{href:"/dashboard/testimonials/new",children:[(0,d.jsx)(r.A,{className:"h-4 w-4 mr-2"}),"New Testimonial"]})})]}),(0,d.jsxs)(p.Zp,{children:[(0,d.jsxs)(p.aR,{children:[(0,d.jsx)(p.ZB,{children:"Filters"}),(0,d.jsx)(p.BT,{children:"Filter and search through testimonials"})]}),(0,d.jsx)(p.Wu,{children:(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(s.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(j.p,{placeholder:"Search testimonials...",value:B,onChange:a=>C(a.target.value),className:"pl-8"})]})}),(0,d.jsxs)(n.l6,{value:D,onValueChange:E,children:[(0,d.jsx)(n.bq,{className:"w-[180px]",children:(0,d.jsx)(n.yv,{placeholder:"All statuses"})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"all",children:"All statuses"}),(0,d.jsx)(n.eb,{value:"PENDING",children:"Pending"}),(0,d.jsx)(n.eb,{value:"APPROVED",children:"Approved"}),(0,d.jsx)(n.eb,{value:"REJECTED",children:"Rejected"})]})]}),(0,d.jsx)(j.p,{placeholder:"Filter by service",value:F,onChange:a=>G(a.target.value),className:"w-[180px]"})]})})]}),L.length>0&&(0,d.jsx)(p.Zp,{children:(0,d.jsx)(p.Wu,{className:"pt-6",children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:[L.length," testimonial(s) selected"]}),(0,d.jsxs)(i.$,{size:"sm",onClick:()=>Q("approve"),children:[(0,d.jsx)(t.A,{className:"h-4 w-4 mr-2"}),"Approve"]}),(0,d.jsxs)(i.$,{size:"sm",variant:"outline",onClick:()=>Q("reject"),children:[(0,d.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Reject"]}),(0,d.jsxs)(i.$,{size:"sm",variant:"destructive",onClick:()=>Q("delete"),children:[(0,d.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})})}),(0,d.jsx)(p.Zp,{children:(0,d.jsxs)(m.XI,{children:[(0,d.jsx)(m.A0,{children:(0,d.jsxs)(m.Hj,{children:[(0,d.jsx)(m.nd,{className:"w-12",children:(0,d.jsx)(l.S,{checked:L.length===a.length&&a.length>0,onCheckedChange:()=>{M(b=>b.length===a.length?[]:a.map(a=>a.id))}})}),(0,d.jsx)(m.nd,{children:"Client"}),(0,d.jsx)(m.nd,{children:"Message"}),(0,d.jsx)(m.nd,{children:"Rating"}),(0,d.jsx)(m.nd,{children:"Service"}),(0,d.jsx)(m.nd,{children:"Status"}),(0,d.jsx)(m.nd,{children:"Date"}),(0,d.jsx)(m.nd,{children:"Actions"})]})}),(0,d.jsx)(m.BF,{children:0===a.length?(0,d.jsx)(m.Hj,{children:(0,d.jsx)(m.nA,{colSpan:8,className:"text-center py-8",children:(0,d.jsxs)("div",{className:"text-muted-foreground",children:["No testimonials found.",(0,d.jsx)(g(),{href:"/dashboard/testimonials/new",className:"text-primary hover:underline ml-1",children:"Add your first testimonial"})]})})}):a.map(a=>{var b;let c;return(0,d.jsxs)(m.Hj,{children:[(0,d.jsx)(m.nA,{children:(0,d.jsx)(l.S,{checked:L.includes(a.id),onCheckedChange:()=>{var b;return b=a.id,void M(a=>a.includes(b)?a.filter(a=>a!==b):[...a,b])}})}),(0,d.jsx)(m.nA,{children:(0,d.jsxs)("div",{className:"flex items-center gap-3",children:[a.image&&(0,d.jsx)("div",{className:"relative w-8 h-8 rounded-full overflow-hidden",children:(0,d.jsx)(h.default,{src:a.image,alt:a.name,fill:!0,className:"object-cover"})}),(0,d.jsxs)("div",{children:[(0,d.jsx)("div",{className:"font-medium",children:a.name}),a.email&&(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:a.email})]})]})}),(0,d.jsx)(m.nA,{children:(0,d.jsx)("div",{className:"max-w-xs",children:(0,z.EJ)(a.message,100)})}),(0,d.jsx)(m.nA,{children:(c=a.rating,(0,d.jsx)("div",{className:"flex",children:[1,2,3,4,5].map(a=>(0,d.jsx)(q.A,{className:`h-4 w-4 ${a<=c?"text-yellow-400 fill-current":"text-gray-300"}`},a))}))}),(0,d.jsx)(m.nA,{children:a.service?(0,d.jsx)(k.E,{variant:"outline",children:a.service}):(0,d.jsx)("span",{className:"text-muted-foreground",children:"Not specified"})}),(0,d.jsx)(m.nA,{children:(b=a.status,(0,d.jsx)(k.E,{variant:{PENDING:"secondary",APPROVED:"default",REJECTED:"destructive"}[b],children:b}))}),(0,d.jsx)(m.nA,{children:(0,z.Yq)(a.createdAt)}),(0,d.jsx)(m.nA,{children:(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(i.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,d.jsx)(g(),{href:`/dashboard/testimonials/${a.id}`,children:(0,d.jsx)(w.A,{className:"h-4 w-4"})})}),(0,d.jsxs)(o.rI,{children:[(0,d.jsx)(o.ty,{asChild:!0,children:(0,d.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,d.jsx)(x.A,{className:"h-4 w-4"})})}),(0,d.jsxs)(o.SQ,{align:"end",children:[(0,d.jsx)(o._2,{asChild:!0,children:(0,d.jsxs)(g(),{href:`/dashboard/testimonials/${a.id}/edit`,children:[(0,d.jsx)(y.A,{className:"h-4 w-4 mr-2"}),"Edit"]})}),"APPROVED"!==a.status&&(0,d.jsxs)(o._2,{onClick:()=>P(a.id,"APPROVED"),children:[(0,d.jsx)(t.A,{className:"h-4 w-4 mr-2"}),"Approve"]}),"REJECTED"!==a.status&&(0,d.jsxs)(o._2,{onClick:()=>P(a.id,"REJECTED"),children:[(0,d.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Reject"]}),(0,d.jsxs)(o._2,{onClick:()=>O(a.id),className:"text-destructive",children:[(0,d.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]})})]},a.id)})})]})}),J>1&&(0,d.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,d.jsx)(i.$,{variant:"outline",onClick:()=>I(H-1),disabled:1===H,children:"Previous"}),(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",H," of ",J]}),(0,d.jsx)(i.$,{variant:"outline",onClick:()=>I(H+1),disabled:H===J,children:"Next"})]})]})}},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},97787:(a,b,c)=>{Promise.resolve().then(c.bind(c,83976))}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[2073,7840,1999,8316,474,5555,6926,4040],()=>b(b.s=37798));module.exports=c})();