(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4680],{285:(e,r,t)=>{"use strict";t.d(r,{$:()=>o});var a=t(5155);t(2115);var n=t(9708),s=t(2085),i=t(9434);let d=(0,s.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:r,variant:t,size:s,asChild:o=!1,...l}=e,u=o?n.DX:"button";return(0,a.jsx)(u,{"data-slot":"button",className:(0,i.cn)(d({variant:t,size:s,className:r})),...l})}},968:(e,r,t)=>{"use strict";t.d(r,{b:()=>d});var a=t(2115),n=t(3655),s=t(5155),i=a.forwardRef((e,r)=>(0,s.jsx)(n.sG.label,{...e,ref:r,onMouseDown:r=>{var t;r.target.closest("button, input, select, textarea")||(null==(t=e.onMouseDown)||t.call(e,r),!r.defaultPrevented&&r.detail>1&&r.preventDefault())}}));i.displayName="Label";var d=i},2297:(e,r,t)=>{Promise.resolve().then(t.bind(t,3738))},2523:(e,r,t)=>{"use strict";t.d(r,{p:()=>s});var a=t(5155);t(2115);var n=t(9434);function s(e){let{className:r,type:t,...s}=e;return(0,a.jsx)("input",{type:t,"data-slot":"input",className:(0,n.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",r),...s})}},3738:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>v});var a=t(5155),n=t(2115),s=t(2108),i=t(5695),d=t(285),o=t(2523),l=t(5057),u=t(6695),c=t(6671);function v(){let[e,r]=(0,n.useState)(""),[t,v]=(0,n.useState)(""),[g,f]=(0,n.useState)(!1),p=(0,i.useRouter)(),m=async r=>{r.preventDefault(),f(!0);try{let r=await (0,s.signIn)("credentials",{email:e,password:t,redirect:!1});(null==r?void 0:r.error)?c.oR.error("Invalid credentials"):(c.oR.success("Signed in successfully"),p.push("/dashboard"))}catch(e){c.oR.error("An error occurred")}finally{f(!1)}};return(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)(u.Zp,{className:"w-full max-w-md",children:[(0,a.jsxs)(u.aR,{children:[(0,a.jsx)(u.ZB,{children:"Sign In"}),(0,a.jsx)(u.BT,{children:"Enter your credentials to access the CMS"})]}),(0,a.jsx)(u.Wu,{children:(0,a.jsxs)("form",{onSubmit:m,className:"space-y-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.J,{htmlFor:"email",children:"Email"}),(0,a.jsx)(o.p,{id:"email",type:"email",value:e,onChange:e=>r(e.target.value),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(l.J,{htmlFor:"password",children:"Password"}),(0,a.jsx)(o.p,{id:"password",type:"password",value:t,onChange:e=>v(e.target.value),required:!0})]}),(0,a.jsx)(d.$,{type:"submit",className:"w-full",disabled:g,children:g?"Signing in...":"Sign In"})]})})]})})}},5057:(e,r,t)=>{"use strict";t.d(r,{J:()=>i});var a=t(5155);t(2115);var n=t(968),s=t(9434);function i(e){let{className:r,...t}=e;return(0,a.jsx)(n.b,{"data-slot":"label",className:(0,s.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",r),...t})}},5695:(e,r,t)=>{"use strict";var a=t(8999);t.o(a,"redirect")&&t.d(r,{redirect:function(){return a.redirect}}),t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"usePathname")&&t.d(r,{usePathname:function(){return a.usePathname}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}})},6695:(e,r,t)=>{"use strict";t.d(r,{BT:()=>o,Wu:()=>l,ZB:()=>d,Zp:()=>s,aR:()=>i});var a=t(5155);t(2115);var n=t(9434);function s(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...t})}function i(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...t})}function d(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",r),...t})}function o(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-description",className:(0,n.cn)("text-muted-foreground text-sm",r),...t})}function l(e){let{className:r,...t}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",r),...t})}},9434:(e,r,t)=>{"use strict";t.d(r,{EJ:()=>d,Yq:()=>i,cn:()=>s});var a=t(2596),n=t(9688);function s(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,n.QP)((0,a.$)(r))}function i(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function d(e,r){return e.length<=r?e:e.substring(0,r).trim()+"..."}}},e=>{e.O(0,[5389,6671,2108,8441,5964,7358],()=>e(e.s=2297)),_N_E=e.O()}]);