"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7586],{4838:(e,t,a)=>{a.d(t,{SQ:()=>o,_2:()=>c,lp:()=>l,mB:()=>u,rI:()=>s,ty:()=>i});var r=a(5155);a(2115);var n=a(8698),d=a(9434);function s(e){let{...t}=e;return(0,r.jsx)(n.bL,{"data-slot":"dropdown-menu",...t})}function i(e){let{...t}=e;return(0,r.jsx)(n.l9,{"data-slot":"dropdown-menu-trigger",...t})}function o(e){let{className:t,sideOffset:a=4,...s}=e;return(0,r.jsx)(n.ZL,{children:(0,r.jsx)(n.<PERSON>,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,d.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...s})})}function c(e){let{className:t,inset:a,variant:s="default",...i}=e;return(0,r.jsx)(n.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":s,className:(0,d.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...i})}function l(e){let{className:t,inset:a,...s}=e;return(0,r.jsx)(n.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,d.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...s})}function u(e){let{className:t,...a}=e;return(0,r.jsx)(n.wv,{"data-slot":"dropdown-menu-separator",className:(0,d.cn)("bg-border -mx-1 my-1 h-px",t),...a})}},5623:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},6981:(e,t,a)=>{a.d(t,{C1:()=>j,bL:()=>y});var r=a(2115),n=a(6101),d=a(6081),s=a(5185),i=a(5845),o=a(5503),c=a(1275),l=a(8905),u=a(3655),p=a(5155),f="Checkbox",[m,v]=(0,d.A)(f),[x,b]=m(f);function h(e){let{__scopeCheckbox:t,checked:a,children:n,defaultChecked:d,disabled:s,form:o,name:c,onCheckedChange:l,required:u,value:m="on",internal_do_not_use_render:v}=e,[b,h]=(0,i.i)({prop:a,defaultProp:null!=d&&d,onChange:l,caller:f}),[g,k]=r.useState(null),[y,w]=r.useState(null),j=r.useRef(!1),C=!g||!!o||!!g.closest("form"),N={checked:b,disabled:s,setChecked:h,control:g,setControl:k,name:c,form:o,value:m,hasConsumerStoppedPropagationRef:j,required:u,defaultChecked:!_(d)&&d,isFormControl:C,bubbleInput:y,setBubbleInput:w};return(0,p.jsx)(x,{scope:t,...N,children:"function"==typeof v?v(N):n})}var g="CheckboxTrigger",k=r.forwardRef((e,t)=>{let{__scopeCheckbox:a,onKeyDown:d,onClick:i,...o}=e,{control:c,value:l,disabled:f,checked:m,required:v,setControl:x,setChecked:h,hasConsumerStoppedPropagationRef:k,isFormControl:y,bubbleInput:w}=b(g,a),j=(0,n.s)(t,x),C=r.useRef(m);return r.useEffect(()=>{let e=null==c?void 0:c.form;if(e){let t=()=>h(C.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[c,h]),(0,p.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":_(m)?"mixed":m,"aria-required":v,"data-state":E(m),"data-disabled":f?"":void 0,disabled:f,value:l,...o,ref:j,onKeyDown:(0,s.m)(d,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,s.m)(i,e=>{h(e=>!!_(e)||!e),w&&y&&(k.current=e.isPropagationStopped(),k.current||e.stopPropagation())})})});k.displayName=g;var y=r.forwardRef((e,t)=>{let{__scopeCheckbox:a,name:r,checked:n,defaultChecked:d,required:s,disabled:i,value:o,onCheckedChange:c,form:l,...u}=e;return(0,p.jsx)(h,{__scopeCheckbox:a,checked:n,defaultChecked:d,disabled:i,required:s,onCheckedChange:c,name:r,form:l,value:o,internal_do_not_use_render:e=>{let{isFormControl:r}=e;return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(k,{...u,ref:t,__scopeCheckbox:a}),r&&(0,p.jsx)(N,{__scopeCheckbox:a})]})}})});y.displayName=f;var w="CheckboxIndicator",j=r.forwardRef((e,t)=>{let{__scopeCheckbox:a,forceMount:r,...n}=e,d=b(w,a);return(0,p.jsx)(l.C,{present:r||_(d.checked)||!0===d.checked,children:(0,p.jsx)(u.sG.span,{"data-state":E(d.checked),"data-disabled":d.disabled?"":void 0,...n,ref:t,style:{pointerEvents:"none",...e.style}})})});j.displayName=w;var C="CheckboxBubbleInput",N=r.forwardRef((e,t)=>{let{__scopeCheckbox:a,...d}=e,{control:s,hasConsumerStoppedPropagationRef:i,checked:l,defaultChecked:f,required:m,disabled:v,name:x,value:h,form:g,bubbleInput:k,setBubbleInput:y}=b(C,a),w=(0,n.s)(t,y),j=(0,o.Z)(l),N=(0,c.X)(s);r.useEffect(()=>{if(!k)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!i.current;if(j!==l&&e){let a=new Event("click",{bubbles:t});k.indeterminate=_(l),e.call(k,!_(l)&&l),k.dispatchEvent(a)}},[k,j,l,i]);let E=r.useRef(!_(l)&&l);return(0,p.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=f?f:E.current,required:m,disabled:v,name:x,value:h,form:g,...d,tabIndex:-1,ref:w,style:{...d.style,...N,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function _(e){return"indeterminate"===e}function E(e){return _(e)?"indeterminate":e?"checked":"unchecked"}N.displayName=C},7262:(e,t,a)=>{a.d(t,{S:()=>i});var r=a(5155);a(2115);var n=a(6981),d=a(5196),s=a(9434);function i(e){let{className:t,...a}=e;return(0,r.jsx)(n.bL,{"data-slot":"checkbox",className:(0,s.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...a,children:(0,r.jsx)(n.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,r.jsx)(d.A,{className:"size-3.5"})})})}},8564:(e,t,a)=>{a.d(t,{A:()=>r});let r=(0,a(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])}}]);