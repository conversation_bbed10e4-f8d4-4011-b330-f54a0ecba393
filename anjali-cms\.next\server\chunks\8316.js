"use strict";exports.id=8316,exports.ids=[8316],exports.modules={43:(a,b,c)=>{c.d(b,{jH:()=>f});var d=c(43210);c(60687);var e=d.createContext(void 0);function f(a){let b=d.useContext(e);return a||b||"ltr"}},1359:(a,b,c)=>{c.d(b,{Oh:()=>f});var d=c(43210),e=0;function f(){d.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??g()),document.body.insertAdjacentElement("beforeend",a[1]??g()),e++,()=>{1===e&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),e--}},[])}function g(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}},2030:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function a(b,c){let d=b[0],e=c[0];if(Array.isArray(d)&&Array.isArray(e)){if(d[0]!==e[0]||d[2]!==e[2])return!0}else if(d!==e)return!0;if(b[4])return!c[4];if(c[4])return!0;let f=Object.values(b[1])[0],g=Object.values(c[1])[0];return!f||!g||a(f,g)}}}),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},2255:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"pathHasPrefix",{enumerable:!0,get:function(){return e}});let d=c(19169);function e(a,b){if("string"!=typeof a)return!1;let{pathname:c}=(0,d.parsePath)(a);return c===b||c.startsWith(b+"/")}},5144:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"PromiseQueue",{enumerable:!0,get:function(){return j}});let d=c(51550),e=c(59656);var f=e._("_maxConcurrency"),g=e._("_runningCount"),h=e._("_queue"),i=e._("_processNext");class j{enqueue(a){let b,c,e=new Promise((a,d)=>{b=a,c=d}),f=async()=>{try{d._(this,g)[g]++;let c=await a();b(c)}catch(a){c(a)}finally{d._(this,g)[g]--,d._(this,i)[i]()}};return d._(this,h)[h].push({promiseFn:e,task:f}),d._(this,i)[i](),e}bump(a){let b=d._(this,h)[h].findIndex(b=>b.promiseFn===a);if(b>-1){let a=d._(this,h)[h].splice(b,1)[0];d._(this,h)[h].unshift(a),d._(this,i)[i](!0)}}constructor(a=5){Object.defineProperty(this,i,{value:k}),Object.defineProperty(this,f,{writable:!0,value:void 0}),Object.defineProperty(this,g,{writable:!0,value:void 0}),Object.defineProperty(this,h,{writable:!0,value:void 0}),d._(this,f)[f]=a,d._(this,g)[g]=0,d._(this,h)[h]=[]}}function k(a){if(void 0===a&&(a=!1),(d._(this,g)[g]<d._(this,f)[f]||a)&&d._(this,h)[h].length>0){var b;null==(b=d._(this,h)[h].shift())||b.task()}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},5334:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{DYNAMIC_STALETIME_MS:function(){return m},STATIC_STALETIME_MS:function(){return n},createSeededPrefetchCacheEntry:function(){return j},getOrCreatePrefetchCacheEntry:function(){return i},prunePrefetchCache:function(){return l}});let d=c(59008),e=c(59154),f=c(75076);function g(a,b,c){let d=a.pathname;return(b&&(d+=a.search),c)?""+c+"%"+d:d}function h(a,b,c){return g(a,b===e.PrefetchKind.FULL,c)}function i(a){let{url:b,nextUrl:c,tree:d,prefetchCache:f,kind:h,allowAliasing:i=!0}=a,j=function(a,b,c,d,f){for(let h of(void 0===b&&(b=e.PrefetchKind.TEMPORARY),[c,null])){let c=g(a,!0,h),i=g(a,!1,h),j=a.search?c:i,k=d.get(j);if(k&&f){if(k.url.pathname===a.pathname&&k.url.search!==a.search)return{...k,aliased:!0};return k}let l=d.get(i);if(f&&a.search&&b!==e.PrefetchKind.FULL&&l&&!l.key.includes("%"))return{...l,aliased:!0}}if(b!==e.PrefetchKind.FULL&&f){for(let b of d.values())if(b.url.pathname===a.pathname&&!b.key.includes("%"))return{...b,aliased:!0}}}(b,h,c,f,i);return j?(j.status=o(j),j.kind!==e.PrefetchKind.FULL&&h===e.PrefetchKind.FULL&&j.data.then(a=>{if(!(Array.isArray(a.flightData)&&a.flightData.some(a=>a.isRootRender&&null!==a.seedData)))return k({tree:d,url:b,nextUrl:c,prefetchCache:f,kind:null!=h?h:e.PrefetchKind.TEMPORARY})}),h&&j.kind===e.PrefetchKind.TEMPORARY&&(j.kind=h),j):k({tree:d,url:b,nextUrl:c,prefetchCache:f,kind:h||e.PrefetchKind.TEMPORARY})}function j(a){let{nextUrl:b,tree:c,prefetchCache:d,url:f,data:g,kind:i}=a,j=g.couldBeIntercepted?h(f,i,b):h(f,i),k={treeAtTimeOfPrefetch:c,data:Promise.resolve(g),kind:i,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:g.staleTime,key:j,status:e.PrefetchCacheEntryStatus.fresh,url:f};return d.set(j,k),k}function k(a){let{url:b,kind:c,tree:g,nextUrl:i,prefetchCache:j}=a,k=h(b,c),l=f.prefetchQueue.enqueue(()=>(0,d.fetchServerResponse)(b,{flightRouterState:g,nextUrl:i,prefetchKind:c}).then(a=>{let c;if(a.couldBeIntercepted&&(c=function(a){let{url:b,nextUrl:c,prefetchCache:d,existingCacheKey:e}=a,f=d.get(e);if(!f)return;let g=h(b,f.kind,c);return d.set(g,{...f,key:g}),d.delete(e),g}({url:b,existingCacheKey:k,nextUrl:i,prefetchCache:j})),a.prerendered){let b=j.get(null!=c?c:k);b&&(b.kind=e.PrefetchKind.FULL,-1!==a.staleTime&&(b.staleTime=a.staleTime))}return a})),m={treeAtTimeOfPrefetch:g,data:l,kind:c,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:k,status:e.PrefetchCacheEntryStatus.fresh,url:b};return j.set(k,m),m}function l(a){for(let[b,c]of a)o(c)===e.PrefetchCacheEntryStatus.expired&&a.delete(b)}let m=1e3*Number("0"),n=1e3*Number("300");function o(a){let{kind:b,prefetchTime:c,lastUsedTime:d,staleTime:f}=a;return -1!==f?Date.now()<c+f?e.PrefetchCacheEntryStatus.fresh:e.PrefetchCacheEntryStatus.stale:Date.now()<(null!=d?d:c)+m?d?e.PrefetchCacheEntryStatus.reusable:e.PrefetchCacheEntryStatus.fresh:b===e.PrefetchKind.AUTO&&Date.now()<c+n?e.PrefetchCacheEntryStatus.stale:b===e.PrefetchKind.FULL&&Date.now()<c+n?e.PrefetchCacheEntryStatus.reusable:e.PrefetchCacheEntryStatus.expired}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},6361:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"assignLocation",{enumerable:!0,get:function(){return e}});let d=c(96127);function e(a,b){if(a.startsWith(".")){let c=b.origin+b.pathname;return new URL((c.endsWith("/")?c:c+"/")+a)}return new URL((0,d.addBasePath)(a),b.href)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},8830:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"reducer",{enumerable:!0,get:function(){return d}}),c(59154),c(25232),c(29651),c(28627),c(78866),c(75076),c(97936),c(35429);let d=function(a,b){return a};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},9510:(a,b,c)=>{c.d(b,{N:()=>i});var d=c(43210),e=c(11273),f=c(98599),g=c(8730),h=c(60687);function i(a){let b=a+"CollectionProvider",[c,i]=(0,e.A)(b),[j,k]=c(b,{collectionRef:{current:null},itemMap:new Map}),l=a=>{let{scope:b,children:c}=a,e=d.useRef(null),f=d.useRef(new Map).current;return(0,h.jsx)(j,{scope:b,itemMap:f,collectionRef:e,children:c})};l.displayName=b;let m=a+"CollectionSlot",n=(0,g.TL)(m),o=d.forwardRef((a,b)=>{let{scope:c,children:d}=a,e=k(m,c),g=(0,f.s)(b,e.collectionRef);return(0,h.jsx)(n,{ref:g,children:d})});o.displayName=m;let p=a+"CollectionItemSlot",q="data-radix-collection-item",r=(0,g.TL)(p),s=d.forwardRef((a,b)=>{let{scope:c,children:e,...g}=a,i=d.useRef(null),j=(0,f.s)(b,i),l=k(p,c);return d.useEffect(()=>(l.itemMap.set(i,{ref:i,...g}),()=>void l.itemMap.delete(i))),(0,h.jsx)(r,{...{[q]:""},ref:j,children:e})});return s.displayName=p,[{Provider:l,Slot:o,ItemSlot:s},function(b){let c=k(a+"CollectionConsumer",b);return d.useCallback(()=>{let a=c.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${q}]`));return Array.from(c.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[c.collectionRef,c.itemMap])},i]}var j=new WeakMap;function k(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=l(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function l(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],j.set(this,!0)}set(a,b){return j.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=l(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let j=this.size+ +!e;g<0&&h++;let k=[...this.#a],m=!1;for(let a=h;a<j;a++)if(h===a){let f=k[a];k[a]===b&&(f=k[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{m||k[a-1]!==b||(m=!0);let c=k[m?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=k(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=k(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return k(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}})},9707:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{addSearchParamsToPageSegments:function(){return l},handleAliasedPrefetchEntry:function(){return k}});let d=c(83913),e=c(89752),f=c(86770),g=c(57391),h=c(33123),i=c(33898),j=c(59435);function k(a,b,c,k,m){let n,o=b.tree,p=b.cache,q=(0,g.createHrefFromUrl)(k);if("string"==typeof c)return!1;for(let b of c){if(!function a(b){if(!b)return!1;let c=b[2];if(b[3])return!0;for(let b in c)if(a(c[b]))return!0;return!1}(b.seedData))continue;let c=b.tree;c=l(c,Object.fromEntries(k.searchParams));let{seedData:g,isRootRender:j,pathToSegment:m}=b,r=["",...m];c=l(c,Object.fromEntries(k.searchParams));let s=(0,f.applyRouterStatePatchToTree)(r,o,c,q),t=(0,e.createEmptyCacheNode)();if(j&&g){let b=g[1];t.loading=g[3],t.rsc=b,function a(b,c,e,f,g){if(0!==Object.keys(f[1]).length)for(let i in f[1]){let j,k=f[1][i],l=k[0],m=(0,h.createRouterCacheKey)(l),n=null!==g&&void 0!==g[2][i]?g[2][i]:null;if(null!==n){let a=n[1],c=n[3];j={lazyData:null,rsc:l.includes(d.PAGE_SEGMENT_KEY)?null:a,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:c,navigatedAt:b}}else j={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let o=c.parallelRoutes.get(i);o?o.set(m,j):c.parallelRoutes.set(i,new Map([[m,j]])),a(b,j,e,k,n)}}(a,t,p,c,g)}else t.rsc=p.rsc,t.prefetchRsc=p.prefetchRsc,t.loading=p.loading,t.parallelRoutes=new Map(p.parallelRoutes),(0,i.fillCacheWithNewSubTreeDataButOnlyLoading)(a,t,p,b);s&&(o=s,p=t,n=!0)}return!!n&&(m.patchedTree=o,m.cache=p,m.canonicalUrl=q,m.hashFragment=k.hash,(0,j.handleMutable)(b,m))}function l(a,b){let[c,e,...f]=a;if(c.includes(d.PAGE_SEGMENT_KEY))return[(0,d.addSearchParamsIfPageSegment)(c,b),e,...f];let g={};for(let[a,c]of Object.entries(e))g[a]=l(c,b);return[c,g,...f]}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},9989:(a,b,c)=>{c.d(b,{Kq:()=>S,UC:()=>W,ZL:()=>V,bL:()=>T,i3:()=>X,l9:()=>U});var d=c(43210),e=c(70569),f=c(98599),g=c(11273),h=c(31355),i=c(96963),j=c(55509),k=c(25028),l=c(46059),m=c(14163),n=c(8730),o=c(65551),p=c(69024),q=c(60687),[r,s]=(0,g.A)("Tooltip",[j.Bk]),t=(0,j.Bk)(),u="TooltipProvider",v="tooltip.open",[w,x]=r(u),y=a=>{let{__scopeTooltip:b,delayDuration:c=700,skipDelayDuration:e=300,disableHoverableContent:f=!1,children:g}=a,h=d.useRef(!0),i=d.useRef(!1),j=d.useRef(0);return d.useEffect(()=>{let a=j.current;return()=>window.clearTimeout(a)},[]),(0,q.jsx)(w,{scope:b,isOpenDelayedRef:h,delayDuration:c,onOpen:d.useCallback(()=>{window.clearTimeout(j.current),h.current=!1},[]),onClose:d.useCallback(()=>{window.clearTimeout(j.current),j.current=window.setTimeout(()=>h.current=!0,e)},[e]),isPointerInTransitRef:i,onPointerInTransitChange:d.useCallback(a=>{i.current=a},[]),disableHoverableContent:f,children:g})};y.displayName=u;var z="Tooltip",[A,B]=r(z),C=a=>{let{__scopeTooltip:b,children:c,open:e,defaultOpen:f,onOpenChange:g,disableHoverableContent:h,delayDuration:k}=a,l=x(z,a.__scopeTooltip),m=t(b),[n,p]=d.useState(null),r=(0,i.B)(),s=d.useRef(0),u=h??l.disableHoverableContent,w=k??l.delayDuration,y=d.useRef(!1),[B,C]=(0,o.i)({prop:e,defaultProp:f??!1,onChange:a=>{a?(l.onOpen(),document.dispatchEvent(new CustomEvent(v))):l.onClose(),g?.(a)},caller:z}),D=d.useMemo(()=>B?y.current?"delayed-open":"instant-open":"closed",[B]),E=d.useCallback(()=>{window.clearTimeout(s.current),s.current=0,y.current=!1,C(!0)},[C]),F=d.useCallback(()=>{window.clearTimeout(s.current),s.current=0,C(!1)},[C]),G=d.useCallback(()=>{window.clearTimeout(s.current),s.current=window.setTimeout(()=>{y.current=!0,C(!0),s.current=0},w)},[w,C]);return d.useEffect(()=>()=>{s.current&&(window.clearTimeout(s.current),s.current=0)},[]),(0,q.jsx)(j.bL,{...m,children:(0,q.jsx)(A,{scope:b,contentId:r,open:B,stateAttribute:D,trigger:n,onTriggerChange:p,onTriggerEnter:d.useCallback(()=>{l.isOpenDelayedRef.current?G():E()},[l.isOpenDelayedRef,G,E]),onTriggerLeave:d.useCallback(()=>{u?F():(window.clearTimeout(s.current),s.current=0)},[F,u]),onOpen:E,onClose:F,disableHoverableContent:u,children:c})})};C.displayName=z;var D="TooltipTrigger",E=d.forwardRef((a,b)=>{let{__scopeTooltip:c,...g}=a,h=B(D,c),i=x(D,c),k=t(c),l=d.useRef(null),n=(0,f.s)(b,l,h.onTriggerChange),o=d.useRef(!1),p=d.useRef(!1),r=d.useCallback(()=>o.current=!1,[]);return d.useEffect(()=>()=>document.removeEventListener("pointerup",r),[r]),(0,q.jsx)(j.Mz,{asChild:!0,...k,children:(0,q.jsx)(m.sG.button,{"aria-describedby":h.open?h.contentId:void 0,"data-state":h.stateAttribute,...g,ref:n,onPointerMove:(0,e.m)(a.onPointerMove,a=>{"touch"!==a.pointerType&&(p.current||i.isPointerInTransitRef.current||(h.onTriggerEnter(),p.current=!0))}),onPointerLeave:(0,e.m)(a.onPointerLeave,()=>{h.onTriggerLeave(),p.current=!1}),onPointerDown:(0,e.m)(a.onPointerDown,()=>{h.open&&h.onClose(),o.current=!0,document.addEventListener("pointerup",r,{once:!0})}),onFocus:(0,e.m)(a.onFocus,()=>{o.current||h.onOpen()}),onBlur:(0,e.m)(a.onBlur,h.onClose),onClick:(0,e.m)(a.onClick,h.onClose)})})});E.displayName=D;var F="TooltipPortal",[G,H]=r(F,{forceMount:void 0}),I=a=>{let{__scopeTooltip:b,forceMount:c,children:d,container:e}=a,f=B(F,b);return(0,q.jsx)(G,{scope:b,forceMount:c,children:(0,q.jsx)(l.C,{present:c||f.open,children:(0,q.jsx)(k.Z,{asChild:!0,container:e,children:d})})})};I.displayName=F;var J="TooltipContent",K=d.forwardRef((a,b)=>{let c=H(J,a.__scopeTooltip),{forceMount:d=c.forceMount,side:e="top",...f}=a,g=B(J,a.__scopeTooltip);return(0,q.jsx)(l.C,{present:d||g.open,children:g.disableHoverableContent?(0,q.jsx)(P,{side:e,...f,ref:b}):(0,q.jsx)(L,{side:e,...f,ref:b})})}),L=d.forwardRef((a,b)=>{let c=B(J,a.__scopeTooltip),e=x(J,a.__scopeTooltip),g=d.useRef(null),h=(0,f.s)(b,g),[i,j]=d.useState(null),{trigger:k,onClose:l}=c,m=g.current,{onPointerInTransitChange:n}=e,o=d.useCallback(()=>{j(null),n(!1)},[n]),p=d.useCallback((a,b)=>{let c=a.currentTarget,d={x:a.clientX,y:a.clientY},e=function(a,b){let c=Math.abs(b.top-a.y),d=Math.abs(b.bottom-a.y),e=Math.abs(b.right-a.x),f=Math.abs(b.left-a.x);switch(Math.min(c,d,e,f)){case f:return"left";case e:return"right";case c:return"top";case d:return"bottom";default:throw Error("unreachable")}}(d,c.getBoundingClientRect());j(function(a){let b=a.slice();return b.sort((a,b)=>a.x<b.x?-1:a.x>b.x?1:a.y<b.y?-1:1*!!(a.y>b.y)),function(a){if(a.length<=1)return a.slice();let b=[];for(let c=0;c<a.length;c++){let d=a[c];for(;b.length>=2;){let a=b[b.length-1],c=b[b.length-2];if((a.x-c.x)*(d.y-c.y)>=(a.y-c.y)*(d.x-c.x))b.pop();else break}b.push(d)}b.pop();let c=[];for(let b=a.length-1;b>=0;b--){let d=a[b];for(;c.length>=2;){let a=c[c.length-1],b=c[c.length-2];if((a.x-b.x)*(d.y-b.y)>=(a.y-b.y)*(d.x-b.x))c.pop();else break}c.push(d)}return(c.pop(),1===b.length&&1===c.length&&b[0].x===c[0].x&&b[0].y===c[0].y)?b:b.concat(c)}(b)}([...function(a,b,c=5){let d=[];switch(b){case"top":d.push({x:a.x-c,y:a.y+c},{x:a.x+c,y:a.y+c});break;case"bottom":d.push({x:a.x-c,y:a.y-c},{x:a.x+c,y:a.y-c});break;case"left":d.push({x:a.x+c,y:a.y-c},{x:a.x+c,y:a.y+c});break;case"right":d.push({x:a.x-c,y:a.y-c},{x:a.x-c,y:a.y+c})}return d}(d,e),...function(a){let{top:b,right:c,bottom:d,left:e}=a;return[{x:e,y:b},{x:c,y:b},{x:c,y:d},{x:e,y:d}]}(b.getBoundingClientRect())])),n(!0)},[n]);return d.useEffect(()=>()=>o(),[o]),d.useEffect(()=>{if(k&&m){let a=a=>p(a,m),b=a=>p(a,k);return k.addEventListener("pointerleave",a),m.addEventListener("pointerleave",b),()=>{k.removeEventListener("pointerleave",a),m.removeEventListener("pointerleave",b)}}},[k,m,p,o]),d.useEffect(()=>{if(i){let a=a=>{let b=a.target,c={x:a.clientX,y:a.clientY},d=k?.contains(b)||m?.contains(b),e=!function(a,b){let{x:c,y:d}=a,e=!1;for(let a=0,f=b.length-1;a<b.length;f=a++){let g=b[a],h=b[f],i=g.x,j=g.y,k=h.x,l=h.y;j>d!=l>d&&c<(k-i)*(d-j)/(l-j)+i&&(e=!e)}return e}(c,i);d?o():e&&(o(),l())};return document.addEventListener("pointermove",a),()=>document.removeEventListener("pointermove",a)}},[k,m,i,l,o]),(0,q.jsx)(P,{...a,ref:h})}),[M,N]=r(z,{isInside:!1}),O=(0,n.Dc)("TooltipContent"),P=d.forwardRef((a,b)=>{let{__scopeTooltip:c,children:e,"aria-label":f,onEscapeKeyDown:g,onPointerDownOutside:i,...k}=a,l=B(J,c),m=t(c),{onClose:n}=l;return d.useEffect(()=>(document.addEventListener(v,n),()=>document.removeEventListener(v,n)),[n]),d.useEffect(()=>{if(l.trigger){let a=a=>{let b=a.target;b?.contains(l.trigger)&&n()};return window.addEventListener("scroll",a,{capture:!0}),()=>window.removeEventListener("scroll",a,{capture:!0})}},[l.trigger,n]),(0,q.jsx)(h.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:g,onPointerDownOutside:i,onFocusOutside:a=>a.preventDefault(),onDismiss:n,children:(0,q.jsxs)(j.UC,{"data-state":l.stateAttribute,...m,...k,ref:b,style:{...k.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,q.jsx)(O,{children:e}),(0,q.jsx)(M,{scope:c,isInside:!0,children:(0,q.jsx)(p.bL,{id:l.contentId,role:"tooltip",children:f||e})})]})})});K.displayName=J;var Q="TooltipArrow",R=d.forwardRef((a,b)=>{let{__scopeTooltip:c,...d}=a,e=t(c);return N(Q,c).isInside?null:(0,q.jsx)(j.i3,{...e,...d,ref:b})});R.displayName=Q;var S=y,T=C,U=E,V=I,W=K,X=R},10022:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},11096:(a,b,c)=>{c.d(b,{H4:()=>w,bL:()=>v});var d=c(43210),e=c(11273),f=c(13495),g=c(66156),h=c(14163),i=c(57379);function j(){return()=>{}}var k=c(60687),l="Avatar",[m,n]=(0,e.A)(l),[o,p]=m(l),q=d.forwardRef((a,b)=>{let{__scopeAvatar:c,...e}=a,[f,g]=d.useState("idle");return(0,k.jsx)(o,{scope:c,imageLoadingStatus:f,onImageLoadingStatusChange:g,children:(0,k.jsx)(h.sG.span,{...e,ref:b})})});q.displayName=l;var r="AvatarImage";d.forwardRef((a,b)=>{let{__scopeAvatar:c,src:e,onLoadingStatusChange:l=()=>{},...m}=a,n=p(r,c),o=function(a,{referrerPolicy:b,crossOrigin:c}){let e=(0,i.useSyncExternalStore)(j,()=>!0,()=>!1),f=d.useRef(null),h=e?(f.current||(f.current=new window.Image),f.current):null,[k,l]=d.useState(()=>u(h,a));return(0,g.N)(()=>{l(u(h,a))},[h,a]),(0,g.N)(()=>{let a=a=>()=>{l(a)};if(!h)return;let d=a("loaded"),e=a("error");return h.addEventListener("load",d),h.addEventListener("error",e),b&&(h.referrerPolicy=b),"string"==typeof c&&(h.crossOrigin=c),()=>{h.removeEventListener("load",d),h.removeEventListener("error",e)}},[h,c,b]),k}(e,m),q=(0,f.c)(a=>{l(a),n.onImageLoadingStatusChange(a)});return(0,g.N)(()=>{"idle"!==o&&q(o)},[o,q]),"loaded"===o?(0,k.jsx)(h.sG.img,{...m,ref:b,src:e}):null}).displayName=r;var s="AvatarFallback",t=d.forwardRef((a,b)=>{let{__scopeAvatar:c,delayMs:e,...f}=a,g=p(s,c),[i,j]=d.useState(void 0===e);return d.useEffect(()=>{if(void 0!==e){let a=window.setTimeout(()=>j(!0),e);return()=>window.clearTimeout(a)}},[e]),i&&"loaded"!==g.imageLoadingStatus?(0,k.jsx)(h.sG.span,{...f,ref:b}):null});function u(a,b){return a?b?(a.src!==b&&(a.src=b),a.complete&&a.naturalWidth>0?"loaded":"loading"):"error":"idle"}t.displayName=s;var v=q,w=t},11273:(a,b,c)=>{c.d(b,{A:()=>g,q:()=>f});var d=c(43210),e=c(60687);function f(a,b){let c=d.createContext(b),f=a=>{let{children:b,...f}=a,g=d.useMemo(()=>f,Object.values(f));return(0,e.jsx)(c.Provider,{value:g,children:b})};return f.displayName=a+"Provider",[f,function(e){let f=d.useContext(c);if(f)return f;if(void 0!==b)return b;throw Error(`\`${e}\` must be used within \`${a}\``)}]}function g(a,b=[]){let c=[],f=()=>{let b=c.map(a=>d.createContext(a));return function(c){let e=c?.[a]||b;return d.useMemo(()=>({[`__scope${a}`]:{...c,[a]:e}}),[c,e])}};return f.scopeName=a,[function(b,f){let g=d.createContext(f),h=c.length;c=[...c,f];let i=b=>{let{scope:c,children:f,...i}=b,j=c?.[a]?.[h]||g,k=d.useMemo(()=>i,Object.values(i));return(0,e.jsx)(j.Provider,{value:k,children:f})};return i.displayName=b+"Provider",[i,function(c,e){let i=e?.[a]?.[h]||g,j=d.useContext(i);if(j)return j;if(void 0!==f)return f;throw Error(`\`${c}\` must be used within \`${b}\``)}]},function(...a){let b=a[0];if(1===a.length)return b;let c=()=>{let c=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let e=c.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return d.useMemo(()=>({[`__scope${b.scopeName}`]:e}),[e])}};return c.scopeName=b.scopeName,c}(f,...b)]}},11860:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},13495:(a,b,c)=>{c.d(b,{c:()=>e});var d=c(43210);function e(a){let b=d.useRef(a);return d.useEffect(()=>{b.current=a}),d.useMemo(()=>(...a)=>b.current?.(...a),[])}},18468:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function a(b,c,f){let g=f.length<=2,[h,i]=f,j=(0,d.createRouterCacheKey)(i),k=c.parallelRoutes.get(h);if(!k)return;let l=b.parallelRoutes.get(h);if(l&&l!==k||(l=new Map(k),b.parallelRoutes.set(h,l)),g)return void l.delete(j);let m=k.get(j),n=l.get(j);n&&m&&(n===m&&(n={lazyData:n.lazyData,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,parallelRoutes:new Map(n.parallelRoutes)},l.set(j,n)),a(n,m,(0,e.getNextFlightSegmentPath)(f)))}}});let d=c(33123),e=c(74007);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},18853:(a,b,c)=>{c.d(b,{X:()=>f});var d=c(43210),e=c(66156);function f(a){let[b,c]=d.useState(void 0);return(0,e.N)(()=>{if(a){c({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let d,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;d=b.inlineSize,e=b.blockSize}else d=a.offsetWidth,e=a.offsetHeight;c({width:d,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}c(void 0)},[a]),b}},19080:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},19169:(a,b)=>{function c(a){let b=a.indexOf("#"),c=a.indexOf("?"),d=c>-1&&(b<0||c<b);return d||b>-1?{pathname:a.substring(0,d?c:b),query:d?a.substring(c,b>-1?b:void 0):"",hash:b>-1?a.slice(b):""}:{pathname:a,query:"",hash:""}}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"parsePath",{enumerable:!0,get:function(){return c}})},22308:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{addRefreshMarkerToActiveParallelSegments:function(){return function a(b,c){let[d,e,,g]=b;for(let h in d.includes(f.PAGE_SEGMENT_KEY)&&"refresh"!==g&&(b[2]=c,b[3]="refresh"),e)a(e[h],c)}},refreshInactiveParallelSegments:function(){return g}});let d=c(56928),e=c(59008),f=c(83913);async function g(a){let b=new Set;await h({...a,rootTree:a.updatedTree,fetchedSegments:b})}async function h(a){let{navigatedAt:b,state:c,updatedTree:f,updatedCache:g,includeNextUrl:i,fetchedSegments:j,rootTree:k=f,canonicalUrl:l}=a,[,m,n,o]=f,p=[];if(n&&n!==l&&"refresh"===o&&!j.has(n)){j.add(n);let a=(0,e.fetchServerResponse)(new URL(n,location.origin),{flightRouterState:[k[0],k[1],k[2],"refetch"],nextUrl:i?c.nextUrl:null}).then(a=>{let{flightData:c}=a;if("string"!=typeof c)for(let a of c)(0,d.applyFlightData)(b,g,g,a)});p.push(a)}for(let a in m){let d=h({navigatedAt:b,state:c,updatedTree:m[a],updatedCache:g,includeNextUrl:i,fetchedSegments:j,rootTree:k,canonicalUrl:l});p.push(d)}await Promise.all(p)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},23240:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("images",[["path",{d:"M18 22H4a2 2 0 0 1-2-2V6",key:"pblm9e"}],["path",{d:"m22 13-1.296-1.296a2.41 2.41 0 0 0-3.408 0L11 18",key:"nf6bnh"}],["circle",{cx:"12",cy:"8",r:"2",key:"1822b1"}],["rect",{width:"16",height:"16",x:"6",y:"2",rx:"2",key:"12espp"}]])},24642:(a,b)=>{function c(a){let b=parseInt(a.slice(0,2),16),c=b>>1&63,d=Array(6);for(let a=0;a<6;a++){let b=c>>5-a&1;d[a]=1===b}return{type:1==(b>>7&1)?"use-cache":"server-action",usedArgs:d,hasRestArgs:1==(1&b)}}function d(a,b){let c=Array(a.length);for(let d=0;d<a.length;d++)(d<6&&b.usedArgs[d]||d>=6&&b.hasRestArgs)&&(c[d]=a[d]);return c}Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{extractInfoFromServerReferenceId:function(){return c},omitUnusedArgs:function(){return d}})},25028:(a,b,c)=>{c.d(b,{Z:()=>i});var d=c(43210),e=c(51215),f=c(14163),g=c(66156),h=c(60687),i=d.forwardRef((a,b)=>{let{container:c,...i}=a,[j,k]=d.useState(!1);(0,g.N)(()=>k(!0),[]);let l=c||j&&globalThis?.document?.body;return l?e.createPortal((0,h.jsx)(f.sG.div,{...i,ref:b}),l):null});i.displayName="Portal"},25232:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{handleExternalUrl:function(){return t},navigateReducer:function(){return function a(b,c){let{url:v,isExternalUrl:w,navigateType:x,shouldScroll:y,allowAliasing:z}=c,A={},{hash:B}=v,C=(0,e.createHrefFromUrl)(v),D="push"===x;if((0,q.prunePrefetchCache)(b.prefetchCache),A.preserveCustomHistoryState=!1,A.pendingPush=D,w)return t(b,A,v.toString(),D);if(document.getElementById("__next-page-redirect"))return t(b,A,C,D);let E=(0,q.getOrCreatePrefetchCacheEntry)({url:v,nextUrl:b.nextUrl,tree:b.tree,prefetchCache:b.prefetchCache,allowAliasing:z}),{treeAtTimeOfPrefetch:F,data:G}=E;return m.prefetchQueue.bump(G),G.then(m=>{let{flightData:q,canonicalUrl:w,postponed:x}=m,z=Date.now(),G=!1;if(E.lastUsedTime||(E.lastUsedTime=z,G=!0),E.aliased){let d=new URL(v.href);w&&(d.pathname=w.pathname);let e=(0,s.handleAliasedPrefetchEntry)(z,b,q,d,A);return!1===e?a(b,{...c,allowAliasing:!1}):e}if("string"==typeof q)return t(b,A,q,D);let H=w?(0,e.createHrefFromUrl)(w):C;if(B&&b.canonicalUrl.split("#",1)[0]===H.split("#",1)[0])return A.onlyHashChange=!0,A.canonicalUrl=H,A.shouldScroll=y,A.hashFragment=B,A.scrollableSegments=[],(0,k.handleMutable)(b,A);let I=b.tree,J=b.cache,K=[];for(let a of q){let{pathToSegment:c,seedData:e,head:k,isHeadPartial:m,isRootRender:q}=a,s=a.tree,w=["",...c],y=(0,g.applyRouterStatePatchToTree)(w,I,s,C);if(null===y&&(y=(0,g.applyRouterStatePatchToTree)(w,F,s,C)),null!==y){if(e&&q&&x){let a=(0,p.startPPRNavigation)(z,J,I,s,e,k,m,!1,K);if(null!==a){if(null===a.route)return t(b,A,C,D);y=a.route;let c=a.node;null!==c&&(A.cache=c);let e=a.dynamicRequestTree;if(null!==e){let c=(0,d.fetchServerResponse)(new URL(H,v.origin),{flightRouterState:e,nextUrl:b.nextUrl});(0,p.listenForDynamicRequest)(a,c)}}else y=s}else{if((0,i.isNavigatingToNewRootLayout)(I,y))return t(b,A,C,D);let d=(0,n.createEmptyCacheNode)(),e=!1;for(let b of(E.status!==j.PrefetchCacheEntryStatus.stale||G?e=(0,l.applyFlightData)(z,J,d,a,E):(e=function(a,b,c,d){let e=!1;for(let f of(a.rsc=b.rsc,a.prefetchRsc=b.prefetchRsc,a.loading=b.loading,a.parallelRoutes=new Map(b.parallelRoutes),u(d).map(a=>[...c,...a])))(0,r.clearCacheNodeDataForSegmentPath)(a,b,f),e=!0;return e}(d,J,c,s),E.lastUsedTime=z),(0,h.shouldHardNavigate)(w,I)?(d.rsc=J.rsc,d.prefetchRsc=J.prefetchRsc,(0,f.invalidateCacheBelowFlightSegmentPath)(d,J,c),A.cache=d):e&&(A.cache=d,J=d),u(s))){let a=[...c,...b];a[a.length-1]!==o.DEFAULT_SEGMENT_KEY&&K.push(a)}}I=y}}return A.patchedTree=I,A.canonicalUrl=H,A.scrollableSegments=K,A.hashFragment=B,A.shouldScroll=y,(0,k.handleMutable)(b,A)},()=>b)}}});let d=c(59008),e=c(57391),f=c(18468),g=c(86770),h=c(65951),i=c(2030),j=c(59154),k=c(59435),l=c(56928),m=c(75076),n=c(89752),o=c(83913),p=c(65956),q=c(5334),r=c(97464),s=c(9707);function t(a,b,c,d){return b.mpaNavigation=!0,b.canonicalUrl=c,b.pendingPush=d,b.scrollableSegments=void 0,(0,k.handleMutable)(a,b)}function u(a){let b=[],[c,d]=a;if(0===Object.keys(d).length)return[[c]];for(let[a,e]of Object.entries(d))for(let d of u(e))""===c?b.push([a,...d]):b.push([c,a,...d]);return b}c(50593),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},25942:(a,b,c)=>{function d(a){return a}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"removeBasePath",{enumerable:!0,get:function(){return d}}),c(26736),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},26134:(a,b,c)=>{c.d(b,{UC:()=>aa,VY:()=>ac,ZL:()=>$,bL:()=>Z,bm:()=>ad,hE:()=>ab,hJ:()=>_});var d=c(43210),e=c(70569),f=c(98599),g=c(11273),h=c(96963),i=c(65551),j=c(31355),k=c(32547),l=c(25028),m=c(46059),n=c(14163),o=c(1359),p=c(42247),q=c(63376),r=c(8730),s=c(60687),t="Dialog",[u,v]=(0,g.A)(t),[w,x]=u(t),y=a=>{let{__scopeDialog:b,children:c,open:e,defaultOpen:f,onOpenChange:g,modal:j=!0}=a,k=d.useRef(null),l=d.useRef(null),[m,n]=(0,i.i)({prop:e,defaultProp:f??!1,onChange:g,caller:t});return(0,s.jsx)(w,{scope:b,triggerRef:k,contentRef:l,contentId:(0,h.B)(),titleId:(0,h.B)(),descriptionId:(0,h.B)(),open:m,onOpenChange:n,onOpenToggle:d.useCallback(()=>n(a=>!a),[n]),modal:j,children:c})};y.displayName=t;var z="DialogTrigger";d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,g=x(z,c),h=(0,f.s)(b,g.triggerRef);return(0,s.jsx)(n.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":g.open,"aria-controls":g.contentId,"data-state":T(g.open),...d,ref:h,onClick:(0,e.m)(a.onClick,g.onOpenToggle)})}).displayName=z;var A="DialogPortal",[B,C]=u(A,{forceMount:void 0}),D=a=>{let{__scopeDialog:b,forceMount:c,children:e,container:f}=a,g=x(A,b);return(0,s.jsx)(B,{scope:b,forceMount:c,children:d.Children.map(e,a=>(0,s.jsx)(m.C,{present:c||g.open,children:(0,s.jsx)(l.Z,{asChild:!0,container:f,children:a})}))})};D.displayName=A;var E="DialogOverlay",F=d.forwardRef((a,b)=>{let c=C(E,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=x(E,a.__scopeDialog);return f.modal?(0,s.jsx)(m.C,{present:d||f.open,children:(0,s.jsx)(H,{...e,ref:b})}):null});F.displayName=E;var G=(0,r.TL)("DialogOverlay.RemoveScroll"),H=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=x(E,c);return(0,s.jsx)(p.A,{as:G,allowPinchZoom:!0,shards:[e.contentRef],children:(0,s.jsx)(n.sG.div,{"data-state":T(e.open),...d,ref:b,style:{pointerEvents:"auto",...d.style}})})}),I="DialogContent",J=d.forwardRef((a,b)=>{let c=C(I,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=x(I,a.__scopeDialog);return(0,s.jsx)(m.C,{present:d||f.open,children:f.modal?(0,s.jsx)(K,{...e,ref:b}):(0,s.jsx)(L,{...e,ref:b})})});J.displayName=I;var K=d.forwardRef((a,b)=>{let c=x(I,a.__scopeDialog),g=d.useRef(null),h=(0,f.s)(b,c.contentRef,g);return d.useEffect(()=>{let a=g.current;if(a)return(0,q.Eq)(a)},[]),(0,s.jsx)(M,{...a,ref:h,trapFocus:c.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,e.m)(a.onCloseAutoFocus,a=>{a.preventDefault(),c.triggerRef.current?.focus()}),onPointerDownOutside:(0,e.m)(a.onPointerDownOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey;(2===b.button||c)&&a.preventDefault()}),onFocusOutside:(0,e.m)(a.onFocusOutside,a=>a.preventDefault())})}),L=d.forwardRef((a,b)=>{let c=x(I,a.__scopeDialog),e=d.useRef(!1),f=d.useRef(!1);return(0,s.jsx)(M,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:b=>{a.onCloseAutoFocus?.(b),b.defaultPrevented||(e.current||c.triggerRef.current?.focus(),b.preventDefault()),e.current=!1,f.current=!1},onInteractOutside:b=>{a.onInteractOutside?.(b),b.defaultPrevented||(e.current=!0,"pointerdown"===b.detail.originalEvent.type&&(f.current=!0));let d=b.target;c.triggerRef.current?.contains(d)&&b.preventDefault(),"focusin"===b.detail.originalEvent.type&&f.current&&b.preventDefault()}})}),M=d.forwardRef((a,b)=>{let{__scopeDialog:c,trapFocus:e,onOpenAutoFocus:g,onCloseAutoFocus:h,...i}=a,l=x(I,c),m=d.useRef(null),n=(0,f.s)(b,m);return(0,o.Oh)(),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(k.n,{asChild:!0,loop:!0,trapped:e,onMountAutoFocus:g,onUnmountAutoFocus:h,children:(0,s.jsx)(j.qW,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":T(l.open),...i,ref:n,onDismiss:()=>l.onOpenChange(!1)})}),(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(X,{titleId:l.titleId}),(0,s.jsx)(Y,{contentRef:m,descriptionId:l.descriptionId})]})]})}),N="DialogTitle",O=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=x(N,c);return(0,s.jsx)(n.sG.h2,{id:e.titleId,...d,ref:b})});O.displayName=N;var P="DialogDescription",Q=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=x(P,c);return(0,s.jsx)(n.sG.p,{id:e.descriptionId,...d,ref:b})});Q.displayName=P;var R="DialogClose",S=d.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,f=x(R,c);return(0,s.jsx)(n.sG.button,{type:"button",...d,ref:b,onClick:(0,e.m)(a.onClick,()=>f.onOpenChange(!1))})});function T(a){return a?"open":"closed"}S.displayName=R;var U="DialogTitleWarning",[V,W]=(0,g.q)(U,{contentName:I,titleName:N,docsSlug:"dialog"}),X=({titleId:a})=>{let b=W(U),c=`\`${b.contentName}\` requires a \`${b.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${b.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${b.docsSlug}`;return d.useEffect(()=>{a&&(document.getElementById(a)||console.error(c))},[c,a]),null},Y=({contentRef:a,descriptionId:b})=>{let c=W("DialogDescriptionWarning"),e=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${c.contentName}}.`;return d.useEffect(()=>{let c=a.current?.getAttribute("aria-describedby");b&&c&&(document.getElementById(b)||console.warn(e))},[e,a,b]),null},Z=y,$=D,_=F,aa=J,ab=O,ac=Q,ad=S},26312:(a,b,c)=>{c.d(b,{UC:()=>aW,q7:()=>aY,JU:()=>aX,ZL:()=>aV,bL:()=>aT,wv:()=>aZ,l9:()=>aU});var d=c(43210),e=c(70569),f=c(98599),g=c(11273),h=c(65551),i=c(14163),j=c(9510),k=c(43),l=c(31355),m=c(1359),n=c(32547),o=c(96963),p=c(55509),q=c(25028),r=c(46059),s=c(72942),t=c(8730),u=c(13495),v=c(63376),w=c(42247),x=c(60687),y=["Enter"," "],z=["ArrowUp","PageDown","End"],A=["ArrowDown","PageUp","Home",...z],B={ltr:[...y,"ArrowRight"],rtl:[...y,"ArrowLeft"]},C={ltr:["ArrowLeft"],rtl:["ArrowRight"]},D="Menu",[E,F,G]=(0,j.N)(D),[H,I]=(0,g.A)(D,[G,p.Bk,s.RG]),J=(0,p.Bk)(),K=(0,s.RG)(),[L,M]=H(D),[N,O]=H(D),P=a=>{let{__scopeMenu:b,open:c=!1,children:e,dir:f,onOpenChange:g,modal:h=!0}=a,i=J(b),[j,l]=d.useState(null),m=d.useRef(!1),n=(0,u.c)(g),o=(0,k.jH)(f);return d.useEffect(()=>{let a=()=>{m.current=!0,document.addEventListener("pointerdown",b,{capture:!0,once:!0}),document.addEventListener("pointermove",b,{capture:!0,once:!0})},b=()=>m.current=!1;return document.addEventListener("keydown",a,{capture:!0}),()=>{document.removeEventListener("keydown",a,{capture:!0}),document.removeEventListener("pointerdown",b,{capture:!0}),document.removeEventListener("pointermove",b,{capture:!0})}},[]),(0,x.jsx)(p.bL,{...i,children:(0,x.jsx)(L,{scope:b,open:c,onOpenChange:n,content:j,onContentChange:l,children:(0,x.jsx)(N,{scope:b,onClose:d.useCallback(()=>n(!1),[n]),isUsingKeyboardRef:m,dir:o,modal:h,children:e})})})};P.displayName=D;var Q=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,e=J(c);return(0,x.jsx)(p.Mz,{...e,...d,ref:b})});Q.displayName="MenuAnchor";var R="MenuPortal",[S,T]=H(R,{forceMount:void 0}),U=a=>{let{__scopeMenu:b,forceMount:c,children:d,container:e}=a,f=M(R,b);return(0,x.jsx)(S,{scope:b,forceMount:c,children:(0,x.jsx)(r.C,{present:c||f.open,children:(0,x.jsx)(q.Z,{asChild:!0,container:e,children:d})})})};U.displayName=R;var V="MenuContent",[W,X]=H(V),Y=d.forwardRef((a,b)=>{let c=T(V,a.__scopeMenu),{forceMount:d=c.forceMount,...e}=a,f=M(V,a.__scopeMenu),g=O(V,a.__scopeMenu);return(0,x.jsx)(E.Provider,{scope:a.__scopeMenu,children:(0,x.jsx)(r.C,{present:d||f.open,children:(0,x.jsx)(E.Slot,{scope:a.__scopeMenu,children:g.modal?(0,x.jsx)(Z,{...e,ref:b}):(0,x.jsx)($,{...e,ref:b})})})})}),Z=d.forwardRef((a,b)=>{let c=M(V,a.__scopeMenu),g=d.useRef(null),h=(0,f.s)(b,g);return d.useEffect(()=>{let a=g.current;if(a)return(0,v.Eq)(a)},[]),(0,x.jsx)(aa,{...a,ref:h,trapFocus:c.open,disableOutsidePointerEvents:c.open,disableOutsideScroll:!0,onFocusOutside:(0,e.m)(a.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>c.onOpenChange(!1)})}),$=d.forwardRef((a,b)=>{let c=M(V,a.__scopeMenu);return(0,x.jsx)(aa,{...a,ref:b,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>c.onOpenChange(!1)})}),_=(0,t.TL)("MenuContent.ScrollLock"),aa=d.forwardRef((a,b)=>{let{__scopeMenu:c,loop:g=!1,trapFocus:h,onOpenAutoFocus:i,onCloseAutoFocus:j,disableOutsidePointerEvents:k,onEntryFocus:o,onEscapeKeyDown:q,onPointerDownOutside:r,onFocusOutside:t,onInteractOutside:u,onDismiss:v,disableOutsideScroll:y,...B}=a,C=M(V,c),D=O(V,c),E=J(c),G=K(c),H=F(c),[I,L]=d.useState(null),N=d.useRef(null),P=(0,f.s)(b,N,C.onContentChange),Q=d.useRef(0),R=d.useRef(""),S=d.useRef(0),T=d.useRef(null),U=d.useRef("right"),X=d.useRef(0),Y=y?w.A:d.Fragment;d.useEffect(()=>()=>window.clearTimeout(Q.current),[]),(0,m.Oh)();let Z=d.useCallback(a=>U.current===T.current?.side&&function(a,b){return!!b&&function(a,b){let{x:c,y:d}=a,e=!1;for(let a=0,f=b.length-1;a<b.length;f=a++){let g=b[a],h=b[f],i=g.x,j=g.y,k=h.x,l=h.y;j>d!=l>d&&c<(k-i)*(d-j)/(l-j)+i&&(e=!e)}return e}({x:a.clientX,y:a.clientY},b)}(a,T.current?.area),[]);return(0,x.jsx)(W,{scope:c,searchRef:R,onItemEnter:d.useCallback(a=>{Z(a)&&a.preventDefault()},[Z]),onItemLeave:d.useCallback(a=>{Z(a)||(N.current?.focus(),L(null))},[Z]),onTriggerLeave:d.useCallback(a=>{Z(a)&&a.preventDefault()},[Z]),pointerGraceTimerRef:S,onPointerGraceIntentChange:d.useCallback(a=>{T.current=a},[]),children:(0,x.jsx)(Y,{...y?{as:_,allowPinchZoom:!0}:void 0,children:(0,x.jsx)(n.n,{asChild:!0,trapped:h,onMountAutoFocus:(0,e.m)(i,a=>{a.preventDefault(),N.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:j,children:(0,x.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:k,onEscapeKeyDown:q,onPointerDownOutside:r,onFocusOutside:t,onInteractOutside:u,onDismiss:v,children:(0,x.jsx)(s.bL,{asChild:!0,...G,dir:D.dir,orientation:"vertical",loop:g,currentTabStopId:I,onCurrentTabStopIdChange:L,onEntryFocus:(0,e.m)(o,a=>{D.isUsingKeyboardRef.current||a.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,x.jsx)(p.UC,{role:"menu","aria-orientation":"vertical","data-state":aA(C.open),"data-radix-menu-content":"",dir:D.dir,...E,...B,ref:P,style:{outline:"none",...B.style},onKeyDown:(0,e.m)(B.onKeyDown,a=>{let b=a.target.closest("[data-radix-menu-content]")===a.currentTarget,c=a.ctrlKey||a.altKey||a.metaKey,d=1===a.key.length;b&&("Tab"===a.key&&a.preventDefault(),!c&&d&&(a=>{let b=R.current+a,c=H().filter(a=>!a.disabled),d=document.activeElement,e=c.find(a=>a.ref.current===d)?.textValue,f=function(a,b,c){var d;let e=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,f=c?a.indexOf(c):-1,g=(d=Math.max(f,0),a.map((b,c)=>a[(d+c)%a.length]));1===e.length&&(g=g.filter(a=>a!==c));let h=g.find(a=>a.toLowerCase().startsWith(e.toLowerCase()));return h!==c?h:void 0}(c.map(a=>a.textValue),b,e),g=c.find(a=>a.textValue===f)?.ref.current;!function a(b){R.current=b,window.clearTimeout(Q.current),""!==b&&(Q.current=window.setTimeout(()=>a(""),1e3))}(b),g&&setTimeout(()=>g.focus())})(a.key));let e=N.current;if(a.target!==e||!A.includes(a.key))return;a.preventDefault();let f=H().filter(a=>!a.disabled).map(a=>a.ref.current);z.includes(a.key)&&f.reverse(),function(a){let b=document.activeElement;for(let c of a)if(c===b||(c.focus(),document.activeElement!==b))return}(f)}),onBlur:(0,e.m)(a.onBlur,a=>{a.currentTarget.contains(a.target)||(window.clearTimeout(Q.current),R.current="")}),onPointerMove:(0,e.m)(a.onPointerMove,aD(a=>{let b=a.target,c=X.current!==a.clientX;a.currentTarget.contains(b)&&c&&(U.current=a.clientX>X.current?"right":"left",X.current=a.clientX)}))})})})})})})});Y.displayName=V;var ab=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,x.jsx)(i.sG.div,{role:"group",...d,ref:b})});ab.displayName="MenuGroup";var ac=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,x.jsx)(i.sG.div,{...d,ref:b})});ac.displayName="MenuLabel";var ad="MenuItem",ae="menu.itemSelect",af=d.forwardRef((a,b)=>{let{disabled:c=!1,onSelect:g,...h}=a,j=d.useRef(null),k=O(ad,a.__scopeMenu),l=X(ad,a.__scopeMenu),m=(0,f.s)(b,j),n=d.useRef(!1);return(0,x.jsx)(ag,{...h,ref:m,disabled:c,onClick:(0,e.m)(a.onClick,()=>{let a=j.current;if(!c&&a){let b=new CustomEvent(ae,{bubbles:!0,cancelable:!0});a.addEventListener(ae,a=>g?.(a),{once:!0}),(0,i.hO)(a,b),b.defaultPrevented?n.current=!1:k.onClose()}}),onPointerDown:b=>{a.onPointerDown?.(b),n.current=!0},onPointerUp:(0,e.m)(a.onPointerUp,a=>{n.current||a.currentTarget?.click()}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{let b=""!==l.searchRef.current;c||b&&" "===a.key||y.includes(a.key)&&(a.currentTarget.click(),a.preventDefault())})})});af.displayName=ad;var ag=d.forwardRef((a,b)=>{let{__scopeMenu:c,disabled:g=!1,textValue:h,...j}=a,k=X(ad,c),l=K(c),m=d.useRef(null),n=(0,f.s)(b,m),[o,p]=d.useState(!1),[q,r]=d.useState("");return d.useEffect(()=>{let a=m.current;a&&r((a.textContent??"").trim())},[j.children]),(0,x.jsx)(E.ItemSlot,{scope:c,disabled:g,textValue:h??q,children:(0,x.jsx)(s.q7,{asChild:!0,...l,focusable:!g,children:(0,x.jsx)(i.sG.div,{role:"menuitem","data-highlighted":o?"":void 0,"aria-disabled":g||void 0,"data-disabled":g?"":void 0,...j,ref:n,onPointerMove:(0,e.m)(a.onPointerMove,aD(a=>{g?k.onItemLeave(a):(k.onItemEnter(a),a.defaultPrevented||a.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,e.m)(a.onPointerLeave,aD(a=>k.onItemLeave(a))),onFocus:(0,e.m)(a.onFocus,()=>p(!0)),onBlur:(0,e.m)(a.onBlur,()=>p(!1))})})})}),ah=d.forwardRef((a,b)=>{let{checked:c=!1,onCheckedChange:d,...f}=a;return(0,x.jsx)(ap,{scope:a.__scopeMenu,checked:c,children:(0,x.jsx)(af,{role:"menuitemcheckbox","aria-checked":aB(c)?"mixed":c,...f,ref:b,"data-state":aC(c),onSelect:(0,e.m)(f.onSelect,()=>d?.(!!aB(c)||!c),{checkForDefaultPrevented:!1})})})});ah.displayName="MenuCheckboxItem";var ai="MenuRadioGroup",[aj,ak]=H(ai,{value:void 0,onValueChange:()=>{}}),al=d.forwardRef((a,b)=>{let{value:c,onValueChange:d,...e}=a,f=(0,u.c)(d);return(0,x.jsx)(aj,{scope:a.__scopeMenu,value:c,onValueChange:f,children:(0,x.jsx)(ab,{...e,ref:b})})});al.displayName=ai;var am="MenuRadioItem",an=d.forwardRef((a,b)=>{let{value:c,...d}=a,f=ak(am,a.__scopeMenu),g=c===f.value;return(0,x.jsx)(ap,{scope:a.__scopeMenu,checked:g,children:(0,x.jsx)(af,{role:"menuitemradio","aria-checked":g,...d,ref:b,"data-state":aC(g),onSelect:(0,e.m)(d.onSelect,()=>f.onValueChange?.(c),{checkForDefaultPrevented:!1})})})});an.displayName=am;var ao="MenuItemIndicator",[ap,aq]=H(ao,{checked:!1}),ar=d.forwardRef((a,b)=>{let{__scopeMenu:c,forceMount:d,...e}=a,f=aq(ao,c);return(0,x.jsx)(r.C,{present:d||aB(f.checked)||!0===f.checked,children:(0,x.jsx)(i.sG.span,{...e,ref:b,"data-state":aC(f.checked)})})});ar.displayName=ao;var as=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a;return(0,x.jsx)(i.sG.div,{role:"separator","aria-orientation":"horizontal",...d,ref:b})});as.displayName="MenuSeparator";var at=d.forwardRef((a,b)=>{let{__scopeMenu:c,...d}=a,e=J(c);return(0,x.jsx)(p.i3,{...e,...d,ref:b})});at.displayName="MenuArrow";var[au,av]=H("MenuSub"),aw="MenuSubTrigger",ax=d.forwardRef((a,b)=>{let c=M(aw,a.__scopeMenu),g=O(aw,a.__scopeMenu),h=av(aw,a.__scopeMenu),i=X(aw,a.__scopeMenu),j=d.useRef(null),{pointerGraceTimerRef:k,onPointerGraceIntentChange:l}=i,m={__scopeMenu:a.__scopeMenu},n=d.useCallback(()=>{j.current&&window.clearTimeout(j.current),j.current=null},[]);return d.useEffect(()=>n,[n]),d.useEffect(()=>{let a=k.current;return()=>{window.clearTimeout(a),l(null)}},[k,l]),(0,x.jsx)(Q,{asChild:!0,...m,children:(0,x.jsx)(ag,{id:h.triggerId,"aria-haspopup":"menu","aria-expanded":c.open,"aria-controls":h.contentId,"data-state":aA(c.open),...a,ref:(0,f.t)(b,h.onTriggerChange),onClick:b=>{a.onClick?.(b),a.disabled||b.defaultPrevented||(b.currentTarget.focus(),c.open||c.onOpenChange(!0))},onPointerMove:(0,e.m)(a.onPointerMove,aD(b=>{i.onItemEnter(b),!b.defaultPrevented&&(a.disabled||c.open||j.current||(i.onPointerGraceIntentChange(null),j.current=window.setTimeout(()=>{c.onOpenChange(!0),n()},100)))})),onPointerLeave:(0,e.m)(a.onPointerLeave,aD(a=>{n();let b=c.content?.getBoundingClientRect();if(b){let d=c.content?.dataset.side,e="right"===d,f=b[e?"left":"right"],g=b[e?"right":"left"];i.onPointerGraceIntentChange({area:[{x:a.clientX+(e?-5:5),y:a.clientY},{x:f,y:b.top},{x:g,y:b.top},{x:g,y:b.bottom},{x:f,y:b.bottom}],side:d}),window.clearTimeout(k.current),k.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(a),a.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,e.m)(a.onKeyDown,b=>{let d=""!==i.searchRef.current;a.disabled||d&&" "===b.key||B[g.dir].includes(b.key)&&(c.onOpenChange(!0),c.content?.focus(),b.preventDefault())})})})});ax.displayName=aw;var ay="MenuSubContent",az=d.forwardRef((a,b)=>{let c=T(V,a.__scopeMenu),{forceMount:g=c.forceMount,...h}=a,i=M(V,a.__scopeMenu),j=O(V,a.__scopeMenu),k=av(ay,a.__scopeMenu),l=d.useRef(null),m=(0,f.s)(b,l);return(0,x.jsx)(E.Provider,{scope:a.__scopeMenu,children:(0,x.jsx)(r.C,{present:g||i.open,children:(0,x.jsx)(E.Slot,{scope:a.__scopeMenu,children:(0,x.jsx)(aa,{id:k.contentId,"aria-labelledby":k.triggerId,...h,ref:m,align:"start",side:"rtl"===j.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:a=>{j.isUsingKeyboardRef.current&&l.current?.focus(),a.preventDefault()},onCloseAutoFocus:a=>a.preventDefault(),onFocusOutside:(0,e.m)(a.onFocusOutside,a=>{a.target!==k.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,e.m)(a.onEscapeKeyDown,a=>{j.onClose(),a.preventDefault()}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{let b=a.currentTarget.contains(a.target),c=C[j.dir].includes(a.key);b&&c&&(i.onOpenChange(!1),k.trigger?.focus(),a.preventDefault())})})})})})});function aA(a){return a?"open":"closed"}function aB(a){return"indeterminate"===a}function aC(a){return aB(a)?"indeterminate":a?"checked":"unchecked"}function aD(a){return b=>"mouse"===b.pointerType?a(b):void 0}az.displayName=ay;var aE="DropdownMenu",[aF,aG]=(0,g.A)(aE,[I]),aH=I(),[aI,aJ]=aF(aE),aK=a=>{let{__scopeDropdownMenu:b,children:c,dir:e,open:f,defaultOpen:g,onOpenChange:i,modal:j=!0}=a,k=aH(b),l=d.useRef(null),[m,n]=(0,h.i)({prop:f,defaultProp:g??!1,onChange:i,caller:aE});return(0,x.jsx)(aI,{scope:b,triggerId:(0,o.B)(),triggerRef:l,contentId:(0,o.B)(),open:m,onOpenChange:n,onOpenToggle:d.useCallback(()=>n(a=>!a),[n]),modal:j,children:(0,x.jsx)(P,{...k,open:m,onOpenChange:n,dir:e,modal:j,children:c})})};aK.displayName=aE;var aL="DropdownMenuTrigger",aM=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,disabled:d=!1,...g}=a,h=aJ(aL,c),j=aH(c);return(0,x.jsx)(Q,{asChild:!0,...j,children:(0,x.jsx)(i.sG.button,{type:"button",id:h.triggerId,"aria-haspopup":"menu","aria-expanded":h.open,"aria-controls":h.open?h.contentId:void 0,"data-state":h.open?"open":"closed","data-disabled":d?"":void 0,disabled:d,...g,ref:(0,f.t)(b,h.triggerRef),onPointerDown:(0,e.m)(a.onPointerDown,a=>{!d&&0===a.button&&!1===a.ctrlKey&&(h.onOpenToggle(),h.open||a.preventDefault())}),onKeyDown:(0,e.m)(a.onKeyDown,a=>{!d&&(["Enter"," "].includes(a.key)&&h.onOpenToggle(),"ArrowDown"===a.key&&h.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(a.key)&&a.preventDefault())})})})});aM.displayName=aL;var aN=a=>{let{__scopeDropdownMenu:b,...c}=a,d=aH(b);return(0,x.jsx)(U,{...d,...c})};aN.displayName="DropdownMenuPortal";var aO="DropdownMenuContent",aP=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...f}=a,g=aJ(aO,c),h=aH(c),i=d.useRef(!1);return(0,x.jsx)(Y,{id:g.contentId,"aria-labelledby":g.triggerId,...h,...f,ref:b,onCloseAutoFocus:(0,e.m)(a.onCloseAutoFocus,a=>{i.current||g.triggerRef.current?.focus(),i.current=!1,a.preventDefault()}),onInteractOutside:(0,e.m)(a.onInteractOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey,d=2===b.button||c;(!g.modal||d)&&(i.current=!0)}),style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});aP.displayName=aO,d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=aH(c);return(0,x.jsx)(ab,{...e,...d,ref:b})}).displayName="DropdownMenuGroup";var aQ=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=aH(c);return(0,x.jsx)(ac,{...e,...d,ref:b})});aQ.displayName="DropdownMenuLabel";var aR=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=aH(c);return(0,x.jsx)(af,{...e,...d,ref:b})});aR.displayName="DropdownMenuItem",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=aH(c);return(0,x.jsx)(ah,{...e,...d,ref:b})}).displayName="DropdownMenuCheckboxItem",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=aH(c);return(0,x.jsx)(al,{...e,...d,ref:b})}).displayName="DropdownMenuRadioGroup",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=aH(c);return(0,x.jsx)(an,{...e,...d,ref:b})}).displayName="DropdownMenuRadioItem",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=aH(c);return(0,x.jsx)(ar,{...e,...d,ref:b})}).displayName="DropdownMenuItemIndicator";var aS=d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=aH(c);return(0,x.jsx)(as,{...e,...d,ref:b})});aS.displayName="DropdownMenuSeparator",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=aH(c);return(0,x.jsx)(at,{...e,...d,ref:b})}).displayName="DropdownMenuArrow",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=aH(c);return(0,x.jsx)(ax,{...e,...d,ref:b})}).displayName="DropdownMenuSubTrigger",d.forwardRef((a,b)=>{let{__scopeDropdownMenu:c,...d}=a,e=aH(c);return(0,x.jsx)(az,{...e,...d,ref:b,style:{...a.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var aT=aK,aU=aM,aV=aN,aW=aP,aX=aQ,aY=aR,aZ=aS},26736:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"hasBasePath",{enumerable:!0,get:function(){return e}});let d=c(2255);function e(a){return(0,d.pathHasPrefix)(a,"")}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},28627:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"restoreReducer",{enumerable:!0,get:function(){return f}});let d=c(57391),e=c(70642);function f(a,b){var c;let{url:f,tree:g}=b,h=(0,d.createHrefFromUrl)(f),i=g||a.tree,j=a.cache;return{canonicalUrl:h,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:a.focusAndScrollRef,cache:j,prefetchCache:a.prefetchCache,tree:i,nextUrl:null!=(c=(0,e.extractPathFromFlightRouterState)(i))?c:f.pathname}}c(65956),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},29651:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"serverPatchReducer",{enumerable:!0,get:function(){return k}});let d=c(57391),e=c(86770),f=c(2030),g=c(25232),h=c(56928),i=c(59435),j=c(89752);function k(a,b){let{serverResponse:{flightData:c,canonicalUrl:k},navigatedAt:l}=b,m={};if(m.preserveCustomHistoryState=!1,"string"==typeof c)return(0,g.handleExternalUrl)(a,m,c,a.pushRef.pendingPush);let n=a.tree,o=a.cache;for(let b of c){let{segmentPath:c,tree:i}=b,p=(0,e.applyRouterStatePatchToTree)(["",...c],n,i,a.canonicalUrl);if(null===p)return a;if((0,f.isNavigatingToNewRootLayout)(n,p))return(0,g.handleExternalUrl)(a,m,a.canonicalUrl,a.pushRef.pendingPush);let q=k?(0,d.createHrefFromUrl)(k):void 0;q&&(m.canonicalUrl=q);let r=(0,j.createEmptyCacheNode)();(0,h.applyFlightData)(l,o,r,b),m.patchedTree=p,m.cache=r,o=r,n=p}return(0,i.handleMutable)(a,m)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},30195:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{formatUrl:function(){return f},formatWithValidation:function(){return h},urlObjectKeys:function(){return g}});let d=c(40740)._(c(76715)),e=/https?|ftp|gopher|file/;function f(a){let{auth:b,hostname:c}=a,f=a.protocol||"",g=a.pathname||"",h=a.hash||"",i=a.query||"",j=!1;b=b?encodeURIComponent(b).replace(/%3A/i,":")+"@":"",a.host?j=b+a.host:c&&(j=b+(~c.indexOf(":")?"["+c+"]":c),a.port&&(j+=":"+a.port)),i&&"object"==typeof i&&(i=String(d.urlQueryToSearchParams(i)));let k=a.search||i&&"?"+i||"";return f&&!f.endsWith(":")&&(f+=":"),a.slashes||(!f||e.test(f))&&!1!==j?(j="//"+(j||""),g&&"/"!==g[0]&&(g="/"+g)):j||(j=""),h&&"#"!==h[0]&&(h="#"+h),k&&"?"!==k[0]&&(k="?"+k),""+f+j+(g=g.replace(/[?#]/g,encodeURIComponent))+(k=k.replace("#","%23"))+h}let g=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function h(a){return f(a)}},31355:(a,b,c)=>{c.d(b,{qW:()=>m});var d,e=c(43210),f=c(70569),g=c(14163),h=c(98599),i=c(13495),j=c(60687),k="dismissableLayer.update",l=e.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),m=e.forwardRef((a,b)=>{let{disableOutsidePointerEvents:c=!1,onEscapeKeyDown:m,onPointerDownOutside:p,onFocusOutside:q,onInteractOutside:r,onDismiss:s,...t}=a,u=e.useContext(l),[v,w]=e.useState(null),x=v?.ownerDocument??globalThis?.document,[,y]=e.useState({}),z=(0,h.s)(b,a=>w(a)),A=Array.from(u.layers),[B]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),C=A.indexOf(B),D=v?A.indexOf(v):-1,E=u.layersWithOutsidePointerEventsDisabled.size>0,F=D>=C,G=function(a,b=globalThis?.document){let c=(0,i.c)(a),d=e.useRef(!1),f=e.useRef(()=>{});return e.useEffect(()=>{let a=a=>{if(a.target&&!d.current){let d=function(){o("dismissableLayer.pointerDownOutside",c,e,{discrete:!0})},e={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",f.current),f.current=d,b.addEventListener("click",f.current,{once:!0})):d()}else b.removeEventListener("click",f.current);d.current=!1},e=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(e),b.removeEventListener("pointerdown",a),b.removeEventListener("click",f.current)}},[b,c]),{onPointerDownCapture:()=>d.current=!0}}(a=>{let b=a.target,c=[...u.branches].some(a=>a.contains(b));F&&!c&&(p?.(a),r?.(a),a.defaultPrevented||s?.())},x),H=function(a,b=globalThis?.document){let c=(0,i.c)(a),d=e.useRef(!1);return e.useEffect(()=>{let a=a=>{a.target&&!d.current&&o("dismissableLayer.focusOutside",c,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,c]),{onFocusCapture:()=>d.current=!0,onBlurCapture:()=>d.current=!1}}(a=>{let b=a.target;![...u.branches].some(a=>a.contains(b))&&(q?.(a),r?.(a),a.defaultPrevented||s?.())},x);return!function(a,b=globalThis?.document){let c=(0,i.c)(a);e.useEffect(()=>{let a=a=>{"Escape"===a.key&&c(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[c,b])}(a=>{D===u.layers.size-1&&(m?.(a),!a.defaultPrevented&&s&&(a.preventDefault(),s()))},x),e.useEffect(()=>{if(v)return c&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(d=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(v)),u.layers.add(v),n(),()=>{c&&1===u.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=d)}},[v,x,c,u]),e.useEffect(()=>()=>{v&&(u.layers.delete(v),u.layersWithOutsidePointerEventsDisabled.delete(v),n())},[v,u]),e.useEffect(()=>{let a=()=>y({});return document.addEventListener(k,a),()=>document.removeEventListener(k,a)},[]),(0,j.jsx)(g.sG.div,{...t,ref:z,style:{pointerEvents:E?F?"auto":"none":void 0,...a.style},onFocusCapture:(0,f.m)(a.onFocusCapture,H.onFocusCapture),onBlurCapture:(0,f.m)(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:(0,f.m)(a.onPointerDownCapture,G.onPointerDownCapture)})});function n(){let a=new CustomEvent(k);document.dispatchEvent(a)}function o(a,b,c,{discrete:d}){let e=c.originalEvent.target,f=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,g.hO)(e,f):e.dispatchEvent(f)}m.displayName="DismissableLayer",e.forwardRef((a,b)=>{let c=e.useContext(l),d=e.useRef(null),f=(0,h.s)(b,d);return e.useEffect(()=>{let a=d.current;if(a)return c.branches.add(a),()=>{c.branches.delete(a)}},[c.branches]),(0,j.jsx)(g.sG.div,{...a,ref:f})}).displayName="DismissableLayerBranch"},32547:(a,b,c)=>{c.d(b,{n:()=>l});var d=c(43210),e=c(98599),f=c(14163),g=c(13495),h=c(60687),i="focusScope.autoFocusOnMount",j="focusScope.autoFocusOnUnmount",k={bubbles:!1,cancelable:!0},l=d.forwardRef((a,b)=>{let{loop:c=!1,trapped:l=!1,onMountAutoFocus:q,onUnmountAutoFocus:r,...s}=a,[t,u]=d.useState(null),v=(0,g.c)(q),w=(0,g.c)(r),x=d.useRef(null),y=(0,e.s)(b,a=>u(a)),z=d.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;d.useEffect(()=>{if(l){let a=function(a){if(z.paused||!t)return;let b=a.target;t.contains(b)?x.current=b:o(x.current,{select:!0})},b=function(a){if(z.paused||!t)return;let b=a.relatedTarget;null!==b&&(t.contains(b)||o(x.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&o(t)});return t&&c.observe(t,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[l,t,z.paused]),d.useEffect(()=>{if(t){p.add(z);let a=document.activeElement;if(!t.contains(a)){let b=new CustomEvent(i,k);t.addEventListener(i,v),t.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(o(d,{select:b}),document.activeElement!==c)return}(m(t).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&o(t))}return()=>{t.removeEventListener(i,v),setTimeout(()=>{let b=new CustomEvent(j,k);t.addEventListener(j,w),t.dispatchEvent(b),b.defaultPrevented||o(a??document.body,{select:!0}),t.removeEventListener(j,w),p.remove(z)},0)}}},[t,v,w,z]);let A=d.useCallback(a=>{if(!c&&!l||z.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,d=document.activeElement;if(b&&d){let b=a.currentTarget,[e,f]=function(a){let b=m(a);return[n(b,a),n(b.reverse(),a)]}(b);e&&f?a.shiftKey||d!==f?a.shiftKey&&d===e&&(a.preventDefault(),c&&o(f,{select:!0})):(a.preventDefault(),c&&o(e,{select:!0})):d===b&&a.preventDefault()}},[c,l,z.paused]);return(0,h.jsx)(f.sG.div,{tabIndex:-1,...s,ref:y,onKeyDown:A})});function m(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function n(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function o(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}l.displayName="FocusScope";var p=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=q(a,b)).unshift(b)},remove(b){a=q(a,b),a[0]?.resume()}}}();function q(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}},32708:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"errorOnce",{enumerable:!0,get:function(){return c}});let c=a=>{}},33898:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{fillCacheWithNewSubTreeData:function(){return i},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return j}});let d=c(34400),e=c(41500),f=c(33123),g=c(83913);function h(a,b,c,h,i,j){let{segmentPath:k,seedData:l,tree:m,head:n}=h,o=b,p=c;for(let b=0;b<k.length;b+=2){let c=k[b],h=k[b+1],q=b===k.length-2,r=(0,f.createRouterCacheKey)(h),s=p.parallelRoutes.get(c);if(!s)continue;let t=o.parallelRoutes.get(c);t&&t!==s||(t=new Map(s),o.parallelRoutes.set(c,t));let u=s.get(r),v=t.get(r);if(q){if(l&&(!v||!v.lazyData||v===u)){let b=l[0],c=l[1],f=l[3];v={lazyData:null,rsc:j||b!==g.PAGE_SEGMENT_KEY?c:null,prefetchRsc:null,head:null,prefetchHead:null,loading:f,parallelRoutes:j&&u?new Map(u.parallelRoutes):new Map,navigatedAt:a},u&&j&&(0,d.invalidateCacheByRouterState)(v,u,m),j&&(0,e.fillLazyItemsTillLeafWithHead)(a,v,u,m,l,n,i),t.set(r,v)}continue}v&&u&&(v===u&&(v={lazyData:v.lazyData,rsc:v.rsc,prefetchRsc:v.prefetchRsc,head:v.head,prefetchHead:v.prefetchHead,parallelRoutes:new Map(v.parallelRoutes),loading:v.loading},t.set(r,v)),o=v,p=u)}}function i(a,b,c,d,e){h(a,b,c,d,e,!0)}function j(a,b,c,d,e){h(a,b,c,d,e,!1)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},34400:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return e}});let d=c(33123);function e(a,b,c){for(let e in c[1]){let f=c[1][e][0],g=(0,d.createRouterCacheKey)(f),h=b.parallelRoutes.get(e);if(h){let b=new Map(h);b.delete(g),a.parallelRoutes.set(e,b)}}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},35416:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{HTML_LIMITED_BOT_UA_RE:function(){return d.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return f},getBotType:function(){return i},isBot:function(){return h}});let d=c(95796),e=/google/i,f=d.HTML_LIMITED_BOT_UA_RE.source;function g(a){return d.HTML_LIMITED_BOT_UA_RE.test(a)}function h(a){return e.test(a)||g(a)}function i(a){return e.test(a)?"dom":g(a)?"html":void 0}},35429:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"serverActionReducer",{enumerable:!0,get:function(){return D}});let d=c(11264),e=c(11448),f=c(91563),g=c(7379),h=c(59154),i=c(6361),j=c(57391),k=c(25232),l=c(86770),m=c(2030),n=c(59435),o=c(41500),p=c(89752),q=c(68214),r=c(96493),s=c(22308),t=c(74007),u=c(36875),v=c(97860),w=c(5334),x=c(25942),y=c(26736),z=c(24642);c(50593);let A=g.createFromFetch;async function B(a,b,c){let h,j,k,l,{actionId:m,actionArgs:n}=c,o=(0,g.createTemporaryReferenceSet)(),p=(0,z.extractInfoFromServerReferenceId)(m),q="use-cache"===p.type?(0,z.omitUnusedArgs)(n,p):n,r=await (0,g.encodeReply)(q,{temporaryReferences:o}),s=await fetch(a.canonicalUrl,{method:"POST",headers:{Accept:f.RSC_CONTENT_TYPE_HEADER,[f.ACTION_HEADER]:m,[f.NEXT_ROUTER_STATE_TREE_HEADER]:(0,t.prepareFlightRouterStateForRequest)(a.tree),...{},...b?{[f.NEXT_URL]:b}:{}},body:r});if("1"===s.headers.get(f.NEXT_ACTION_NOT_FOUND_HEADER))throw Object.defineProperty(Error('Server Action "'+m+'" was not found on the server. \nRead more: https://nextjs.org/docs/messages/failed-to-find-server-action'),"__NEXT_ERROR_CODE",{value:"E715",enumerable:!1,configurable:!0});let u=s.headers.get("x-action-redirect"),[w,x]=(null==u?void 0:u.split(";"))||[];switch(x){case"push":h=v.RedirectType.push;break;case"replace":h=v.RedirectType.replace;break;default:h=void 0}let y=!!s.headers.get(f.NEXT_IS_PRERENDER_HEADER);try{let a=JSON.parse(s.headers.get("x-action-revalidated")||"[[],0,0]");j={paths:a[0]||[],tag:!!a[1],cookie:a[2]}}catch(a){j=C}let B=w?(0,i.assignLocation)(w,new URL(a.canonicalUrl,window.location.href)):void 0,D=s.headers.get("content-type"),E=!!(D&&D.startsWith(f.RSC_CONTENT_TYPE_HEADER));if(!E&&!B)throw Object.defineProperty(Error(s.status>=400&&"text/plain"===D?await s.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});if(E){let a=await A(Promise.resolve(s),{callServer:d.callServer,findSourceMapURL:e.findSourceMapURL,temporaryReferences:o});k=B?void 0:a.a,l=(0,t.normalizeFlightData)(a.f)}else k=void 0,l=void 0;return{actionResult:k,actionFlightData:l,redirectLocation:B,redirectType:h,revalidatedParts:j,isPrerender:y}}let C={paths:[],tag:!1,cookie:!1};function D(a,b){let{resolve:c,reject:d}=b,e={},f=a.tree;e.preserveCustomHistoryState=!1;let g=a.nextUrl&&(0,q.hasInterceptionRouteInCurrentTree)(a.tree)?a.nextUrl:null,i=Date.now();return B(a,g,b).then(async q=>{let t,{actionResult:z,actionFlightData:A,redirectLocation:B,redirectType:C,isPrerender:D,revalidatedParts:E}=q;if(B&&(C===v.RedirectType.replace?(a.pushRef.pendingPush=!1,e.pendingPush=!1):(a.pushRef.pendingPush=!0,e.pendingPush=!0),e.canonicalUrl=t=(0,j.createHrefFromUrl)(B,!1)),!A)return(c(z),B)?(0,k.handleExternalUrl)(a,e,B.href,a.pushRef.pendingPush):a;if("string"==typeof A)return c(z),(0,k.handleExternalUrl)(a,e,A,a.pushRef.pendingPush);let F=E.paths.length>0||E.tag||E.cookie;for(let d of A){let{tree:h,seedData:j,head:n,isRootRender:q}=d;if(!q)return console.log("SERVER ACTION APPLY FAILED"),c(z),a;let u=(0,l.applyRouterStatePatchToTree)([""],f,h,t||a.canonicalUrl);if(null===u)return c(z),(0,r.handleSegmentMismatch)(a,b,h);if((0,m.isNavigatingToNewRootLayout)(f,u))return c(z),(0,k.handleExternalUrl)(a,e,t||a.canonicalUrl,a.pushRef.pendingPush);if(null!==j){let b=j[1],c=(0,p.createEmptyCacheNode)();c.rsc=b,c.prefetchRsc=null,c.loading=j[3],(0,o.fillLazyItemsTillLeafWithHead)(i,c,void 0,h,j,n,void 0),e.cache=c,e.prefetchCache=new Map,F&&await (0,s.refreshInactiveParallelSegments)({navigatedAt:i,state:a,updatedTree:u,updatedCache:c,includeNextUrl:!!g,canonicalUrl:e.canonicalUrl||a.canonicalUrl})}e.patchedTree=u,f=u}return B&&t?(F||((0,w.createSeededPrefetchCacheEntry)({url:B,data:{flightData:A,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:a.tree,prefetchCache:a.prefetchCache,nextUrl:a.nextUrl,kind:D?h.PrefetchKind.FULL:h.PrefetchKind.AUTO}),e.prefetchCache=a.prefetchCache),d((0,u.getRedirectError)((0,y.hasBasePath)(t)?(0,x.removeBasePath)(t):t,C||v.RedirectType.push))):c(z),(0,n.handleMutable)(a,e)},b=>(d(b),a))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},40083:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},41312:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},41500:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function a(b,c,f,g,h,i,j){if(0===Object.keys(g[1]).length){c.head=i;return}for(let k in g[1]){let l,m=g[1][k],n=m[0],o=(0,d.createRouterCacheKey)(n),p=null!==h&&void 0!==h[2][k]?h[2][k]:null;if(f){let d=f.parallelRoutes.get(k);if(d){let f,g=(null==j?void 0:j.kind)==="auto"&&j.status===e.PrefetchCacheEntryStatus.reusable,h=new Map(d),l=h.get(o);f=null!==p?{lazyData:null,rsc:p[1],prefetchRsc:null,head:null,prefetchHead:null,loading:p[3],parallelRoutes:new Map(null==l?void 0:l.parallelRoutes),navigatedAt:b}:g&&l?{lazyData:l.lazyData,rsc:l.rsc,prefetchRsc:l.prefetchRsc,head:l.head,prefetchHead:l.prefetchHead,parallelRoutes:new Map(l.parallelRoutes),loading:l.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==l?void 0:l.parallelRoutes),loading:null,navigatedAt:b},h.set(o,f),a(b,f,l,m,p||null,i,j),c.parallelRoutes.set(k,h);continue}}if(null!==p){let a=p[1],c=p[3];l={lazyData:null,rsc:a,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:c,navigatedAt:b}}else l={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:b};let q=c.parallelRoutes.get(k);q?q.set(o,l):c.parallelRoutes.set(k,new Map([[o,l]])),a(b,l,void 0,m,p,i,j)}}}});let d=c(33123),e=c(59154);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},42247:(a,b,c)=>{c.d(b,{A:()=>U});var d,e,f=function(){return(f=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function g(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var h=("function"==typeof SuppressedError&&SuppressedError,c(43210)),i="right-scroll-bar-position",j="width-before-scroll-bar";function k(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var l="undefined"!=typeof window?h.useLayoutEffect:h.useEffect,m=new WeakMap;function n(a){return a}var o=function(a){void 0===a&&(a={});var b,c,d,e=(void 0===b&&(b=n),c=[],d=!1,{read:function(){if(d)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var e=b(a,d);return c.push(e),function(){c=c.filter(function(a){return a!==e})}},assignSyncMedium:function(a){for(d=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){d=!0;var b=[];if(c.length){var e=c;c=[],e.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return e.options=f({async:!0,ssr:!1},a),e}(),p=function(){},q=h.forwardRef(function(a,b){var c,d,e,i,j=h.useRef(null),n=h.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),q=n[0],r=n[1],s=a.forwardProps,t=a.children,u=a.className,v=a.removeScrollBar,w=a.enabled,x=a.shards,y=a.sideCar,z=a.noRelative,A=a.noIsolation,B=a.inert,C=a.allowPinchZoom,D=a.as,E=a.gapMode,F=g(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),G=(c=[j,b],d=function(a){return c.forEach(function(b){return k(b,a)})},(e=(0,h.useState)(function(){return{value:null,callback:d,facade:{get current(){return e.value},set current(value){var a=e.value;a!==value&&(e.value=value,e.callback(value,a))}}}})[0]).callback=d,i=e.facade,l(function(){var a=m.get(i);if(a){var b=new Set(a),d=new Set(c),e=i.current;b.forEach(function(a){d.has(a)||k(a,null)}),d.forEach(function(a){b.has(a)||k(a,e)})}m.set(i,c)},[c]),i),H=f(f({},F),q);return h.createElement(h.Fragment,null,w&&h.createElement(y,{sideCar:o,removeScrollBar:v,shards:x,noRelative:z,noIsolation:A,inert:B,setCallbacks:r,allowPinchZoom:!!C,lockRef:j,gapMode:E}),s?h.cloneElement(h.Children.only(t),f(f({},H),{ref:G})):h.createElement(void 0===D?"div":D,f({},H,{className:u,ref:G}),t))});q.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},q.classNames={fullWidth:j,zeroRight:i};var r=function(a){var b=a.sideCar,c=g(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var d=b.read();if(!d)throw Error("Sidecar medium not found");return h.createElement(d,f({},c))};r.isSideCarExport=!0;var s=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=e||c.nc;return b&&a.setAttribute("nonce",b),a}())){var f,g;(f=b).styleSheet?f.styleSheet.cssText=d:f.appendChild(document.createTextNode(d)),g=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(g)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},t=function(){var a=s();return function(b,c){h.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},u=function(){var a=t();return function(b){return a(b.styles,b.dynamic),null}},v={left:0,top:0,right:0,gap:0},w=function(a){return parseInt(a||"",10)||0},x=function(a){var b=window.getComputedStyle(document.body),c=b["padding"===a?"paddingLeft":"marginLeft"],d=b["padding"===a?"paddingTop":"marginTop"],e=b["padding"===a?"paddingRight":"marginRight"];return[w(c),w(d),w(e)]},y=function(a){if(void 0===a&&(a="margin"),"undefined"==typeof window)return v;var b=x(a),c=document.documentElement.clientWidth,d=window.innerWidth;return{left:b[0],top:b[1],right:b[2],gap:Math.max(0,d-c+b[2]-b[0])}},z=u(),A="data-scroll-locked",B=function(a,b,c,d){var e=a.left,f=a.top,g=a.right,h=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(h,"px ").concat(d,";\n  }\n  body[").concat(A,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(g,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(h,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(h,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(i," {\n    right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(j," {\n    margin-right: ").concat(h,"px ").concat(d,";\n  }\n  \n  .").concat(i," .").concat(i," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(j," .").concat(j," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(A,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(h,"px;\n  }\n")},C=function(){var a=parseInt(document.body.getAttribute(A)||"0",10);return isFinite(a)?a:0},D=function(){h.useEffect(function(){return document.body.setAttribute(A,(C()+1).toString()),function(){var a=C()-1;a<=0?document.body.removeAttribute(A):document.body.setAttribute(A,a.toString())}},[])},E=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;D();var f=h.useMemo(function(){return y(e)},[e]);return h.createElement(z,{styles:B(f,!b,e,c?"":"!important")})},F=!1;if("undefined"!=typeof window)try{var G=Object.defineProperty({},"passive",{get:function(){return F=!0,!0}});window.addEventListener("test",G,G),window.removeEventListener("test",G,G)}catch(a){F=!1}var H=!!F&&{passive:!1},I=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},J=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),K(a,d)){var e=L(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body);return!1},K=function(a,b){return"v"===a?I(b,"overflowY"):I(b,"overflowX")},L=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},M=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=L(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&K(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i));return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},N=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},O=function(a){return[a.deltaX,a.deltaY]},P=function(a){return a&&"current"in a?a.current:a},Q=0,R=[];let S=(d=function(a){var b=h.useRef([]),c=h.useRef([0,0]),d=h.useRef(),e=h.useState(Q++)[0],f=h.useState(u)[0],g=h.useRef(a);h.useEffect(function(){g.current=a},[a]),h.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(P),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=h.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!g.current.allowPinchZoom;var e,f=N(a),h=c.current,i="deltaX"in a?a.deltaX:h[0]-f[0],j="deltaY"in a?a.deltaY:h[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=J(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=J(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return M(n,b,a,"h"===n?i:j,!0)},[]),j=h.useCallback(function(a){if(R.length&&R[R.length-1]===f){var c="deltaY"in a?O(a):N(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(g.current.shards||[]).map(P).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!g.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=h.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=h.useCallback(function(a){c.current=N(a),d.current=void 0},[]),m=h.useCallback(function(b){k(b.type,O(b),b.target,i(b,a.lockRef.current))},[]),n=h.useCallback(function(b){k(b.type,N(b),b.target,i(b,a.lockRef.current))},[]);h.useEffect(function(){return R.push(f),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,H),document.addEventListener("touchmove",j,H),document.addEventListener("touchstart",l,H),function(){R=R.filter(function(a){return a!==f}),document.removeEventListener("wheel",j,H),document.removeEventListener("touchmove",j,H),document.removeEventListener("touchstart",l,H)}},[]);var o=a.removeScrollBar,p=a.inert;return h.createElement(h.Fragment,null,p?h.createElement(f,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?h.createElement(E,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},o.useMedium(d),r);var T=h.forwardRef(function(a,b){return h.createElement(q,f({},a,{ref:b,sideCar:S}))});T.classNames=q.classNames;let U=T},44397:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"findHeadInCache",{enumerable:!0,get:function(){return f}});let d=c(83913),e=c(33123);function f(a,b){return function a(b,c,f){if(0===Object.keys(c).length)return[b,f];let g=Object.keys(c).filter(a=>"children"!==a);for(let h of("children"in c&&g.unshift("children"),g)){let[g,i]=c[h];if(g===d.DEFAULT_SEGMENT_KEY)continue;let j=b.parallelRoutes.get(h);if(!j)continue;let k=(0,e.createRouterCacheKey)(g),l=j.get(k);if(!l)continue;let m=a(l,i,f+"/"+k);if(m)return m}return null}(a,b,"")}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},46059:(a,b,c)=>{c.d(b,{C:()=>g});var d=c(43210),e=c(98599),f=c(66156),g=a=>{let{present:b,children:c}=a,g=function(a){var b,c;let[e,g]=d.useState(),i=d.useRef(null),j=d.useRef(a),k=d.useRef("none"),[l,m]=(b=a?"mounted":"unmounted",c={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},d.useReducer((a,b)=>c[a][b]??a,b));return d.useEffect(()=>{let a=h(i.current);k.current="mounted"===l?a:"none"},[l]),(0,f.N)(()=>{let b=i.current,c=j.current;if(c!==a){let d=k.current,e=h(b);a?m("MOUNT"):"none"===e||b?.display==="none"?m("UNMOUNT"):c&&d!==e?m("ANIMATION_OUT"):m("UNMOUNT"),j.current=a}},[a,m]),(0,f.N)(()=>{if(e){let a,b=e.ownerDocument.defaultView??window,c=c=>{let d=h(i.current).includes(c.animationName);if(c.target===e&&d&&(m("ANIMATION_END"),!j.current)){let c=e.style.animationFillMode;e.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===e.style.animationFillMode&&(e.style.animationFillMode=c)})}},d=a=>{a.target===e&&(k.current=h(i.current))};return e.addEventListener("animationstart",d),e.addEventListener("animationcancel",c),e.addEventListener("animationend",c),()=>{b.clearTimeout(a),e.removeEventListener("animationstart",d),e.removeEventListener("animationcancel",c),e.removeEventListener("animationend",c)}}m("ANIMATION_END")},[e,m]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:d.useCallback(a=>{i.current=a?getComputedStyle(a):null,g(a)},[])}}(b),i="function"==typeof c?c({present:g.isPresent}):d.Children.only(c),j=(0,e.s)(g.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(i));return"function"==typeof c||g.isPresent?d.cloneElement(i,{ref:j}):null};function h(a){return a?.animationName||"none"}g.displayName="Presence"},49625:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},50593:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{NavigationResultTag:function(){return m},PrefetchPriority:function(){return n},cancelPrefetchTask:function(){return i},createCacheKey:function(){return l},getCurrentCacheVersion:function(){return g},isPrefetchTaskDirty:function(){return k},navigate:function(){return e},prefetch:function(){return d},reschedulePrefetchTask:function(){return j},revalidateEntireCache:function(){return f},schedulePrefetchTask:function(){return h}});let c=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},d=c,e=c,f=c,g=c,h=c,i=c,j=c,k=c,l=c;var m=function(a){return a[a.MPA=0]="MPA",a[a.Success=1]="Success",a[a.NoOp=2]="NoOp",a[a.Async=3]="Async",a}({}),n=function(a){return a[a.Intent=2]="Intent",a[a.Default=1]="Default",a[a.Background=0]="Background",a}({});("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},51214:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},51550:(a,b,c)=>{function d(a,b){if(!Object.prototype.hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}c.r(b),c.d(b,{_:()=>d})},53038:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"useMergedRef",{enumerable:!0,get:function(){return e}});let d=c(43210);function e(a,b){let c=(0,d.useRef)(null),e=(0,d.useRef)(null);return(0,d.useCallback)(d=>{if(null===d){let a=c.current;a&&(c.current=null,a());let b=e.current;b&&(e.current=null,b())}else a&&(c.current=f(a,d)),b&&(e.current=f(b,d))},[a,b])}function f(a,b){if("function"!=typeof a)return a.current=b,()=>{a.current=null};{let c=a(b);return"function"==typeof c?c:()=>a(null)}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},53332:(a,b,c)=>{var d=c(43210),e="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},f=d.useState,g=d.useEffect,h=d.useLayoutEffect,i=d.useDebugValue;function j(a){var b=a.getSnapshot;a=a.value;try{var c=b();return!e(a,c)}catch(a){return!0}}var k="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(a,b){return b()}:function(a,b){var c=b(),d=f({inst:{value:c,getSnapshot:b}}),e=d[0].inst,k=d[1];return h(function(){e.value=c,e.getSnapshot=b,j(e)&&k({inst:e})},[a,c,b]),g(function(){return j(e)&&k({inst:e}),a(function(){j(e)&&k({inst:e})})},[a]),i(c),c};b.useSyncExternalStore=void 0!==d.useSyncExternalStore?d.useSyncExternalStore:k},54674:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return f}});let d=c(84949),e=c(19169),f=a=>{if(!a.startsWith("/"))return a;let{pathname:b,query:c,hash:f}=(0,e.parsePath)(a);return""+(0,d.removeTrailingSlash)(b)+c+f};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},55509:(a,b,c)=>{c.d(b,{Mz:()=>a1,i3:()=>a3,UC:()=>a2,bL:()=>a0,Bk:()=>aM});var d=c(43210);let e=["top","right","bottom","left"],f=Math.min,g=Math.max,h=Math.round,i=Math.floor,j=a=>({x:a,y:a}),k={left:"right",right:"left",bottom:"top",top:"bottom"},l={start:"end",end:"start"};function m(a,b){return"function"==typeof a?a(b):a}function n(a){return a.split("-")[0]}function o(a){return a.split("-")[1]}function p(a){return"x"===a?"y":"x"}function q(a){return"y"===a?"height":"width"}let r=new Set(["top","bottom"]);function s(a){return r.has(n(a))?"y":"x"}function t(a){return a.replace(/start|end/g,a=>l[a])}let u=["left","right"],v=["right","left"],w=["top","bottom"],x=["bottom","top"];function y(a){return a.replace(/left|right|bottom|top/g,a=>k[a])}function z(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function A(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function B(a,b,c){let d,{reference:e,floating:f}=a,g=s(b),h=p(s(b)),i=q(h),j=n(b),k="y"===g,l=e.x+e.width/2-f.width/2,m=e.y+e.height/2-f.height/2,r=e[i]/2-f[i]/2;switch(j){case"top":d={x:l,y:e.y-f.height};break;case"bottom":d={x:l,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:m};break;case"left":d={x:e.x-f.width,y:m};break;default:d={x:e.x,y:e.y}}switch(o(b)){case"start":d[h]-=r*(c&&k?-1:1);break;case"end":d[h]+=r*(c&&k?-1:1)}return d}let C=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=B(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=B(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function D(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:k="viewport",elementContext:l="floating",altBoundary:n=!1,padding:o=0}=m(b,a),p=z(o),q=h[n?"floating"===l?"reference":"floating":l],r=A(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(q)))||c?q:q.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:k,strategy:i})),s="floating"===l?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,t=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),u=await (null==f.isElement?void 0:f.isElement(t))&&await (null==f.getScale?void 0:f.getScale(t))||{x:1,y:1},v=A(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:s,offsetParent:t,strategy:i}):s);return{top:(r.top-v.top+p.top)/u.y,bottom:(v.bottom-r.bottom+p.bottom)/u.y,left:(r.left-v.left+p.left)/u.x,right:(v.right-r.right+p.right)/u.x}}function E(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function F(a){return e.some(b=>a[b]>=0)}let G=new Set(["left","top"]);async function H(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=n(c),h=o(c),i="y"===s(c),j=G.has(g)?-1:1,k=f&&i?-1:1,l=m(b,a),{mainAxis:p,crossAxis:q,alignmentAxis:r}="number"==typeof l?{mainAxis:l,crossAxis:0,alignmentAxis:null}:{mainAxis:l.mainAxis||0,crossAxis:l.crossAxis||0,alignmentAxis:l.alignmentAxis};return h&&"number"==typeof r&&(q="end"===h?-1*r:r),i?{x:q*k,y:p*j}:{x:p*j,y:q*k}}function I(){return"undefined"!=typeof window}function J(a){return M(a)?(a.nodeName||"").toLowerCase():"#document"}function K(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function L(a){var b;return null==(b=(M(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function M(a){return!!I()&&(a instanceof Node||a instanceof K(a).Node)}function N(a){return!!I()&&(a instanceof Element||a instanceof K(a).Element)}function O(a){return!!I()&&(a instanceof HTMLElement||a instanceof K(a).HTMLElement)}function P(a){return!!I()&&"undefined"!=typeof ShadowRoot&&(a instanceof ShadowRoot||a instanceof K(a).ShadowRoot)}let Q=new Set(["inline","contents"]);function R(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=aa(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!Q.has(e)}let S=new Set(["table","td","th"]),T=[":popover-open",":modal"];function U(a){return T.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let V=["transform","translate","scale","rotate","perspective"],W=["transform","translate","scale","rotate","perspective","filter"],X=["paint","layout","strict","content"];function Y(a){let b=Z(),c=N(a)?aa(a):a;return V.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||W.some(a=>(c.willChange||"").includes(a))||X.some(a=>(c.contain||"").includes(a))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let $=new Set(["html","body","#document"]);function _(a){return $.has(J(a))}function aa(a){return K(a).getComputedStyle(a)}function ab(a){return N(a)?{scrollLeft:a.scrollLeft,scrollTop:a.scrollTop}:{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function ac(a){if("html"===J(a))return a;let b=a.assignedSlot||a.parentNode||P(a)&&a.host||L(a);return P(b)?b.host:b}function ad(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=ac(b);return _(c)?b.ownerDocument?b.ownerDocument.body:b.body:O(c)&&R(c)?c:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=K(e);if(f){let a=ae(g);return b.concat(g,g.visualViewport||[],R(e)?e:[],a&&c?ad(a):[])}return b.concat(e,ad(e,[],c))}function ae(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function af(a){let b=aa(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=O(a),f=e?a.offsetWidth:c,g=e?a.offsetHeight:d,i=h(c)!==f||h(d)!==g;return i&&(c=f,d=g),{width:c,height:d,$:i}}function ag(a){return N(a)?a:a.contextElement}function ah(a){let b=ag(a);if(!O(b))return j(1);let c=b.getBoundingClientRect(),{width:d,height:e,$:f}=af(b),g=(f?h(c.width):c.width)/d,i=(f?h(c.height):c.height)/e;return g&&Number.isFinite(g)||(g=1),i&&Number.isFinite(i)||(i=1),{x:g,y:i}}let ai=j(0);function aj(a){let b=K(a);return Z()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:ai}function ak(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=ag(a),h=j(1);b&&(d?N(d)&&(h=ah(d)):h=ah(a));let i=(void 0===(e=c)&&(e=!1),d&&(!e||d===K(g))&&e)?aj(g):j(0),k=(f.left+i.x)/h.x,l=(f.top+i.y)/h.y,m=f.width/h.x,n=f.height/h.y;if(g){let a=K(g),b=d&&N(d)?K(d):d,c=a,e=ae(c);for(;e&&d&&b!==c;){let a=ah(e),b=e.getBoundingClientRect(),d=aa(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;k*=a.x,l*=a.y,m*=a.x,n*=a.y,k+=f,l+=g,e=ae(c=K(e))}}return A({width:m,height:n,x:k,y:l})}function al(a,b){let c=ab(a).scrollLeft;return b?b.left+c:ak(L(a)).left+c}function am(a,b,c){void 0===c&&(c=!1);let d=a.getBoundingClientRect();return{x:d.left+b.scrollLeft-(c?0:al(a,d)),y:d.top+b.scrollTop}}let an=new Set(["absolute","fixed"]);function ao(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=K(a),d=L(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=Z();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=L(a),c=ab(a),d=a.ownerDocument.body,e=g(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),f=g(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),h=-c.scrollLeft+al(a),i=-c.scrollTop;return"rtl"===aa(d).direction&&(h+=g(b.clientWidth,d.clientWidth)-e),{width:e,height:f,x:h,y:i}}(L(a));else if(N(b))d=function(a,b){let c=ak(a,!0,"fixed"===b),d=c.top+a.clientTop,e=c.left+a.clientLeft,f=O(a)?ah(a):j(1),g=a.clientWidth*f.x,h=a.clientHeight*f.y;return{width:g,height:h,x:e*f.x,y:d*f.y}}(b,c);else{let c=aj(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}return A(d)}function ap(a){return"static"===aa(a).position}function aq(a,b){if(!O(a)||"fixed"===aa(a).position)return null;if(b)return b(a);let c=a.offsetParent;return L(a)===c&&(c=c.ownerDocument.body),c}function ar(a,b){var c;let d=K(a);if(U(a))return d;if(!O(a)){let b=ac(a);for(;b&&!_(b);){if(N(b)&&!ap(b))return b;b=ac(b)}return d}let e=aq(a,b);for(;e&&(c=e,S.has(J(c)))&&ap(e);)e=aq(e,b);return e&&_(e)&&ap(e)&&!Y(e)?d:e||function(a){let b=ac(a);for(;O(b)&&!_(b);){if(Y(b))return b;if(U(b))break;b=ac(b)}return null}(a)||d}let as=async function(a){let b=this.getOffsetParent||ar,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){let d=O(b),e=L(b),f="fixed"===c,g=ak(a,!0,f,b),h={scrollLeft:0,scrollTop:0},i=j(0);if(d||!d&&!f)if(("body"!==J(b)||R(e))&&(h=ab(b)),d){let a=ak(b,!0,f,b);i.x=a.x+b.clientLeft,i.y=a.y+b.clientTop}else e&&(i.x=al(e));f&&!d&&e&&(i.x=al(e));let k=!e||d||f?j(0):am(e,h);return{x:g.left+h.scrollLeft-i.x-k.x,y:g.top+h.scrollTop-i.y-k.y,width:g.width,height:g.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},at={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){let{elements:b,rect:c,offsetParent:d,strategy:e}=a,f="fixed"===e,g=L(d),h=!!b&&U(b.floating);if(d===g||h&&f)return c;let i={scrollLeft:0,scrollTop:0},k=j(1),l=j(0),m=O(d);if((m||!m&&!f)&&(("body"!==J(d)||R(g))&&(i=ab(d)),O(d))){let a=ak(d);k=ah(d),l.x=a.x+d.clientLeft,l.y=a.y+d.clientTop}let n=!g||m||f?j(0):am(g,i,!0);return{width:c.width*k.x,height:c.height*k.y,x:c.x*k.x-i.scrollLeft*k.x+l.x+n.x,y:c.y*k.y-i.scrollTop*k.y+l.y+n.y}},getDocumentElement:L,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:d,strategy:e}=a,h=[..."clippingAncestors"===c?U(b)?[]:function(a,b){let c=b.get(a);if(c)return c;let d=ad(a,[],!1).filter(a=>N(a)&&"body"!==J(a)),e=null,f="fixed"===aa(a).position,g=f?ac(a):a;for(;N(g)&&!_(g);){let b=aa(g),c=Y(g);c||"fixed"!==b.position||(e=null),(f?!c&&!e:!c&&"static"===b.position&&!!e&&an.has(e.position)||R(g)&&!c&&function a(b,c){let d=ac(b);return!(d===c||!N(d)||_(d))&&("fixed"===aa(d).position||a(d,c))}(a,g))?d=d.filter(a=>a!==g):e=b,g=ac(g)}return b.set(a,d),d}(b,this._c):[].concat(c),d],i=h[0],j=h.reduce((a,c)=>{let d=ao(b,c,e);return a.top=g(d.top,a.top),a.right=f(d.right,a.right),a.bottom=f(d.bottom,a.bottom),a.left=g(d.left,a.left),a},ao(b,i,e));return{width:j.right-j.left,height:j.bottom-j.top,x:j.left,y:j.top}},getOffsetParent:ar,getElementRects:as,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=af(a);return{width:b,height:c}},getScale:ah,isElement:N,isRTL:function(a){return"rtl"===aa(a).direction}};function au(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let av=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:d,placement:e,rects:h,platform:i,elements:j,middlewareData:k}=b,{element:l,padding:n=0}=m(a,b)||{};if(null==l)return{};let r=z(n),t={x:c,y:d},u=p(s(e)),v=q(u),w=await i.getDimensions(l),x="y"===u,y=x?"clientHeight":"clientWidth",A=h.reference[v]+h.reference[u]-t[u]-h.floating[v],B=t[u]-h.reference[u],C=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l)),D=C?C[y]:0;D&&await (null==i.isElement?void 0:i.isElement(C))||(D=j.floating[y]||h.floating[v]);let E=D/2-w[v]/2-1,F=f(r[x?"top":"left"],E),G=f(r[x?"bottom":"right"],E),H=D-w[v]-G,I=D/2-w[v]/2+(A/2-B/2),J=g(F,f(I,H)),K=!k.arrow&&null!=o(e)&&I!==J&&h.reference[v]/2-(I<F?F:G)-w[v]/2<0,L=K?I<F?I-F:I-H:0;return{[u]:t[u]+L,data:{[u]:J,centerOffset:I-J-L,...K&&{alignmentOffset:L}},reset:K}}});var aw=c(51215),ax="undefined"!=typeof document?d.useLayoutEffect:function(){};function ay(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!ay(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!ay(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function az(a){return"undefined"==typeof window?1:(a.ownerDocument.defaultView||window).devicePixelRatio||1}function aA(a,b){let c=az(a);return Math.round(b*c)/c}function aB(a){let b=d.useRef(a);return ax(()=>{b.current=a}),b}var aC=c(14163),aD=c(60687),aE=d.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,aD.jsx)(aC.sG.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,aD.jsx)("polygon",{points:"0,0 30,0 15,10"})})});aE.displayName="Arrow";var aF=c(98599),aG=c(11273),aH=c(13495),aI=c(66156),aJ=c(18853),aK="Popper",[aL,aM]=(0,aG.A)(aK),[aN,aO]=aL(aK),aP=a=>{let{__scopePopper:b,children:c}=a,[e,f]=d.useState(null);return(0,aD.jsx)(aN,{scope:b,anchor:e,onAnchorChange:f,children:c})};aP.displayName=aK;var aQ="PopperAnchor",aR=d.forwardRef((a,b)=>{let{__scopePopper:c,virtualRef:e,...f}=a,g=aO(aQ,c),h=d.useRef(null),i=(0,aF.s)(b,h);return d.useEffect(()=>{g.onAnchorChange(e?.current||h.current)}),e?null:(0,aD.jsx)(aC.sG.div,{...f,ref:i})});aR.displayName=aQ;var aS="PopperContent",[aT,aU]=aL(aS),aV=d.forwardRef((a,b)=>{let{__scopePopper:c,side:e="bottom",sideOffset:h=0,align:j="center",alignOffset:k=0,arrowPadding:l=0,avoidCollisions:r=!0,collisionBoundary:z=[],collisionPadding:A=0,sticky:B="partial",hideWhenDetached:I=!1,updatePositionStrategy:J="optimized",onPlaced:K,...M}=a,N=aO(aS,c),[O,P]=d.useState(null),Q=(0,aF.s)(b,a=>P(a)),[R,S]=d.useState(null),T=(0,aJ.X)(R),U=T?.width??0,V=T?.height??0,W="number"==typeof A?A:{top:0,right:0,bottom:0,left:0,...A},X=Array.isArray(z)?z:[z],Y=X.length>0,Z={padding:W,boundary:X.filter(aZ),altBoundary:Y},{refs:$,floatingStyles:_,placement:aa,isPositioned:ab,middlewareData:ac}=function(a){void 0===a&&(a={});let{placement:b="bottom",strategy:c="absolute",middleware:e=[],platform:f,elements:{reference:g,floating:h}={},transform:i=!0,whileElementsMounted:j,open:k}=a,[l,m]=d.useState({x:0,y:0,strategy:c,placement:b,middlewareData:{},isPositioned:!1}),[n,o]=d.useState(e);ay(n,e)||o(e);let[p,q]=d.useState(null),[r,s]=d.useState(null),t=d.useCallback(a=>{a!==x.current&&(x.current=a,q(a))},[]),u=d.useCallback(a=>{a!==y.current&&(y.current=a,s(a))},[]),v=g||p,w=h||r,x=d.useRef(null),y=d.useRef(null),z=d.useRef(l),A=null!=j,B=aB(j),D=aB(f),E=aB(k),F=d.useCallback(()=>{if(!x.current||!y.current)return;let a={placement:b,strategy:c,middleware:n};D.current&&(a.platform=D.current),((a,b,c)=>{let d=new Map,e={platform:at,...c},f={...e.platform,_c:d};return C(a,b,{...e,platform:f})})(x.current,y.current,a).then(a=>{let b={...a,isPositioned:!1!==E.current};G.current&&!ay(z.current,b)&&(z.current=b,aw.flushSync(()=>{m(b)}))})},[n,b,c,D,E]);ax(()=>{!1===k&&z.current.isPositioned&&(z.current.isPositioned=!1,m(a=>({...a,isPositioned:!1})))},[k]);let G=d.useRef(!1);ax(()=>(G.current=!0,()=>{G.current=!1}),[]),ax(()=>{if(v&&(x.current=v),w&&(y.current=w),v&&w){if(B.current)return B.current(v,w,F);F()}},[v,w,F,B,A]);let H=d.useMemo(()=>({reference:x,floating:y,setReference:t,setFloating:u}),[t,u]),I=d.useMemo(()=>({reference:v,floating:w}),[v,w]),J=d.useMemo(()=>{let a={position:c,left:0,top:0};if(!I.floating)return a;let b=aA(I.floating,l.x),d=aA(I.floating,l.y);return i?{...a,transform:"translate("+b+"px, "+d+"px)",...az(I.floating)>=1.5&&{willChange:"transform"}}:{position:c,left:b,top:d}},[c,i,I.floating,l.x,l.y]);return d.useMemo(()=>({...l,update:F,refs:H,elements:I,floatingStyles:J}),[l,F,H,I,J])}({strategy:"fixed",placement:e+("center"!==j?"-"+j:""),whileElementsMounted:(...a)=>(function(a,b,c,d){let e;void 0===d&&(d={});let{ancestorScroll:h=!0,ancestorResize:j=!0,elementResize:k="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:m=!1}=d,n=ag(a),o=h||j?[...n?ad(n):[],...ad(b)]:[];o.forEach(a=>{h&&a.addEventListener("scroll",c,{passive:!0}),j&&a.addEventListener("resize",c)});let p=n&&l?function(a,b){let c,d=null,e=L(a);function h(){var a;clearTimeout(c),null==(a=d)||a.disconnect(),d=null}return!function j(k,l){void 0===k&&(k=!1),void 0===l&&(l=1),h();let m=a.getBoundingClientRect(),{left:n,top:o,width:p,height:q}=m;if(k||b(),!p||!q)return;let r=i(o),s=i(e.clientWidth-(n+p)),t={rootMargin:-r+"px "+-s+"px "+-i(e.clientHeight-(o+q))+"px "+-i(n)+"px",threshold:g(0,f(1,l))||1},u=!0;function v(b){let d=b[0].intersectionRatio;if(d!==l){if(!u)return j();d?j(!1,d):c=setTimeout(()=>{j(!1,1e-7)},1e3)}1!==d||au(m,a.getBoundingClientRect())||j(),u=!1}try{d=new IntersectionObserver(v,{...t,root:e.ownerDocument})}catch(a){d=new IntersectionObserver(v,t)}d.observe(a)}(!0),h}(n,c):null,q=-1,r=null;k&&(r=new ResizeObserver(a=>{let[d]=a;d&&d.target===n&&r&&(r.unobserve(b),cancelAnimationFrame(q),q=requestAnimationFrame(()=>{var a;null==(a=r)||a.observe(b)})),c()}),n&&!m&&r.observe(n),r.observe(b));let s=m?ak(a):null;return m&&function b(){let d=ak(a);s&&!au(s,d)&&c(),s=d,e=requestAnimationFrame(b)}(),c(),()=>{var a;o.forEach(a=>{h&&a.removeEventListener("scroll",c),j&&a.removeEventListener("resize",c)}),null==p||p(),null==(a=r)||a.disconnect(),r=null,m&&cancelAnimationFrame(e)}})(...a,{animationFrame:"always"===J}),elements:{reference:N.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await H(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,b]}))({mainAxis:h+V,alignmentAxis:k}),r&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:d,placement:e}=b,{mainAxis:h=!0,crossAxis:i=!1,limiter:j={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...k}=m(a,b),l={x:c,y:d},o=await D(b,k),q=s(n(e)),r=p(q),t=l[r],u=l[q];if(h){let a="y"===r?"top":"left",b="y"===r?"bottom":"right",c=t+o[a],d=t-o[b];t=g(c,f(t,d))}if(i){let a="y"===q?"top":"left",b="y"===q?"bottom":"right",c=u+o[a],d=u-o[b];u=g(c,f(u,d))}let v=j.fn({...b,[r]:t,[q]:u});return{...v,data:{x:v.x-c,y:v.y-d,enabled:{[r]:h,[q]:i}}}}}}(a),options:[a,b]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===B?((a,b)=>({...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=m(a,b),k={x:c,y:d},l=s(e),o=p(l),q=k[o],r=k[l],t=m(h,b),u="number"==typeof t?{mainAxis:t,crossAxis:0}:{mainAxis:0,crossAxis:0,...t};if(i){let a="y"===o?"height":"width",b=f.reference[o]-f.floating[a]+u.mainAxis,c=f.reference[o]+f.reference[a]-u.mainAxis;q<b?q=b:q>c&&(q=c)}if(j){var v,w;let a="y"===o?"width":"height",b=G.has(n(e)),c=f.reference[l]-f.floating[a]+(b&&(null==(v=g.offset)?void 0:v[l])||0)+(b?0:u.crossAxis),d=f.reference[l]+f.reference[a]+(b?0:(null==(w=g.offset)?void 0:w[l])||0)-(b?u.crossAxis:0);r<c?r=c:r>d&&(r=d)}return{[o]:q,[l]:r}}}}(a),options:[a,b]}))():void 0,...Z}),r&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:k,platform:l,elements:r}=b,{mainAxis:z=!0,crossAxis:A=!0,fallbackPlacements:B,fallbackStrategy:C="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:F=!0,...G}=m(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let H=n(h),I=s(k),J=n(k)===k,K=await (null==l.isRTL?void 0:l.isRTL(r.floating)),L=B||(J||!F?[y(k)]:function(a){let b=y(a);return[t(a),b,t(b)]}(k)),M="none"!==E;!B&&M&&L.push(...function(a,b,c,d){let e=o(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?v:u;return b?u:v;case"left":case"right":return b?w:x;default:return[]}}(n(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(t)))),f}(k,F,E,K));let N=[k,...L],O=await D(b,G),P=[],Q=(null==(d=i.flip)?void 0:d.overflows)||[];if(z&&P.push(O[H]),A){let a=function(a,b,c){void 0===c&&(c=!1);let d=o(a),e=p(s(a)),f=q(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=y(g)),[g,y(g)]}(h,j,K);P.push(O[a[0]],O[a[1]])}if(Q=[...Q,{placement:h,overflows:P}],!P.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=N[a];if(b&&("alignment"!==A||I===s(b)||Q.every(a=>a.overflows[0]>0&&s(a.placement)===I)))return{data:{index:a,overflows:Q},reset:{placement:b}};let c=null==(f=Q.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(C){case"bestFit":{let a=null==(g=Q.filter(a=>{if(M){let b=s(a.placement);return b===I||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=k}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,b]}))({...Z}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,d;let e,h,{placement:i,rects:j,platform:k,elements:l}=b,{apply:p=()=>{},...q}=m(a,b),r=await D(b,q),t=n(i),u=o(i),v="y"===s(i),{width:w,height:x}=j.floating;"top"===t||"bottom"===t?(e=t,h=u===(await (null==k.isRTL?void 0:k.isRTL(l.floating))?"start":"end")?"left":"right"):(h=t,e="end"===u?"top":"bottom");let y=x-r.top-r.bottom,z=w-r.left-r.right,A=f(x-r[e],y),B=f(w-r[h],z),C=!b.middlewareData.shift,E=A,F=B;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(F=z),null!=(d=b.middlewareData.shift)&&d.enabled.y&&(E=y),C&&!u){let a=g(r.left,0),b=g(r.right,0),c=g(r.top,0),d=g(r.bottom,0);v?F=w-2*(0!==a||0!==b?a+b:g(r.left,r.right)):E=x-2*(0!==c||0!==d?c+d:g(r.top,r.bottom))}await p({...b,availableWidth:F,availableHeight:E});let G=await k.getDimensions(l.floating);return w!==G.width||x!==G.height?{reset:{rects:!0}}:{}}}}(a),options:[a,b]}))({...Z,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),R&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?av({element:c.current,padding:d}).fn(b):{}:c?av({element:c,padding:d}).fn(b):{}}}))(a),options:[a,b]}))({element:R,padding:l}),a$({arrowWidth:U,arrowHeight:V}),I&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=m(a,b);switch(d){case"referenceHidden":{let a=E(await D(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:F(a)}}}case"escaped":{let a=E(await D(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:F(a)}}}default:return{}}}}}(a),options:[a,b]}))({strategy:"referenceHidden",...Z})]}),[ae,af]=a_(aa),ah=(0,aH.c)(K);(0,aI.N)(()=>{ab&&ah?.()},[ab,ah]);let ai=ac.arrow?.x,aj=ac.arrow?.y,al=ac.arrow?.centerOffset!==0,[am,an]=d.useState();return(0,aI.N)(()=>{O&&an(window.getComputedStyle(O).zIndex)},[O]),(0,aD.jsx)("div",{ref:$.setFloating,"data-radix-popper-content-wrapper":"",style:{..._,transform:ab?_.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:am,"--radix-popper-transform-origin":[ac.transformOrigin?.x,ac.transformOrigin?.y].join(" "),...ac.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,aD.jsx)(aT,{scope:c,placedSide:ae,onArrowChange:S,arrowX:ai,arrowY:aj,shouldHideArrow:al,children:(0,aD.jsx)(aC.sG.div,{"data-side":ae,"data-align":af,...M,ref:Q,style:{...M.style,animation:ab?void 0:"none"}})})})});aV.displayName=aS;var aW="PopperArrow",aX={top:"bottom",right:"left",bottom:"top",left:"right"},aY=d.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=aU(aW,c),f=aX[e.placedSide];return(0,aD.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,aD.jsx)(aE,{...d,ref:b,style:{...d.style,display:"block"}})})});function aZ(a){return null!==a}aY.displayName=aW;var a$=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=a_(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function a_(a){let[b,c="center"]=a.split("-");return[b,c]}var a0=aP,a1=aR,a2=aV,a3=aY},56928:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"applyFlightData",{enumerable:!0,get:function(){return f}});let d=c(41500),e=c(33898);function f(a,b,c,f,g){let{tree:h,seedData:i,head:j,isRootRender:k}=f;if(null===i)return!1;if(k){let e=i[1];c.loading=i[3],c.rsc=e,c.prefetchRsc=null,(0,d.fillLazyItemsTillLeafWithHead)(a,c,b,h,i,j,g)}else c.rsc=b.rsc,c.prefetchRsc=b.prefetchRsc,c.parallelRoutes=new Map(b.parallelRoutes),c.loading=b.loading,(0,e.fillCacheWithNewSubTreeData)(a,c,b,f,g);return!0}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},57379:(a,b,c)=>{a.exports=c(53332)},57800:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},58869:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},58887:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},59435:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"handleMutable",{enumerable:!0,get:function(){return f}});let d=c(70642);function e(a){return void 0!==a}function f(a,b){var c,f;let g=null==(c=b.shouldScroll)||c,h=a.nextUrl;if(e(b.patchedTree)){let c=(0,d.computeChangedPath)(a.tree,b.patchedTree);c?h=c:h||(h=a.canonicalUrl)}return{canonicalUrl:e(b.canonicalUrl)?b.canonicalUrl===a.canonicalUrl?a.canonicalUrl:b.canonicalUrl:a.canonicalUrl,pushRef:{pendingPush:e(b.pendingPush)?b.pendingPush:a.pushRef.pendingPush,mpaNavigation:e(b.mpaNavigation)?b.mpaNavigation:a.pushRef.mpaNavigation,preserveCustomHistoryState:e(b.preserveCustomHistoryState)?b.preserveCustomHistoryState:a.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!g&&(!!e(null==b?void 0:b.scrollableSegments)||a.focusAndScrollRef.apply),onlyHashChange:b.onlyHashChange||!1,hashFragment:g?b.hashFragment&&""!==b.hashFragment?decodeURIComponent(b.hashFragment.slice(1)):a.focusAndScrollRef.hashFragment:null,segmentPaths:g?null!=(f=null==b?void 0:b.scrollableSegments)?f:a.focusAndScrollRef.segmentPaths:[]},cache:b.cache?b.cache:a.cache,prefetchCache:b.prefetchCache?b.prefetchCache:a.prefetchCache,tree:e(b.patchedTree)?b.patchedTree:a.tree,nextUrl:h}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},59656:(a,b,c)=>{c.r(b),c.d(b,{_:()=>e});var d=0;function e(a){return"__private_"+d+++"_"+a}},61611:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},61794:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"isLocalURL",{enumerable:!0,get:function(){return f}});let d=c(79289),e=c(26736);function f(a){if(!(0,d.isAbsoluteUrl)(a))return!0;try{let b=(0,d.getLocationOrigin)(),c=new URL(a,b);return c.origin===b&&(0,e.hasBasePath)(c.pathname)}catch(a){return!1}}},62369:(a,b,c)=>{c.d(b,{b:()=>j});var d=c(43210),e=c(14163),f=c(60687),g="horizontal",h=["horizontal","vertical"],i=d.forwardRef((a,b)=>{var c;let{decorative:d,orientation:i=g,...j}=a,k=(c=i,h.includes(c))?i:g;return(0,f.jsx)(e.sG.div,{"data-orientation":k,...d?{role:"none"}:{"aria-orientation":"vertical"===k?k:void 0,role:"separator"},...j,ref:b})});i.displayName="Separator";var j=i},62688:(a,b,c)=>{c.d(b,{A:()=>i});var d=c(43210);let e=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},f=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var g={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let h=(0,d.forwardRef)(({color:a="currentColor",size:b=24,strokeWidth:c=2,absoluteStrokeWidth:e,className:h="",children:i,iconNode:j,...k},l)=>(0,d.createElement)("svg",{ref:l,...g,width:b,height:b,stroke:a,strokeWidth:e?24*Number(c)/Number(b):c,className:f("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,b])=>(0,d.createElement)(a,b)),...Array.isArray(i)?i:[i]])),i=(a,b)=>{let c=(0,d.forwardRef)(({className:c,...g},i)=>(0,d.createElement)(h,{ref:i,iconNode:b,className:f(`lucide-${e(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,c),...g}));return c.displayName=e(a),c}},63376:(a,b,c)=>{c.d(b,{Eq:()=>j});var d=new WeakMap,e=new WeakMap,f={},g=0,h=function(a){return a&&(a.host||h(a.parentNode))},i=function(a,b,c,i){var j=(Array.isArray(a)?a:[a]).map(function(a){if(b.contains(a))return a;var c=h(a);return c&&b.contains(c)?c:(console.error("aria-hidden",a,"in not contained inside",b,". Doing nothing"),null)}).filter(function(a){return!!a});f[c]||(f[c]=new WeakMap);var k=f[c],l=[],m=new Set,n=new Set(j),o=function(a){!a||m.has(a)||(m.add(a),o(a.parentNode))};j.forEach(o);var p=function(a){!a||n.has(a)||Array.prototype.forEach.call(a.children,function(a){if(m.has(a))p(a);else try{var b=a.getAttribute(i),f=null!==b&&"false"!==b,g=(d.get(a)||0)+1,h=(k.get(a)||0)+1;d.set(a,g),k.set(a,h),l.push(a),1===g&&f&&e.set(a,!0),1===h&&a.setAttribute(c,"true"),f||a.setAttribute(i,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return p(b),m.clear(),g++,function(){l.forEach(function(a){var b=d.get(a)-1,f=k.get(a)-1;d.set(a,b),k.set(a,f),b||(e.has(a)||a.removeAttribute(i),e.delete(a)),f||a.removeAttribute(c)}),--g||(d=new WeakMap,d=new WeakMap,e=new WeakMap,f={})}},j=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),i(d,e,c,"aria-hidden")):function(){return null}}},63690:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{createMutableActionQueue:function(){return o},dispatchNavigateAction:function(){return q},dispatchTraverseAction:function(){return r},getCurrentAppRouterState:function(){return p},publicAppRouterInstance:function(){return s}});let d=c(59154),e=c(8830),f=c(43210),g=c(91992);c(50593);let h=c(19129),i=c(96127),j=c(89752),k=c(75076),l=c(73406);function m(a,b){null!==a.pending&&(a.pending=a.pending.next,null!==a.pending?n({actionQueue:a,action:a.pending,setState:b}):a.needsRefresh&&(a.needsRefresh=!1,a.dispatch({type:d.ACTION_REFRESH,origin:window.location.origin},b)))}async function n(a){let{actionQueue:b,action:c,setState:d}=a,e=b.state;b.pending=c;let f=c.payload,h=b.action(e,f);function i(a){c.discarded||(b.state=a,m(b,d),c.resolve(a))}(0,g.isThenable)(h)?h.then(i,a=>{m(b,d),c.reject(a)}):i(h)}function o(a,b){let c={state:a,dispatch:(a,b)=>(function(a,b,c){let e={resolve:c,reject:()=>{}};if(b.type!==d.ACTION_RESTORE){let a=new Promise((a,b)=>{e={resolve:a,reject:b}});(0,f.startTransition)(()=>{c(a)})}let g={payload:b,next:null,resolve:e.resolve,reject:e.reject};null===a.pending?(a.last=g,n({actionQueue:a,action:g,setState:c})):b.type===d.ACTION_NAVIGATE||b.type===d.ACTION_RESTORE?(a.pending.discarded=!0,g.next=a.pending.next,a.pending.payload.type===d.ACTION_SERVER_ACTION&&(a.needsRefresh=!0),n({actionQueue:a,action:g,setState:c})):(null!==a.last&&(a.last.next=g),a.last=g)})(c,a,b),action:async(a,b)=>(0,e.reducer)(a,b),pending:null,last:null,onRouterTransitionStart:null!==b&&"function"==typeof b.onRouterTransitionStart?b.onRouterTransitionStart:null};return c}function p(){return null}function q(a,b,c,e){let f=new URL((0,i.addBasePath)(a),location.href);(0,l.setLinkForCurrentNavigation)(e);(0,h.dispatchAppRouterAction)({type:d.ACTION_NAVIGATE,url:f,isExternalUrl:(0,j.isExternalURL)(f),locationSearch:location.search,shouldScroll:c,navigateType:b,allowAliasing:!0})}function r(a,b){(0,h.dispatchAppRouterAction)({type:d.ACTION_RESTORE,url:new URL(a),tree:b})}let s={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(a,b)=>{let c=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),e=(0,j.createPrefetchURL)(a);if(null!==e){var f;(0,k.prefetchReducer)(c.state,{type:d.ACTION_PREFETCH,url:e,kind:null!=(f=null==b?void 0:b.kind)?f:d.PrefetchKind.FULL})}},replace:(a,b)=>{(0,f.startTransition)(()=>{var c;q(a,"replace",null==(c=null==b?void 0:b.scroll)||c,null)})},push:(a,b)=>{(0,f.startTransition)(()=>{var c;q(a,"push",null==(c=null==b?void 0:b.scroll)||c,null)})},refresh:()=>{(0,f.startTransition)(()=>{(0,h.dispatchAppRouterAction)({type:d.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},65551:(a,b,c)=>{c.d(b,{i:()=>h});var d,e=c(43210),f=c(66156),g=(d||(d=c.t(e,2)))[" useInsertionEffect ".trim().toString()]||f.N;function h({prop:a,defaultProp:b,onChange:c=()=>{},caller:d}){let[f,h,i]=function({defaultProp:a,onChange:b}){let[c,d]=e.useState(a),f=e.useRef(c),h=e.useRef(b);return g(()=>{h.current=b},[b]),e.useEffect(()=>{f.current!==c&&(h.current?.(c),f.current=c)},[c,f]),[c,d,h]}({defaultProp:b,onChange:c}),j=void 0!==a,k=j?a:f;{let b=e.useRef(void 0!==a);e.useEffect(()=>{let a=b.current;if(a!==j){let b=j?"controlled":"uncontrolled";console.warn(`${d} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}b.current=j},[j,d])}return[k,e.useCallback(b=>{if(j){let c="function"==typeof b?b(a):b;c!==a&&i.current?.(c)}else h(b)},[j,a,h,i])]}Symbol("RADIX:SYNC_STATE")},65951:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"shouldHardNavigate",{enumerable:!0,get:function(){return function a(b,c){let[f,g]=c,[h,i]=b;return(0,e.matchSegment)(h,f)?!(b.length<=2)&&a((0,d.getNextFlightSegmentPath)(b),g[i]):!!Array.isArray(h)}}});let d=c(74007),e=c(14077);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},65956:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{abortTask:function(){return o},listenForDynamicRequest:function(){return n},startPPRNavigation:function(){return j},updateCacheNodeOnPopstateRestoration:function(){return function a(b,c){let d=c[1],e=b.parallelRoutes,g=new Map(e);for(let b in d){let c=d[b],h=c[0],i=(0,f.createRouterCacheKey)(h),j=e.get(b);if(void 0!==j){let d=j.get(i);if(void 0!==d){let e=a(d,c),f=new Map(j);f.set(i,e),g.set(b,f)}}}let h=b.rsc,i=r(h)&&"pending"===h.status;return{lazyData:null,rsc:h,head:b.head,prefetchHead:i?b.prefetchHead:[null,null],prefetchRsc:i?b.prefetchRsc:null,loading:b.loading,parallelRoutes:g,navigatedAt:b.navigatedAt}}}});let d=c(83913),e=c(14077),f=c(33123),g=c(2030),h=c(5334),i={route:null,node:null,dynamicRequestTree:null,children:null};function j(a,b,c,g,h,j,m,n,o){return function a(b,c,g,h,j,m,n,o,p,q,r){let s=g[1],t=h[1],u=null!==m?m[2]:null;j||!0===h[4]&&(j=!0);let v=c.parallelRoutes,w=new Map(v),x={},y=null,z=!1,A={};for(let c in t){let g,h=t[c],l=s[c],m=v.get(c),B=null!==u?u[c]:null,C=h[0],D=q.concat([c,C]),E=(0,f.createRouterCacheKey)(C),F=void 0!==l?l[0]:void 0,G=void 0!==m?m.get(E):void 0;if(null!==(g=C===d.DEFAULT_SEGMENT_KEY?void 0!==l?{route:l,node:null,dynamicRequestTree:null,children:null}:k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r):p&&0===Object.keys(h[1]).length?k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r):void 0!==l&&void 0!==F&&(0,e.matchSegment)(C,F)&&void 0!==G&&void 0!==l?a(b,G,l,h,j,B,n,o,p,D,r):k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r))){if(null===g.route)return i;null===y&&(y=new Map),y.set(c,g);let a=g.node;if(null!==a){let b=new Map(m);b.set(E,a),w.set(c,b)}let b=g.route;x[c]=b;let d=g.dynamicRequestTree;null!==d?(z=!0,A[c]=d):A[c]=b}else x[c]=h,A[c]=h}if(null===y)return null;let B={lazyData:null,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,loading:c.loading,parallelRoutes:w,navigatedAt:b};return{route:l(h,x),node:B,dynamicRequestTree:z?l(h,A):null,children:y}}(a,b,c,g,!1,h,j,m,n,[],o)}function k(a,b,c,d,e,j,k,n,o,p){return!e&&(void 0===b||(0,g.isNavigatingToNewRootLayout)(b,c))?i:function a(b,c,d,e,g,i,j,k){let n,o,p,q,r=c[1],s=0===Object.keys(r).length;if(void 0!==d&&d.navigatedAt+h.DYNAMIC_STALETIME_MS>b)n=d.rsc,o=d.loading,p=d.head,q=d.navigatedAt;else if(null===e)return m(b,c,null,g,i,j,k);else if(n=e[1],o=e[3],p=s?g:null,q=b,e[4]||i&&s)return m(b,c,e,g,i,j,k);let t=null!==e?e[2]:null,u=new Map,v=void 0!==d?d.parallelRoutes:null,w=new Map(v),x={},y=!1;if(s)k.push(j);else for(let c in r){let d=r[c],e=null!==t?t[c]:null,h=null!==v?v.get(c):void 0,l=d[0],m=j.concat([c,l]),n=(0,f.createRouterCacheKey)(l),o=a(b,d,void 0!==h?h.get(n):void 0,e,g,i,m,k);u.set(c,o);let p=o.dynamicRequestTree;null!==p?(y=!0,x[c]=p):x[c]=d;let q=o.node;if(null!==q){let a=new Map;a.set(n,q),w.set(c,a)}}return{route:c,node:{lazyData:null,rsc:n,prefetchRsc:null,head:p,prefetchHead:null,loading:o,parallelRoutes:w,navigatedAt:q},dynamicRequestTree:y?l(c,x):null,children:u}}(a,c,d,j,k,n,o,p)}function l(a,b){let c=[a[0],b];return 2 in a&&(c[2]=a[2]),3 in a&&(c[3]=a[3]),4 in a&&(c[4]=a[4]),c}function m(a,b,c,d,e,g,h){let i=l(b,b[1]);return i[3]="refetch",{route:b,node:function a(b,c,d,e,g,h,i){let j=c[1],k=null!==d?d[2]:null,l=new Map;for(let c in j){let d=j[c],m=null!==k?k[c]:null,n=d[0],o=h.concat([c,n]),p=(0,f.createRouterCacheKey)(n),q=a(b,d,void 0===m?null:m,e,g,o,i),r=new Map;r.set(p,q),l.set(c,r)}let m=0===l.size;m&&i.push(h);let n=null!==d?d[1]:null,o=null!==d?d[3]:null;return{lazyData:null,parallelRoutes:l,prefetchRsc:void 0!==n?n:null,prefetchHead:m?e:[null,null],loading:void 0!==o?o:null,rsc:s(),head:m?s():null,navigatedAt:b}}(a,b,c,d,e,g,h),dynamicRequestTree:i,children:null}}function n(a,b){b.then(b=>{let{flightData:c}=b;if("string"!=typeof c){for(let b of c){let{segmentPath:c,tree:d,seedData:g,head:h}=b;g&&function(a,b,c,d,g){let h=a;for(let a=0;a<b.length;a+=2){let c=b[a],d=b[a+1],f=h.children;if(null!==f){let a=f.get(c);if(void 0!==a){let b=a.route[0];if((0,e.matchSegment)(d,b)){h=a;continue}}}return}!function a(b,c,d,g){if(null===b.dynamicRequestTree)return;let h=b.children,i=b.node;if(null===h){null!==i&&(function a(b,c,d,g,h){let i=c[1],j=d[1],k=g[2],l=b.parallelRoutes;for(let b in i){let c=i[b],d=j[b],g=k[b],m=l.get(b),n=c[0],o=(0,f.createRouterCacheKey)(n),q=void 0!==m?m.get(o):void 0;void 0!==q&&(void 0!==d&&(0,e.matchSegment)(n,d[0])&&null!=g?a(q,c,d,g,h):p(c,q,null))}let m=b.rsc,n=g[1];null===m?b.rsc=n:r(m)&&m.resolve(n);let o=b.head;r(o)&&o.resolve(h)}(i,b.route,c,d,g),b.dynamicRequestTree=null);return}let j=c[1],k=d[2];for(let b in c){let c=j[b],d=k[b],f=h.get(b);if(void 0!==f){let b=f.route[0];if((0,e.matchSegment)(c[0],b)&&null!=d)return a(f,c,d,g)}}}(h,c,d,g)}(a,c,d,g,h)}o(a,null)}},b=>{o(a,b)})}function o(a,b){let c=a.node;if(null===c)return;let d=a.children;if(null===d)p(a.route,c,b);else for(let a of d.values())o(a,b);a.dynamicRequestTree=null}function p(a,b,c){let d=a[1],e=b.parallelRoutes;for(let a in d){let b=d[a],g=e.get(a);if(void 0===g)continue;let h=b[0],i=(0,f.createRouterCacheKey)(h),j=g.get(i);void 0!==j&&p(b,j,c)}let g=b.rsc;r(g)&&(null===c?g.resolve(null):g.reject(c));let h=b.head;r(h)&&h.resolve(null)}let q=Symbol();function r(a){return a&&a.tag===q}function s(){let a,b,c=new Promise((c,d)=>{a=c,b=d});return c.status="pending",c.resolve=b=>{"pending"===c.status&&(c.status="fulfilled",c.value=b,a(b))},c.reject=a=>{"pending"===c.status&&(c.status="rejected",c.reason=a,b(a))},c.tag=q,c}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},66156:(a,b,c)=>{c.d(b,{N:()=>e});var d=c(43210),e=globalThis?.document?d.useLayoutEffect:()=>{}},69018:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{GracefulDegradeBoundary:function(){return f},default:function(){return g}});let d=c(60687),e=c(43210);class f extends e.Component{static getDerivedStateFromError(a){return{hasError:!0}}componentDidMount(){let a=this.htmlRef.current;this.state.hasError&&a&&Object.entries(this.htmlAttributes).forEach(b=>{let[c,d]=b;a.setAttribute(c,d)})}render(){let{hasError:a}=this.state;return a?(0,d.jsx)("html",{ref:this.htmlRef,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:this.rootHtml}}):this.props.children}constructor(a){super(a),this.state={hasError:!1},this.rootHtml="",this.htmlAttributes={},this.htmlRef=(0,e.createRef)()}}let g=f;("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},69024:(a,b,c)=>{c.d(b,{Qg:()=>g,bL:()=>i});var d=c(43210),e=c(14163),f=c(60687),g=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),h=d.forwardRef((a,b)=>(0,f.jsx)(e.sG.span,{...a,ref:b,style:{...g,...a.style}}));h.displayName="VisuallyHidden";var i=h},70569:(a,b,c)=>{c.d(b,{m:()=>d});function d(a,b,{checkForDefaultPrevented:c=!0}={}){return function(d){if(a?.(d),!1===c||!d.defaultPrevented)return b?.(d)}}},70642:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{computeChangedPath:function(){return j},extractPathFromFlightRouterState:function(){return i},getSelectedParams:function(){return function a(b,c){for(let d of(void 0===c&&(c={}),Object.values(b[1]))){let b=d[0],f=Array.isArray(b),g=f?b[1]:b;!g||g.startsWith(e.PAGE_SEGMENT_KEY)||(f&&("c"===b[2]||"oc"===b[2])?c[b[0]]=b[1].split("/"):f&&(c[b[0]]=b[1]),c=a(d,c))}return c}}});let d=c(72859),e=c(83913),f=c(14077),g=a=>"string"==typeof a?"children"===a?"":a:a[1];function h(a){return a.reduce((a,b)=>{let c;return""===(b="/"===(c=b)[0]?c.slice(1):c)||(0,e.isGroupSegment)(b)?a:a+"/"+b},"")||"/"}function i(a){var b;let c=Array.isArray(a[0])?a[0][1]:a[0];if(c===e.DEFAULT_SEGMENT_KEY||d.INTERCEPTION_ROUTE_MARKERS.some(a=>c.startsWith(a)))return;if(c.startsWith(e.PAGE_SEGMENT_KEY))return"";let f=[g(c)],j=null!=(b=a[1])?b:{},k=j.children?i(j.children):void 0;if(void 0!==k)f.push(k);else for(let[a,b]of Object.entries(j)){if("children"===a)continue;let c=i(b);void 0!==c&&f.push(c)}return h(f)}function j(a,b){let c=function a(b,c){let[e,h]=b,[j,k]=c,l=g(e),m=g(j);if(d.INTERCEPTION_ROUTE_MARKERS.some(a=>l.startsWith(a)||m.startsWith(a)))return"";if(!(0,f.matchSegment)(e,j)){var n;return null!=(n=i(c))?n:""}for(let b in h)if(k[b]){let c=a(h[b],k[b]);if(null!==c)return g(j)+"/"+c}return null}(a,b);return null==c||"/"===c?c:h(c.split("/"))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},72942:(a,b,c)=>{c.d(b,{RG:()=>v,bL:()=>E,q7:()=>F});var d=c(43210),e=c(70569),f=c(9510),g=c(98599),h=c(11273),i=c(96963),j=c(14163),k=c(13495),l=c(65551),m=c(43),n=c(60687),o="rovingFocusGroup.onEntryFocus",p={bubbles:!1,cancelable:!0},q="RovingFocusGroup",[r,s,t]=(0,f.N)(q),[u,v]=(0,h.A)(q,[t]),[w,x]=u(q),y=d.forwardRef((a,b)=>(0,n.jsx)(r.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,n.jsx)(r.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,n.jsx)(z,{...a,ref:b})})}));y.displayName=q;var z=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,orientation:f,loop:h=!1,dir:i,currentTabStopId:r,defaultCurrentTabStopId:t,onCurrentTabStopIdChange:u,onEntryFocus:v,preventScrollOnEntryFocus:x=!1,...y}=a,z=d.useRef(null),A=(0,g.s)(b,z),B=(0,m.jH)(i),[C,E]=(0,l.i)({prop:r,defaultProp:t??null,onChange:u,caller:q}),[F,G]=d.useState(!1),H=(0,k.c)(v),I=s(c),J=d.useRef(!1),[K,L]=d.useState(0);return d.useEffect(()=>{let a=z.current;if(a)return a.addEventListener(o,H),()=>a.removeEventListener(o,H)},[H]),(0,n.jsx)(w,{scope:c,orientation:f,dir:B,loop:h,currentTabStopId:C,onItemFocus:d.useCallback(a=>E(a),[E]),onItemShiftTab:d.useCallback(()=>G(!0),[]),onFocusableItemAdd:d.useCallback(()=>L(a=>a+1),[]),onFocusableItemRemove:d.useCallback(()=>L(a=>a-1),[]),children:(0,n.jsx)(j.sG.div,{tabIndex:F||0===K?-1:0,"data-orientation":f,...y,ref:A,style:{outline:"none",...a.style},onMouseDown:(0,e.m)(a.onMouseDown,()=>{J.current=!0}),onFocus:(0,e.m)(a.onFocus,a=>{let b=!J.current;if(a.target===a.currentTarget&&b&&!F){let b=new CustomEvent(o,p);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=I().filter(a=>a.focusable);D([a.find(a=>a.active),a.find(a=>a.id===C),...a].filter(Boolean).map(a=>a.ref.current),x)}}J.current=!1}),onBlur:(0,e.m)(a.onBlur,()=>G(!1))})})}),A="RovingFocusGroupItem",B=d.forwardRef((a,b)=>{let{__scopeRovingFocusGroup:c,focusable:f=!0,active:g=!1,tabStopId:h,children:k,...l}=a,m=(0,i.B)(),o=h||m,p=x(A,c),q=p.currentTabStopId===o,t=s(c),{onFocusableItemAdd:u,onFocusableItemRemove:v,currentTabStopId:w}=p;return d.useEffect(()=>{if(f)return u(),()=>v()},[f,u,v]),(0,n.jsx)(r.ItemSlot,{scope:c,id:o,focusable:f,active:g,children:(0,n.jsx)(j.sG.span,{tabIndex:q?0:-1,"data-orientation":p.orientation,...l,ref:b,onMouseDown:(0,e.m)(a.onMouseDown,a=>{f?p.onItemFocus(o):a.preventDefault()}),onFocus:(0,e.m)(a.onFocus,()=>p.onItemFocus(o)),onKeyDown:(0,e.m)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void p.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return C[e]}(a,p.orientation,p.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=t().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=p.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>D(c))}}),children:"function"==typeof k?k({isCurrentTabStop:q,hasTabStop:null!=w}):k})})});B.displayName=A;var C={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var E=y,F=B},73406:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{IDLE_LINK_STATUS:function(){return j},PENDING_LINK_STATUS:function(){return i},mountFormInstance:function(){return s},mountLinkInstance:function(){return r},onLinkVisibilityChanged:function(){return u},onNavigationIntent:function(){return v},pingVisibleLinks:function(){return x},setLinkForCurrentNavigation:function(){return k},unmountLinkForCurrentNavigation:function(){return l},unmountPrefetchableInstance:function(){return t}}),c(63690);let d=c(89752),e=c(59154),f=c(50593),g=c(43210),h=null,i={pending:!0},j={pending:!1};function k(a){(0,g.startTransition)(()=>{null==h||h.setOptimisticLinkStatus(j),null==a||a.setOptimisticLinkStatus(i),h=a})}function l(a){h===a&&(h=null)}let m="function"==typeof WeakMap?new WeakMap:new Map,n=new Set,o="function"==typeof IntersectionObserver?new IntersectionObserver(function(a){for(let b of a){let a=b.intersectionRatio>0;u(b.target,a)}},{rootMargin:"200px"}):null;function p(a,b){void 0!==m.get(a)&&t(a),m.set(a,b),null!==o&&o.observe(a)}function q(a){try{return(0,d.createPrefetchURL)(a)}catch(b){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+a+"' because it cannot be converted to a URL."),null}}function r(a,b,c,d,e,f){if(e){let e=q(b);if(null!==e){let b={router:c,kind:d,isVisible:!1,prefetchTask:null,prefetchHref:e.href,setOptimisticLinkStatus:f};return p(a,b),b}}return{router:c,kind:d,isVisible:!1,prefetchTask:null,prefetchHref:null,setOptimisticLinkStatus:f}}function s(a,b,c,d){let e=q(b);null!==e&&p(a,{router:c,kind:d,isVisible:!1,prefetchTask:null,prefetchHref:e.href,setOptimisticLinkStatus:null})}function t(a){let b=m.get(a);if(void 0!==b){m.delete(a),n.delete(b);let c=b.prefetchTask;null!==c&&(0,f.cancelPrefetchTask)(c)}null!==o&&o.unobserve(a)}function u(a,b){let c=m.get(a);void 0!==c&&(c.isVisible=b,b?n.add(c):n.delete(c),w(c,f.PrefetchPriority.Default))}function v(a,b){let c=m.get(a);void 0!==c&&void 0!==c&&w(c,f.PrefetchPriority.Intent)}function w(a,b){let c=a.prefetchTask;if(!a.isVisible){null!==c&&(0,f.cancelPrefetchTask)(c);return}}function x(a,b){for(let c of n){let d=c.prefetchTask;if(null!==d&&!(0,f.isPrefetchTaskDirty)(d,a,b))continue;null!==d&&(0,f.cancelPrefetchTask)(d);let g=(0,f.createCacheKey)(c.prefetchHref,a);c.prefetchTask=(0,f.schedulePrefetchTask)(g,b,c.kind===e.PrefetchKind.FULL,f.PrefetchPriority.Default,null)}}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},75076:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{prefetchQueue:function(){return f},prefetchReducer:function(){return g}});let d=c(5144),e=c(5334),f=new d.PromiseQueue(5),g=function(a,b){(0,e.prunePrefetchCache)(a.prefetchCache);let{url:c}=b;return(0,e.getOrCreatePrefetchCacheEntry)({url:c,nextUrl:a.nextUrl,prefetchCache:a.prefetchCache,kind:b.kind,tree:a.tree,allowAliasing:!0}),a};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},76715:(a,b)=>{function c(a){let b={};for(let[c,d]of a.entries()){let a=b[c];void 0===a?b[c]=d:Array.isArray(a)?a.push(d):b[c]=[a,d]}return b}function d(a){return"string"==typeof a?a:("number"!=typeof a||isNaN(a))&&"boolean"!=typeof a?"":String(a)}function e(a){let b=new URLSearchParams;for(let[c,e]of Object.entries(a))if(Array.isArray(e))for(let a of e)b.append(c,d(a));else b.set(c,d(e));return b}function f(a){for(var b=arguments.length,c=Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];for(let b of c){for(let c of b.keys())a.delete(c);for(let[c,d]of b.entries())a.append(c,d)}return a}Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{assign:function(){return f},searchParamsToUrlQuery:function(){return c},urlQueryToSearchParams:function(){return e}})},77022:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"AppRouterAnnouncer",{enumerable:!0,get:function(){return g}});let d=c(43210),e=c(51215),f="next-route-announcer";function g(a){let{tree:b}=a,[c,g]=(0,d.useState)(null);(0,d.useEffect)(()=>(g(function(){var a;let b=document.getElementsByName(f)[0];if(null==b||null==(a=b.shadowRoot)?void 0:a.childNodes[0])return b.shadowRoot.childNodes[0];{let a=document.createElement(f);a.style.cssText="position:absolute";let b=document.createElement("div");return b.ariaLive="assertive",b.id="__next-route-announcer__",b.role="alert",b.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",a.attachShadow({mode:"open"}).appendChild(b),document.body.appendChild(a),b}}()),()=>{let a=document.getElementsByTagName(f)[0];(null==a?void 0:a.isConnected)&&document.body.removeChild(a)}),[]);let[h,i]=(0,d.useState)(""),j=(0,d.useRef)(void 0);return(0,d.useEffect)(()=>{let a="";if(document.title)a=document.title;else{let b=document.querySelector("h1");b&&(a=b.innerText||b.textContent||"")}void 0!==j.current&&j.current!==a&&i(a),j.current=a},[b]),c?(0,e.createPortal)(h,c):null}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},78866:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"refreshReducer",{enumerable:!0,get:function(){return o}});let d=c(59008),e=c(57391),f=c(86770),g=c(2030),h=c(25232),i=c(59435),j=c(41500),k=c(89752),l=c(96493),m=c(68214),n=c(22308);function o(a,b){let{origin:c}=b,o={},p=a.canonicalUrl,q=a.tree;o.preserveCustomHistoryState=!1;let r=(0,k.createEmptyCacheNode)(),s=(0,m.hasInterceptionRouteInCurrentTree)(a.tree);r.lazyData=(0,d.fetchServerResponse)(new URL(p,c),{flightRouterState:[q[0],q[1],q[2],"refetch"],nextUrl:s?a.nextUrl:null});let t=Date.now();return r.lazyData.then(async c=>{let{flightData:d,canonicalUrl:k}=c;if("string"==typeof d)return(0,h.handleExternalUrl)(a,o,d,a.pushRef.pendingPush);for(let c of(r.lazyData=null,d)){let{tree:d,seedData:i,head:m,isRootRender:u}=c;if(!u)return console.log("REFRESH FAILED"),a;let v=(0,f.applyRouterStatePatchToTree)([""],q,d,a.canonicalUrl);if(null===v)return(0,l.handleSegmentMismatch)(a,b,d);if((0,g.isNavigatingToNewRootLayout)(q,v))return(0,h.handleExternalUrl)(a,o,p,a.pushRef.pendingPush);let w=k?(0,e.createHrefFromUrl)(k):void 0;if(k&&(o.canonicalUrl=w),null!==i){let a=i[1],b=i[3];r.rsc=a,r.prefetchRsc=null,r.loading=b,(0,j.fillLazyItemsTillLeafWithHead)(t,r,void 0,d,i,m,void 0),o.prefetchCache=new Map}await (0,n.refreshInactiveParallelSegments)({navigatedAt:t,state:a,updatedTree:v,updatedCache:r,includeNextUrl:s,canonicalUrl:o.canonicalUrl||a.canonicalUrl}),o.cache=r,o.patchedTree=v,q=v}return(0,i.handleMutable)(a,o)},()=>a)}c(50593),("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},79289:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{DecodeError:function(){return o},MiddlewareNotFoundError:function(){return s},MissingStaticPage:function(){return r},NormalizeError:function(){return p},PageNotFoundError:function(){return q},SP:function(){return m},ST:function(){return n},WEB_VITALS:function(){return c},execOnce:function(){return d},getDisplayName:function(){return i},getLocationOrigin:function(){return g},getURL:function(){return h},isAbsoluteUrl:function(){return f},isResSent:function(){return j},loadGetInitialProps:function(){return l},normalizeRepeatedSlashes:function(){return k},stringifyError:function(){return t}});let c=["CLS","FCP","FID","INP","LCP","TTFB"];function d(a){let b,c=!1;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return c||(c=!0,b=a(...e)),b}}let e=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,f=a=>e.test(a);function g(){let{protocol:a,hostname:b,port:c}=window.location;return a+"//"+b+(c?":"+c:"")}function h(){let{href:a}=window.location,b=g();return a.substring(b.length)}function i(a){return"string"==typeof a?a:a.displayName||a.name||"Unknown"}function j(a){return a.finished||a.headersSent}function k(a){let b=a.split("?");return b[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(b[1]?"?"+b.slice(1).join("?"):"")}async function l(a,b){let c=b.res||b.ctx&&b.ctx.res;if(!a.getInitialProps)return b.ctx&&b.Component?{pageProps:await l(b.Component,b.ctx)}:{};let d=await a.getInitialProps(b);if(c&&j(c))return d;if(!d)throw Object.defineProperty(Error('"'+i(a)+'.getInitialProps()" should resolve to an object. But found "'+d+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return d}let m="undefined"!=typeof performance,n=m&&["mark","measure","getEntriesByName"].every(a=>"function"==typeof performance[a]);class o extends Error{}class p extends Error{}class q extends Error{constructor(a){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+a}}class r extends Error{constructor(a,b){super(),this.message="Failed to load static file for page: "+a+" "+b}}class s extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function t(a){return JSON.stringify({message:a.message,stack:a.stack})}},84027:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},84949:(a,b)=>{function c(a){return a.replace(/\/$/,"")||"/"}Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"removeTrailingSlash",{enumerable:!0,get:function(){return c}})},85814:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{default:function(){return q},useLinkStatus:function(){return s}});let d=c(40740),e=c(60687),f=d._(c(43210)),g=c(30195),h=c(22142),i=c(59154),j=c(53038),k=c(79289),l=c(96127);c(50148);let m=c(73406),n=c(61794),o=c(63690);function p(a){return"string"==typeof a?a:(0,g.formatUrl)(a)}function q(a){let b,c,d,[g,q]=(0,f.useOptimistic)(m.IDLE_LINK_STATUS),s=(0,f.useRef)(null),{href:t,as:u,children:v,prefetch:w=null,passHref:x,replace:y,shallow:z,scroll:A,onClick:B,onMouseEnter:C,onTouchStart:D,legacyBehavior:E=!1,onNavigate:F,ref:G,unstable_dynamicOnHover:H,...I}=a;b=v,E&&("string"==typeof b||"number"==typeof b)&&(b=(0,e.jsx)("a",{children:b}));let J=f.default.useContext(h.AppRouterContext),K=!1!==w,L=null===w||"auto"===w?i.PrefetchKind.AUTO:i.PrefetchKind.FULL,{href:M,as:N}=f.default.useMemo(()=>{let a=p(t);return{href:a,as:u?p(u):a}},[t,u]);E&&(c=f.default.Children.only(b));let O=E?c&&"object"==typeof c&&c.ref:G,P=f.default.useCallback(a=>(null!==J&&(s.current=(0,m.mountLinkInstance)(a,M,J,L,K,q)),()=>{s.current&&((0,m.unmountLinkForCurrentNavigation)(s.current),s.current=null),(0,m.unmountPrefetchableInstance)(a)}),[K,M,J,L,q]),Q={ref:(0,j.useMergedRef)(P,O),onClick(a){E||"function"!=typeof B||B(a),E&&c.props&&"function"==typeof c.props.onClick&&c.props.onClick(a),J&&(a.defaultPrevented||function(a,b,c,d,e,g,h){let{nodeName:i}=a.currentTarget;if(!("A"===i.toUpperCase()&&function(a){let b=a.currentTarget.getAttribute("target");return b&&"_self"!==b||a.metaKey||a.ctrlKey||a.shiftKey||a.altKey||a.nativeEvent&&2===a.nativeEvent.which}(a)||a.currentTarget.hasAttribute("download"))){if(!(0,n.isLocalURL)(b)){e&&(a.preventDefault(),location.replace(b));return}if(a.preventDefault(),h){let a=!1;if(h({preventDefault:()=>{a=!0}}),a)return}f.default.startTransition(()=>{(0,o.dispatchNavigateAction)(c||b,e?"replace":"push",null==g||g,d.current)})}}(a,M,N,s,y,A,F))},onMouseEnter(a){E||"function"!=typeof C||C(a),E&&c.props&&"function"==typeof c.props.onMouseEnter&&c.props.onMouseEnter(a),J&&K&&(0,m.onNavigationIntent)(a.currentTarget,!0===H)},onTouchStart:function(a){E||"function"!=typeof D||D(a),E&&c.props&&"function"==typeof c.props.onTouchStart&&c.props.onTouchStart(a),J&&K&&(0,m.onNavigationIntent)(a.currentTarget,!0===H)}};return(0,k.isAbsoluteUrl)(N)?Q.href=N:E&&!x&&("a"!==c.type||"href"in c.props)||(Q.href=(0,l.addBasePath)(N)),d=E?f.default.cloneElement(c,Q):(0,e.jsx)("a",{...I,...Q,children:b}),(0,e.jsx)(r.Provider,{value:g,children:d})}c(32708);let r=(0,f.createContext)(m.IDLE_LINK_STATUS),s=()=>(0,f.useContext)(r);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},86770:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function a(b,c,d,i){let j,[k,l,m,n,o]=c;if(1===b.length){let a=h(c,d);return(0,g.addRefreshMarkerToActiveParallelSegments)(a,i),a}let[p,q]=b;if(!(0,f.matchSegment)(p,k))return null;if(2===b.length)j=h(l[q],d);else if(null===(j=a((0,e.getNextFlightSegmentPath)(b),l[q],d,i)))return null;let r=[b[0],{...l,[q]:j},m,n];return o&&(r[4]=!0),(0,g.addRefreshMarkerToActiveParallelSegments)(r,i),r}}});let d=c(83913),e=c(74007),f=c(14077),g=c(22308);function h(a,b){let[c,e]=a,[g,i]=b;if(g===d.DEFAULT_SEGMENT_KEY&&c!==d.DEFAULT_SEGMENT_KEY)return a;if((0,f.matchSegment)(c,g)){let b={};for(let a in e)void 0!==i[a]?b[a]=h(e[a],i[a]):b[a]=e[a];for(let a in i)b[a]||(b[a]=i[a]);let d=[c,b];return a[2]&&(d[2]=a[2]),a[3]&&(d[3]=a[3]),a[4]&&(d[4]=a[4]),d}return b}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},89752:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(b,{createEmptyCacheNode:function(){return G},createPrefetchURL:function(){return E},default:function(){return K},isExternalURL:function(){return D}});let d=c(14985),e=c(40740),f=c(60687),g=e._(c(43210)),h=c(22142),i=c(59154),j=c(57391),k=c(10449),l=c(19129),m=c(35656),n=d._(c(25227)),o=c(35416),p=c(96127),q=c(77022),r=c(67086),s=c(44397),t=c(89330),u=c(25942),v=c(26736),w=c(70642),x=c(12776),y=c(63690),z=c(36875),A=c(97860);c(73406);let B=d._(c(69018)),C={};function D(a){return a.origin!==window.location.origin}function E(a){let b;if((0,o.isBot)(window.navigator.userAgent))return null;try{b=new URL((0,p.addBasePath)(a),window.location.href)}catch(b){throw Object.defineProperty(Error("Cannot prefetch '"+a+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return D(b)?null:b}function F(a){let{appRouterState:b}=a;return(0,g.useInsertionEffect)(()=>{let{tree:a,pushRef:c,canonicalUrl:d}=b,e={...c.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:a};c.pendingPush&&(0,j.createHrefFromUrl)(new URL(window.location.href))!==d?(c.pendingPush=!1,window.history.pushState(e,"",d)):window.history.replaceState(e,"",d)},[b]),(0,g.useEffect)(()=>{},[b.nextUrl,b.tree]),null}function G(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function H(a){null==a&&(a={});let b=window.history.state,c=null==b?void 0:b.__NA;c&&(a.__NA=c);let d=null==b?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE;return d&&(a.__PRIVATE_NEXTJS_INTERNALS_TREE=d),a}function I(a){let{headCacheNode:b}=a,c=null!==b?b.head:null,d=null!==b?b.prefetchHead:null,e=null!==d?d:c;return(0,g.useDeferredValue)(c,e)}function J(a){let b,{actionQueue:c,assetPrefix:d,globalError:e,gracefullyDegrade:j}=a,n=(0,l.useActionQueue)(c),{canonicalUrl:o}=n,{searchParams:p,pathname:x}=(0,g.useMemo)(()=>{let a=new URL(o,"http://n");return{searchParams:a.searchParams,pathname:(0,v.hasBasePath)(a.pathname)?(0,u.removeBasePath)(a.pathname):a.pathname}},[o]);(0,g.useEffect)(()=>{function a(a){var b;a.persisted&&(null==(b=window.history.state)?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(C.pendingMpaPath=void 0,(0,l.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",a),()=>{window.removeEventListener("pageshow",a)}},[]),(0,g.useEffect)(()=>{function a(a){let b="reason"in a?a.reason:a.error;if((0,A.isRedirectError)(b)){a.preventDefault();let c=(0,z.getURLFromRedirectError)(b);(0,z.getRedirectTypeFromError)(b)===A.RedirectType.push?y.publicAppRouterInstance.push(c,{}):y.publicAppRouterInstance.replace(c,{})}}return window.addEventListener("error",a),window.addEventListener("unhandledrejection",a),()=>{window.removeEventListener("error",a),window.removeEventListener("unhandledrejection",a)}},[]);let{pushRef:D}=n;if(D.mpaNavigation){if(C.pendingMpaPath!==o){let a=window.location;D.pendingPush?a.assign(o):a.replace(o),C.pendingMpaPath=o}throw t.unresolvedThenable}(0,g.useEffect)(()=>{let a=window.history.pushState.bind(window.history),b=window.history.replaceState.bind(window.history),c=a=>{var b;let c=window.location.href,d=null==(b=window.history.state)?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,g.startTransition)(()=>{(0,l.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(null!=a?a:c,c),tree:d})})};window.history.pushState=function(b,d,e){return(null==b?void 0:b.__NA)||(null==b?void 0:b._N)||(b=H(b),e&&c(e)),a(b,d,e)},window.history.replaceState=function(a,d,e){return(null==a?void 0:a.__NA)||(null==a?void 0:a._N)||(a=H(a),e&&c(e)),b(a,d,e)};let d=a=>{if(a.state){if(!a.state.__NA)return void window.location.reload();(0,g.startTransition)(()=>{(0,y.dispatchTraverseAction)(window.location.href,a.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",d),()=>{window.history.pushState=a,window.history.replaceState=b,window.removeEventListener("popstate",d)}},[]);let{cache:E,tree:G,nextUrl:J,focusAndScrollRef:K}=n,L=(0,g.useMemo)(()=>(0,s.findHeadInCache)(E,G[1]),[E,G]),M=(0,g.useMemo)(()=>(0,w.getSelectedParams)(G),[G]),O=(0,g.useMemo)(()=>({parentTree:G,parentCacheNode:E,parentSegmentPath:null,url:o}),[G,E,o]),P=(0,g.useMemo)(()=>({tree:G,focusAndScrollRef:K,nextUrl:J}),[G,K,J]);if(null!==L){let[a,c]=L;b=(0,f.jsx)(I,{headCacheNode:a},c)}else b=null;let Q=(0,f.jsxs)(r.RedirectBoundary,{children:[b,E.rsc,(0,f.jsx)(q.AppRouterAnnouncer,{tree:G})]});return Q=j?(0,f.jsx)(B.default,{children:Q}):(0,f.jsx)(m.ErrorBoundary,{errorComponent:e[0],errorStyles:e[1],children:Q}),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(F,{appRouterState:n}),(0,f.jsx)(N,{}),(0,f.jsx)(k.PathParamsContext.Provider,{value:M,children:(0,f.jsx)(k.PathnameContext.Provider,{value:x,children:(0,f.jsx)(k.SearchParamsContext.Provider,{value:p,children:(0,f.jsx)(h.GlobalLayoutRouterContext.Provider,{value:P,children:(0,f.jsx)(h.AppRouterContext.Provider,{value:y.publicAppRouterInstance,children:(0,f.jsx)(h.LayoutRouterContext.Provider,{value:O,children:Q})})})})})})]})}function K(a){let{actionQueue:b,globalErrorState:c,assetPrefix:d,gracefullyDegrade:e}=a;(0,x.useNavFailureHandler)();let g=(0,f.jsx)(J,{actionQueue:b,assetPrefix:d,globalError:c,gracefullyDegrade:e});return e?g:(0,f.jsx)(m.ErrorBoundary,{errorComponent:n.default,children:g})}let L=new Set,M=new Set;function N(){let[,a]=g.default.useState(0),b=L.size;return(0,g.useEffect)(()=>{let c=()=>a(a=>a+1);return M.add(c),b!==L.size&&c(),()=>{M.delete(c)}},[b,a]),[...L].map((a,b)=>(0,f.jsx)("link",{rel:"stylesheet",href:""+a,precedence:"next"},b))}globalThis._N_E_STYLE_LOAD=function(a){let b=L.size;return L.add(a),L.size!==b&&M.forEach(a=>a()),Promise.resolve()},("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},95796:(a,b)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return c}});let c=/Mediapartners-Google|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti/i},96127:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"addBasePath",{enumerable:!0,get:function(){return f}});let d=c(98834),e=c(54674);function f(a,b){return(0,e.normalizePathTrailingSlash)((0,d.addPathPrefix)(a,""))}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},96493:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"handleSegmentMismatch",{enumerable:!0,get:function(){return e}});let d=c(25232);function e(a,b,c){return(0,d.handleExternalUrl)(a,{},a.canonicalUrl,!0)}("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},96963:(a,b,c)=>{c.d(b,{B:()=>i});var d,e=c(43210),f=c(66156),g=(d||(d=c.t(e,2)))[" useId ".trim().toString()]||(()=>void 0),h=0;function i(a){let[b,c]=e.useState(g());return(0,f.N)(()=>{a||c(a=>a??String(h++))},[a]),a||(b?`radix-${b}`:"")}},97464:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function a(b,c,f){let g=f.length<=2,[h,i]=f,j=(0,e.createRouterCacheKey)(i),k=c.parallelRoutes.get(h),l=b.parallelRoutes.get(h);l&&l!==k||(l=new Map(k),b.parallelRoutes.set(h,l));let m=null==k?void 0:k.get(j),n=l.get(j);if(g){n&&n.lazyData&&n!==m||l.set(j,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!n||!m){n||l.set(j,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return n===m&&(n={lazyData:n.lazyData,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,parallelRoutes:new Map(n.parallelRoutes),loading:n.loading},l.set(j,n)),a(n,m,(0,d.getNextFlightSegmentPath)(f))}}});let d=c(74007),e=c(33123);("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},97936:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"hmrRefreshReducer",{enumerable:!0,get:function(){return d}}),c(59008),c(57391),c(86770),c(2030),c(25232),c(59435),c(56928),c(89752),c(96493),c(68214);let d=function(a,b){return a};("function"==typeof b.default||"object"==typeof b.default&&null!==b.default)&&void 0===b.default.__esModule&&(Object.defineProperty(b.default,"__esModule",{value:!0}),Object.assign(b.default,b),a.exports=b.default)},98834:(a,b,c)=>{Object.defineProperty(b,"__esModule",{value:!0}),Object.defineProperty(b,"addPathPrefix",{enumerable:!0,get:function(){return e}});let d=c(19169);function e(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:e,hash:f}=(0,d.parsePath)(a);return""+b+c+e+f}}};