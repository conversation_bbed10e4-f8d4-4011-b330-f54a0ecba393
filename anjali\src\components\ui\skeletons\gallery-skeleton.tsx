import { Skeleton } from '@/components/ui/skeleton'
import { StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'

export function GalleryItemSkeleton() {
  return (
    <div className="group relative aspect-square overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10">
      <Skeleton className="w-full h-full" />
      
      {/* Overlay skeleton */}
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent">
        <div className="absolute bottom-0 left-0 right-0 p-6">
          <Skeleton className="h-4 w-16 mb-3 bg-white/20" />
          <Skeleton className="h-5 w-3/4 mb-2 bg-white/30" />
          <Skeleton className="h-3 w-full mb-3 bg-white/20" />
          <div className="flex flex-wrap gap-1">
            <Skeleton className="h-5 w-12 bg-white/20" />
            <Skeleton className="h-5 w-16 bg-white/20" />
            <Skeleton className="h-5 w-14 bg-white/20" />
          </div>
        </div>
      </div>

      {/* View icon skeleton */}
      <div className="absolute top-4 right-4">
        <Skeleton className="w-10 h-10 rounded-full bg-white/20" />
      </div>
    </div>
  )
}

export function GalleryGridSkeleton() {
  return (
    <StaggeredContainer className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {Array.from({ length: 12 }).map((_, index) => (
        <StaggeredItem key={index}>
          <GalleryItemSkeleton />
        </StaggeredItem>
      ))}
    </StaggeredContainer>
  )
}

export function GalleryCategoriesSkeleton() {
  return (
    <div className="flex flex-wrap justify-center gap-3 mb-12">
      {Array.from({ length: 6 }).map((_, index) => (
        <div key={index} className="flex items-center gap-2">
          <Skeleton className="h-8 w-20 rounded-full" />
        </div>
      ))}
    </div>
  )
}

export function GalleryMasonrySkeleton() {
  const heights = ['aspect-[3/4]', 'aspect-square', 'aspect-[4/5]', 'aspect-[3/5]']
  
  return (
    <StaggeredContainer className="columns-1 md:columns-2 lg:columns-3 xl:columns-4 gap-6 space-y-6">
      {Array.from({ length: 8 }).map((_, index) => {
        const randomHeight = heights[index % heights.length]
        
        return (
          <StaggeredItem key={`masonry-${index}`}>
            <div className={`group relative ${randomHeight} overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 break-inside-avoid`}>
              <Skeleton className="w-full h-full" />
              
              {/* Overlay skeleton */}
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent">
                <div className="absolute bottom-0 left-0 right-0 p-4">
                  <Skeleton className="h-3 w-12 mb-2 bg-white/20" />
                  <Skeleton className="h-4 w-2/3 mb-1 bg-white/30" />
                  <Skeleton className="h-3 w-full bg-white/20" />
                </div>
              </div>

              {/* View icon skeleton */}
              <div className="absolute top-3 right-3">
                <Skeleton className="w-8 h-8 rounded-full bg-white/20" />
              </div>
            </div>
          </StaggeredItem>
        )
      })}
    </StaggeredContainer>
  )
}
