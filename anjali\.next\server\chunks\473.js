"use strict";exports.id=473,exports.ids=[473],exports.modules={34473:(a,b,c)=>{c.d(b,{A:()=>v});var d=c(60687),e=c(43210),f=c(27605),g=c(63442),h=c(37566),i=c(27900),j=c(48340),k=c(33872),l=c(29523),m=c(4780);let n=e.forwardRef(({className:a,type:b,...c},e)=>(0,d.jsx)("input",{type:b,className:(0,m.cn)("flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-text-primary placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold disabled:cursor-not-allowed disabled:opacity-50",a),ref:e,...c}));n.displayName="Input";let o=e.forwardRef(({className:a,...b},c)=>(0,d.jsx)("textarea",{className:(0,m.cn)("flex min-h-[60px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold disabled:cursor-not-allowed disabled:opacity-50",a),ref:c,...b}));o.displayName="Textarea";var p=c(44493),q=c(96834),r=c(53462),s=c(70225);let t=h.Ik({name:h.Yj().min(2,"Name must be at least 2 characters"),email:h.Yj().email("Please enter a valid email address"),phone:h.Yj().min(10,"Please enter a valid phone number"),service:h.Yj().min(1,"Please select a service"),date:h.Yj().min(1,"Please select a preferred date"),message:h.Yj().min(10,"Message must be at least 10 characters")}),u=["Bridal Makeup","Party Makeup","Engagement Makeup","Traditional Makeup","Photoshoot Makeup","Makeup Lessons","Other"];function v(){let[a,b]=(0,e.useState)(!1),[c,h]=(0,e.useState)(!1),v=(0,r.Q2)(),{register:w,handleSubmit:x,formState:{errors:y},reset:z}=(0,f.mN)({resolver:(0,g.u)(t)}),A=async a=>{b(!0);try{(0,s.iF)({service:a.service,source:"contact_page"}),await new Promise(a=>setTimeout(a,2e3)),h(!0),z()}catch(a){console.error("Error submitting form:",a)}finally{b(!1)}},B=(0,m.ec)(v.contact.whatsapp,v.whatsappMessage);return c?(0,d.jsx)(p.Card,{className:"max-w-md mx-auto text-center",children:(0,d.jsxs)(p.CardContent,{className:"pt-6",children:[(0,d.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,d.jsx)(i.A,{className:"w-8 h-8 text-green-600"})}),(0,d.jsx)("h3",{className:"font-display text-xl font-semibold text-text-primary mb-2",children:"Message Sent Successfully!"}),(0,d.jsx)("p",{className:"text-text-secondary mb-6",children:"Thank you for your inquiry. We'll get back to you within 24 hours."}),(0,d.jsx)(l.$,{onClick:()=>h(!1),variant:"outline",className:"w-full",children:"Send Another Message"})]})}):(0,d.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[(0,d.jsxs)(p.Card,{children:[(0,d.jsxs)(p.aR,{children:[(0,d.jsx)(p.ZB,{className:"font-display text-2xl",children:"Send us a Message"}),(0,d.jsx)(p.BT,{children:"Fill out the form below and we'll get back to you as soon as possible."})]}),(0,d.jsx)(p.CardContent,{children:(0,d.jsxs)("form",{onSubmit:x(A),className:"space-y-4",children:[(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{htmlFor:"name",className:"text-sm font-medium text-text-primary",children:"Full Name *"}),(0,d.jsx)(n,{id:"name",placeholder:"Your full name",...w("name"),className:y.name?"border-red-500":""}),y.name&&(0,d.jsx)("p",{className:"text-red-500 text-xs",children:y.name.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{htmlFor:"email",className:"text-sm font-medium text-text-primary",children:"Email Address *"}),(0,d.jsx)(n,{id:"email",type:"email",placeholder:"<EMAIL>",...w("email"),className:y.email?"border-red-500":""}),y.email&&(0,d.jsx)("p",{className:"text-red-500 text-xs",children:y.email.message})]})]}),(0,d.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{htmlFor:"phone",className:"text-sm font-medium text-text-primary",children:"Phone Number *"}),(0,d.jsx)(n,{id:"phone",placeholder:"+977-9800000000",...w("phone"),className:y.phone?"border-red-500":""}),y.phone&&(0,d.jsx)("p",{className:"text-red-500 text-xs",children:y.phone.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{htmlFor:"date",className:"text-sm font-medium text-text-primary",children:"Preferred Date *"}),(0,d.jsx)(n,{id:"date",type:"date",...w("date"),className:y.date?"border-red-500":""}),y.date&&(0,d.jsx)("p",{className:"text-red-500 text-xs",children:y.date.message})]})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{htmlFor:"service",className:"text-sm font-medium text-text-primary",children:"Service Interested In *"}),(0,d.jsxs)("select",{id:"service",...w("service"),className:`flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold ${y.service?"border-red-500":""}`,children:[(0,d.jsx)("option",{value:"",children:"Select a service"}),u.map(a=>(0,d.jsx)("option",{value:a,children:a},a))]}),y.service&&(0,d.jsx)("p",{className:"text-red-500 text-xs",children:y.service.message})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("label",{htmlFor:"message",className:"text-sm font-medium text-text-primary",children:"Message *"}),(0,d.jsx)(o,{id:"message",placeholder:"Tell us about your requirements, occasion, and any specific preferences...",rows:4,...w("message"),className:y.message?"border-red-500":""}),y.message&&(0,d.jsx)("p",{className:"text-red-500 text-xs",children:y.message.message})]}),(0,d.jsx)(l.$,{type:"submit",variant:"gradient",size:"lg",className:"w-full",disabled:a,children:a?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Sending..."]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(i.A,{className:"w-4 h-4 mr-2"}),"Send Message"]})})]})})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)(p.Card,{className:"bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0",children:[(0,d.jsxs)(p.aR,{children:[(0,d.jsxs)(p.ZB,{className:"font-display text-xl flex items-center gap-2",children:[(0,d.jsx)(j.A,{className:"w-5 h-5 text-rose-gold-dark"}),"Quick Contact"]}),(0,d.jsx)(p.BT,{children:"Need immediate assistance? Contact us directly via WhatsApp or phone."})]}),(0,d.jsxs)(p.CardContent,{className:"space-y-4",children:[(0,d.jsx)(l.$,{asChild:!0,variant:"gradient",size:"lg",className:"w-full",children:(0,d.jsxs)("a",{href:B,target:"_blank",rel:"noopener noreferrer",onClick:()=>(0,s.uJ)("contact_form","quick_contact"),children:[(0,d.jsx)(k.A,{className:"w-5 h-5 mr-2"}),"WhatsApp Now"]})}),(0,d.jsx)(l.$,{asChild:!0,variant:"outline",size:"lg",className:"w-full",children:(0,d.jsxs)("a",{href:`tel:${v.contact.phone}`,onClick:()=>(0,s.KA)("contact_form"),children:[(0,d.jsx)(j.A,{className:"w-5 h-5 mr-2"}),"Call Now"]})})]})]}),(0,d.jsxs)(p.Card,{children:[(0,d.jsx)(p.aR,{children:(0,d.jsx)(p.ZB,{className:"font-display text-xl",children:"Business Hours"})}),(0,d.jsxs)(p.CardContent,{className:"space-y-3",children:[(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-text-secondary",children:"Monday - Saturday"}),(0,d.jsx)(q.Badge,{variant:"outline",children:"9:00 AM - 6:00 PM"})]}),(0,d.jsxs)("div",{className:"flex justify-between items-center",children:[(0,d.jsx)("span",{className:"text-text-secondary",children:"Sunday"}),(0,d.jsx)(q.Badge,{variant:"outline",children:"10:00 AM - 4:00 PM"})]}),(0,d.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,d.jsx)("p",{className:"text-sm text-text-muted",children:"Emergency bookings available with advance notice"})})]})]})]})]})}}};