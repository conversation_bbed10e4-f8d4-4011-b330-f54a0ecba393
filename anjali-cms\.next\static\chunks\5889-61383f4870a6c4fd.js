"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5889],{468:(e,r,n)=>{n.d(r,{ServiceForm:()=>b});var t=n(5155),s=n(2115),a=n(5695),i=n(2177),l=n(221),c=n(690),o=n(285),d=n(2523),u=n(8539),h=n(7262),p=n(9409),x=n(7759),m=n(6695),j=n(2154),v=n(6671),f=n(4416),g=n(4616);let y=c.Ik({title:c.Yj().min(1,"Title is required"),description:c.Yj().min(1,"Description is required"),features:c.YO(c.Ik({value:c.Yj().min(1,"Feature cannot be empty")})).default([]),duration:c.Yj().optional(),price:c.Yj().optional(),image:c.Yj().optional(),category:c.Yj().optional(),popular:c.zM().default(!1),status:c.k5(["ACTIVE","INACTIVE","ARCHIVED"]).default("ACTIVE")});function b(e){var r;let{initialData:n,isEditing:c=!1}=e,b=(0,a.useRouter)(),[N,k]=(0,s.useState)(!1),C=(0,i.mN)({resolver:(0,l.u)(y),defaultValues:{title:(null==n?void 0:n.title)||"",description:(null==n?void 0:n.description)||"",features:(null==n||null==(r=n.features)?void 0:r.map(e=>({value:e})))||[{value:""}],duration:(null==n?void 0:n.duration)||"",price:(null==n?void 0:n.price)||"",image:(null==n?void 0:n.image)||"",category:(null==n?void 0:n.category)||"",popular:(null==n?void 0:n.popular)||!1,status:(null==n?void 0:n.status)||"ACTIVE"}}),{fields:R,append:I,remove:M}=(0,i.jz)({control:C.control,name:"features"}),A=async e=>{k(!0);try{let r={...e,features:e.features.map(e=>e.value).filter(e=>""!==e.trim())},t=c?"/api/services/".concat(null==n?void 0:n.id):"/api/services",s=await fetch(t,{method:c?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(s.ok)v.oR.success(c?"Service updated successfully":"Service created successfully"),b.push("/dashboard/services");else{let e=await s.json();v.oR.error(e.error||"Something went wrong")}}catch(e){v.oR.error("Error saving service")}finally{k(!1)}};return(0,t.jsx)(x.lV,{...C,children:(0,t.jsx)("form",{onSubmit:C.handleSubmit(A),className:"space-y-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(m.Zp,{children:[(0,t.jsxs)(m.aR,{children:[(0,t.jsx)(m.ZB,{children:"Basic Information"}),(0,t.jsx)(m.BT,{children:"Enter the basic details for your service"})]}),(0,t.jsxs)(m.Wu,{className:"space-y-4",children:[(0,t.jsx)(x.zB,{control:C.control,name:"title",render:e=>{let{field:r}=e;return(0,t.jsxs)(x.eI,{children:[(0,t.jsx)(x.lR,{children:"Service Title"}),(0,t.jsx)(x.MJ,{children:(0,t.jsx)(d.p,{placeholder:"e.g., Bridal Makeup",...r})}),(0,t.jsx)(x.C5,{})]})}}),(0,t.jsx)(x.zB,{control:C.control,name:"description",render:e=>{let{field:r}=e;return(0,t.jsxs)(x.eI,{children:[(0,t.jsx)(x.lR,{children:"Description"}),(0,t.jsx)(x.MJ,{children:(0,t.jsx)(u.T,{placeholder:"Describe your service in detail",className:"min-h-[100px]",...r})}),(0,t.jsx)(x.Rr,{children:"Provide a detailed description of what this service includes"}),(0,t.jsx)(x.C5,{})]})}}),(0,t.jsx)(x.zB,{control:C.control,name:"category",render:e=>{let{field:r}=e;return(0,t.jsxs)(x.eI,{children:[(0,t.jsx)(x.lR,{children:"Category"}),(0,t.jsx)(x.MJ,{children:(0,t.jsx)(d.p,{placeholder:"e.g., Bridal, Party, Traditional",...r})}),(0,t.jsx)(x.Rr,{children:"Categorize your service for better organization"}),(0,t.jsx)(x.C5,{})]})}}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsx)(x.zB,{control:C.control,name:"duration",render:e=>{let{field:r}=e;return(0,t.jsxs)(x.eI,{children:[(0,t.jsx)(x.lR,{children:"Duration"}),(0,t.jsx)(x.MJ,{children:(0,t.jsx)(d.p,{placeholder:"e.g., 2-3 hours",...r})}),(0,t.jsx)(x.C5,{})]})}}),(0,t.jsx)(x.zB,{control:C.control,name:"price",render:e=>{let{field:r}=e;return(0,t.jsxs)(x.eI,{children:[(0,t.jsx)(x.lR,{children:"Price"}),(0,t.jsx)(x.MJ,{children:(0,t.jsx)(d.p,{placeholder:"e.g., Starting from NPR 15,000",...r})}),(0,t.jsx)(x.C5,{})]})}})]})]})]}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsxs)(m.aR,{children:[(0,t.jsx)(m.ZB,{children:"Service Features"}),(0,t.jsx)(m.BT,{children:"List the key features and benefits of this service"})]}),(0,t.jsxs)(m.Wu,{className:"space-y-4",children:[R.map((e,r)=>(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(x.zB,{control:C.control,name:"features.".concat(r,".value"),render:e=>{let{field:r}=e;return(0,t.jsxs)(x.eI,{className:"flex-1",children:[(0,t.jsx)(x.MJ,{children:(0,t.jsx)(d.p,{placeholder:"e.g., Pre-bridal consultation",...r})}),(0,t.jsx)(x.C5,{})]})}}),(0,t.jsx)(o.$,{type:"button",variant:"outline",size:"sm",onClick:()=>M(r),disabled:1===R.length,children:(0,t.jsx)(f.A,{className:"h-4 w-4"})})]},e.id)),(0,t.jsxs)(o.$,{type:"button",variant:"outline",size:"sm",onClick:()=>I({value:""}),children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Add Feature"]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(m.Zp,{children:[(0,t.jsxs)(m.aR,{children:[(0,t.jsx)(m.ZB,{children:"Service Image"}),(0,t.jsx)(m.BT,{children:"Upload an image that represents this service"})]}),(0,t.jsx)(m.Wu,{children:(0,t.jsx)(x.zB,{control:C.control,name:"image",render:e=>{let{field:r}=e;return(0,t.jsxs)(x.eI,{children:[(0,t.jsx)(x.MJ,{children:(0,t.jsx)(j.B,{value:r.value,onChange:r.onChange,onRemove:()=>r.onChange(""),folder:"services"})}),(0,t.jsx)(x.C5,{})]})}})})]}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsx)(m.aR,{children:(0,t.jsx)(m.ZB,{children:"Settings"})}),(0,t.jsxs)(m.Wu,{className:"space-y-4",children:[(0,t.jsx)(x.zB,{control:C.control,name:"status",render:e=>{let{field:r}=e;return(0,t.jsxs)(x.eI,{children:[(0,t.jsx)(x.lR,{children:"Status"}),(0,t.jsxs)(p.l6,{onValueChange:r.onChange,defaultValue:r.value,children:[(0,t.jsx)(x.MJ,{children:(0,t.jsx)(p.bq,{children:(0,t.jsx)(p.yv,{placeholder:"Select status"})})}),(0,t.jsxs)(p.gC,{children:[(0,t.jsx)(p.eb,{value:"ACTIVE",children:"Active"}),(0,t.jsx)(p.eb,{value:"INACTIVE",children:"Inactive"}),(0,t.jsx)(p.eb,{value:"ARCHIVED",children:"Archived"})]})]}),(0,t.jsx)(x.C5,{})]})}}),(0,t.jsx)(x.zB,{control:C.control,name:"popular",render:e=>{let{field:r}=e;return(0,t.jsxs)(x.eI,{className:"flex flex-row items-start space-x-3 space-y-0",children:[(0,t.jsx)(x.MJ,{children:(0,t.jsx)(h.S,{checked:r.value,onCheckedChange:r.onChange})}),(0,t.jsxs)("div",{className:"space-y-1 leading-none",children:[(0,t.jsx)(x.lR,{children:"Popular Service"}),(0,t.jsx)(x.Rr,{children:"Mark this service as popular to highlight it"})]})]})}}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(o.$,{type:"submit",disabled:N,children:N?"Saving...":c?"Update":"Create"}),(0,t.jsx)(o.$,{type:"button",variant:"outline",onClick:()=>b.back(),children:"Cancel"})]})]})]})]})]})})})}},1154:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},4416:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4616:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5695:(e,r,n)=>{var t=n(8999);n.o(t,"redirect")&&n.d(r,{redirect:function(){return t.redirect}}),n.o(t,"useParams")&&n.d(r,{useParams:function(){return t.useParams}}),n.o(t,"usePathname")&&n.d(r,{usePathname:function(){return t.usePathname}}),n.o(t,"useRouter")&&n.d(r,{useRouter:function(){return t.useRouter}})},6981:(e,r,n)=>{n.d(r,{C1:()=>k,bL:()=>b});var t=n(2115),s=n(6101),a=n(6081),i=n(5185),l=n(5845),c=n(5503),o=n(1275),d=n(8905),u=n(3655),h=n(5155),p="Checkbox",[x,m]=(0,a.A)(p),[j,v]=x(p);function f(e){let{__scopeCheckbox:r,checked:n,children:s,defaultChecked:a,disabled:i,form:c,name:o,onCheckedChange:d,required:u,value:x="on",internal_do_not_use_render:m}=e,[v,f]=(0,l.i)({prop:n,defaultProp:null!=a&&a,onChange:d,caller:p}),[g,y]=t.useState(null),[b,N]=t.useState(null),k=t.useRef(!1),C=!g||!!c||!!g.closest("form"),R={checked:v,disabled:i,setChecked:f,control:g,setControl:y,name:o,form:c,value:x,hasConsumerStoppedPropagationRef:k,required:u,defaultChecked:!I(a)&&a,isFormControl:C,bubbleInput:b,setBubbleInput:N};return(0,h.jsx)(j,{scope:r,...R,children:"function"==typeof m?m(R):s})}var g="CheckboxTrigger",y=t.forwardRef((e,r)=>{let{__scopeCheckbox:n,onKeyDown:a,onClick:l,...c}=e,{control:o,value:d,disabled:p,checked:x,required:m,setControl:j,setChecked:f,hasConsumerStoppedPropagationRef:y,isFormControl:b,bubbleInput:N}=v(g,n),k=(0,s.s)(r,j),C=t.useRef(x);return t.useEffect(()=>{let e=null==o?void 0:o.form;if(e){let r=()=>f(C.current);return e.addEventListener("reset",r),()=>e.removeEventListener("reset",r)}},[o,f]),(0,h.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":I(x)?"mixed":x,"aria-required":m,"data-state":M(x),"data-disabled":p?"":void 0,disabled:p,value:d,...c,ref:k,onKeyDown:(0,i.m)(a,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(l,e=>{f(e=>!!I(e)||!e),N&&b&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});y.displayName=g;var b=t.forwardRef((e,r)=>{let{__scopeCheckbox:n,name:t,checked:s,defaultChecked:a,required:i,disabled:l,value:c,onCheckedChange:o,form:d,...u}=e;return(0,h.jsx)(f,{__scopeCheckbox:n,checked:s,defaultChecked:a,disabled:l,required:i,onCheckedChange:o,name:t,form:d,value:c,internal_do_not_use_render:e=>{let{isFormControl:t}=e;return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(y,{...u,ref:r,__scopeCheckbox:n}),t&&(0,h.jsx)(R,{__scopeCheckbox:n})]})}})});b.displayName=p;var N="CheckboxIndicator",k=t.forwardRef((e,r)=>{let{__scopeCheckbox:n,forceMount:t,...s}=e,a=v(N,n);return(0,h.jsx)(d.C,{present:t||I(a.checked)||!0===a.checked,children:(0,h.jsx)(u.sG.span,{"data-state":M(a.checked),"data-disabled":a.disabled?"":void 0,...s,ref:r,style:{pointerEvents:"none",...e.style}})})});k.displayName=N;var C="CheckboxBubbleInput",R=t.forwardRef((e,r)=>{let{__scopeCheckbox:n,...a}=e,{control:i,hasConsumerStoppedPropagationRef:l,checked:d,defaultChecked:p,required:x,disabled:m,name:j,value:f,form:g,bubbleInput:y,setBubbleInput:b}=v(C,n),N=(0,s.s)(r,b),k=(0,c.Z)(d),R=(0,o.X)(i);t.useEffect(()=>{if(!y)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,r=!l.current;if(k!==d&&e){let n=new Event("click",{bubbles:r});y.indeterminate=I(d),e.call(y,!I(d)&&d),y.dispatchEvent(n)}},[y,k,d,l]);let M=t.useRef(!I(d)&&d);return(0,h.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=p?p:M.current,required:x,disabled:m,name:j,value:f,form:g,...a,tabIndex:-1,ref:N,style:{...a.style,...R,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function I(e){return"indeterminate"===e}function M(e){return I(e)?"indeterminate":e?"checked":"unchecked"}R.displayName=C},7262:(e,r,n)=>{n.d(r,{S:()=>l});var t=n(5155);n(2115);var s=n(6981),a=n(5196),i=n(9434);function l(e){let{className:r,...n}=e;return(0,t.jsx)(s.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",r),...n,children:(0,t.jsx)(s.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(a.A,{className:"size-3.5"})})})}},8905:(e,r,n)=>{n.d(r,{C:()=>i});var t=n(2115),s=n(6101),a=n(2712),i=e=>{let{present:r,children:n}=e,i=function(e){var r,n;let[s,i]=t.useState(),c=t.useRef(null),o=t.useRef(e),d=t.useRef("none"),[u,h]=(r=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,r)=>{let t=n[e][r];return null!=t?t:e},r));return t.useEffect(()=>{let e=l(c.current);d.current="mounted"===u?e:"none"},[u]),(0,a.N)(()=>{let r=c.current,n=o.current;if(n!==e){let t=d.current,s=l(r);e?h("MOUNT"):"none"===s||(null==r?void 0:r.display)==="none"?h("UNMOUNT"):n&&t!==s?h("ANIMATION_OUT"):h("UNMOUNT"),o.current=e}},[e,h]),(0,a.N)(()=>{if(s){var e;let r,n=null!=(e=s.ownerDocument.defaultView)?e:window,t=e=>{let t=l(c.current).includes(e.animationName);if(e.target===s&&t&&(h("ANIMATION_END"),!o.current)){let e=s.style.animationFillMode;s.style.animationFillMode="forwards",r=n.setTimeout(()=>{"forwards"===s.style.animationFillMode&&(s.style.animationFillMode=e)})}},a=e=>{e.target===s&&(d.current=l(c.current))};return s.addEventListener("animationstart",a),s.addEventListener("animationcancel",t),s.addEventListener("animationend",t),()=>{n.clearTimeout(r),s.removeEventListener("animationstart",a),s.removeEventListener("animationcancel",t),s.removeEventListener("animationend",t)}}h("ANIMATION_END")},[s,h]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:t.useCallback(e=>{c.current=e?getComputedStyle(e):null,i(e)},[])}}(r),c="function"==typeof n?n({present:i.isPresent}):t.Children.only(n),o=(0,s.s)(i.ref,function(e){var r,n;let t=null==(r=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:r.get,s=t&&"isReactWarning"in t&&t.isReactWarning;return s?e.ref:(s=(t=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in t&&t.isReactWarning)?e.props.ref:e.props.ref||e.ref}(c));return"function"==typeof n||i.isPresent?t.cloneElement(c,{ref:o}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},9869:(e,r,n)=>{n.d(r,{A:()=>t});let t=(0,n(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])}}]);