"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2825],{2346:(e,s,r)=>{r.d(s,{w:()=>n});var t=r(5155);r(2115);var a=r(7489),i=r(9434);function n(e){let{className:s,orientation:r="horizontal",decorative:n=!0,...l}=e;return(0,t.jsx)(a.b,{"data-slot":"separator",decorative:n,orientation:r,className:(0,i.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",s),...l})}},2825:(e,s,r)=>{r.d(s,{BlogForm:()=>q});var t=r(5155),a=r(2115),i=r(5695),n=r(2177),l=r(221),o=r(690),c=r(285),d=r(2523),h=r(5057),u=r(8539),x=r(7262),j=r(9409),g=r(7759),m=r(6695),p=r(6126),v=r(5109),f=r(3315),b=r(1891),y=r(6761),w=r(6377),k=r(1261),C=r(3872),N=r(9543),A=r(2346),R=r(9727),z=r(9144),T=r(2643),B=r(9621),I=r(8440),S=r(2705),E=r(2406),M=r(5968),$=r(9140),D=r(224),J=r(8164),L=r(7213),Z=r(3654),H=r(8932);function O(e){let{content:s,onChange:r,placeholder:a}=e,i=(0,v.hG)({extensions:[f.A,b.Ay.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),y.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-blue-500 underline"}}),w.xJ,w.Q1,k.A.configure({HTMLAttributes:{class:"list-disc list-inside"}}),C.A.configure({HTMLAttributes:{class:"list-decimal list-inside"}}),N.A],content:s,onUpdate:e=>{let{editor:s}=e;r(s.getHTML())},editorProps:{attributes:{class:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4"}},immediatelyRender:!1});return i?(0,t.jsxs)("div",{className:"border rounded-lg",children:[(0,t.jsxs)("div",{className:"border-b p-2 flex flex-wrap gap-1",children:[(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>i.chain().focus().toggleBold().run(),className:i.isActive("bold")?"bg-muted":"",children:(0,t.jsx)(R.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>i.chain().focus().toggleItalic().run(),className:i.isActive("italic")?"bg-muted":"",children:(0,t.jsx)(z.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>i.chain().focus().toggleStrike().run(),className:i.isActive("strike")?"bg-muted":"",children:(0,t.jsx)(T.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>i.chain().focus().toggleCode().run(),className:i.isActive("code")?"bg-muted":"",children:(0,t.jsx)(B.A,{className:"h-4 w-4"})}),(0,t.jsx)(A.w,{orientation:"vertical",className:"h-8"}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>i.chain().focus().toggleHeading({level:1}).run(),className:i.isActive("heading",{level:1})?"bg-muted":"",children:(0,t.jsx)(I.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>i.chain().focus().toggleHeading({level:2}).run(),className:i.isActive("heading",{level:2})?"bg-muted":"",children:(0,t.jsx)(S.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>i.chain().focus().toggleHeading({level:3}).run(),className:i.isActive("heading",{level:3})?"bg-muted":"",children:(0,t.jsx)(E.A,{className:"h-4 w-4"})}),(0,t.jsx)(A.w,{orientation:"vertical",className:"h-8"}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>i.chain().focus().toggleBulletList().run(),className:i.isActive("bulletList")?"bg-muted":"",children:(0,t.jsx)(M.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>i.chain().focus().toggleOrderedList().run(),className:i.isActive("orderedList")?"bg-muted":"",children:(0,t.jsx)($.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>i.chain().focus().toggleBlockquote().run(),className:i.isActive("blockquote")?"bg-muted":"",children:(0,t.jsx)(D.A,{className:"h-4 w-4"})}),(0,t.jsx)(A.w,{orientation:"vertical",className:"h-8"}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>{let e=window.prompt("Enter URL:");e&&i.chain().focus().setLink({href:e}).run()},children:(0,t.jsx)(J.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>{let e=window.prompt("Enter image URL:");e&&i.chain().focus().setImage({src:e}).run()},children:(0,t.jsx)(L.A,{className:"h-4 w-4"})}),(0,t.jsx)(A.w,{orientation:"vertical",className:"h-8"}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>i.chain().focus().undo().run(),disabled:!i.can().undo(),children:(0,t.jsx)(Z.A,{className:"h-4 w-4"})}),(0,t.jsx)(c.$,{variant:"ghost",size:"sm",onClick:()=>i.chain().focus().redo().run(),disabled:!i.can().redo(),children:(0,t.jsx)(H.A,{className:"h-4 w-4"})})]}),(0,t.jsx)(v.$Z,{editor:i})]}):null}var Y=r(2154),P=r(6671),F=r(4416);let V=o.Ik({title:o.Yj().min(1,"Title is required"),excerpt:o.Yj().optional(),content:o.Yj().min(1,"Content is required"),author:o.Yj().min(1,"Author is required"),featured:o.zM().default(!1),image:o.Yj().optional(),readTime:o.Yj().optional(),status:o.k5(["DRAFT","PUBLISHED","ARCHIVED"]).default("DRAFT"),metaTitle:o.Yj().optional(),metaDescription:o.Yj().optional(),keywords:o.Yj().optional(),categoryId:o.Yj().optional(),tagIds:o.YO(o.Yj()).default([])});function q(e){var s;let{initialData:r,isEditing:o=!1}=e,v=(0,i.useRouter)(),[f,b]=(0,a.useState)([]),[y,w]=(0,a.useState)([]),[k,C]=(0,a.useState)([]),[N,A]=(0,a.useState)(""),[R,z]=(0,a.useState)(!1),T=(0,n.mN)({resolver:(0,l.u)(V),defaultValues:{title:(null==r?void 0:r.title)||"",excerpt:(null==r?void 0:r.excerpt)||"",content:(null==r?void 0:r.content)||"",author:(null==r?void 0:r.author)||"",featured:(null==r?void 0:r.featured)||!1,image:(null==r?void 0:r.image)||"",readTime:(null==r?void 0:r.readTime)||"",status:(null==r?void 0:r.status)||"DRAFT",metaTitle:(null==r?void 0:r.metaTitle)||"",metaDescription:(null==r?void 0:r.metaDescription)||"",keywords:(null==r?void 0:r.keywords)||"",categoryId:(null==r?void 0:r.categoryId)||"none",tagIds:(null==r||null==(s=r.tags)?void 0:s.map(e=>e.tag.id))||[]}});(0,a.useEffect)(()=>{B(),I(),(null==r?void 0:r.tags)&&C(r.tags.map(e=>e.tag.id))},[r]);let B=async()=>{try{let e=await fetch("/api/categories");if(e.ok){let s=await e.json();b(s)}}catch(e){console.error("Error fetching categories:",e)}},I=async()=>{try{let e=await fetch("/api/tags");if(e.ok){let s=await e.json();w(s)}}catch(e){console.error("Error fetching tags:",e)}},S=async()=>{if(N.trim())try{let e=await fetch("/api/tags",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:N.trim()})});if(e.ok){let s=await e.json();w(e=>[...e,s]),C(e=>[...e,s.id]),A(""),P.oR.success("Tag created successfully")}else P.oR.error("Failed to create tag")}catch(e){P.oR.error("Error creating tag")}},E=async e=>{z(!0);try{let s={...e,keywords:e.keywords?e.keywords.split(",").map(e=>e.trim()):[],tagIds:k,categoryId:"none"===e.categoryId?null:e.categoryId},t=o?"/api/blogs/".concat(null==r?void 0:r.id):"/api/blogs",a=await fetch(t,{method:o?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(a.ok)P.oR.success(o?"Blog updated successfully":"Blog created successfully"),v.push("/dashboard/blogs");else{let e=await a.json();P.oR.error(e.error||"Something went wrong")}}catch(e){P.oR.error("Error saving blog")}finally{z(!1)}};return(0,t.jsx)(g.lV,{...T,children:(0,t.jsx)("form",{onSubmit:T.handleSubmit(E),className:"space-y-6",children:(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(m.Zp,{children:[(0,t.jsxs)(m.aR,{children:[(0,t.jsx)(m.ZB,{children:"Basic Information"}),(0,t.jsx)(m.BT,{children:"Enter the basic details for your blog post"})]}),(0,t.jsxs)(m.Wu,{className:"space-y-4",children:[(0,t.jsx)(g.zB,{control:T.control,name:"title",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{children:[(0,t.jsx)(g.lR,{children:"Title"}),(0,t.jsx)(g.MJ,{children:(0,t.jsx)(d.p,{placeholder:"Enter blog title",...s})}),(0,t.jsx)(g.C5,{})]})}}),(0,t.jsx)(g.zB,{control:T.control,name:"excerpt",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{children:[(0,t.jsx)(g.lR,{children:"Excerpt"}),(0,t.jsx)(g.MJ,{children:(0,t.jsx)(u.T,{placeholder:"Brief description of the blog post",...s})}),(0,t.jsx)(g.Rr,{children:"A short summary that will appear in blog listings"}),(0,t.jsx)(g.C5,{})]})}}),(0,t.jsx)(g.zB,{control:T.control,name:"author",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{children:[(0,t.jsx)(g.lR,{children:"Author"}),(0,t.jsx)(g.MJ,{children:(0,t.jsx)(d.p,{placeholder:"Author name",...s})}),(0,t.jsx)(g.C5,{})]})}}),(0,t.jsx)(g.zB,{control:T.control,name:"readTime",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{children:[(0,t.jsx)(g.lR,{children:"Read Time"}),(0,t.jsx)(g.MJ,{children:(0,t.jsx)(d.p,{placeholder:"e.g., 5 min read",...s})}),(0,t.jsx)(g.C5,{})]})}})]})]}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsxs)(m.aR,{children:[(0,t.jsx)(m.ZB,{children:"Content"}),(0,t.jsx)(m.BT,{children:"Write your blog post content using the rich text editor"})]}),(0,t.jsx)(m.Wu,{children:(0,t.jsx)(g.zB,{control:T.control,name:"content",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{children:[(0,t.jsx)(g.MJ,{children:(0,t.jsx)(O,{content:s.value,onChange:s.onChange,placeholder:"Start writing your blog post..."})}),(0,t.jsx)(g.C5,{})]})}})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(m.Zp,{children:[(0,t.jsx)(m.aR,{children:(0,t.jsx)(m.ZB,{children:"Publishing"})}),(0,t.jsxs)(m.Wu,{className:"space-y-4",children:[(0,t.jsx)(g.zB,{control:T.control,name:"status",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{children:[(0,t.jsx)(g.lR,{children:"Status"}),(0,t.jsxs)(j.l6,{onValueChange:s.onChange,defaultValue:s.value,children:[(0,t.jsx)(g.MJ,{children:(0,t.jsx)(j.bq,{children:(0,t.jsx)(j.yv,{placeholder:"Select status"})})}),(0,t.jsxs)(j.gC,{children:[(0,t.jsx)(j.eb,{value:"DRAFT",children:"Draft"}),(0,t.jsx)(j.eb,{value:"PUBLISHED",children:"Published"}),(0,t.jsx)(j.eb,{value:"ARCHIVED",children:"Archived"})]})]}),(0,t.jsx)(g.C5,{})]})}}),(0,t.jsx)(g.zB,{control:T.control,name:"featured",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{className:"flex flex-row items-start space-x-3 space-y-0",children:[(0,t.jsx)(g.MJ,{children:(0,t.jsx)(x.S,{checked:s.value,onCheckedChange:s.onChange})}),(0,t.jsxs)("div",{className:"space-y-1 leading-none",children:[(0,t.jsx)(g.lR,{children:"Featured Post"}),(0,t.jsx)(g.Rr,{children:"Mark this post as featured"})]})]})}}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(c.$,{type:"submit",disabled:R,children:R?"Saving...":o?"Update":"Create"}),(0,t.jsx)(c.$,{type:"button",variant:"outline",onClick:()=>v.back(),children:"Cancel"})]})]})]}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsx)(m.aR,{children:(0,t.jsx)(m.ZB,{children:"Featured Image"})}),(0,t.jsx)(m.Wu,{children:(0,t.jsx)(g.zB,{control:T.control,name:"image",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{children:[(0,t.jsx)(g.MJ,{children:(0,t.jsx)(Y.B,{value:s.value,onChange:s.onChange,onRemove:()=>s.onChange(""),folder:"blogs"})}),(0,t.jsx)(g.C5,{})]})}})})]}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsx)(m.aR,{children:(0,t.jsx)(m.ZB,{children:"Category & Tags"})}),(0,t.jsxs)(m.Wu,{className:"space-y-4",children:[(0,t.jsx)(g.zB,{control:T.control,name:"categoryId",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{children:[(0,t.jsx)(g.lR,{children:"Category"}),(0,t.jsxs)(j.l6,{onValueChange:s.onChange,defaultValue:s.value,children:[(0,t.jsx)(g.MJ,{children:(0,t.jsx)(j.bq,{children:(0,t.jsx)(j.yv,{placeholder:"Select category"})})}),(0,t.jsxs)(j.gC,{children:[(0,t.jsx)(j.eb,{value:"none",children:"No category"}),f.map(e=>(0,t.jsx)(j.eb,{value:e.id,children:e.name},e.id))]})]}),(0,t.jsx)(g.C5,{})]})}}),(0,t.jsxs)("div",{children:[(0,t.jsx)(h.J,{children:"Tags"}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(d.p,{placeholder:"Add new tag",value:N,onChange:e=>A(e.target.value),onKeyPress:e=>"Enter"===e.key&&(e.preventDefault(),S())}),(0,t.jsx)(c.$,{type:"button",onClick:S,size:"sm",children:"Add"})]}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:y.map(e=>(0,t.jsxs)(p.E,{variant:k.includes(e.id)?"default":"outline",className:"cursor-pointer",onClick:()=>{var s;return s=e.id,void C(e=>e.includes(s)?e.filter(e=>e!==s):[...e,s])},children:[e.name,k.includes(e.id)&&(0,t.jsx)(F.A,{className:"h-3 w-3 ml-1"})]},e.id))})]})]})]})]}),(0,t.jsxs)(m.Zp,{children:[(0,t.jsxs)(m.aR,{children:[(0,t.jsx)(m.ZB,{children:"SEO Settings"}),(0,t.jsx)(m.BT,{children:"Optimize your post for search engines"})]}),(0,t.jsxs)(m.Wu,{className:"space-y-4",children:[(0,t.jsx)(g.zB,{control:T.control,name:"metaTitle",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{children:[(0,t.jsx)(g.lR,{children:"Meta Title"}),(0,t.jsx)(g.MJ,{children:(0,t.jsx)(d.p,{placeholder:"SEO title",...s})}),(0,t.jsx)(g.Rr,{children:"Leave empty to use the post title"}),(0,t.jsx)(g.C5,{})]})}}),(0,t.jsx)(g.zB,{control:T.control,name:"metaDescription",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{children:[(0,t.jsx)(g.lR,{children:"Meta Description"}),(0,t.jsx)(g.MJ,{children:(0,t.jsx)(u.T,{placeholder:"SEO description",...s})}),(0,t.jsx)(g.Rr,{children:"Brief description for search engines"}),(0,t.jsx)(g.C5,{})]})}}),(0,t.jsx)(g.zB,{control:T.control,name:"keywords",render:e=>{let{field:s}=e;return(0,t.jsxs)(g.eI,{children:[(0,t.jsx)(g.lR,{children:"Keywords"}),(0,t.jsx)(g.MJ,{children:(0,t.jsx)(d.p,{placeholder:"keyword1, keyword2, keyword3",...s})}),(0,t.jsx)(g.Rr,{children:"Comma-separated keywords for SEO"}),(0,t.jsx)(g.C5,{})]})}})]})]})]})]})})})}},6126:(e,s,r)=>{r.d(s,{E:()=>o});var t=r(5155);r(2115);var a=r(9708),i=r(2085),n=r(9434);let l=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function o(e){let{className:s,variant:r,asChild:i=!1,...o}=e,c=i?a.DX:"span";return(0,t.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(l({variant:r}),s),...o})}},7262:(e,s,r)=>{r.d(s,{S:()=>l});var t=r(5155);r(2115);var a=r(6981),i=r(5196),n=r(9434);function l(e){let{className:s,...r}=e;return(0,t.jsx)(a.bL,{"data-slot":"checkbox",className:(0,n.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",s),...r,children:(0,t.jsx)(a.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,t.jsx)(i.A,{className:"size-3.5"})})})}}}]);