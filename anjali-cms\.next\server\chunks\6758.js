"use strict";exports.id=6758,exports.ids=[6758],exports.modules={4101:(a,b,c)=>{c.d(b,{GalleryForm:()=>v});var d=c(60687),e=c(43210),f=c(16189),g=c(27605),h=c(63442),i=c(37566),j=c(29523),k=c(89667),l=c(34729),m=c(56896),n=c(15079),o=c(71669),p=c(44493),q=c(19312),r=c(52581),s=c(11860),t=c(96474);let u=i.Ik({title:i.Yj().min(1,"Title is required"),description:i.Yj().optional(),image:i.Yj().min(1,"Image is required"),category:i.Yj().optional(),tags:i.YO(i.Ik({value:i.Yj().min(1,"Tag cannot be empty")})).default([]),featured:i.zM().default(!1),status:i.k5(["ACTIVE","INACTIVE","ARCHIVED"]).default("ACTIVE")});function v({initialData:a,isEditing:b=!1}){let c=(0,f.useRouter)(),[i,v]=(0,e.useState)(!1),w=(0,g.mN)({resolver:(0,h.u)(u),defaultValues:{title:a?.title||"",description:a?.description||"",image:a?.image||"",category:a?.category||"",tags:a?.tags?.map(a=>({value:a}))||[],featured:a?.featured||!1,status:a?.status||"ACTIVE"}}),{fields:x,append:y,remove:z}=(0,g.jz)({control:w.control,name:"tags"}),A=async d=>{v(!0);try{let e={...d,tags:d.tags.map(a=>a.value).filter(a=>""!==a.trim())},f=b?`/api/gallery/${a?.id}`:"/api/gallery",g=await fetch(f,{method:b?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(g.ok)r.oR.success(b?"Gallery item updated successfully":"Gallery item created successfully"),c.push("/dashboard/gallery");else{let a=await g.json();r.oR.error(a.error||"Something went wrong")}}catch(a){r.oR.error("Error saving gallery item")}finally{v(!1)}};return(0,d.jsx)(o.lV,{...w,children:(0,d.jsx)("form",{onSubmit:w.handleSubmit(A),className:"space-y-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,d.jsxs)(p.Zp,{children:[(0,d.jsxs)(p.aR,{children:[(0,d.jsx)(p.ZB,{children:"Image Upload"}),(0,d.jsx)(p.BT,{children:"Upload the main image for your gallery"})]}),(0,d.jsx)(p.Wu,{children:(0,d.jsx)(o.zB,{control:w.control,name:"image",render:({field:a})=>(0,d.jsxs)(o.eI,{children:[(0,d.jsx)(o.MJ,{children:(0,d.jsx)(q.B,{value:a.value,onChange:a.onChange,onRemove:()=>a.onChange(""),folder:"gallery"})}),(0,d.jsx)(o.C5,{})]})})})]}),(0,d.jsxs)(p.Zp,{children:[(0,d.jsxs)(p.aR,{children:[(0,d.jsx)(p.ZB,{children:"Image Details"}),(0,d.jsx)(p.BT,{children:"Add details and metadata for your image"})]}),(0,d.jsxs)(p.Wu,{className:"space-y-4",children:[(0,d.jsx)(o.zB,{control:w.control,name:"title",render:({field:a})=>(0,d.jsxs)(o.eI,{children:[(0,d.jsx)(o.lR,{children:"Title"}),(0,d.jsx)(o.MJ,{children:(0,d.jsx)(k.p,{placeholder:"Enter image title",...a})}),(0,d.jsx)(o.C5,{})]})}),(0,d.jsx)(o.zB,{control:w.control,name:"description",render:({field:a})=>(0,d.jsxs)(o.eI,{children:[(0,d.jsx)(o.lR,{children:"Description (Optional)"}),(0,d.jsx)(o.MJ,{children:(0,d.jsx)(l.T,{placeholder:"Describe this image or the work shown",className:"min-h-[100px]",...a})}),(0,d.jsx)(o.Rr,{children:"Add context about the makeup look, occasion, or techniques used"}),(0,d.jsx)(o.C5,{})]})}),(0,d.jsx)(o.zB,{control:w.control,name:"category",render:({field:a})=>(0,d.jsxs)(o.eI,{children:[(0,d.jsx)(o.lR,{children:"Category (Optional)"}),(0,d.jsx)(o.MJ,{children:(0,d.jsx)(k.p,{placeholder:"e.g., Bridal, Party, Traditional, Editorial",...a})}),(0,d.jsx)(o.Rr,{children:"Categorize this image for better organization"}),(0,d.jsx)(o.C5,{})]})})]})]}),(0,d.jsxs)(p.Zp,{children:[(0,d.jsxs)(p.aR,{children:[(0,d.jsx)(p.ZB,{children:"Tags"}),(0,d.jsx)(p.BT,{children:"Add tags to help categorize and search for this image"})]}),(0,d.jsxs)(p.Wu,{className:"space-y-4",children:[x.map((a,b)=>(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(o.zB,{control:w.control,name:`tags.${b}.value`,render:({field:a})=>(0,d.jsxs)(o.eI,{className:"flex-1",children:[(0,d.jsx)(o.MJ,{children:(0,d.jsx)(k.p,{placeholder:"e.g., smokey eyes, natural look, bold lips",...a})}),(0,d.jsx)(o.C5,{})]})}),(0,d.jsx)(j.$,{type:"button",variant:"outline",size:"sm",onClick:()=>z(b),children:(0,d.jsx)(s.A,{className:"h-4 w-4"})})]},a.id)),(0,d.jsxs)(j.$,{type:"button",variant:"outline",size:"sm",onClick:()=>y({value:""}),children:[(0,d.jsx)(t.A,{className:"h-4 w-4 mr-2"}),"Add Tag"]}),0===x.length&&(0,d.jsx)("div",{className:"text-sm text-muted-foreground",children:'No tags added yet. Click "Add Tag" to start adding tags.'})]})]})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)(p.Zp,{children:[(0,d.jsxs)(p.aR,{children:[(0,d.jsx)(p.ZB,{children:"Settings"}),(0,d.jsx)(p.BT,{children:"Configure visibility and status settings"})]}),(0,d.jsxs)(p.Wu,{className:"space-y-4",children:[(0,d.jsx)(o.zB,{control:w.control,name:"status",render:({field:a})=>(0,d.jsxs)(o.eI,{children:[(0,d.jsx)(o.lR,{children:"Status"}),(0,d.jsxs)(n.l6,{onValueChange:a.onChange,defaultValue:a.value,children:[(0,d.jsx)(o.MJ,{children:(0,d.jsx)(n.bq,{children:(0,d.jsx)(n.yv,{placeholder:"Select status"})})}),(0,d.jsxs)(n.gC,{children:[(0,d.jsx)(n.eb,{value:"ACTIVE",children:"Active"}),(0,d.jsx)(n.eb,{value:"INACTIVE",children:"Inactive"}),(0,d.jsx)(n.eb,{value:"ARCHIVED",children:"Archived"})]})]}),(0,d.jsx)(o.Rr,{children:"Only active images will be visible in the public gallery"}),(0,d.jsx)(o.C5,{})]})}),(0,d.jsx)(o.zB,{control:w.control,name:"featured",render:({field:a})=>(0,d.jsxs)(o.eI,{className:"flex flex-row items-start space-x-3 space-y-0",children:[(0,d.jsx)(o.MJ,{children:(0,d.jsx)(m.S,{checked:a.value,onCheckedChange:a.onChange})}),(0,d.jsxs)("div",{className:"space-y-1 leading-none",children:[(0,d.jsx)(o.lR,{children:"Featured Image"}),(0,d.jsx)(o.Rr,{children:"Mark this image as featured to highlight it in your portfolio"})]})]})}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(j.$,{type:"submit",disabled:i,children:i?"Saving...":b?"Update":"Add to Gallery"}),(0,d.jsx)(j.$,{type:"button",variant:"outline",onClick:()=>c.back(),children:"Cancel"})]})]})]}),(0,d.jsxs)(p.Zp,{children:[(0,d.jsx)(p.aR,{children:(0,d.jsx)(p.ZB,{children:"Tips"})}),(0,d.jsxs)(p.Wu,{className:"space-y-2 text-sm text-muted-foreground",children:[(0,d.jsx)("p",{children:"• Use high-quality images for the best showcase"}),(0,d.jsx)("p",{children:"• Add descriptive titles and tags for better organization"}),(0,d.jsx)("p",{children:"• Featured images appear prominently in your portfolio"}),(0,d.jsx)("p",{children:"• Categories help visitors find specific types of work"}),(0,d.jsx)("p",{children:"• Tags make images searchable and discoverable"})]})]})]})]})})})}},16023:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},40211:(a,b,c)=>{c.d(b,{C1:()=>x,bL:()=>v});var d=c(43210),e=c(98599),f=c(11273),g=c(70569),h=c(65551),i=c(83721),j=c(18853),k=c(46059),l=c(14163),m=c(60687),n="Checkbox",[o,p]=(0,f.A)(n),[q,r]=o(n);function s(a){let{__scopeCheckbox:b,checked:c,children:e,defaultChecked:f,disabled:g,form:i,name:j,onCheckedChange:k,required:l,value:o="on",internal_do_not_use_render:p}=a,[r,s]=(0,h.i)({prop:c,defaultProp:f??!1,onChange:k,caller:n}),[t,u]=d.useState(null),[v,w]=d.useState(null),x=d.useRef(!1),y=!t||!!i||!!t.closest("form"),z={checked:r,disabled:g,setChecked:s,control:t,setControl:u,name:j,form:i,value:o,hasConsumerStoppedPropagationRef:x,required:l,defaultChecked:!A(f)&&f,isFormControl:y,bubbleInput:v,setBubbleInput:w};return(0,m.jsx)(q,{scope:b,...z,children:"function"==typeof p?p(z):e})}var t="CheckboxTrigger",u=d.forwardRef(({__scopeCheckbox:a,onKeyDown:b,onClick:c,...f},h)=>{let{control:i,value:j,disabled:k,checked:n,required:o,setControl:p,setChecked:q,hasConsumerStoppedPropagationRef:s,isFormControl:u,bubbleInput:v}=r(t,a),w=(0,e.s)(h,p),x=d.useRef(n);return d.useEffect(()=>{let a=i?.form;if(a){let b=()=>q(x.current);return a.addEventListener("reset",b),()=>a.removeEventListener("reset",b)}},[i,q]),(0,m.jsx)(l.sG.button,{type:"button",role:"checkbox","aria-checked":A(n)?"mixed":n,"aria-required":o,"data-state":B(n),"data-disabled":k?"":void 0,disabled:k,value:j,...f,ref:w,onKeyDown:(0,g.m)(b,a=>{"Enter"===a.key&&a.preventDefault()}),onClick:(0,g.m)(c,a=>{q(a=>!!A(a)||!a),v&&u&&(s.current=a.isPropagationStopped(),s.current||a.stopPropagation())})})});u.displayName=t;var v=d.forwardRef((a,b)=>{let{__scopeCheckbox:c,name:d,checked:e,defaultChecked:f,required:g,disabled:h,value:i,onCheckedChange:j,form:k,...l}=a;return(0,m.jsx)(s,{__scopeCheckbox:c,checked:e,defaultChecked:f,disabled:h,required:g,onCheckedChange:j,name:d,form:k,value:i,internal_do_not_use_render:({isFormControl:a})=>(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(u,{...l,ref:b,__scopeCheckbox:c}),a&&(0,m.jsx)(z,{__scopeCheckbox:c})]})})});v.displayName=n;var w="CheckboxIndicator",x=d.forwardRef((a,b)=>{let{__scopeCheckbox:c,forceMount:d,...e}=a,f=r(w,c);return(0,m.jsx)(k.C,{present:d||A(f.checked)||!0===f.checked,children:(0,m.jsx)(l.sG.span,{"data-state":B(f.checked),"data-disabled":f.disabled?"":void 0,...e,ref:b,style:{pointerEvents:"none",...a.style}})})});x.displayName=w;var y="CheckboxBubbleInput",z=d.forwardRef(({__scopeCheckbox:a,...b},c)=>{let{control:f,hasConsumerStoppedPropagationRef:g,checked:h,defaultChecked:k,required:n,disabled:o,name:p,value:q,form:s,bubbleInput:t,setBubbleInput:u}=r(y,a),v=(0,e.s)(c,u),w=(0,i.Z)(h),x=(0,j.X)(f);d.useEffect(()=>{if(!t)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,b=!g.current;if(w!==h&&a){let c=new Event("click",{bubbles:b});t.indeterminate=A(h),a.call(t,!A(h)&&h),t.dispatchEvent(c)}},[t,w,h,g]);let z=d.useRef(!A(h)&&h);return(0,m.jsx)(l.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:k??z.current,required:n,disabled:o,name:p,value:q,form:s,...b,tabIndex:-1,ref:v,style:{...b.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function A(a){return"indeterminate"===a}function B(a){return A(a)?"indeterminate":a?"checked":"unchecked"}z.displayName=y},41862:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},56896:(a,b,c)=>{c.d(b,{S:()=>h});var d=c(60687);c(43210);var e=c(40211),f=c(13964),g=c(4780);function h({className:a,...b}){return(0,d.jsx)(e.bL,{"data-slot":"checkbox",className:(0,g.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...b,children:(0,d.jsx)(e.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,d.jsx)(f.A,{className:"size-3.5"})})})}},96474:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])}};