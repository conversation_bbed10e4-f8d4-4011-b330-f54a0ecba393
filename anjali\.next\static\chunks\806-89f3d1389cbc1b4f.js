"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[806],{3986:(e,t,a)=>{a.d(t,{f0:()=>f,Kh:()=>y,LC:()=>m,Kz:()=>d,R0:()=>u,_K:()=>x,Uz:()=>b,iX:()=>k,XM:()=>h,Sq:()=>p,zZ:()=>v});var l=a(2960);class s extends Error{constructor(e,t,a){super(e),this.status=t,this.data=a,this.name="ApiError"}}async function n(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a="".concat("http://localhost:3001/api").concat(e),l={headers:{"Content-Type":"application/json",...t.headers},...t};try{let e=await fetch(a,l);if(!e.ok){let t;try{t=await e.json()}catch(a){t={message:e.statusText}}throw new s(t.error||t.message||"An error occurred",e.status,t)}if(204===e.status)return{};return await e.json()}catch(e){if(e instanceof s)throw e;throw new s("Network error or server unavailable",0,{originalError:e})}}let i={get:e=>n(e),post:(e,t)=>n(e,{method:"POST",body:t?JSON.stringify(t):void 0})};function r(e){let t=new URLSearchParams;Object.entries(e).forEach(e=>{let[a,l]=e;null!=l&&""!==l&&t.append(a,String(l))});let a=t.toString();return a?"?".concat(a):""}let o={getSettings:async()=>i.get("/settings"),getSetting:async e=>i.get("/settings/".concat(e)),getSettingValue:async e=>(await i.get("/settings/".concat(e))).value,getSiteInfo:async()=>{var e,t,a,l,s,n;let i=(null==(e=(await o.getSettings()).grouped)?void 0:e.site)||[];return{name:(null==(t=i.find(e=>"site.name"===e.key))?void 0:t.value)||"Anjali Makeup Artist",tagline:(null==(a=i.find(e=>"site.tagline"===e.key))?void 0:a.value)||"Enhancing Natural Beauty",description:(null==(l=i.find(e=>"site.description"===e.key))?void 0:l.value)||"",logo:null==(s=i.find(e=>"site.logo"===e.key))?void 0:s.value,favicon:null==(n=i.find(e=>"site.favicon"===e.key))?void 0:n.value}},getContactInfo:async()=>{var e,t,a,l,s,n,i,r;let c=(null==(e=(await o.getSettings()).grouped)?void 0:e.contact)||[];return{email:null==(t=c.find(e=>"contact.email"===e.key))?void 0:t.value,phone:null==(a=c.find(e=>"contact.phone"===e.key))?void 0:a.value,address:null==(l=c.find(e=>"contact.address"===e.key))?void 0:l.value,city:null==(s=c.find(e=>"contact.city"===e.key))?void 0:s.value,state:null==(n=c.find(e=>"contact.state"===e.key))?void 0:n.value,zipcode:null==(i=c.find(e=>"contact.zipcode"===e.key))?void 0:i.value,country:null==(r=c.find(e=>"contact.country"===e.key))?void 0:r.value}},getSocialMedia:async()=>{var e,t,a,l,s,n,i;let r=(null==(e=(await o.getSettings()).grouped)?void 0:e.social)||[];return{facebook:null==(t=r.find(e=>"social.facebook"===e.key))?void 0:t.value,instagram:null==(a=r.find(e=>"social.instagram"===e.key))?void 0:a.value,twitter:null==(l=r.find(e=>"social.twitter"===e.key))?void 0:l.value,youtube:null==(s=r.find(e=>"social.youtube"===e.key))?void 0:s.value,linkedin:null==(n=r.find(e=>"social.linkedin"===e.key))?void 0:n.value,tiktok:null==(i=r.find(e=>"social.tiktok"===e.key))?void 0:i.value}},getSEODefaults:async()=>{var e,t,a,l,s;let n=(null==(e=(await o.getSettings()).grouped)?void 0:e.seo)||[];return{metaTitle:null==(t=n.find(e=>"seo.meta_title"===e.key))?void 0:t.value,metaDescription:null==(a=n.find(e=>"seo.meta_description"===e.key))?void 0:a.value,keywords:null==(l=n.find(e=>"seo.keywords"===e.key))?void 0:l.value,ogImage:null==(s=n.find(e=>"seo.og_image"===e.key))?void 0:s.value}},getBusinessInfo:async()=>{var e,t,a,l,s;let n=(null==(e=(await o.getSettings()).grouped)?void 0:e.business)||[];return{hours:null==(t=n.find(e=>"business.hours"===e.key))?void 0:t.value,servicesIntro:null==(a=n.find(e=>"business.services_intro"===e.key))?void 0:a.value,aboutText:null==(l=n.find(e=>"business.about_text"===e.key))?void 0:l.value,bookingUrl:null==(s=n.find(e=>"business.booking_url"===e.key))?void 0:s.value}}},c={blogs:{getBlogs:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=r(e);return i.get("/blogs".concat(t))},getBlog:async e=>i.get("/blogs/".concat(e)),getBlogBySlug:async e=>i.get("/blogs/slug/".concat(e)),getFeaturedBlogs:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3;return i.get("/blogs?featured=true&limit=".concat(e,"&status=PUBLISHED"))},getRecentBlogs:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;return i.get("/blogs?limit=".concat(e,"&status=PUBLISHED"))}},services:{getServices:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=r(e);return i.get("/services".concat(t))},getService:async e=>i.get("/services/".concat(e)),getServiceBySlug:async e=>i.get("/services/slug/".concat(e)),getActiveServices:async()=>i.get("/services?status=ACTIVE"),getPopularServices:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6;return i.get("/services?popular=true&status=ACTIVE&limit=".concat(e))},getServicesByCategory:async e=>i.get("/services?category=".concat(encodeURIComponent(e),"&status=ACTIVE"))},packages:{getPackages:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=r(e);return i.get("/packages".concat(t))},getPackage:async e=>i.get("/packages/".concat(e)),getPackageBySlug:async e=>i.get("/packages/slug/".concat(e)),getActivePackages:async()=>i.get("/packages?status=ACTIVE"),getPopularPackages:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3;return i.get("/packages?popular=true&status=ACTIVE&limit=".concat(e))},getFeaturedPackages:async()=>i.get("/packages?popular=true&status=ACTIVE")},testimonials:{getTestimonials:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=r(e);return i.get("/testimonials".concat(t))},getTestimonial:async e=>i.get("/testimonials/".concat(e)),getApprovedTestimonials:async e=>{let t=r({status:"APPROVED",...e&&{limit:e}});return i.get("/testimonials".concat(t))},getTestimonialsByService:async e=>i.get("/testimonials?service=".concat(encodeURIComponent(e),"&status=APPROVED")),createTestimonial:async e=>i.post("/testimonials",e)},gallery:{getGallery:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=r(e);return i.get("/gallery".concat(t))},getGalleryItem:async e=>i.get("/gallery/".concat(e)),getActiveGallery:async e=>{let t=r({status:"ACTIVE",...e&&{limit:e}});return i.get("/gallery".concat(t))},getFeaturedGallery:async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:12;return i.get("/gallery?featured=true&status=ACTIVE&limit=".concat(e))},getGalleryByCategory:async e=>i.get("/gallery?category=".concat(encodeURIComponent(e),"&status=ACTIVE"))},settings:o},g={blogs:{all:["blogs"],lists:()=>[...g.blogs.all,"list"],list:e=>[...g.blogs.lists(),e],details:()=>[...g.blogs.all,"detail"],detail:e=>[...g.blogs.details(),e],slug:e=>[...g.blogs.all,"slug",e],featured:e=>[...g.blogs.all,"featured",e],recent:e=>[...g.blogs.all,"recent",e]},services:{all:["services"],lists:()=>[...g.services.all,"list"],list:e=>[...g.services.lists(),e],details:()=>[...g.services.all,"detail"],detail:e=>[...g.services.details(),e],slug:e=>[...g.services.all,"slug",e],active:()=>[...g.services.all,"active"],popular:e=>[...g.services.all,"popular",e],category:e=>[...g.services.all,"category",e]},packages:{all:["packages"],lists:()=>[...g.packages.all,"list"],list:e=>[...g.packages.lists(),e],details:()=>[...g.packages.all,"detail"],detail:e=>[...g.packages.details(),e],slug:e=>[...g.packages.all,"slug",e],active:()=>[...g.packages.all,"active"],popular:e=>[...g.packages.all,"popular",e],featured:()=>[...g.packages.all,"featured"]},testimonials:{all:["testimonials"],lists:()=>[...g.testimonials.all,"list"],list:e=>[...g.testimonials.lists(),e],details:()=>[...g.testimonials.all,"detail"],detail:e=>[...g.testimonials.details(),e],approved:e=>[...g.testimonials.all,"approved",e],service:e=>[...g.testimonials.all,"service",e]},gallery:{all:["gallery"],lists:()=>[...g.gallery.all,"list"],list:e=>[...g.gallery.lists(),e],details:()=>[...g.gallery.all,"detail"],detail:e=>[...g.gallery.details(),e],active:e=>[...g.gallery.all,"active",e],featured:e=>[...g.gallery.all,"featured",e],category:e=>[...g.gallery.all,"category",e]},settings:{all:["settings"],detail:e=>[...g.settings.all,e],siteInfo:()=>[...g.settings.all,"siteInfo"],contactInfo:()=>[...g.settings.all,"contactInfo"],socialMedia:()=>[...g.settings.all,"socialMedia"],seoDefaults:()=>[...g.settings.all,"seoDefaults"],businessInfo:()=>[...g.settings.all,"businessInfo"]}},u=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,l.I)({queryKey:g.blogs.list(e),queryFn:()=>c.blogs.getBlogs(e),staleTime:3e5})},d=e=>(0,l.I)({queryKey:g.blogs.slug(e),queryFn:()=>c.blogs.getBlogBySlug(e),enabled:!!e}),y=()=>(0,l.I)({queryKey:g.services.active(),queryFn:()=>c.services.getActiveServices(),staleTime:9e5}),v=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:6;return(0,l.I)({queryKey:g.services.popular(e),queryFn:()=>c.services.getPopularServices(e),staleTime:9e5})},f=()=>(0,l.I)({queryKey:g.packages.active(),queryFn:()=>c.packages.getActivePackages(),staleTime:9e5}),p=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:3;return(0,l.I)({queryKey:g.packages.popular(e),queryFn:()=>c.packages.getPopularPackages(e),staleTime:9e5})},m=e=>(0,l.I)({queryKey:g.testimonials.approved(e),queryFn:()=>c.testimonials.getApprovedTestimonials(e),staleTime:6e5}),k=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};return(0,l.I)({queryKey:g.gallery.list(e),queryFn:()=>c.gallery.getGallery(e),staleTime:6e5})},b=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:12;return(0,l.I)({queryKey:g.gallery.featured(e),queryFn:()=>c.gallery.getFeaturedGallery(e),staleTime:9e5})},h=e=>(0,l.I)({queryKey:g.gallery.category(e),queryFn:()=>c.gallery.getGalleryByCategory(e),enabled:!!e,staleTime:6e5}),x=()=>(0,l.I)({queryKey:g.settings.contactInfo(),queryFn:()=>c.settings.getContactInfo(),staleTime:36e5})},4090:(e,t,a)=>{a.r(t),a.d(t,{Section:()=>n,SectionHeader:()=>i});var l=a(5155),s=a(9434);function n(e){let{children:t,className:a,id:n,background:i="default"}=e;return(0,l.jsx)("section",{id:n,className:(0,s.cn)("py-16 md:py-24",{default:"bg-white",cream:"bg-cream","soft-gray":"bg-soft-gray",gradient:"bg-gradient-to-br from-cream to-soft-gray"}[i],a),children:(0,l.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:t})})}function i(e){let{title:t,subtitle:a,description:n,centered:i=!0,className:r}=e;return(0,l.jsxs)("div",{className:(0,s.cn)("mb-12 md:mb-16",i&&"text-center",r),children:[a&&(0,l.jsx)("p",{className:"text-rose-gold-dark font-medium text-sm uppercase tracking-wide mb-2",children:a}),(0,l.jsx)("h2",{className:"font-display text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary mb-4",children:t}),n&&(0,l.jsx)("p",{className:"text-text-secondary text-lg max-w-3xl mx-auto leading-relaxed",children:n})]})}},6126:(e,t,a)=>{a.d(t,{Badge:()=>r});var l=a(5155);a(2115);var s=a(2085),n=a(9434);let i=(0,s.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-rose-gold text-white shadow hover:bg-rose-gold-dark",secondary:"border-transparent bg-blush-pink text-text-primary hover:bg-blush-pink-dark",destructive:"border-transparent bg-red-500 text-white shadow hover:bg-red-600",outline:"text-text-primary border-gray-300",success:"border-transparent bg-green-500 text-white shadow hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white shadow hover:bg-yellow-600",lavender:"border-transparent bg-lavender text-text-primary hover:bg-lavender-dark"}},defaultVariants:{variant:"default"}});function r(e){let{className:t,variant:a,...s}=e;return(0,l.jsx)("div",{className:(0,n.cn)(i({variant:a}),t),...s})}},6695:(e,t,a)=>{a.d(t,{BT:()=>c,Card:()=>i,CardContent:()=>g,ZB:()=>o,aR:()=>r});var l=a(5155),s=a(2115),n=a(9434);let i=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)("div",{ref:t,className:(0,n.cn)("rounded-xl border border-gray-200 bg-white text-text-primary shadow-sm transition-shadow hover:shadow-md",a),...s})});i.displayName="Card";let r=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",a),...s})});r.displayName="CardHeader";let o=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)("h3",{ref:t,className:(0,n.cn)("font-display text-2xl font-semibold leading-none tracking-tight",a),...s})});o.displayName="CardTitle";let c=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-text-secondary",a),...s})});c.displayName="CardDescription";let g=s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",a),...s})});g.displayName="CardContent",s.forwardRef((e,t)=>{let{className:a,...s}=e;return(0,l.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",a),...s})}).displayName="CardFooter"}}]);