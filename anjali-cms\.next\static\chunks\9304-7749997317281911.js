"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9304],{285:(e,t,a)=>{a.d(t,{$:()=>l});var r=a(5155);a(2115);var s=a(9708),n=a(2085),i=a(9434);let o=(0,n.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:a,size:n,asChild:l=!1,...d}=e,c=l?s.DX:"button";return(0,r.jsx)(c,{"data-slot":"button",className:(0,i.cn)(o({variant:a,size:n,className:t})),...d})}},2154:(e,t,a)=>{a.d(t,{B:()=>u});var r=a(5155),s=a(2115),n=a(285),i=a(5057),o=a(4416),l=a(1154),d=a(9869),c=a(6766);function u(e){let{value:t,onChange:a,onRemove:u,disabled:m,folder:p="anjali-cms",label:g="Image"}=e,[f,x]=(0,s.useState)(!1),[v,h]=(0,s.useState)(0),[b,j]=(0,s.useState)(null),y=(0,s.useRef)(null),w="groceease",N=async e=>{if(!w)throw Error("Cloudinary cloud name not configured");let t=await fetch("/api/cloudinary-signature",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({folder:p})});if(!t.ok)throw Error("Failed to get upload signature");let a=await t.json();console.log("Signature data received:",a);let r=new FormData;for(let[t,s]of(r.append("file",e),r.append("signature",a.signature),r.append("timestamp",a.timestamp.toString()),r.append("api_key",a.api_key),r.append("folder",p),console.log("Upload parameters:"),r.entries()))"file"!==t&&console.log("".concat(t,": ").concat(s));let s=await fetch("https://api.cloudinary.com/v1_1/".concat(w,"/image/upload"),{method:"POST",body:r});if(!s.ok){var n;throw Error((null==(n=(await s.json()).error)?void 0:n.message)||"Upload failed")}return s.json()},k=async e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(r){if(!["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(r.type))return void j("Please select a valid image file (JPG, PNG, GIF, WebP)");if(r.size>5242880)return void j("File size must be less than 5MB");x(!0),j(null),h(0);try{let e=setInterval(()=>{h(e=>Math.min(e+10,90))},200),t=await N(r);clearInterval(e),h(100),setTimeout(()=>{a(t.secure_url),x(!1),h(0)},500)}catch(e){x(!1),h(0),j(e instanceof Error?e.message:"Upload failed"),console.error("Upload error:",e)}y.current&&(y.current.value="")}};return(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsx)(i.J,{children:g}),t&&(0,r.jsxs)("div",{className:"relative w-32 h-32 rounded-lg overflow-hidden border",children:[(0,r.jsx)(c.default,{src:t,alt:"Uploaded image",fill:!0,className:"object-cover"}),(0,r.jsx)("button",{type:"button",onClick:()=>{u?u():a(""),j(null)},className:"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors",disabled:m||f,children:(0,r.jsx)(o.A,{className:"w-4 h-4"})})]}),(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsx)("input",{ref:y,type:"file",accept:"image/*",onChange:k,disabled:m||f,className:"hidden"}),(0,r.jsx)(n.$,{type:"button",variant:"outline",onClick:()=>{var e;return null==(e=y.current)?void 0:e.click()},disabled:m||f,className:"w-full",children:f?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(l.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Uploading... ",v,"%"]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(d.A,{className:"w-4 h-4 mr-2"}),t?"Change Image":"Upload Image"]})}),f&&(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:"".concat(v,"%")}})}),b&&(0,r.jsx)("p",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:b})]}),(0,r.jsxs)("div",{className:"text-sm text-gray-500 space-y-1",children:[(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Supported formats:"})," JPG, PNG, GIF, WebP"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Max file size:"})," 5MB"]}),(0,r.jsxs)("p",{children:[(0,r.jsx)("strong",{children:"Upload method:"})," Signed upload (no preset required)"]}),!w&&(0,r.jsx)("p",{className:"text-red-600",children:"⚠️ Cloudinary not configured. Please set up environment variables."})]})]})}},2523:(e,t,a)=>{a.d(t,{p:()=>n});var r=a(5155);a(2115);var s=a(9434);function n(e){let{className:t,type:a,...n}=e;return(0,r.jsx)("input",{type:a,"data-slot":"input",className:(0,s.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}},5057:(e,t,a)=>{a.d(t,{J:()=>i});var r=a(5155);a(2115);var s=a(968),n=a(9434);function i(e){let{className:t,...a}=e;return(0,r.jsx)(s.b,{"data-slot":"label",className:(0,n.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",t),...a})}},6695:(e,t,a)=>{a.d(t,{BT:()=>l,Wu:()=>d,ZB:()=>o,Zp:()=>n,aR:()=>i});var r=a(5155);a(2115);var s=a(9434);function n(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...a})}function i(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...a})}function o(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",t),...a})}function l(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",t),...a})}function d(e){let{className:t,...a}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",t),...a})}},7759:(e,t,a)=>{a.d(t,{C5:()=>h,MJ:()=>x,Rr:()=>v,eI:()=>g,lR:()=>f,lV:()=>d,zB:()=>u});var r=a(5155),s=a(2115),n=a(9708),i=a(2177),o=a(9434),l=a(5057);let d=i.Op,c=s.createContext({}),u=e=>{let{...t}=e;return(0,r.jsx)(c.Provider,{value:{name:t.name},children:(0,r.jsx)(i.xI,{...t})})},m=()=>{let e=s.useContext(c),t=s.useContext(p),{getFieldState:a}=(0,i.xW)(),r=(0,i.lN)({name:e.name}),n=a(e.name,r);if(!e)throw Error("useFormField should be used within <FormField>");let{id:o}=t;return{id:o,name:e.name,formItemId:"".concat(o,"-form-item"),formDescriptionId:"".concat(o,"-form-item-description"),formMessageId:"".concat(o,"-form-item-message"),...n}},p=s.createContext({});function g(e){let{className:t,...a}=e,n=s.useId();return(0,r.jsx)(p.Provider,{value:{id:n},children:(0,r.jsx)("div",{"data-slot":"form-item",className:(0,o.cn)("grid gap-2",t),...a})})}function f(e){let{className:t,...a}=e,{error:s,formItemId:n}=m();return(0,r.jsx)(l.J,{"data-slot":"form-label","data-error":!!s,className:(0,o.cn)("data-[error=true]:text-destructive",t),htmlFor:n,...a})}function x(e){let{...t}=e,{error:a,formItemId:s,formDescriptionId:i,formMessageId:o}=m();return(0,r.jsx)(n.DX,{"data-slot":"form-control",id:s,"aria-describedby":a?"".concat(i," ").concat(o):"".concat(i),"aria-invalid":!!a,...t})}function v(e){let{className:t,...a}=e,{formDescriptionId:s}=m();return(0,r.jsx)("p",{"data-slot":"form-description",id:s,className:(0,o.cn)("text-muted-foreground text-sm",t),...a})}function h(e){var t;let{className:a,...s}=e,{error:n,formMessageId:i}=m(),l=n?String(null!=(t=null==n?void 0:n.message)?t:""):s.children;return l?(0,r.jsx)("p",{"data-slot":"form-message",id:i,className:(0,o.cn)("text-destructive text-sm",a),...s,children:l}):null}},8539:(e,t,a)=>{a.d(t,{T:()=>n});var r=a(5155);a(2115);var s=a(9434);function n(e){let{className:t,...a}=e;return(0,r.jsx)("textarea",{"data-slot":"textarea",className:(0,s.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",t),...a})}},9409:(e,t,a)=>{a.d(t,{bq:()=>u,eb:()=>p,gC:()=>m,l6:()=>d,yv:()=>c});var r=a(5155);a(2115);var s=a(4582),n=a(6474),i=a(5196),o=a(7863),l=a(9434);function d(e){let{...t}=e;return(0,r.jsx)(s.bL,{"data-slot":"select",...t})}function c(e){let{...t}=e;return(0,r.jsx)(s.WT,{"data-slot":"select-value",...t})}function u(e){let{className:t,size:a="default",children:i,...o}=e;return(0,r.jsxs)(s.l9,{"data-slot":"select-trigger","data-size":a,className:(0,l.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...o,children:[i,(0,r.jsx)(s.In,{asChild:!0,children:(0,r.jsx)(n.A,{className:"size-4 opacity-50"})})]})}function m(e){let{className:t,children:a,position:n="popper",...i}=e;return(0,r.jsx)(s.ZL,{children:(0,r.jsxs)(s.UC,{"data-slot":"select-content",className:(0,l.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===n&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",t),position:n,...i,children:[(0,r.jsx)(g,{}),(0,r.jsx)(s.LM,{className:(0,l.cn)("p-1","popper"===n&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:a}),(0,r.jsx)(f,{})]})})}function p(e){let{className:t,children:a,...n}=e;return(0,r.jsxs)(s.q7,{"data-slot":"select-item",className:(0,l.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",t),...n,children:[(0,r.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,r.jsx)(s.VF,{children:(0,r.jsx)(i.A,{className:"size-4"})})}),(0,r.jsx)(s.p4,{children:a})]})}function g(e){let{className:t,...a}=e;return(0,r.jsx)(s.PP,{"data-slot":"select-scroll-up-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(o.A,{className:"size-4"})})}function f(e){let{className:t,...a}=e;return(0,r.jsx)(s.wn,{"data-slot":"select-scroll-down-button",className:(0,l.cn)("flex cursor-default items-center justify-center py-1",t),...a,children:(0,r.jsx)(n.A,{className:"size-4"})})}},9434:(e,t,a)=>{a.d(t,{EJ:()=>o,Yq:()=>i,cn:()=>n});var r=a(2596),s=a(9688);function n(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,s.QP)((0,r.$)(t))}function i(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function o(e,t){return e.length<=t?e:e.substring(0,t).trim()+"..."}}}]);