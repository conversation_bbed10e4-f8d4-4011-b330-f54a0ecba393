import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'
import { StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'

export function ServiceCardSkeleton() {
  return (
    <Card className="group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm">
      <div className="relative aspect-[4/3] overflow-hidden rounded-t-xl">
        <Skeleton className="w-full h-full" />
      </div>
      
      <CardHeader className="pb-3">
        <Skeleton className="h-6 w-3/4 mb-2" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-2/3" />
      </CardHeader>
      
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between text-sm">
          <div className="flex items-center gap-2">
            <Skeleton className="w-4 h-4 rounded-full" />
            <Skeleton className="h-4 w-16" />
          </div>
          <Skeleton className="h-4 w-20" />
        </div>
        
        <div className="space-y-2">
          <Skeleton className="h-3 w-full" />
          <Skeleton className="h-3 w-4/5" />
          <Skeleton className="h-3 w-3/5" />
        </div>
        
        <Skeleton className="h-10 w-full rounded-md" />
      </CardContent>
    </Card>
  )
}

export function ServicesOverviewSkeleton() {
  return (
    <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12">
      {Array.from({ length: 6 }).map((_, index) => (
        <StaggeredItem key={index}>
          <ServiceCardSkeleton />
        </StaggeredItem>
      ))}
    </StaggeredContainer>
  )
}
