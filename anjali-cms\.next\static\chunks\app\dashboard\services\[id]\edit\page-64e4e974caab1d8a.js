(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8425],{2924:(e,i,s)=>{Promise.resolve().then(s.bind(s,8759))},8759:(e,i,s)=>{"use strict";s.r(i),s.d(i,{default:()=>d});var t=s(5155),r=s(2115),a=s(5695),n=s(468),c=s(6671);function d(){let e=(0,a.useParams)(),[i,s]=(0,r.useState)(null),[d,l]=(0,r.useState)(!0);return((0,r.useEffect)(()=>{let i=async()=>{try{let i=await fetch("/api/services/".concat(e.id));if(i.ok){let e=await i.json();s(e)}else c.oR.error("Service not found")}catch(e){c.oR.error("Error fetching service")}finally{l(!1)}};e.id&&i()},[e.id]),d)?(0,t.jsx)("div",{children:"Loading..."}):i?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Service"}),(0,t.jsx)("p",{className:"text-muted-foreground",children:"Update your service details and settings"})]}),(0,t.jsx)(n.ServiceForm,{initialData:i,isEditing:!0})]}):(0,t.jsx)("div",{children:"Service not found"})}}},e=>{e.O(0,[5389,6671,651,7536,7764,8062,2804,9304,5889,8441,5964,7358],()=>e(e.s=2924)),_N_E=e.O()}]);