(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5611],{1385:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>A});var l=a(5155),r=a(2115),c=a(6874),i=a.n(c),n=a(6766),t=a(285),d=a(2523),h=a(6126),x=a(5127),o=a(9409),j=a(6695),u=a(4616),m=a(7924),g=a(8564),p=a(2657),v=a(3717),f=a(2525),N=a(9434),k=a(6671);function A(){let[e,s]=(0,r.useState)([]),[a,c]=(0,r.useState)(!0),[A,y]=(0,r.useState)(""),[w,C]=(0,r.useState)("all"),[b,E]=(0,r.useState)(""),[S,P]=(0,r.useState)(1),[I,R]=(0,r.useState)(1),V=async()=>{try{let e=new URLSearchParams({page:S.toString(),limit:"10",...A&&{search:A},...w&&"all"!==w&&{status:w},...b&&{category:b}}),a=await fetch("/api/packages?".concat(e));if(a.ok){let e=await a.json();s(e.packages),R(e.pagination.pages)}else k.oR.error("Failed to fetch packages")}catch(e){k.oR.error("Error fetching packages")}finally{c(!1)}},F=async e=>{if(confirm("Are you sure you want to delete this package?"))try{(await fetch("/api/packages/".concat(e),{method:"DELETE"})).ok?(k.oR.success("Package deleted successfully"),V()):k.oR.error("Failed to delete package")}catch(e){k.oR.error("Error deleting package")}};return((0,r.useEffect)(()=>{V()},[S,A,w,b]),a)?(0,l.jsx)("div",{children:"Loading..."}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold",children:"Packages"}),(0,l.jsx)("p",{className:"text-muted-foreground",children:"Manage your makeup service packages and pricing tiers"})]}),(0,l.jsx)(t.$,{asChild:!0,children:(0,l.jsxs)(i(),{href:"/dashboard/packages/new",children:[(0,l.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"New Package"]})})]}),(0,l.jsxs)(j.Zp,{children:[(0,l.jsxs)(j.aR,{children:[(0,l.jsx)(j.ZB,{children:"Filters"}),(0,l.jsx)(j.BT,{children:"Filter and search through your packages"})]}),(0,l.jsx)(j.Wu,{children:(0,l.jsxs)("div",{className:"flex gap-4",children:[(0,l.jsx)("div",{className:"flex-1",children:(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)(m.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,l.jsx)(d.p,{placeholder:"Search packages...",value:A,onChange:e=>y(e.target.value),className:"pl-8"})]})}),(0,l.jsxs)(o.l6,{value:w,onValueChange:C,children:[(0,l.jsx)(o.bq,{className:"w-[180px]",children:(0,l.jsx)(o.yv,{placeholder:"All statuses"})}),(0,l.jsxs)(o.gC,{children:[(0,l.jsx)(o.eb,{value:"all",children:"All statuses"}),(0,l.jsx)(o.eb,{value:"ACTIVE",children:"Active"}),(0,l.jsx)(o.eb,{value:"INACTIVE",children:"Inactive"}),(0,l.jsx)(o.eb,{value:"ARCHIVED",children:"Archived"})]})]}),(0,l.jsx)(d.p,{placeholder:"Filter by category",value:b,onChange:e=>E(e.target.value),className:"w-[180px]"})]})})]}),(0,l.jsx)(j.Zp,{children:(0,l.jsxs)(x.XI,{children:[(0,l.jsx)(x.A0,{children:(0,l.jsxs)(x.Hj,{children:[(0,l.jsx)(x.nd,{children:"Package"}),(0,l.jsx)(x.nd,{children:"Category"}),(0,l.jsx)(x.nd,{children:"Price"}),(0,l.jsx)(x.nd,{children:"Services"}),(0,l.jsx)(x.nd,{children:"Status"}),(0,l.jsx)(x.nd,{children:"Created"}),(0,l.jsx)(x.nd,{children:"Actions"})]})}),(0,l.jsx)(x.BF,{children:0===e.length?(0,l.jsx)(x.Hj,{children:(0,l.jsx)(x.nA,{colSpan:7,className:"text-center py-8",children:(0,l.jsxs)("div",{className:"text-muted-foreground",children:["No packages found.",(0,l.jsx)(i(),{href:"/dashboard/packages/new",className:"text-primary hover:underline ml-1",children:"Create your first package"})]})})}):e.map(e=>{var s;return(0,l.jsxs)(x.Hj,{children:[(0,l.jsx)(x.nA,{children:(0,l.jsxs)("div",{className:"flex items-center gap-3",children:[e.image&&(0,l.jsx)("div",{className:"relative w-10 h-10 rounded-md overflow-hidden",children:(0,l.jsx)(n.default,{src:e.image,alt:e.name,fill:!0,className:"object-cover"})}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("div",{className:"font-medium flex items-center gap-2",children:[e.name,e.popular&&(0,l.jsx)(g.A,{className:"h-4 w-4 text-yellow-500 fill-current"})]}),(0,l.jsx)("div",{className:"text-sm text-muted-foreground",children:(0,N.EJ)(e.description,50)})]})]})}),(0,l.jsx)(x.nA,{children:e.category?(0,l.jsx)(h.E,{variant:"outline",children:e.category}):(0,l.jsx)("span",{className:"text-muted-foreground",children:"No category"})}),(0,l.jsx)(x.nA,{children:(0,l.jsxs)("div",{children:[(0,l.jsx)("span",{className:"font-medium",children:e.price}),e.originalPrice&&(0,l.jsx)("div",{className:"text-sm text-muted-foreground line-through",children:e.originalPrice})]})}),(0,l.jsx)(x.nA,{children:(0,l.jsx)("div",{className:"text-sm",children:e.services.length>0?(0,l.jsxs)("span",{children:[e.services.length," service(s)"]}):(0,l.jsx)("span",{className:"text-muted-foreground",children:"No services"})})}),(0,l.jsx)(x.nA,{children:(s=e.status,(0,l.jsx)(h.E,{variant:{ACTIVE:"default",INACTIVE:"secondary",ARCHIVED:"outline"}[s],children:s}))}),(0,l.jsx)(x.nA,{children:(0,N.Yq)(e.createdAt)}),(0,l.jsx)(x.nA,{children:(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(t.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,l.jsx)(i(),{href:"/dashboard/packages/".concat(e.id),children:(0,l.jsx)(p.A,{className:"h-4 w-4"})})}),(0,l.jsx)(t.$,{variant:"ghost",size:"sm",asChild:!0,children:(0,l.jsx)(i(),{href:"/dashboard/packages/".concat(e.id,"/edit"),children:(0,l.jsx)(v.A,{className:"h-4 w-4"})})}),(0,l.jsx)(t.$,{variant:"ghost",size:"sm",onClick:()=>F(e.id),children:(0,l.jsx)(f.A,{className:"h-4 w-4"})})]})})]},e.id)})})]})}),I>1&&(0,l.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,l.jsx)(t.$,{variant:"outline",onClick:()=>P(S-1),disabled:1===S,children:"Previous"}),(0,l.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",S," of ",I]}),(0,l.jsx)(t.$,{variant:"outline",onClick:()=>P(S+1),disabled:S===I,children:"Next"})]})]})}},1868:(e,s,a)=>{Promise.resolve().then(a.bind(a,1385))},8564:(e,s,a)=>{"use strict";a.d(s,{A:()=>l});let l=(0,a(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])}},e=>{e.O(0,[5389,6671,651,7536,7764,8062,6874,8064,8441,5964,7358],()=>e(e.s=1868)),_N_E=e.O()}]);