{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "GN5xHTJEOZKh6AmUB-gLD", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "qqs/51Z3btC6w/UJ2TiHtxQZT70yB58AMKU3p+RYPQs=", "__NEXT_PREVIEW_MODE_ID": "31c971e1e307df0219ae05bf2eb02219", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d0c8fd5cf378938f8d150c9db97a29381a1b4150ecc653fa0372a4808e57b229", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5e37769d4285851b4096d035cb24d2f979a5b71d4cb827e49cc2bd2779275e70"}}}, "functions": {}, "sortedMiddleware": ["/"]}