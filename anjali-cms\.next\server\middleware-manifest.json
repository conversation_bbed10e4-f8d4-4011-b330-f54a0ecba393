{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_82ad035c._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_fb905aa2.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/api(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/api/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "qqs/51Z3btC6w/UJ2TiHtxQZT70yB58AMKU3p+RYPQs=", "__NEXT_PREVIEW_MODE_ID": "7c4535a91972fe546b45075bf9390f39", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6277ed9d73e0bf5897170078ff4f74efb0432a7126dfe44f21349c6998ae5c3c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ed6f51fd8084fb96779bb671ad622b03a19ebc717528dc797b56754cda79a9d3"}}}, "sortedMiddleware": ["/"], "functions": {}}