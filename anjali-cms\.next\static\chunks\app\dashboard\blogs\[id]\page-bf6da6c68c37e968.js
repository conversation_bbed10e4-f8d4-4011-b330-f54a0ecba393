(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4143],{285:(e,t,s)=>{"use strict";s.d(t,{$:()=>c});var r=s(5155);s(2115);var a=s(9708),i=s(2085),n=s(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:t,variant:s,size:i,asChild:c=!1,...l}=e,o=c?a.DX:"button";return(0,r.jsx)(o,{"data-slot":"button",className:(0,n.cn)(d({variant:s,size:i,className:t})),...l})}},1007:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},2346:(e,t,s)=>{"use strict";s.d(t,{w:()=>n});var r=s(5155);s(2115);var a=s(7489),i=s(9434);function n(e){let{className:t,orientation:s="horizontal",decorative:n=!0,...d}=e;return(0,r.jsx)(a.b,{"data-slot":"separator",decorative:n,orientation:s,className:(0,i.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...d})}},3332:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},3717:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4186:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},5169:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5247:(e,t,s)=>{Promise.resolve().then(s.bind(s,5357))},5357:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var r=s(5155),a=s(2115),i=s(5695),n=s(6874),d=s.n(n),c=s(6766),l=s(285),o=s(6126),u=s(6695),h=s(2346),x=s(5169),m=s(1007),v=s(9074),g=s(4186),p=s(3717),f=s(3332),j=s(9434),b=s(6671);function y(){let e=(0,i.useParams)(),t=(0,i.useRouter)(),[s,n]=(0,a.useState)(null),[y,k]=(0,a.useState)(!0);if((0,a.useEffect)(()=>{let s=async()=>{try{let s=await fetch("/api/blogs/".concat(e.id));if(s.ok){let e=await s.json();n(e)}else b.oR.error("Blog not found"),t.push("/dashboard/blogs")}catch(e){b.oR.error("Error fetching blog"),t.push("/dashboard/blogs")}finally{k(!1)}};e.id&&s()},[e.id,t]),y)return(0,r.jsx)("div",{children:"Loading..."});if(!s)return(0,r.jsx)("div",{children:"Blog not found"});let w=e=>(0,r.jsx)(o.E,{variant:{DRAFT:"secondary",PUBLISHED:"default",ARCHIVED:"outline"}[e],children:e});return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)(l.$,{variant:"ghost",size:"sm",onClick:()=>t.back(),children:[(0,r.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold",children:s.title}),(0,r.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground mt-2",children:[(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(m.A,{className:"h-4 w-4"}),s.author]}),(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(v.A,{className:"h-4 w-4"}),(0,j.Yq)(s.createdAt)]}),s.readTime&&(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(g.A,{className:"h-4 w-4"}),s.readTime]})]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[w(s.status),s.featured&&(0,r.jsx)(o.E,{variant:"outline",children:"Featured"}),(0,r.jsx)(l.$,{asChild:!0,children:(0,r.jsxs)(d(),{href:"/dashboard/blogs/".concat(s.id,"/edit"),children:[(0,r.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Edit"]})})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[s.image&&(0,r.jsx)(u.Zp,{children:(0,r.jsx)(u.Wu,{className:"p-0",children:(0,r.jsx)("div",{className:"relative aspect-video w-full overflow-hidden rounded-lg",children:(0,r.jsx)(c.default,{src:s.image,alt:s.title,fill:!0,className:"object-cover"})})})}),s.excerpt&&(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{children:"Excerpt"})}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("p",{className:"text-muted-foreground",children:s.excerpt})})]}),(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{children:"Content"})}),(0,r.jsx)(u.Wu,{children:(0,r.jsx)("div",{className:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl max-w-none",dangerouslySetInnerHTML:{__html:s.content}})})]})]}),(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{children:"Details"})}),(0,r.jsxs)(u.Wu,{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)(N,{className:"text-sm font-medium",children:"Status"}),(0,r.jsx)("div",{className:"mt-1",children:w(s.status)})]}),s.category&&(0,r.jsxs)("div",{children:[(0,r.jsx)(N,{className:"text-sm font-medium",children:"Category"}),(0,r.jsx)("div",{className:"mt-1",children:(0,r.jsx)(o.E,{variant:"outline",children:s.category.name})})]}),s.tags.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)(N,{className:"text-sm font-medium",children:"Tags"}),(0,r.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:s.tags.map(e=>(0,r.jsxs)(o.E,{variant:"outline",children:[(0,r.jsx)(f.A,{className:"h-3 w-3 mr-1"}),e.tag.name]},e.tag.id))})]}),(0,r.jsx)(h.w,{}),(0,r.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Created:"})," ",(0,j.Yq)(s.createdAt)]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Updated:"})," ",(0,j.Yq)(s.updatedAt)]}),s.publishedAt&&(0,r.jsxs)("div",{children:[(0,r.jsx)("span",{className:"font-medium",children:"Published:"})," ",(0,j.Yq)(s.publishedAt)]})]})]})]}),(s.metaTitle||s.metaDescription||s.keywords.length>0)&&(0,r.jsxs)(u.Zp,{children:[(0,r.jsx)(u.aR,{children:(0,r.jsx)(u.ZB,{children:"SEO Settings"})}),(0,r.jsxs)(u.Wu,{className:"space-y-4",children:[s.metaTitle&&(0,r.jsxs)("div",{children:[(0,r.jsx)(N,{className:"text-sm font-medium",children:"Meta Title"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:s.metaTitle})]}),s.metaDescription&&(0,r.jsxs)("div",{children:[(0,r.jsx)(N,{className:"text-sm font-medium",children:"Meta Description"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:s.metaDescription})]}),s.keywords.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsx)(N,{className:"text-sm font-medium",children:"Keywords"}),(0,r.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:s.keywords.join(", ")})]})]})]})]})]})]})}function N(e){let{children:t,className:s}=e;return(0,r.jsx)("div",{className:s,children:t})}},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"redirect")&&s.d(t,{redirect:function(){return r.redirect}}),s.o(r,"useParams")&&s.d(t,{useParams:function(){return r.useParams}}),s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},6126:(e,t,s)=>{"use strict";s.d(t,{E:()=>c});var r=s(5155);s(2115);var a=s(9708),i=s(2085),n=s(9434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:s,asChild:i=!1,...c}=e,l=i?a.DX:"span";return(0,r.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(d({variant:s}),t),...c})}},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>n});var r=s(5155);s(2115);var a=s(9434);function i(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function n(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function d(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",t),...s})}function c(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",t),...s})}function l(e){let{className:t,...s}=e;return(0,r.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",t),...s})}},7489:(e,t,s)=>{"use strict";s.d(t,{b:()=>l});var r=s(2115),a=s(3655),i=s(5155),n="horizontal",d=["horizontal","vertical"],c=r.forwardRef((e,t)=>{var s;let{decorative:r,orientation:c=n,...l}=e,o=(s=c,d.includes(s))?c:n;return(0,i.jsx)(a.sG.div,{"data-orientation":o,...r?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...l,ref:t})});c.displayName="Separator";var l=c},9074:(e,t,s)=>{"use strict";s.d(t,{A:()=>r});let r=(0,s(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9434:(e,t,s)=>{"use strict";s.d(t,{EJ:()=>d,Yq:()=>n,cn:()=>i});var r=s(2596),a=s(9688);function i(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,a.QP)((0,r.$)(t))}function n(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function d(e,t){return e.length<=t?e:e.substring(0,t).trim()+"..."}}},e=>{e.O(0,[5389,6671,651,6874,8441,5964,7358],()=>e(e.s=5247)),_N_E=e.O()}]);