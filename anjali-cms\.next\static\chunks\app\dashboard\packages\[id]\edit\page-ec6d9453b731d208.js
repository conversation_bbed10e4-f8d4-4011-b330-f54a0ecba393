(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5520],{2:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>d});var s=t(5155),i=t(2115),n=t(5695),c=t(3543),r=t(6671);function d(){let e=(0,n.useParams)(),[a,t]=(0,i.useState)(null),[d,l]=(0,i.useState)(!0);return((0,i.useEffect)(()=>{let a=async()=>{try{let a=await fetch("/api/packages/".concat(e.id));if(a.ok){let e=await a.json();t(e)}else r.oR.error("Package not found")}catch(e){r.oR.error("Error fetching package")}finally{l(!1)}};e.id&&a()},[e.id]),d)?(0,s.jsx)("div",{children:"Loading..."}):a?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Package"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Update your package details and settings"})]}),(0,s.jsx)(c.PackageForm,{initialData:a,isEditing:!0})]}):(0,s.jsx)("div",{children:"Package not found"})}},5437:(e,a,t)=>{Promise.resolve().then(t.bind(t,2))}},e=>{e.O(0,[5389,6671,651,7536,7764,8062,2804,9304,566,8441,5964,7358],()=>e(e.s=5437)),_N_E=e.O()}]);