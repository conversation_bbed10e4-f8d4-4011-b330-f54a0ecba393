"use strict";exports.id=5555,exports.ids=[5555],exports.modules={3589:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},13964:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},72951:(a,b,c)=>{c.d(b,{UC:()=>aG,In:()=>aE,q7:()=>aI,VF:()=>aK,p4:()=>aJ,ZL:()=>aF,bL:()=>aB,wn:()=>aM,PP:()=>aL,l9:()=>aC,WT:()=>aD,LM:()=>aH});var d=c(43210),e=c(51215);function f(a,[b,c]){return Math.min(c,Math.max(b,a))}var g=c(70569),h=c(9510),i=c(98599),j=c(11273),k=c(43),l=c(31355),m=c(1359),n=c(32547),o=c(96963),p=c(55509),q=c(25028),r=c(14163),s=c(8730),t=c(13495),u=c(65551),v=c(66156),w=c(83721),x=c(69024),y=c(63376),z=c(42247),A=c(60687),B=[" ","Enter","ArrowUp","ArrowDown"],C=[" ","Enter"],D="Select",[E,F,G]=(0,h.N)(D),[H,I]=(0,j.A)(D,[G,p.Bk]),J=(0,p.Bk)(),[K,L]=H(D),[M,N]=H(D),O=a=>{let{__scopeSelect:b,children:c,open:e,defaultOpen:f,onOpenChange:g,value:h,defaultValue:i,onValueChange:j,dir:l,name:m,autoComplete:n,disabled:q,required:r,form:s}=a,t=J(b),[v,w]=d.useState(null),[x,y]=d.useState(null),[z,B]=d.useState(!1),C=(0,k.jH)(l),[F,G]=(0,u.i)({prop:e,defaultProp:f??!1,onChange:g,caller:D}),[H,I]=(0,u.i)({prop:h,defaultProp:i,onChange:j,caller:D}),L=d.useRef(null),N=!v||s||!!v.closest("form"),[O,P]=d.useState(new Set),Q=Array.from(O).map(a=>a.props.value).join(";");return(0,A.jsx)(p.bL,{...t,children:(0,A.jsxs)(K,{required:r,scope:b,trigger:v,onTriggerChange:w,valueNode:x,onValueNodeChange:y,valueNodeHasChildren:z,onValueNodeHasChildrenChange:B,contentId:(0,o.B)(),value:H,onValueChange:I,open:F,onOpenChange:G,dir:C,triggerPointerDownPosRef:L,disabled:q,children:[(0,A.jsx)(E.Provider,{scope:b,children:(0,A.jsx)(M,{scope:a.__scopeSelect,onNativeOptionAdd:d.useCallback(a=>{P(b=>new Set(b).add(a))},[]),onNativeOptionRemove:d.useCallback(a=>{P(b=>{let c=new Set(b);return c.delete(a),c})},[]),children:c})}),N?(0,A.jsxs)(ax,{"aria-hidden":!0,required:r,tabIndex:-1,name:m,autoComplete:n,value:H,onChange:a=>I(a.target.value),disabled:q,form:s,children:[void 0===H?(0,A.jsx)("option",{value:""}):null,Array.from(O)]},Q):null]})})};O.displayName=D;var P="SelectTrigger",Q=d.forwardRef((a,b)=>{let{__scopeSelect:c,disabled:e=!1,...f}=a,h=J(c),j=L(P,c),k=j.disabled||e,l=(0,i.s)(b,j.onTriggerChange),m=F(c),n=d.useRef("touch"),[o,q,s]=az(a=>{let b=m().filter(a=>!a.disabled),c=b.find(a=>a.value===j.value),d=aA(b,a,c);void 0!==d&&j.onValueChange(d.value)}),t=a=>{k||(j.onOpenChange(!0),s()),a&&(j.triggerPointerDownPosRef.current={x:Math.round(a.pageX),y:Math.round(a.pageY)})};return(0,A.jsx)(p.Mz,{asChild:!0,...h,children:(0,A.jsx)(r.sG.button,{type:"button",role:"combobox","aria-controls":j.contentId,"aria-expanded":j.open,"aria-required":j.required,"aria-autocomplete":"none",dir:j.dir,"data-state":j.open?"open":"closed",disabled:k,"data-disabled":k?"":void 0,"data-placeholder":ay(j.value)?"":void 0,...f,ref:l,onClick:(0,g.m)(f.onClick,a=>{a.currentTarget.focus(),"mouse"!==n.current&&t(a)}),onPointerDown:(0,g.m)(f.onPointerDown,a=>{n.current=a.pointerType;let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),0===a.button&&!1===a.ctrlKey&&"mouse"===a.pointerType&&(t(a),a.preventDefault())}),onKeyDown:(0,g.m)(f.onKeyDown,a=>{let b=""!==o.current;a.ctrlKey||a.altKey||a.metaKey||1!==a.key.length||q(a.key),(!b||" "!==a.key)&&B.includes(a.key)&&(t(),a.preventDefault())})})})});Q.displayName=P;var R="SelectValue",S=d.forwardRef((a,b)=>{let{__scopeSelect:c,className:d,style:e,children:f,placeholder:g="",...h}=a,j=L(R,c),{onValueNodeHasChildrenChange:k}=j,l=void 0!==f,m=(0,i.s)(b,j.onValueNodeChange);return(0,v.N)(()=>{k(l)},[k,l]),(0,A.jsx)(r.sG.span,{...h,ref:m,style:{pointerEvents:"none"},children:ay(j.value)?(0,A.jsx)(A.Fragment,{children:g}):f})});S.displayName=R;var T=d.forwardRef((a,b)=>{let{__scopeSelect:c,children:d,...e}=a;return(0,A.jsx)(r.sG.span,{"aria-hidden":!0,...e,ref:b,children:d||"▼"})});T.displayName="SelectIcon";var U=a=>(0,A.jsx)(q.Z,{asChild:!0,...a});U.displayName="SelectPortal";var V="SelectContent",W=d.forwardRef((a,b)=>{let c=L(V,a.__scopeSelect),[f,g]=d.useState();return((0,v.N)(()=>{g(new DocumentFragment)},[]),c.open)?(0,A.jsx)($,{...a,ref:b}):f?e.createPortal((0,A.jsx)(X,{scope:a.__scopeSelect,children:(0,A.jsx)(E.Slot,{scope:a.__scopeSelect,children:(0,A.jsx)("div",{children:a.children})})}),f):null});W.displayName=V;var[X,Y]=H(V),Z=(0,s.TL)("SelectContent.RemoveScroll"),$=d.forwardRef((a,b)=>{let{__scopeSelect:c,position:e="item-aligned",onCloseAutoFocus:f,onEscapeKeyDown:h,onPointerDownOutside:j,side:k,sideOffset:o,align:p,alignOffset:q,arrowPadding:r,collisionBoundary:s,collisionPadding:t,sticky:u,hideWhenDetached:v,avoidCollisions:w,...x}=a,B=L(V,c),[C,D]=d.useState(null),[E,G]=d.useState(null),H=(0,i.s)(b,a=>D(a)),[I,J]=d.useState(null),[K,M]=d.useState(null),N=F(c),[O,P]=d.useState(!1),Q=d.useRef(!1);d.useEffect(()=>{if(C)return(0,y.Eq)(C)},[C]),(0,m.Oh)();let R=d.useCallback(a=>{let[b,...c]=N().map(a=>a.ref.current),[d]=c.slice(-1),e=document.activeElement;for(let c of a)if(c===e||(c?.scrollIntoView({block:"nearest"}),c===b&&E&&(E.scrollTop=0),c===d&&E&&(E.scrollTop=E.scrollHeight),c?.focus(),document.activeElement!==e))return},[N,E]),S=d.useCallback(()=>R([I,C]),[R,I,C]);d.useEffect(()=>{O&&S()},[O,S]);let{onOpenChange:T,triggerPointerDownPosRef:U}=B;d.useEffect(()=>{if(C){let a={x:0,y:0},b=b=>{a={x:Math.abs(Math.round(b.pageX)-(U.current?.x??0)),y:Math.abs(Math.round(b.pageY)-(U.current?.y??0))}},c=c=>{a.x<=10&&a.y<=10?c.preventDefault():C.contains(c.target)||T(!1),document.removeEventListener("pointermove",b),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",b),document.addEventListener("pointerup",c,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",b),document.removeEventListener("pointerup",c,{capture:!0})}}},[C,T,U]),d.useEffect(()=>{let a=()=>T(!1);return window.addEventListener("blur",a),window.addEventListener("resize",a),()=>{window.removeEventListener("blur",a),window.removeEventListener("resize",a)}},[T]);let[W,Y]=az(a=>{let b=N().filter(a=>!a.disabled),c=b.find(a=>a.ref.current===document.activeElement),d=aA(b,a,c);d&&setTimeout(()=>d.ref.current.focus())}),$=d.useCallback((a,b,c)=>{let d=!Q.current&&!c;(void 0!==B.value&&B.value===b||d)&&(J(a),d&&(Q.current=!0))},[B.value]),ab=d.useCallback(()=>C?.focus(),[C]),ac=d.useCallback((a,b,c)=>{let d=!Q.current&&!c;(void 0!==B.value&&B.value===b||d)&&M(a)},[B.value]),ad="popper"===e?aa:_,ae=ad===aa?{side:k,sideOffset:o,align:p,alignOffset:q,arrowPadding:r,collisionBoundary:s,collisionPadding:t,sticky:u,hideWhenDetached:v,avoidCollisions:w}:{};return(0,A.jsx)(X,{scope:c,content:C,viewport:E,onViewportChange:G,itemRefCallback:$,selectedItem:I,onItemLeave:ab,itemTextRefCallback:ac,focusSelectedItem:S,selectedItemText:K,position:e,isPositioned:O,searchRef:W,children:(0,A.jsx)(z.A,{as:Z,allowPinchZoom:!0,children:(0,A.jsx)(n.n,{asChild:!0,trapped:B.open,onMountAutoFocus:a=>{a.preventDefault()},onUnmountAutoFocus:(0,g.m)(f,a=>{B.trigger?.focus({preventScroll:!0}),a.preventDefault()}),children:(0,A.jsx)(l.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:h,onPointerDownOutside:j,onFocusOutside:a=>a.preventDefault(),onDismiss:()=>B.onOpenChange(!1),children:(0,A.jsx)(ad,{role:"listbox",id:B.contentId,"data-state":B.open?"open":"closed",dir:B.dir,onContextMenu:a=>a.preventDefault(),...x,...ae,onPlaced:()=>P(!0),ref:H,style:{display:"flex",flexDirection:"column",outline:"none",...x.style},onKeyDown:(0,g.m)(x.onKeyDown,a=>{let b=a.ctrlKey||a.altKey||a.metaKey;if("Tab"===a.key&&a.preventDefault(),b||1!==a.key.length||Y(a.key),["ArrowUp","ArrowDown","Home","End"].includes(a.key)){let b=N().filter(a=>!a.disabled).map(a=>a.ref.current);if(["ArrowUp","End"].includes(a.key)&&(b=b.slice().reverse()),["ArrowUp","ArrowDown"].includes(a.key)){let c=a.target,d=b.indexOf(c);b=b.slice(d+1)}setTimeout(()=>R(b)),a.preventDefault()}})})})})})})});$.displayName="SelectContentImpl";var _=d.forwardRef((a,b)=>{let{__scopeSelect:c,onPlaced:e,...g}=a,h=L(V,c),j=Y(V,c),[k,l]=d.useState(null),[m,n]=d.useState(null),o=(0,i.s)(b,a=>n(a)),p=F(c),q=d.useRef(!1),s=d.useRef(!0),{viewport:t,selectedItem:u,selectedItemText:w,focusSelectedItem:x}=j,y=d.useCallback(()=>{if(h.trigger&&h.valueNode&&k&&m&&t&&u&&w){let a=h.trigger.getBoundingClientRect(),b=m.getBoundingClientRect(),c=h.valueNode.getBoundingClientRect(),d=w.getBoundingClientRect();if("rtl"!==h.dir){let e=d.left-b.left,g=c.left-e,h=a.left-g,i=a.width+h,j=Math.max(i,b.width),l=f(g,[10,Math.max(10,window.innerWidth-10-j)]);k.style.minWidth=i+"px",k.style.left=l+"px"}else{let e=b.right-d.right,g=window.innerWidth-c.right-e,h=window.innerWidth-a.right-g,i=a.width+h,j=Math.max(i,b.width),l=f(g,[10,Math.max(10,window.innerWidth-10-j)]);k.style.minWidth=i+"px",k.style.right=l+"px"}let g=p(),i=window.innerHeight-20,j=t.scrollHeight,l=window.getComputedStyle(m),n=parseInt(l.borderTopWidth,10),o=parseInt(l.paddingTop,10),r=parseInt(l.borderBottomWidth,10),s=n+o+j+parseInt(l.paddingBottom,10)+r,v=Math.min(5*u.offsetHeight,s),x=window.getComputedStyle(t),y=parseInt(x.paddingTop,10),z=parseInt(x.paddingBottom,10),A=a.top+a.height/2-10,B=u.offsetHeight/2,C=n+o+(u.offsetTop+B);if(C<=A){let a=g.length>0&&u===g[g.length-1].ref.current;k.style.bottom="0px";let b=Math.max(i-A,B+(a?z:0)+(m.clientHeight-t.offsetTop-t.offsetHeight)+r);k.style.height=C+b+"px"}else{let a=g.length>0&&u===g[0].ref.current;k.style.top="0px";let b=Math.max(A,n+t.offsetTop+(a?y:0)+B);k.style.height=b+(s-C)+"px",t.scrollTop=C-A+t.offsetTop}k.style.margin="10px 0",k.style.minHeight=v+"px",k.style.maxHeight=i+"px",e?.(),requestAnimationFrame(()=>q.current=!0)}},[p,h.trigger,h.valueNode,k,m,t,u,w,h.dir,e]);(0,v.N)(()=>y(),[y]);let[z,B]=d.useState();(0,v.N)(()=>{m&&B(window.getComputedStyle(m).zIndex)},[m]);let C=d.useCallback(a=>{a&&!0===s.current&&(y(),x?.(),s.current=!1)},[y,x]);return(0,A.jsx)(ab,{scope:c,contentWrapper:k,shouldExpandOnScrollRef:q,onScrollButtonChange:C,children:(0,A.jsx)("div",{ref:l,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:z},children:(0,A.jsx)(r.sG.div,{...g,ref:o,style:{boxSizing:"border-box",maxHeight:"100%",...g.style}})})})});_.displayName="SelectItemAlignedPosition";var aa=d.forwardRef((a,b)=>{let{__scopeSelect:c,align:d="start",collisionPadding:e=10,...f}=a,g=J(c);return(0,A.jsx)(p.UC,{...g,...f,ref:b,align:d,collisionPadding:e,style:{boxSizing:"border-box",...f.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});aa.displayName="SelectPopperPosition";var[ab,ac]=H(V,{}),ad="SelectViewport",ae=d.forwardRef((a,b)=>{let{__scopeSelect:c,nonce:e,...f}=a,h=Y(ad,c),j=ac(ad,c),k=(0,i.s)(b,h.onViewportChange),l=d.useRef(0);return(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:e}),(0,A.jsx)(E.Slot,{scope:c,children:(0,A.jsx)(r.sG.div,{"data-radix-select-viewport":"",role:"presentation",...f,ref:k,style:{position:"relative",flex:1,overflow:"hidden auto",...f.style},onScroll:(0,g.m)(f.onScroll,a=>{let b=a.currentTarget,{contentWrapper:c,shouldExpandOnScrollRef:d}=j;if(d?.current&&c){let a=Math.abs(l.current-b.scrollTop);if(a>0){let d=window.innerHeight-20,e=Math.max(parseFloat(c.style.minHeight),parseFloat(c.style.height));if(e<d){let f=e+a,g=Math.min(d,f),h=f-g;c.style.height=g+"px","0px"===c.style.bottom&&(b.scrollTop=h>0?h:0,c.style.justifyContent="flex-end")}}}l.current=b.scrollTop})})})]})});ae.displayName=ad;var af="SelectGroup",[ag,ah]=H(af);d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=(0,o.B)();return(0,A.jsx)(ag,{scope:c,id:e,children:(0,A.jsx)(r.sG.div,{role:"group","aria-labelledby":e,...d,ref:b})})}).displayName=af;var ai="SelectLabel";d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=ah(ai,c);return(0,A.jsx)(r.sG.div,{id:e.id,...d,ref:b})}).displayName=ai;var aj="SelectItem",[ak,al]=H(aj),am=d.forwardRef((a,b)=>{let{__scopeSelect:c,value:e,disabled:f=!1,textValue:h,...j}=a,k=L(aj,c),l=Y(aj,c),m=k.value===e,[n,p]=d.useState(h??""),[q,s]=d.useState(!1),t=(0,i.s)(b,a=>l.itemRefCallback?.(a,e,f)),u=(0,o.B)(),v=d.useRef("touch"),w=()=>{f||(k.onValueChange(e),k.onOpenChange(!1))};if(""===e)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,A.jsx)(ak,{scope:c,value:e,disabled:f,textId:u,isSelected:m,onItemTextChange:d.useCallback(a=>{p(b=>b||(a?.textContent??"").trim())},[]),children:(0,A.jsx)(E.ItemSlot,{scope:c,value:e,disabled:f,textValue:n,children:(0,A.jsx)(r.sG.div,{role:"option","aria-labelledby":u,"data-highlighted":q?"":void 0,"aria-selected":m&&q,"data-state":m?"checked":"unchecked","aria-disabled":f||void 0,"data-disabled":f?"":void 0,tabIndex:f?void 0:-1,...j,ref:t,onFocus:(0,g.m)(j.onFocus,()=>s(!0)),onBlur:(0,g.m)(j.onBlur,()=>s(!1)),onClick:(0,g.m)(j.onClick,()=>{"mouse"!==v.current&&w()}),onPointerUp:(0,g.m)(j.onPointerUp,()=>{"mouse"===v.current&&w()}),onPointerDown:(0,g.m)(j.onPointerDown,a=>{v.current=a.pointerType}),onPointerMove:(0,g.m)(j.onPointerMove,a=>{v.current=a.pointerType,f?l.onItemLeave?.():"mouse"===v.current&&a.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,g.m)(j.onPointerLeave,a=>{a.currentTarget===document.activeElement&&l.onItemLeave?.()}),onKeyDown:(0,g.m)(j.onKeyDown,a=>{(l.searchRef?.current===""||" "!==a.key)&&(C.includes(a.key)&&w()," "===a.key&&a.preventDefault())})})})})});am.displayName=aj;var an="SelectItemText",ao=d.forwardRef((a,b)=>{let{__scopeSelect:c,className:f,style:g,...h}=a,j=L(an,c),k=Y(an,c),l=al(an,c),m=N(an,c),[n,o]=d.useState(null),p=(0,i.s)(b,a=>o(a),l.onItemTextChange,a=>k.itemTextRefCallback?.(a,l.value,l.disabled)),q=n?.textContent,s=d.useMemo(()=>(0,A.jsx)("option",{value:l.value,disabled:l.disabled,children:q},l.value),[l.disabled,l.value,q]),{onNativeOptionAdd:t,onNativeOptionRemove:u}=m;return(0,v.N)(()=>(t(s),()=>u(s)),[t,u,s]),(0,A.jsxs)(A.Fragment,{children:[(0,A.jsx)(r.sG.span,{id:l.textId,...h,ref:p}),l.isSelected&&j.valueNode&&!j.valueNodeHasChildren?e.createPortal(h.children,j.valueNode):null]})});ao.displayName=an;var ap="SelectItemIndicator",aq=d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return al(ap,c).isSelected?(0,A.jsx)(r.sG.span,{"aria-hidden":!0,...d,ref:b}):null});aq.displayName=ap;var ar="SelectScrollUpButton",as=d.forwardRef((a,b)=>{let c=Y(ar,a.__scopeSelect),e=ac(ar,a.__scopeSelect),[f,g]=d.useState(!1),h=(0,i.s)(b,e.onScrollButtonChange);return(0,v.N)(()=>{if(c.viewport&&c.isPositioned){let a=function(){g(b.scrollTop>0)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),f?(0,A.jsx)(av,{...a,ref:h,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop-b.offsetHeight)}}):null});as.displayName=ar;var at="SelectScrollDownButton",au=d.forwardRef((a,b)=>{let c=Y(at,a.__scopeSelect),e=ac(at,a.__scopeSelect),[f,g]=d.useState(!1),h=(0,i.s)(b,e.onScrollButtonChange);return(0,v.N)(()=>{if(c.viewport&&c.isPositioned){let a=function(){let a=b.scrollHeight-b.clientHeight;g(Math.ceil(b.scrollTop)<a)},b=c.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[c.viewport,c.isPositioned]),f?(0,A.jsx)(av,{...a,ref:h,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=c;a&&b&&(a.scrollTop=a.scrollTop+b.offsetHeight)}}):null});au.displayName=at;var av=d.forwardRef((a,b)=>{let{__scopeSelect:c,onAutoScroll:e,...f}=a,h=Y("SelectScrollButton",c),i=d.useRef(null),j=F(c),k=d.useCallback(()=>{null!==i.current&&(window.clearInterval(i.current),i.current=null)},[]);return d.useEffect(()=>()=>k(),[k]),(0,v.N)(()=>{let a=j().find(a=>a.ref.current===document.activeElement);a?.ref.current?.scrollIntoView({block:"nearest"})},[j]),(0,A.jsx)(r.sG.div,{"aria-hidden":!0,...f,ref:b,style:{flexShrink:0,...f.style},onPointerDown:(0,g.m)(f.onPointerDown,()=>{null===i.current&&(i.current=window.setInterval(e,50))}),onPointerMove:(0,g.m)(f.onPointerMove,()=>{h.onItemLeave?.(),null===i.current&&(i.current=window.setInterval(e,50))}),onPointerLeave:(0,g.m)(f.onPointerLeave,()=>{k()})})});d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a;return(0,A.jsx)(r.sG.div,{"aria-hidden":!0,...d,ref:b})}).displayName="SelectSeparator";var aw="SelectArrow";d.forwardRef((a,b)=>{let{__scopeSelect:c,...d}=a,e=J(c),f=L(aw,c),g=Y(aw,c);return f.open&&"popper"===g.position?(0,A.jsx)(p.i3,{...e,...d,ref:b}):null}).displayName=aw;var ax=d.forwardRef(({__scopeSelect:a,value:b,...c},e)=>{let f=d.useRef(null),g=(0,i.s)(e,f),h=(0,w.Z)(b);return d.useEffect(()=>{let a=f.current;if(!a)return;let c=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(h!==b&&c){let d=new Event("change",{bubbles:!0});c.call(a,b),a.dispatchEvent(d)}},[h,b]),(0,A.jsx)(r.sG.select,{...c,style:{...x.Qg,...c.style},ref:g,defaultValue:b})});function ay(a){return""===a||void 0===a}function az(a){let b=(0,t.c)(a),c=d.useRef(""),e=d.useRef(0),f=d.useCallback(a=>{let d=c.current+a;b(d),function a(b){c.current=b,window.clearTimeout(e.current),""!==b&&(e.current=window.setTimeout(()=>a(""),1e3))}(d)},[b]),g=d.useCallback(()=>{c.current="",window.clearTimeout(e.current)},[]);return d.useEffect(()=>()=>window.clearTimeout(e.current),[]),[c,f,g]}function aA(a,b,c){var d,e;let f=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,g=c?a.indexOf(c):-1,h=(d=a,e=Math.max(g,0),d.map((a,b)=>d[(e+b)%d.length]));1===f.length&&(h=h.filter(a=>a!==c));let i=h.find(a=>a.textValue.toLowerCase().startsWith(f.toLowerCase()));return i!==c?i:void 0}ax.displayName="SelectBubbleInput";var aB=O,aC=Q,aD=S,aE=T,aF=U,aG=W,aH=ae,aI=am,aJ=ao,aK=aq,aL=as,aM=au},78272:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},83721:(a,b,c)=>{c.d(b,{Z:()=>e});var d=c(43210);function e(a){let b=d.useRef({value:a,previous:a});return d.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}}};