exports.id=6926,exports.ids=[6926],exports.modules={4780:(a,b,c)=>{"use strict";c.d(b,{EJ:()=>h,Yq:()=>g,cn:()=>f});var d=c(49384),e=c(82348);function f(...a){return(0,e.QP)((0,d.$)(a))}function g(a){return new Date(a).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function h(a,b){return a.length<=b?a:a.substring(0,b).trim()+"..."}},8647:(a,b,c)=>{Promise.resolve().then(c.bind(c,15888)),Promise.resolve().then(c.bind(c,64616))},12820:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>ah});var d=c(60687),e=c(82136),f=c(16189),g=c(43210),h=c(8730),i=c(24224),j=c(51214),k=c(4780),l=c(29523);c(89667),c(35950);var m=c(26134),n=c(11860);function o({...a}){return(0,d.jsx)(m.bL,{"data-slot":"sheet",...a})}function p({...a}){return(0,d.jsx)(m.ZL,{"data-slot":"sheet-portal",...a})}function q({className:a,...b}){return(0,d.jsx)(m.hJ,{"data-slot":"sheet-overlay",className:(0,k.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...b})}function r({className:a,children:b,side:c="right",...e}){return(0,d.jsxs)(p,{children:[(0,d.jsx)(q,{}),(0,d.jsxs)(m.UC,{"data-slot":"sheet-content",className:(0,k.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===c&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===c&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===c&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===c&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",a),...e,children:[b,(0,d.jsxs)(m.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,d.jsx)(n.A,{className:"size-4"}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function s({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"sheet-header",className:(0,k.cn)("flex flex-col gap-1.5 p-4",a),...b})}function t({className:a,...b}){return(0,d.jsx)(m.hE,{"data-slot":"sheet-title",className:(0,k.cn)("text-foreground font-semibold",a),...b})}function u({className:a,...b}){return(0,d.jsx)(m.VY,{"data-slot":"sheet-description",className:(0,k.cn)("text-muted-foreground text-sm",a),...b})}var v=c(9989);function w({delayDuration:a=0,...b}){return(0,d.jsx)(v.Kq,{"data-slot":"tooltip-provider",delayDuration:a,...b})}function x({...a}){return(0,d.jsx)(w,{children:(0,d.jsx)(v.bL,{"data-slot":"tooltip",...a})})}function y({...a}){return(0,d.jsx)(v.l9,{"data-slot":"tooltip-trigger",...a})}function z({className:a,sideOffset:b=0,children:c,...e}){return(0,d.jsx)(v.ZL,{children:(0,d.jsxs)(v.UC,{"data-slot":"tooltip-content",sideOffset:b,className:(0,k.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",a),...e,children:[c,(0,d.jsx)(v.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}let A=g.createContext(null);function B(){let a=g.useContext(A);if(!a)throw Error("useSidebar must be used within a SidebarProvider.");return a}function C({defaultOpen:a=!0,open:b,onOpenChange:c,className:e,style:f,children:h,...i}){let j=function(){let[a,b]=g.useState(void 0);return g.useEffect(()=>{let a=window.matchMedia("(max-width: 767px)"),c=()=>{b(window.innerWidth<768)};return a.addEventListener("change",c),b(window.innerWidth<768),()=>a.removeEventListener("change",c)},[]),!!a}(),[l,m]=g.useState(!1),[n,o]=g.useState(a),p=b??n,q=g.useCallback(a=>{let b="function"==typeof a?a(p):a;c?c(b):o(b),document.cookie=`sidebar_state=${b}; path=/; max-age=604800`},[c,p]),r=g.useCallback(()=>j?m(a=>!a):q(a=>!a),[j,q,m]);g.useEffect(()=>{let a=a=>{"b"===a.key&&(a.metaKey||a.ctrlKey)&&(a.preventDefault(),r())};return window.addEventListener("keydown",a),()=>window.removeEventListener("keydown",a)},[r]);let s=p?"expanded":"collapsed",t=g.useMemo(()=>({state:s,open:p,setOpen:q,isMobile:j,openMobile:l,setOpenMobile:m,toggleSidebar:r}),[s,p,q,j,l,m,r]);return(0,d.jsx)(A.Provider,{value:t,children:(0,d.jsx)(w,{delayDuration:0,children:(0,d.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...f},className:(0,k.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",e),...i,children:h})})})}function D({side:a="left",variant:b="sidebar",collapsible:c="offcanvas",className:e,children:f,...g}){let{isMobile:h,state:i,openMobile:j,setOpenMobile:l}=B();return"none"===c?(0,d.jsx)("div",{"data-slot":"sidebar",className:(0,k.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",e),...g,children:f}):h?(0,d.jsx)(o,{open:j,onOpenChange:l,...g,children:(0,d.jsxs)(r,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:a,children:[(0,d.jsxs)(s,{className:"sr-only",children:[(0,d.jsx)(t,{children:"Sidebar"}),(0,d.jsx)(u,{children:"Displays the mobile sidebar."})]}),(0,d.jsx)("div",{className:"flex h-full w-full flex-col",children:f})]})}):(0,d.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":i,"data-collapsible":"collapsed"===i?c:"","data-variant":b,"data-side":a,"data-slot":"sidebar",children:[(0,d.jsx)("div",{"data-slot":"sidebar-gap",className:(0,k.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===b||"inset"===b?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,d.jsx)("div",{"data-slot":"sidebar-container",className:(0,k.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===a?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===b||"inset"===b?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",e),...g,children:(0,d.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:f})})]})}function E({className:a,onClick:b,...c}){let{toggleSidebar:e}=B();return(0,d.jsxs)(l.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,k.cn)("size-7",a),onClick:a=>{b?.(a),e()},...c,children:[(0,d.jsx)(j.A,{}),(0,d.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function F({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,k.cn)("flex flex-col gap-2 p-2",a),...b})}function G({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,k.cn)("flex flex-col gap-2 p-2",a),...b})}function H({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,k.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",a),...b})}function I({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,k.cn)("relative flex w-full min-w-0 flex-col p-2",a),...b})}function J({className:a,asChild:b=!1,...c}){let e=b?h.DX:"div";return(0,d.jsx)(e,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,k.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",a),...c})}function K({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,k.cn)("w-full text-sm",a),...b})}function L({className:a,...b}){return(0,d.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,k.cn)("flex w-full min-w-0 flex-col gap-1",a),...b})}function M({className:a,...b}){return(0,d.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,k.cn)("group/menu-item relative",a),...b})}let N=(0,i.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function O({asChild:a=!1,isActive:b=!1,variant:c="default",size:e="default",tooltip:f,className:g,...i}){let j=a?h.DX:"button",{isMobile:l,state:m}=B(),n=(0,d.jsx)(j,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":e,"data-active":b,className:(0,k.cn)(N({variant:c,size:e}),g),...i});return f?("string"==typeof f&&(f={children:f}),(0,d.jsxs)(x,{children:[(0,d.jsx)(y,{asChild:!0,children:n}),(0,d.jsx)(z,{side:"right",align:"center",hidden:"collapsed"!==m||l,...f})]})):n}var P=c(85814),Q=c.n(P),R=c(49625),S=c(10022),T=c(57800),U=c(19080),V=c(58887),W=c(23240),X=c(41312),Y=c(61611),Z=c(84027);let $=[{title:"Dashboard",url:"/dashboard",icon:R.A},{title:"Blog Posts",url:"/dashboard/blogs",icon:S.A},{title:"Services",url:"/dashboard/services",icon:T.A},{title:"Packages",url:"/dashboard/packages",icon:U.A},{title:"Testimonials",url:"/dashboard/testimonials",icon:V.A},{title:"Gallery",url:"/dashboard/gallery",icon:W.A},{title:"Users",url:"/dashboard/users",icon:X.A},{title:"Data Management",url:"/dashboard/data",icon:Y.A},{title:"Settings",url:"/dashboard/settings",icon:Z.A}];function _(){let a=(0,f.usePathname)();return(0,d.jsxs)(D,{children:[(0,d.jsx)(F,{children:(0,d.jsx)("div",{className:"px-4 py-2",children:(0,d.jsx)("h2",{className:"text-lg font-semibold",children:"Anjali CMS"})})}),(0,d.jsx)(H,{children:(0,d.jsxs)(I,{children:[(0,d.jsx)(J,{children:"Navigation"}),(0,d.jsx)(K,{children:(0,d.jsx)(L,{children:$.map(b=>(0,d.jsx)(M,{children:(0,d.jsx)(O,{asChild:!0,isActive:a===b.url,children:(0,d.jsxs)(Q(),{href:b.url,children:[(0,d.jsx)(b.icon,{className:"h-4 w-4"}),(0,d.jsx)("span",{children:b.title})]})})},b.title))})})]})}),(0,d.jsx)(G,{children:(0,d.jsx)("div",{className:"px-4 py-2 text-sm text-muted-foreground",children:"\xa9 2024 Anjali CMS"})})]})}var aa=c(21342),ab=c(11096);function ac({className:a,...b}){return(0,d.jsx)(ab.bL,{"data-slot":"avatar",className:(0,k.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",a),...b})}function ad({className:a,...b}){return(0,d.jsx)(ab.H4,{"data-slot":"avatar-fallback",className:(0,k.cn)("bg-muted flex size-full items-center justify-center rounded-full",a),...b})}var ae=c(58869),af=c(40083);function ag(){let{data:a}=(0,e.useSession)();return(0,d.jsx)("header",{className:"border-b bg-background px-6 py-3",children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)(E,{}),(0,d.jsx)("h1",{className:"text-xl font-semibold",children:"Content Management System"})]}),(0,d.jsx)("div",{className:"flex items-center gap-4",children:(0,d.jsxs)(aa.rI,{children:[(0,d.jsx)(aa.ty,{asChild:!0,children:(0,d.jsx)(l.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,d.jsx)(ac,{className:"h-8 w-8",children:(0,d.jsx)(ad,{children:a?.user?.name?.charAt(0)||a?.user?.email?.charAt(0)||"U"})})})}),(0,d.jsxs)(aa.SQ,{className:"w-56",align:"end",forceMount:!0,children:[(0,d.jsx)(aa.lp,{className:"font-normal",children:(0,d.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium leading-none",children:a?.user?.name||"User"}),(0,d.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:a?.user?.email})]})}),(0,d.jsx)(aa.mB,{}),(0,d.jsxs)(aa._2,{children:[(0,d.jsx)(ae.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Profile"})]}),(0,d.jsx)(aa.mB,{}),(0,d.jsxs)(aa._2,{onClick:()=>{(0,e.signOut)({callbackUrl:"/auth/signin"})},children:[(0,d.jsx)(af.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Log out"})]})]})]})})]})})}function ah({children:a}){let{data:b,status:c}=(0,e.useSession)();return"loading"===c?(0,d.jsx)("div",{children:"Loading..."}):(b||(0,f.redirect)("/auth/signin"),(0,d.jsx)(C,{children:(0,d.jsxs)("div",{className:"flex min-h-screen w-full",children:[(0,d.jsx)(_,{}),(0,d.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,d.jsx)(ag,{}),(0,d.jsx)("main",{className:"flex-1 p-6",children:a})]})]})}))}},15888:(a,b,c)=>{"use strict";c.d(b,{Providers:()=>f});var d=c(60687),e=c(82136);function f({children:a}){return(0,d.jsx)(e.SessionProvider,{children:a})}},18166:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,16133,23)),Promise.resolve().then(c.t.bind(c,16444,23)),Promise.resolve().then(c.t.bind(c,16042,23)),Promise.resolve().then(c.t.bind(c,49477,23)),Promise.resolve().then(c.t.bind(c,29345,23)),Promise.resolve().then(c.t.bind(c,12089,23)),Promise.resolve().then(c.t.bind(c,46577,23)),Promise.resolve().then(c.t.bind(c,31307,23)),Promise.resolve().then(c.t.bind(c,14817,23))},21342:(a,b,c)=>{"use strict";c.d(b,{SQ:()=>i,_2:()=>j,lp:()=>k,mB:()=>l,rI:()=>g,ty:()=>h});var d=c(60687);c(43210);var e=c(26312),f=c(4780);function g({...a}){return(0,d.jsx)(e.bL,{"data-slot":"dropdown-menu",...a})}function h({...a}){return(0,d.jsx)(e.l9,{"data-slot":"dropdown-menu-trigger",...a})}function i({className:a,sideOffset:b=4,...c}){return(0,d.jsx)(e.ZL,{children:(0,d.jsx)(e.UC,{"data-slot":"dropdown-menu-content",sideOffset:b,className:(0,f.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",a),...c})})}function j({className:a,inset:b,variant:c="default",...g}){return(0,d.jsx)(e.q7,{"data-slot":"dropdown-menu-item","data-inset":b,"data-variant":c,className:(0,f.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...g})}function k({className:a,inset:b,...c}){return(0,d.jsx)(e.JU,{"data-slot":"dropdown-menu-label","data-inset":b,className:(0,f.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",a),...c})}function l({className:a,...b}){return(0,d.jsx)(e.wv,{"data-slot":"dropdown-menu-separator",className:(0,f.cn)("bg-border -mx-1 my-1 h-px",a),...b})}},25519:(a,b,c)=>{Promise.resolve().then(c.bind(c,48150)),Promise.resolve().then(c.bind(c,48482))},29523:(a,b,c)=>{"use strict";c.d(b,{$:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function i({className:a,variant:b,size:c,asChild:f=!1,...i}){let j=f?e.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,g.cn)(h({variant:b,size:c,className:a})),...i})}},35950:(a,b,c)=>{"use strict";c.d(b,{w:()=>g});var d=c(60687);c(43210);var e=c(62369),f=c(4780);function g({className:a,orientation:b="horizontal",decorative:c=!0,...g}){return(0,d.jsx)(e.b,{"data-slot":"separator",decorative:c,orientation:b,className:(0,f.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",a),...g})}},46734:(a,b,c)=>{Promise.resolve().then(c.bind(c,63144))},48150:(a,b,c)=>{"use strict";c.d(b,{Providers:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Providers() from the server but Providers is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali-cms\\src\\components\\providers.tsx","Providers")},48482:(a,b,c)=>{"use strict";c.d(b,{Toaster:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali-cms\\src\\components\\ui\\sonner.tsx","Toaster")},61135:()=>{},63144:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\anjali-portfolio\\\\anjali-cms\\\\src\\\\app\\\\dashboard\\\\layout.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali-cms\\src\\app\\dashboard\\layout.tsx","default")},64616:(a,b,c)=>{"use strict";c.d(b,{Toaster:()=>g});var d=c(60687),e=c(10218),f=c(52581);let g=({...a})=>{let{theme:b="system"}=(0,e.D)();return(0,d.jsx)(f.l$,{theme:b,className:"toaster group",style:{"--normal-bg":"var(--popover)","--normal-text":"var(--popover-foreground)","--normal-border":"var(--border)"},...a})}},65022:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,25227,23)),Promise.resolve().then(c.t.bind(c,86346,23)),Promise.resolve().then(c.t.bind(c,27924,23)),Promise.resolve().then(c.t.bind(c,40099,23)),Promise.resolve().then(c.t.bind(c,38243,23)),Promise.resolve().then(c.t.bind(c,28827,23)),Promise.resolve().then(c.t.bind(c,62763,23)),Promise.resolve().then(c.t.bind(c,97173,23)),Promise.resolve().then(c.bind(c,25587))},70440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(31658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},86486:(a,b,c)=>{Promise.resolve().then(c.bind(c,12820))},89667:(a,b,c)=>{"use strict";c.d(b,{p:()=>f});var d=c(60687);c(43210);var e=c(4780);function f({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,e.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}},94431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>l,metadata:()=>k});var d=c(37413),e=c(22376),f=c.n(e),g=c(68726),h=c.n(g);c(61135);var i=c(48150),j=c(48482);let k={title:"Anjali CMS",description:"Content Management System for Anjali Makeup Artist"};function l({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:(0,d.jsxs)(i.Providers,{children:[a,(0,d.jsx)(j.Toaster,{})]})})})}}};