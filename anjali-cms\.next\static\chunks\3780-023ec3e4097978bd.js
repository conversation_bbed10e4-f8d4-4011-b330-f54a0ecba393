"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3780],{1154:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},4416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4616:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},5695:(e,t,r)=>{var n=r(8999);r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},6981:(e,t,r)=>{r.d(t,{C1:()=>N,bL:()=>b});var n=r(2115),a=r(6101),s=r(6081),i=r(5185),l=r(5845),d=r(5503),o=r(1275),c=r(8905),u=r(3655),h=r(5155),m="Checkbox",[p,x]=(0,s.A)(m),[j,g]=p(m);function f(e){let{__scopeCheckbox:t,checked:r,children:a,defaultChecked:s,disabled:i,form:d,name:o,onCheckedChange:c,required:u,value:p="on",internal_do_not_use_render:x}=e,[g,f]=(0,l.i)({prop:r,defaultProp:null!=s&&s,onChange:c,caller:m}),[v,y]=n.useState(null),[b,k]=n.useState(null),N=n.useRef(!1),C=!v||!!d||!!v.closest("form"),T={checked:g,disabled:i,setChecked:f,control:v,setControl:y,name:o,form:d,value:p,hasConsumerStoppedPropagationRef:N,required:u,defaultChecked:!A(s)&&s,isFormControl:C,bubbleInput:b,setBubbleInput:k};return(0,h.jsx)(j,{scope:t,...T,children:"function"==typeof x?x(T):a})}var v="CheckboxTrigger",y=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,onKeyDown:s,onClick:l,...d}=e,{control:o,value:c,disabled:m,checked:p,required:x,setControl:j,setChecked:f,hasConsumerStoppedPropagationRef:y,isFormControl:b,bubbleInput:k}=g(v,r),N=(0,a.s)(t,j),C=n.useRef(p);return n.useEffect(()=>{let e=null==o?void 0:o.form;if(e){let t=()=>f(C.current);return e.addEventListener("reset",t),()=>e.removeEventListener("reset",t)}},[o,f]),(0,h.jsx)(u.sG.button,{type:"button",role:"checkbox","aria-checked":A(p)?"mixed":p,"aria-required":x,"data-state":R(p),"data-disabled":m?"":void 0,disabled:m,value:c,...d,ref:N,onKeyDown:(0,i.m)(s,e=>{"Enter"===e.key&&e.preventDefault()}),onClick:(0,i.m)(l,e=>{f(e=>!!A(e)||!e),k&&b&&(y.current=e.isPropagationStopped(),y.current||e.stopPropagation())})})});y.displayName=v;var b=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,name:n,checked:a,defaultChecked:s,required:i,disabled:l,value:d,onCheckedChange:o,form:c,...u}=e;return(0,h.jsx)(f,{__scopeCheckbox:r,checked:a,defaultChecked:s,disabled:l,required:i,onCheckedChange:o,name:n,form:c,value:d,internal_do_not_use_render:e=>{let{isFormControl:n}=e;return(0,h.jsxs)(h.Fragment,{children:[(0,h.jsx)(y,{...u,ref:t,__scopeCheckbox:r}),n&&(0,h.jsx)(T,{__scopeCheckbox:r})]})}})});b.displayName=m;var k="CheckboxIndicator",N=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,forceMount:n,...a}=e,s=g(k,r);return(0,h.jsx)(c.C,{present:n||A(s.checked)||!0===s.checked,children:(0,h.jsx)(u.sG.span,{"data-state":R(s.checked),"data-disabled":s.disabled?"":void 0,...a,ref:t,style:{pointerEvents:"none",...e.style}})})});N.displayName=k;var C="CheckboxBubbleInput",T=n.forwardRef((e,t)=>{let{__scopeCheckbox:r,...s}=e,{control:i,hasConsumerStoppedPropagationRef:l,checked:c,defaultChecked:m,required:p,disabled:x,name:j,value:f,form:v,bubbleInput:y,setBubbleInput:b}=g(C,r),k=(0,a.s)(t,b),N=(0,d.Z)(c),T=(0,o.X)(i);n.useEffect(()=>{if(!y)return;let e=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,t=!l.current;if(N!==c&&e){let r=new Event("click",{bubbles:t});y.indeterminate=A(c),e.call(y,!A(c)&&c),y.dispatchEvent(r)}},[y,N,c,l]);let R=n.useRef(!A(c)&&c);return(0,h.jsx)(u.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:null!=m?m:R.current,required:p,disabled:x,name:j,value:f,form:v,...s,tabIndex:-1,ref:k,style:{...s.style,...T,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function A(e){return"indeterminate"===e}function R(e){return A(e)?"indeterminate":e?"checked":"unchecked"}T.displayName=C},7262:(e,t,r)=>{r.d(t,{S:()=>l});var n=r(5155);r(2115);var a=r(6981),s=r(5196),i=r(9434);function l(e){let{className:t,...r}=e;return(0,n.jsx)(a.bL,{"data-slot":"checkbox",className:(0,i.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",t),...r,children:(0,n.jsx)(a.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,n.jsx)(s.A,{className:"size-3.5"})})})}},7605:(e,t,r)=>{r.d(t,{GalleryForm:()=>b});var n=r(5155),a=r(2115),s=r(5695),i=r(2177),l=r(221),d=r(690),o=r(285),c=r(2523),u=r(8539),h=r(7262),m=r(9409),p=r(7759),x=r(6695),j=r(2154),g=r(6671),f=r(4416),v=r(4616);let y=d.Ik({title:d.Yj().min(1,"Title is required"),description:d.Yj().optional(),image:d.Yj().min(1,"Image is required"),category:d.Yj().optional(),tags:d.YO(d.Ik({value:d.Yj().min(1,"Tag cannot be empty")})).default([]),featured:d.zM().default(!1),status:d.k5(["ACTIVE","INACTIVE","ARCHIVED"]).default("ACTIVE")});function b(e){var t;let{initialData:r,isEditing:d=!1}=e,b=(0,s.useRouter)(),[k,N]=(0,a.useState)(!1),C=(0,i.mN)({resolver:(0,l.u)(y),defaultValues:{title:(null==r?void 0:r.title)||"",description:(null==r?void 0:r.description)||"",image:(null==r?void 0:r.image)||"",category:(null==r?void 0:r.category)||"",tags:(null==r||null==(t=r.tags)?void 0:t.map(e=>({value:e})))||[],featured:(null==r?void 0:r.featured)||!1,status:(null==r?void 0:r.status)||"ACTIVE"}}),{fields:T,append:A,remove:R}=(0,i.jz)({control:C.control,name:"tags"}),I=async e=>{N(!0);try{let t={...e,tags:e.tags.map(e=>e.value).filter(e=>""!==e.trim())},n=d?"/api/gallery/".concat(null==r?void 0:r.id):"/api/gallery",a=await fetch(n,{method:d?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(a.ok)g.oR.success(d?"Gallery item updated successfully":"Gallery item created successfully"),b.push("/dashboard/gallery");else{let e=await a.json();g.oR.error(e.error||"Something went wrong")}}catch(e){g.oR.error("Error saving gallery item")}finally{N(!1)}};return(0,n.jsx)(p.lV,{...C,children:(0,n.jsx)("form",{onSubmit:C.handleSubmit(I),className:"space-y-6",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,n.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,n.jsxs)(x.Zp,{children:[(0,n.jsxs)(x.aR,{children:[(0,n.jsx)(x.ZB,{children:"Image Upload"}),(0,n.jsx)(x.BT,{children:"Upload the main image for your gallery"})]}),(0,n.jsx)(x.Wu,{children:(0,n.jsx)(p.zB,{control:C.control,name:"image",render:e=>{let{field:t}=e;return(0,n.jsxs)(p.eI,{children:[(0,n.jsx)(p.MJ,{children:(0,n.jsx)(j.B,{value:t.value,onChange:t.onChange,onRemove:()=>t.onChange(""),folder:"gallery"})}),(0,n.jsx)(p.C5,{})]})}})})]}),(0,n.jsxs)(x.Zp,{children:[(0,n.jsxs)(x.aR,{children:[(0,n.jsx)(x.ZB,{children:"Image Details"}),(0,n.jsx)(x.BT,{children:"Add details and metadata for your image"})]}),(0,n.jsxs)(x.Wu,{className:"space-y-4",children:[(0,n.jsx)(p.zB,{control:C.control,name:"title",render:e=>{let{field:t}=e;return(0,n.jsxs)(p.eI,{children:[(0,n.jsx)(p.lR,{children:"Title"}),(0,n.jsx)(p.MJ,{children:(0,n.jsx)(c.p,{placeholder:"Enter image title",...t})}),(0,n.jsx)(p.C5,{})]})}}),(0,n.jsx)(p.zB,{control:C.control,name:"description",render:e=>{let{field:t}=e;return(0,n.jsxs)(p.eI,{children:[(0,n.jsx)(p.lR,{children:"Description (Optional)"}),(0,n.jsx)(p.MJ,{children:(0,n.jsx)(u.T,{placeholder:"Describe this image or the work shown",className:"min-h-[100px]",...t})}),(0,n.jsx)(p.Rr,{children:"Add context about the makeup look, occasion, or techniques used"}),(0,n.jsx)(p.C5,{})]})}}),(0,n.jsx)(p.zB,{control:C.control,name:"category",render:e=>{let{field:t}=e;return(0,n.jsxs)(p.eI,{children:[(0,n.jsx)(p.lR,{children:"Category (Optional)"}),(0,n.jsx)(p.MJ,{children:(0,n.jsx)(c.p,{placeholder:"e.g., Bridal, Party, Traditional, Editorial",...t})}),(0,n.jsx)(p.Rr,{children:"Categorize this image for better organization"}),(0,n.jsx)(p.C5,{})]})}})]})]}),(0,n.jsxs)(x.Zp,{children:[(0,n.jsxs)(x.aR,{children:[(0,n.jsx)(x.ZB,{children:"Tags"}),(0,n.jsx)(x.BT,{children:"Add tags to help categorize and search for this image"})]}),(0,n.jsxs)(x.Wu,{className:"space-y-4",children:[T.map((e,t)=>(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)(p.zB,{control:C.control,name:"tags.".concat(t,".value"),render:e=>{let{field:t}=e;return(0,n.jsxs)(p.eI,{className:"flex-1",children:[(0,n.jsx)(p.MJ,{children:(0,n.jsx)(c.p,{placeholder:"e.g., smokey eyes, natural look, bold lips",...t})}),(0,n.jsx)(p.C5,{})]})}}),(0,n.jsx)(o.$,{type:"button",variant:"outline",size:"sm",onClick:()=>R(t),children:(0,n.jsx)(f.A,{className:"h-4 w-4"})})]},e.id)),(0,n.jsxs)(o.$,{type:"button",variant:"outline",size:"sm",onClick:()=>A({value:""}),children:[(0,n.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"Add Tag"]}),0===T.length&&(0,n.jsx)("div",{className:"text-sm text-muted-foreground",children:'No tags added yet. Click "Add Tag" to start adding tags.'})]})]})]}),(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsxs)(x.Zp,{children:[(0,n.jsxs)(x.aR,{children:[(0,n.jsx)(x.ZB,{children:"Settings"}),(0,n.jsx)(x.BT,{children:"Configure visibility and status settings"})]}),(0,n.jsxs)(x.Wu,{className:"space-y-4",children:[(0,n.jsx)(p.zB,{control:C.control,name:"status",render:e=>{let{field:t}=e;return(0,n.jsxs)(p.eI,{children:[(0,n.jsx)(p.lR,{children:"Status"}),(0,n.jsxs)(m.l6,{onValueChange:t.onChange,defaultValue:t.value,children:[(0,n.jsx)(p.MJ,{children:(0,n.jsx)(m.bq,{children:(0,n.jsx)(m.yv,{placeholder:"Select status"})})}),(0,n.jsxs)(m.gC,{children:[(0,n.jsx)(m.eb,{value:"ACTIVE",children:"Active"}),(0,n.jsx)(m.eb,{value:"INACTIVE",children:"Inactive"}),(0,n.jsx)(m.eb,{value:"ARCHIVED",children:"Archived"})]})]}),(0,n.jsx)(p.Rr,{children:"Only active images will be visible in the public gallery"}),(0,n.jsx)(p.C5,{})]})}}),(0,n.jsx)(p.zB,{control:C.control,name:"featured",render:e=>{let{field:t}=e;return(0,n.jsxs)(p.eI,{className:"flex flex-row items-start space-x-3 space-y-0",children:[(0,n.jsx)(p.MJ,{children:(0,n.jsx)(h.S,{checked:t.value,onCheckedChange:t.onChange})}),(0,n.jsxs)("div",{className:"space-y-1 leading-none",children:[(0,n.jsx)(p.lR,{children:"Featured Image"}),(0,n.jsx)(p.Rr,{children:"Mark this image as featured to highlight it in your portfolio"})]})]})}}),(0,n.jsxs)("div",{className:"flex gap-2",children:[(0,n.jsx)(o.$,{type:"submit",disabled:k,children:k?"Saving...":d?"Update":"Add to Gallery"}),(0,n.jsx)(o.$,{type:"button",variant:"outline",onClick:()=>b.back(),children:"Cancel"})]})]})]}),(0,n.jsxs)(x.Zp,{children:[(0,n.jsx)(x.aR,{children:(0,n.jsx)(x.ZB,{children:"Tips"})}),(0,n.jsxs)(x.Wu,{className:"space-y-2 text-sm text-muted-foreground",children:[(0,n.jsx)("p",{children:"• Use high-quality images for the best showcase"}),(0,n.jsx)("p",{children:"• Add descriptive titles and tags for better organization"}),(0,n.jsx)("p",{children:"• Featured images appear prominently in your portfolio"}),(0,n.jsx)("p",{children:"• Categories help visitors find specific types of work"}),(0,n.jsx)("p",{children:"• Tags make images searchable and discoverable"})]})]})]})]})})})}},8905:(e,t,r)=>{r.d(t,{C:()=>i});var n=r(2115),a=r(6101),s=r(2712),i=e=>{let{present:t,children:r}=e,i=function(e){var t,r;let[a,i]=n.useState(),d=n.useRef(null),o=n.useRef(e),c=n.useRef("none"),[u,h]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},t));return n.useEffect(()=>{let e=l(d.current);c.current="mounted"===u?e:"none"},[u]),(0,s.N)(()=>{let t=d.current,r=o.current;if(r!==e){let n=c.current,a=l(t);e?h("MOUNT"):"none"===a||(null==t?void 0:t.display)==="none"?h("UNMOUNT"):r&&n!==a?h("ANIMATION_OUT"):h("UNMOUNT"),o.current=e}},[e,h]),(0,s.N)(()=>{if(a){var e;let t,r=null!=(e=a.ownerDocument.defaultView)?e:window,n=e=>{let n=l(d.current).includes(e.animationName);if(e.target===a&&n&&(h("ANIMATION_END"),!o.current)){let e=a.style.animationFillMode;a.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===a.style.animationFillMode&&(a.style.animationFillMode=e)})}},s=e=>{e.target===a&&(c.current=l(d.current))};return a.addEventListener("animationstart",s),a.addEventListener("animationcancel",n),a.addEventListener("animationend",n),()=>{r.clearTimeout(t),a.removeEventListener("animationstart",s),a.removeEventListener("animationcancel",n),a.removeEventListener("animationend",n)}}h("ANIMATION_END")},[a,h]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:n.useCallback(e=>{d.current=e?getComputedStyle(e):null,i(e)},[])}}(t),d="function"==typeof r?r({present:i.isPresent}):n.Children.only(r),o=(0,a.s)(i.ref,function(e){var t,r;let n=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,a=n&&"isReactWarning"in n&&n.isReactWarning;return a?e.ref:(a=(n=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in n&&n.isReactWarning)?e.props.ref:e.props.ref||e.ref}(d));return"function"==typeof r||i.isPresent?n.cloneElement(d,{ref:o}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},9869:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])}}]);