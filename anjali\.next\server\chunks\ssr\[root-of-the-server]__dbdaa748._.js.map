{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/badge.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Badge = registerClientReference(\n    function() { throw new Error(\"Attempted to call Badge() from the server but Badge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/badge.tsx <module evaluation>\",\n    \"Badge\",\n);\nexport const badgeVariants = registerClientReference(\n    function() { throw new Error(\"Attempted to call badgeVariants() from the server but badgeVariants is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/badge.tsx <module evaluation>\",\n    \"badgeVariants\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,6DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6DACA", "debugId": null}}, {"offset": {"line": 46, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/badge.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Badge = registerClientReference(\n    function() { throw new Error(\"Attempted to call Badge() from the server but Badge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/badge.tsx\",\n    \"Badge\",\n);\nexport const badgeVariants = registerClientReference(\n    function() { throw new Error(\"Attempted to call badgeVariants() from the server but badgeVariants is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/badge.tsx\",\n    \"badgeVariants\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,yCACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yCACA", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Section = registerClientReference(\n    function() { throw new Error(\"Attempted to call Section() from the server but Section is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/section.tsx <module evaluation>\",\n    \"Section\",\n);\nexport const SectionHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SectionHeader() from the server but SectionHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/section.tsx <module evaluation>\",\n    \"SectionHeader\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+DACA", "debugId": null}}, {"offset": {"line": 86, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Section = registerClientReference(\n    function() { throw new Error(\"Attempted to call Section() from the server but Section is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/section.tsx\",\n    \"Section\",\n);\nexport const SectionHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SectionHeader() from the server but SectionHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/section.tsx\",\n    \"SectionHeader\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2CACA", "debugId": null}}, {"offset": {"line": 102, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-hero.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { Star, Award, Users, MapPin } from 'lucide-react'\nimport { Badge } from '@/components/ui/badge'\nimport { Section } from '@/components/ui/section'\n\nexport default function AboutHero() {\n  return (\n    <Section className=\"pt-24 pb-16\">\n      <div className=\"grid lg:grid-cols-2 gap-12 items-center\">\n        {/* Content */}\n        <div className=\"space-y-8\">\n          <div className=\"space-y-4\">\n            <Badge variant=\"secondary\" className=\"text-sm px-4 py-2\">\n              <Award className=\"w-4 h-4 mr-2\" />\n              Professional Makeup Artist\n            </Badge>\n            \n            <h1 className=\"font-display text-4xl md:text-5xl lg:text-6xl font-bold text-text-primary leading-tight\">\n              Meet\n              <span className=\"block text-transparent bg-gradient-to-r from-rose-gold to-blush-pink bg-clip-text\">\n                Anja<PERSON>\n              </span>\n            </h1>\n            \n            <p className=\"text-xl text-text-secondary leading-relaxed\">\n              A passionate makeup artist dedicated to enhancing your natural beauty \n              and making every moment unforgettable. With over 5 years of experience \n              in the beauty industry, I specialize in creating stunning looks for \n              all your special occasions.\n            </p>\n          </div>\n\n          {/* Stats */}\n          <div className=\"grid grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-text-primary mb-1\">5+</div>\n              <div className=\"text-sm text-text-secondary\">Years Experience</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-text-primary mb-1\">100+</div>\n              <div className=\"text-sm text-text-secondary\">Happy Clients</div>\n            </div>\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-text-primary mb-1\">7</div>\n              <div className=\"text-sm text-text-secondary\">Cities Served</div>\n            </div>\n          </div>\n\n          {/* Highlights */}\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center gap-3\">\n              <Star className=\"w-5 h-5 text-yellow-400 fill-current\" />\n              <span className=\"text-text-secondary\">5.0 Average Rating from 50+ Reviews</span>\n            </div>\n            <div className=\"flex items-center gap-3\">\n              <Users className=\"w-5 h-5 text-rose-gold-dark\" />\n              <span className=\"text-text-secondary\">Trusted by Brides Across Nepal</span>\n            </div>\n            <div className=\"flex items-center gap-3\">\n              <MapPin className=\"w-5 h-5 text-rose-gold-dark\" />\n              <span className=\"text-text-secondary\">Serving Biratnagar, Itahari, Dharan & More</span>\n            </div>\n          </div>\n        </div>\n\n        {/* Image */}\n        <div className=\"relative\">\n          <div className=\"relative aspect-[4/5] rounded-2xl overflow-hidden shadow-2xl\">\n            <Image\n              src=\"https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=600&h=750&fit=crop&crop=face\"\n              alt=\"Anjali - Professional Makeup Artist\"\n              fill\n              className=\"object-cover\"\n              priority\n            />\n            \n            {/* Floating Elements */}\n            <div className=\"absolute top-6 right-6 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg\">\n              <div className=\"flex items-center gap-2\">\n                <div className=\"flex text-yellow-400\">\n                  {[...Array(5)].map((_, i) => (\n                    <Star key={i} className=\"w-3 h-3 fill-current\" />\n                  ))}\n                </div>\n                <span className=\"text-xs font-medium text-text-primary\">5.0</span>\n              </div>\n            </div>\n\n            <div className=\"absolute bottom-6 left-6 bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-lg\">\n              <div className=\"text-center\">\n                <div className=\"text-lg font-bold text-text-primary\">100+</div>\n                <div className=\"text-xs text-text-secondary\">Makeup Sessions</div>\n              </div>\n            </div>\n          </div>\n\n          {/* Decorative Elements */}\n          <div className=\"absolute -top-4 -left-4 w-24 h-24 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full opacity-20 blur-xl\"></div>\n          <div className=\"absolute -bottom-4 -right-4 w-32 h-32 bg-gradient-to-br from-lavender to-blush-pink rounded-full opacity-20 blur-xl\"></div>\n        </div>\n      </div>\n    </Section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,WAAU;kBACjB,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,iIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;;sDACnC,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAIpC,8OAAC;oCAAG,WAAU;;wCAA0F;sDAEtG,8OAAC;4CAAK,WAAU;sDAAoF;;;;;;;;;;;;8CAKtG,8OAAC;oCAAE,WAAU;8CAA8C;;;;;;;;;;;;sCAS7D,8OAAC;4BAA<PERSON>,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA4C;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;;;;;;;8CAE/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA4C;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;;;;;;;8CAE/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAA4C;;;;;;sDAC3D,8OAAC;4CAAI,WAAU;sDAA8B;;;;;;;;;;;;;;;;;;sCAKjD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAExC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,oMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAExC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;;;;;;;;;;;;;8BAM5C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,WAAU;oCACV,QAAQ;;;;;;8CAIV,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DACZ;uDAAI,MAAM;iDAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,8OAAC,kMAAA,CAAA,OAAI;wDAAS,WAAU;uDAAb;;;;;;;;;;0DAGf,8OAAC;gDAAK,WAAU;0DAAwC;;;;;;;;;;;;;;;;;8CAI5D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAsC;;;;;;0DACrD,8OAAC;gDAAI,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;sCAMnD,8OAAC;4BAAI,WAAU;;;;;;sCACf,8OAAC;4BAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 490, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/card.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Card = registerClientReference(\n    function() { throw new Error(\"Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx <module evaluation>\",\n    \"Card\",\n);\nexport const CardContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardContent() from the server but CardContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx <module evaluation>\",\n    \"CardContent\",\n);\nexport const CardDescription = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardDescription() from the server but CardDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx <module evaluation>\",\n    \"CardDescription\",\n);\nexport const CardFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardFooter() from the server but CardFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx <module evaluation>\",\n    \"CardFooter\",\n);\nexport const CardHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardHeader() from the server but CardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx <module evaluation>\",\n    \"CardHeader\",\n);\nexport const CardTitle = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardTitle() from the server but CardTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx <module evaluation>\",\n    \"CardTitle\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,4DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,4DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,4DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,4DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,4DACA", "debugId": null}}, {"offset": {"line": 522, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/card.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Card = registerClientReference(\n    function() { throw new Error(\"Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx\",\n    \"Card\",\n);\nexport const CardContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardContent() from the server but CardContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx\",\n    \"CardContent\",\n);\nexport const CardDescription = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardDescription() from the server but CardDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx\",\n    \"CardDescription\",\n);\nexport const CardFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardFooter() from the server but CardFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx\",\n    \"CardFooter\",\n);\nexport const CardHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardHeader() from the server but CardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx\",\n    \"CardHeader\",\n);\nexport const CardTitle = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardTitle() from the server but CardTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx\",\n    \"CardTitle\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,wCACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wCACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,wCACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,wCACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,wCACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,wCACA", "debugId": null}}, {"offset": {"line": 554, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 562, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-story.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { <PERSON>, <PERSON><PERSON><PERSON>, Target } from 'lucide-react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Section, SectionHeader } from '@/components/ui/section'\n\nconst values = [\n  {\n    icon: Heart,\n    title: 'Passion for Beauty',\n    description: 'Every makeup session is an opportunity to enhance natural beauty and boost confidence. I believe makeup is an art form that celebrates individuality.'\n  },\n  {\n    icon: Sparkles,\n    title: 'Attention to Detail',\n    description: 'From the perfect foundation match to the precise eyeliner application, every detail matters in creating a flawless, long-lasting look.'\n  },\n  {\n    icon: Target,\n    title: 'Client-Focused Approach',\n    description: 'Understanding your vision, preferences, and the occasion helps me create personalized looks that make you feel confident and beautiful.'\n  }\n]\n\nexport default function AboutStory() {\n  return (\n    <Section background=\"cream\">\n      <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n        {/* Story Content */}\n        <div className=\"space-y-8\">\n          <SectionHeader\n            subtitle=\"My Story\"\n            title=\"A Journey of Passion & Artistry\"\n            description=\"\"\n            centered={false}\n          />\n          \n          <div className=\"space-y-6 text-text-secondary leading-relaxed\">\n            <p>\n              My journey into the world of makeup artistry began over five years ago with a simple \n              fascination for colors, textures, and the transformative power of makeup. What started \n              as a hobby quickly evolved into a passionate career dedicated to helping people look \n              and feel their absolute best.\n            </p>\n            \n            <p>\n              Based in the beautiful city of Biratnagar, I&apos;ve had the privilege of working with \n              clients across Nepal, from intimate family gatherings to grand wedding celebrations. \n              Each client brings a unique story, and I consider myself fortunate to be part of \n              their special moments.\n            </p>\n            \n            <p>\n              My approach combines traditional techniques with modern trends, ensuring that every \n              look is both timeless and contemporary. Whether it&apos;s a bride&apos;s special day, a \n              professional photoshoot, or a festive celebration, I believe in creating makeup \n              that enhances natural beauty while reflecting personal style.\n            </p>\n            \n            <p>\n              Continuous learning is at the heart of my practice. I regularly update my skills \n              with the latest techniques and products, ensuring that my clients receive the best \n              possible service with current trends and high-quality products.\n            </p>\n          </div>\n        </div>\n\n        {/* Image Collage */}\n        <div className=\"relative\">\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"space-y-4\">\n              <div className=\"relative aspect-[3/4] rounded-xl overflow-hidden shadow-lg\">\n                <Image\n                  src=\"https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=400&fit=crop&crop=face\"\n                  alt=\"Makeup artistry work\"\n                  fill\n                  className=\"object-cover\"\n                />\n              </div>\n              <div className=\"relative aspect-square rounded-xl overflow-hidden shadow-lg\">\n                <Image\n                  src=\"https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=300&fit=crop&crop=face\"\n                  alt=\"Professional makeup session\"\n                  fill\n                  className=\"object-cover\"\n                />\n              </div>\n            </div>\n            <div className=\"space-y-4 pt-8\">\n              <div className=\"relative aspect-square rounded-xl overflow-hidden shadow-lg\">\n                <Image\n                  src=\"https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=300&fit=crop&crop=face\"\n                  alt=\"Bridal makeup\"\n                  fill\n                  className=\"object-cover\"\n                />\n              </div>\n              <div className=\"relative aspect-[3/4] rounded-xl overflow-hidden shadow-lg\">\n                <Image\n                  src=\"https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=400&fit=crop&crop=face\"\n                  alt=\"Traditional makeup\"\n                  fill\n                  className=\"object-cover\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Values Section */}\n      <div className=\"mt-20\">\n        <div>\n          <SectionHeader\n            subtitle=\"My Values\"\n            title=\"What Drives My Artistry\"\n            description=\"The principles that guide every makeup session and client interaction.\"\n          />\n        </div>\n\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          {values.map((value) => (\n            <div key={value.title}>\n              <Card className=\"h-full bg-white/80 backdrop-blur-sm border-0 hover:shadow-lg transition-shadow duration-300\">\n                <CardContent className=\"p-6 text-center space-y-4\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto\">\n                    <value.icon className=\"w-8 h-8 text-white\" />\n                  </div>\n                  <h3 className=\"font-display text-xl font-semibold text-text-primary\">\n                    {value.title}\n                  </h3>\n                  <p className=\"text-text-secondary leading-relaxed\">\n                    {value.description}\n                  </p>\n                </CardContent>\n              </Card>\n            </div>\n          ))}\n        </div>\n      </div>\n    </Section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;;AAEA,MAAM,SAAS;IACb;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,YAAW;;0BAClB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mIAAA,CAAA,gBAAa;gCACZ,UAAS;gCACT,OAAM;gCACN,aAAY;gCACZ,UAAU;;;;;;0CAGZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAE;;;;;;kDAOH,8OAAC;kDAAE;;;;;;kDAOH,8OAAC;kDAAE;;;;;;kDAOH,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;kCASP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;;;;;;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCACC,cAAA,8OAAC,mIAAA,CAAA,gBAAa;4BACZ,UAAS;4BACT,OAAM;4BACN,aAAY;;;;;;;;;;;kCAIhB,8OAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;0CACC,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,MAAM,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAExB,8OAAC;gDAAG,WAAU;0DACX,MAAM,KAAK;;;;;;0DAEd,8OAAC;gDAAE,WAAU;0DACV,MAAM,WAAW;;;;;;;;;;;;;;;;;+BAVhB,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;AAoBjC", "debugId": null}}, {"offset": {"line": 859, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-experience.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/about-experience.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/about-experience.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgT,GAC7U,8EACA", "debugId": null}}, {"offset": {"line": 871, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-experience.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/about-experience.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/about-experience.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4R,GACzT,0DACA", "debugId": null}}, {"offset": {"line": 883, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 891, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-cities.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/about-cities.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/about-cities.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-cities.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/about-cities.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/about-cities.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 915, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 923, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-cta.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/about-cta.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/about-cta.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyS,GACtU,uEACA", "debugId": null}}, {"offset": {"line": 935, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-cta.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/about-cta.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/about-cta.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqR,GAClT,mDACA", "debugId": null}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/app/about/page.tsx"], "sourcesContent": ["import AboutHero from '@/components/sections/about-hero'\nimport AboutStory from '@/components/sections/about-story'\nimport AboutExperience from '@/components/sections/about-experience'\nimport AboutCities from '@/components/sections/about-cities'\nimport AboutCTA from '@/components/sections/about-cta'\n\nexport default function AboutPage() {\n  return (\n    <>\n      <AboutHero />\n      <AboutStory />\n      <AboutExperience />\n      <AboutCities />\n      <AboutCTA />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC,+IAAA,CAAA,UAAS;;;;;0BACV,8OAAC,gJAAA,CAAA,UAAU;;;;;0BACX,8OAAC,qJAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,iJAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,8IAAA,CAAA,UAAQ;;;;;;;AAGf", "debugId": null}}]}