(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1954],{285:(e,t,a)=>{"use strict";a.d(t,{$:()=>o});var s=a(5155);a(2115);var i=a(9708),r=a(2085),n=a(9434);let d=(0,r.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function o(e){let{className:t,variant:a,size:r,asChild:o=!1,...l}=e,c=o?i.DX:"button";return(0,s.jsx)(c,{"data-slot":"button",className:(0,n.cn)(d({variant:a,size:r,className:t})),...l})}},2346:(e,t,a)=>{"use strict";a.d(t,{w:()=>n});var s=a(5155);a(2115);var i=a(7489),r=a(9434);function n(e){let{className:t,orientation:a="horizontal",decorative:n=!0,...d}=e;return(0,s.jsx)(i.b,{"data-slot":"separator",decorative:n,orientation:a,className:(0,r.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",t),...d})}},2523:(e,t,a)=>{"use strict";a.d(t,{p:()=>r});var s=a(5155);a(2115);var i=a(9434);function r(e){let{className:t,type:a,...r}=e;return(0,s.jsx)("input",{type:a,"data-slot":"input",className:(0,i.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...r})}},4082:(e,t,a)=>{Promise.resolve().then(a.bind(a,9286))},4838:(e,t,a)=>{"use strict";a.d(t,{SQ:()=>o,_2:()=>l,lp:()=>c,mB:()=>u,rI:()=>n,ty:()=>d});var s=a(5155);a(2115);var i=a(8698),r=a(9434);function n(e){let{...t}=e;return(0,s.jsx)(i.bL,{"data-slot":"dropdown-menu",...t})}function d(e){let{...t}=e;return(0,s.jsx)(i.l9,{"data-slot":"dropdown-menu-trigger",...t})}function o(e){let{className:t,sideOffset:a=4,...n}=e;return(0,s.jsx)(i.ZL,{children:(0,s.jsx)(i.UC,{"data-slot":"dropdown-menu-content",sideOffset:a,className:(0,r.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...n})})}function l(e){let{className:t,inset:a,variant:n="default",...d}=e;return(0,s.jsx)(i.q7,{"data-slot":"dropdown-menu-item","data-inset":a,"data-variant":n,className:(0,r.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...d})}function c(e){let{className:t,inset:a,...n}=e;return(0,s.jsx)(i.JU,{"data-slot":"dropdown-menu-label","data-inset":a,className:(0,r.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...n})}function u(e){let{className:t,...a}=e;return(0,s.jsx)(i.wv,{"data-slot":"dropdown-menu-separator",className:(0,r.cn)("bg-border -mx-1 my-1 h-px",t),...a})}},9286:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>ed});var s=a(5155),i=a(2108),r=a(5695),n=a(2115),d=a(9708),o=a(2085),l=a(2432),c=a(9434),u=a(285);a(2523),a(2346);var f=a(5452),x=a(4416);function h(e){let{...t}=e;return(0,s.jsx)(f.bL,{"data-slot":"sheet",...t})}function b(e){let{...t}=e;return(0,s.jsx)(f.ZL,{"data-slot":"sheet-portal",...t})}function m(e){let{className:t,...a}=e;return(0,s.jsx)(f.hJ,{"data-slot":"sheet-overlay",className:(0,c.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...a})}function g(e){let{className:t,children:a,side:i="right",...r}=e;return(0,s.jsxs)(b,{children:[(0,s.jsx)(m,{}),(0,s.jsxs)(f.UC,{"data-slot":"sheet-content",className:(0,c.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===i&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===i&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===i&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===i&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...r,children:[a,(0,s.jsxs)(f.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,s.jsx)(x.A,{className:"size-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}function p(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sheet-header",className:(0,c.cn)("flex flex-col gap-1.5 p-4",t),...a})}function v(e){let{className:t,...a}=e;return(0,s.jsx)(f.hE,{"data-slot":"sheet-title",className:(0,c.cn)("text-foreground font-semibold",t),...a})}function j(e){let{className:t,...a}=e;return(0,s.jsx)(f.VY,{"data-slot":"sheet-description",className:(0,c.cn)("text-muted-foreground text-sm",t),...a})}var w=a(9613);function y(e){let{delayDuration:t=0,...a}=e;return(0,s.jsx)(w.Kq,{"data-slot":"tooltip-provider",delayDuration:t,...a})}function N(e){let{...t}=e;return(0,s.jsx)(y,{children:(0,s.jsx)(w.bL,{"data-slot":"tooltip",...t})})}function k(e){let{...t}=e;return(0,s.jsx)(w.l9,{"data-slot":"tooltip-trigger",...t})}function z(e){let{className:t,sideOffset:a=0,children:i,...r}=e;return(0,s.jsx)(w.ZL,{children:(0,s.jsxs)(w.UC,{"data-slot":"tooltip-content",sideOffset:a,className:(0,c.cn)("bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance",t),...r,children:[i,(0,s.jsx)(w.i3,{className:"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]"})]})})}let _=n.createContext(null);function C(){let e=n.useContext(_);if(!e)throw Error("useSidebar must be used within a SidebarProvider.");return e}function A(e){let{defaultOpen:t=!0,open:a,onOpenChange:i,className:r,style:d,children:o,...l}=e,u=function(){let[e,t]=n.useState(void 0);return n.useEffect(()=>{let e=window.matchMedia("(max-width: ".concat(767,"px)")),a=()=>{t(window.innerWidth<768)};return e.addEventListener("change",a),t(window.innerWidth<768),()=>e.removeEventListener("change",a)},[]),!!e}(),[f,x]=n.useState(!1),[h,b]=n.useState(t),m=null!=a?a:h,g=n.useCallback(e=>{let t="function"==typeof e?e(m):e;i?i(t):b(t),document.cookie="".concat("sidebar_state","=").concat(t,"; path=/; max-age=").concat(604800)},[i,m]),p=n.useCallback(()=>u?x(e=>!e):g(e=>!e),[u,g,x]);n.useEffect(()=>{let e=e=>{"b"===e.key&&(e.metaKey||e.ctrlKey)&&(e.preventDefault(),p())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)},[p]);let v=m?"expanded":"collapsed",j=n.useMemo(()=>({state:v,open:m,setOpen:g,isMobile:u,openMobile:f,setOpenMobile:x,toggleSidebar:p}),[v,m,g,u,f,x,p]);return(0,s.jsx)(_.Provider,{value:j,children:(0,s.jsx)(y,{delayDuration:0,children:(0,s.jsx)("div",{"data-slot":"sidebar-wrapper",style:{"--sidebar-width":"16rem","--sidebar-width-icon":"3rem",...d},className:(0,c.cn)("group/sidebar-wrapper has-data-[variant=inset]:bg-sidebar flex min-h-svh w-full",r),...l,children:o})})})}function S(e){let{side:t="left",variant:a="sidebar",collapsible:i="offcanvas",className:r,children:n,...d}=e,{isMobile:o,state:l,openMobile:u,setOpenMobile:f}=C();return"none"===i?(0,s.jsx)("div",{"data-slot":"sidebar",className:(0,c.cn)("bg-sidebar text-sidebar-foreground flex h-full w-(--sidebar-width) flex-col",r),...d,children:n}):o?(0,s.jsx)(h,{open:u,onOpenChange:f,...d,children:(0,s.jsxs)(g,{"data-sidebar":"sidebar","data-slot":"sidebar","data-mobile":"true",className:"bg-sidebar text-sidebar-foreground w-(--sidebar-width) p-0 [&>button]:hidden",style:{"--sidebar-width":"18rem"},side:t,children:[(0,s.jsxs)(p,{className:"sr-only",children:[(0,s.jsx)(v,{children:"Sidebar"}),(0,s.jsx)(j,{children:"Displays the mobile sidebar."})]}),(0,s.jsx)("div",{className:"flex h-full w-full flex-col",children:n})]})}):(0,s.jsxs)("div",{className:"group peer text-sidebar-foreground hidden md:block","data-state":l,"data-collapsible":"collapsed"===l?i:"","data-variant":a,"data-side":t,"data-slot":"sidebar",children:[(0,s.jsx)("div",{"data-slot":"sidebar-gap",className:(0,c.cn)("relative w-(--sidebar-width) bg-transparent transition-[width] duration-200 ease-linear","group-data-[collapsible=offcanvas]:w-0","group-data-[side=right]:rotate-180","floating"===a||"inset"===a?"group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4)))]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon)")}),(0,s.jsx)("div",{"data-slot":"sidebar-container",className:(0,c.cn)("fixed inset-y-0 z-10 hidden h-svh w-(--sidebar-width) transition-[left,right,width] duration-200 ease-linear md:flex","left"===t?"left-0 group-data-[collapsible=offcanvas]:left-[calc(var(--sidebar-width)*-1)]":"right-0 group-data-[collapsible=offcanvas]:right-[calc(var(--sidebar-width)*-1)]","floating"===a||"inset"===a?"p-2 group-data-[collapsible=icon]:w-[calc(var(--sidebar-width-icon)+(--spacing(4))+2px)]":"group-data-[collapsible=icon]:w-(--sidebar-width-icon) group-data-[side=left]:border-r group-data-[side=right]:border-l",r),...d,children:(0,s.jsx)("div",{"data-sidebar":"sidebar","data-slot":"sidebar-inner",className:"bg-sidebar group-data-[variant=floating]:border-sidebar-border flex h-full w-full flex-col group-data-[variant=floating]:rounded-lg group-data-[variant=floating]:border group-data-[variant=floating]:shadow-sm",children:n})})]})}function L(e){let{className:t,onClick:a,...i}=e,{toggleSidebar:r}=C();return(0,s.jsxs)(u.$,{"data-sidebar":"trigger","data-slot":"sidebar-trigger",variant:"ghost",size:"icon",className:(0,c.cn)("size-7",t),onClick:e=>{null==a||a(e),r()},...i,children:[(0,s.jsx)(l.A,{}),(0,s.jsx)("span",{className:"sr-only",children:"Toggle Sidebar"})]})}function E(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-header","data-sidebar":"header",className:(0,c.cn)("flex flex-col gap-2 p-2",t),...a})}function D(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-footer","data-sidebar":"footer",className:(0,c.cn)("flex flex-col gap-2 p-2",t),...a})}function U(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-content","data-sidebar":"content",className:(0,c.cn)("flex min-h-0 flex-1 flex-col gap-2 overflow-auto group-data-[collapsible=icon]:overflow-hidden",t),...a})}function P(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-group","data-sidebar":"group",className:(0,c.cn)("relative flex w-full min-w-0 flex-col p-2",t),...a})}function M(e){let{className:t,asChild:a=!1,...i}=e,r=a?d.DX:"div";return(0,s.jsx)(r,{"data-slot":"sidebar-group-label","data-sidebar":"group-label",className:(0,c.cn)("text-sidebar-foreground/70 ring-sidebar-ring flex h-8 shrink-0 items-center rounded-md px-2 text-xs font-medium outline-hidden transition-[margin,opacity] duration-200 ease-linear focus-visible:ring-2 [&>svg]:size-4 [&>svg]:shrink-0","group-data-[collapsible=icon]:-mt-8 group-data-[collapsible=icon]:opacity-0",t),...i})}function O(e){let{className:t,...a}=e;return(0,s.jsx)("div",{"data-slot":"sidebar-group-content","data-sidebar":"group-content",className:(0,c.cn)("w-full text-sm",t),...a})}function B(e){let{className:t,...a}=e;return(0,s.jsx)("ul",{"data-slot":"sidebar-menu","data-sidebar":"menu",className:(0,c.cn)("flex w-full min-w-0 flex-col gap-1",t),...a})}function $(e){let{className:t,...a}=e;return(0,s.jsx)("li",{"data-slot":"sidebar-menu-item","data-sidebar":"menu-item",className:(0,c.cn)("group/menu-item relative",t),...a})}let q=(0,o.F)("peer/menu-button flex w-full items-center gap-2 overflow-hidden rounded-md p-2 text-left text-sm outline-hidden ring-sidebar-ring transition-[width,height,padding] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground focus-visible:ring-2 active:bg-sidebar-accent active:text-sidebar-accent-foreground disabled:pointer-events-none disabled:opacity-50 group-has-data-[sidebar=menu-action]/menu-item:pr-8 aria-disabled:pointer-events-none aria-disabled:opacity-50 data-[active=true]:bg-sidebar-accent data-[active=true]:font-medium data-[active=true]:text-sidebar-accent-foreground data-[state=open]:hover:bg-sidebar-accent data-[state=open]:hover:text-sidebar-accent-foreground group-data-[collapsible=icon]:size-8! group-data-[collapsible=icon]:p-2! [&>span:last-child]:truncate [&>svg]:size-4 [&>svg]:shrink-0",{variants:{variant:{default:"hover:bg-sidebar-accent hover:text-sidebar-accent-foreground",outline:"bg-background shadow-[0_0_0_1px_hsl(var(--sidebar-border))] hover:bg-sidebar-accent hover:text-sidebar-accent-foreground hover:shadow-[0_0_0_1px_hsl(var(--sidebar-accent))]"},size:{default:"h-8 text-sm",sm:"h-7 text-xs",lg:"h-12 text-sm group-data-[collapsible=icon]:p-0!"}},defaultVariants:{variant:"default",size:"default"}});function J(e){let{asChild:t=!1,isActive:a=!1,variant:i="default",size:r="default",tooltip:n,className:o,...l}=e,u=t?d.DX:"button",{isMobile:f,state:x}=C(),h=(0,s.jsx)(u,{"data-slot":"sidebar-menu-button","data-sidebar":"menu-button","data-size":r,"data-active":a,className:(0,c.cn)(q({variant:i,size:r}),o),...l});return n?("string"==typeof n&&(n={children:n}),(0,s.jsxs)(N,{children:[(0,s.jsx)(k,{asChild:!0,children:h}),(0,s.jsx)(z,{side:"right",align:"center",hidden:"collapsed"!==x||f,...n})]})):h}var K=a(6874),Q=a.n(K),V=a(3783),X=a(7434),Z=a(7576),F=a(7108),I=a(1497),T=a(5562),W=a(7580),Y=a(4213),G=a(381);let H=[{title:"Dashboard",url:"/dashboard",icon:V.A},{title:"Blog Posts",url:"/dashboard/blogs",icon:X.A},{title:"Services",url:"/dashboard/services",icon:Z.A},{title:"Packages",url:"/dashboard/packages",icon:F.A},{title:"Testimonials",url:"/dashboard/testimonials",icon:I.A},{title:"Gallery",url:"/dashboard/gallery",icon:T.A},{title:"Users",url:"/dashboard/users",icon:W.A},{title:"Data Management",url:"/dashboard/data",icon:Y.A},{title:"Settings",url:"/dashboard/settings",icon:G.A}];function R(){let e=(0,r.usePathname)();return(0,s.jsxs)(S,{children:[(0,s.jsx)(E,{children:(0,s.jsx)("div",{className:"px-4 py-2",children:(0,s.jsx)("h2",{className:"text-lg font-semibold",children:"Anjali CMS"})})}),(0,s.jsx)(U,{children:(0,s.jsxs)(P,{children:[(0,s.jsx)(M,{children:"Navigation"}),(0,s.jsx)(O,{children:(0,s.jsx)(B,{children:H.map(t=>(0,s.jsx)($,{children:(0,s.jsx)(J,{asChild:!0,isActive:e===t.url,children:(0,s.jsxs)(Q(),{href:t.url,children:[(0,s.jsx)(t.icon,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:t.title})]})})},t.title))})})]})}),(0,s.jsx)(D,{children:(0,s.jsx)("div",{className:"px-4 py-2 text-sm text-muted-foreground",children:"\xa9 2024 Anjali CMS"})})]})}var ee=a(4838),et=a(4011);function ea(e){let{className:t,...a}=e;return(0,s.jsx)(et.bL,{"data-slot":"avatar",className:(0,c.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...a})}function es(e){let{className:t,...a}=e;return(0,s.jsx)(et.H4,{"data-slot":"avatar-fallback",className:(0,c.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...a})}var ei=a(1007),er=a(4835);function en(){var e,t,a,r,n,d;let{data:o}=(0,i.useSession)();return(0,s.jsx)("header",{className:"border-b bg-background px-6 py-3",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center gap-4",children:[(0,s.jsx)(L,{}),(0,s.jsx)("h1",{className:"text-xl font-semibold",children:"Content Management System"})]}),(0,s.jsx)("div",{className:"flex items-center gap-4",children:(0,s.jsxs)(ee.rI,{children:[(0,s.jsx)(ee.ty,{asChild:!0,children:(0,s.jsx)(u.$,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,s.jsx)(ea,{className:"h-8 w-8",children:(0,s.jsx)(es,{children:(null==o||null==(t=o.user)||null==(e=t.name)?void 0:e.charAt(0))||(null==o||null==(r=o.user)||null==(a=r.email)?void 0:a.charAt(0))||"U"})})})}),(0,s.jsxs)(ee.SQ,{className:"w-56",align:"end",forceMount:!0,children:[(0,s.jsx)(ee.lp,{className:"font-normal",children:(0,s.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,s.jsx)("p",{className:"text-sm font-medium leading-none",children:(null==o||null==(n=o.user)?void 0:n.name)||"User"}),(0,s.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:null==o||null==(d=o.user)?void 0:d.email})]})}),(0,s.jsx)(ee.mB,{}),(0,s.jsxs)(ee._2,{children:[(0,s.jsx)(ei.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Profile"})]}),(0,s.jsx)(ee.mB,{}),(0,s.jsxs)(ee._2,{onClick:()=>{(0,i.signOut)({callbackUrl:"/auth/signin"})},children:[(0,s.jsx)(er.A,{className:"mr-2 h-4 w-4"}),(0,s.jsx)("span",{children:"Log out"})]})]})]})})]})})}function ed(e){let{children:t}=e,{data:a,status:n}=(0,i.useSession)();return"loading"===n?(0,s.jsx)("div",{children:"Loading..."}):(a||(0,r.redirect)("/auth/signin"),(0,s.jsx)(A,{children:(0,s.jsxs)("div",{className:"flex min-h-screen w-full",children:[(0,s.jsx)(R,{}),(0,s.jsxs)("div",{className:"flex-1 flex flex-col",children:[(0,s.jsx)(en,{}),(0,s.jsx)("main",{className:"flex-1 p-6",children:t})]})]})}))}},9434:(e,t,a)=>{"use strict";a.d(t,{EJ:()=>d,Yq:()=>n,cn:()=>r});var s=a(2596),i=a(9688);function r(){for(var e=arguments.length,t=Array(e),a=0;a<e;a++)t[a]=arguments[a];return(0,i.QP)((0,s.$)(t))}function n(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function d(e,t){return e.length<=t?e:e.substring(0,t).trim()+"..."}}},e=>{e.O(0,[5389,7536,7764,6874,8698,2108,1802,8441,5964,7358],()=>e(e.s=4082)),_N_E=e.O()}]);