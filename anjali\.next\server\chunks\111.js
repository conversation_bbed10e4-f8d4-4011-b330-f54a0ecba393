"use strict";exports.id=111,exports.ids=[111],exports.modules={18419:(a,b,c)=>{c.d(b,{MU:()=>j,a6:()=>h,gN:()=>i});var d=c(60687),e=c(51743),f=c(4780);let g={fadeIn:{initial:{opacity:0},animate:{opacity:1}},slideUp:{initial:{opacity:0,y:50},animate:{opacity:1,y:0}},slideLeft:{initial:{opacity:0,x:50},animate:{opacity:1,x:0}},slideRight:{initial:{opacity:0,x:-50},animate:{opacity:1,x:0}},scale:{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1}},bounce:{initial:{opacity:0,y:-20},animate:{opacity:1,y:0}}};function h({children:a,className:b,animation:c="fadeIn",delay:h=0,duration:i=.6,once:j=!0}){let k=g[c];return(0,d.jsx)(e.P.div,{className:(0,f.cn)(b),initial:k.initial,whileInView:k.animate,viewport:{once:j,margin:"-100px"},transition:{duration:i,delay:h,ease:"easeOut"},children:a})}function i({children:a,className:b,staggerDelay:c=.1}){return(0,d.jsx)(e.P.div,{className:(0,f.cn)(b),initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},variants:{hidden:{},visible:{transition:{staggerChildren:c}}},children:a})}function j({children:a,className:b,animation:c="slideUp"}){let h=g[c];return(0,d.jsx)(e.P.div,{className:(0,f.cn)(b),variants:{hidden:h.initial,visible:h.animate},transition:{duration:.6,ease:"easeOut"},children:a})}},29915:(a,b,c)=>{c.d(b,{qp:()=>m,gO:()=>l,bY:()=>n,t3:()=>r,pI:()=>j,Oz:()=>p});var d=c(60687),e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{className:(0,e.cn)("animate-pulse rounded-md bg-muted",a),...b})}var g=c(44493),h=c(18419);function i(){return(0,d.jsxs)(g.Card,{className:"group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm",children:[(0,d.jsx)("div",{className:"relative aspect-[4/3] overflow-hidden rounded-t-xl",children:(0,d.jsx)(f,{className:"w-full h-full"})}),(0,d.jsxs)(g.aR,{className:"pb-3",children:[(0,d.jsx)(f,{className:"h-6 w-3/4 mb-2"}),(0,d.jsx)(f,{className:"h-4 w-full"}),(0,d.jsx)(f,{className:"h-4 w-2/3"})]}),(0,d.jsxs)(g.CardContent,{className:"space-y-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(f,{className:"w-4 h-4 rounded-full"}),(0,d.jsx)(f,{className:"h-4 w-16"})]}),(0,d.jsx)(f,{className:"h-4 w-20"})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(f,{className:"h-3 w-full"}),(0,d.jsx)(f,{className:"h-3 w-4/5"}),(0,d.jsx)(f,{className:"h-3 w-3/5"})]}),(0,d.jsx)(f,{className:"h-10 w-full rounded-md"})]})]})}function j(){return(0,d.jsx)(h.gN,{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12",children:Array.from({length:6}).map((a,b)=>(0,d.jsx)(h.MU,{children:(0,d.jsx)(i,{})},b))})}function k(){return(0,d.jsxs)("div",{className:"group relative aspect-square overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10",children:[(0,d.jsx)(f,{className:"w-full h-full"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent",children:(0,d.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-6",children:[(0,d.jsx)(f,{className:"h-4 w-16 mb-3 bg-white/20"}),(0,d.jsx)(f,{className:"h-5 w-3/4 mb-2 bg-white/30"}),(0,d.jsx)(f,{className:"h-3 w-full mb-3 bg-white/20"}),(0,d.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,d.jsx)(f,{className:"h-5 w-12 bg-white/20"}),(0,d.jsx)(f,{className:"h-5 w-16 bg-white/20"}),(0,d.jsx)(f,{className:"h-5 w-14 bg-white/20"})]})]})}),(0,d.jsx)("div",{className:"absolute top-4 right-4",children:(0,d.jsx)(f,{className:"w-10 h-10 rounded-full bg-white/20"})})]})}function l(){return(0,d.jsx)(h.gN,{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:Array.from({length:12}).map((a,b)=>(0,d.jsx)(h.MU,{children:(0,d.jsx)(k,{})},b))})}function m(){return(0,d.jsx)("div",{className:"flex flex-wrap justify-center gap-3 mb-12",children:Array.from({length:6}).map((a,b)=>(0,d.jsx)("div",{className:"flex items-center gap-2",children:(0,d.jsx)(f,{className:"h-8 w-20 rounded-full"})},b))})}function n(){let a=["aspect-[3/4]","aspect-square","aspect-[4/5]","aspect-[3/5]"];return(0,d.jsx)(h.gN,{className:"columns-1 md:columns-2 lg:columns-3 xl:columns-4 gap-6 space-y-6",children:Array.from({length:8}).map((b,c)=>{let e=a[c%a.length];return(0,d.jsx)(h.MU,{children:(0,d.jsxs)("div",{className:`group relative ${e} overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 break-inside-avoid`,children:[(0,d.jsx)(f,{className:"w-full h-full"}),(0,d.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent",children:(0,d.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-4",children:[(0,d.jsx)(f,{className:"h-3 w-12 mb-2 bg-white/20"}),(0,d.jsx)(f,{className:"h-4 w-2/3 mb-1 bg-white/30"}),(0,d.jsx)(f,{className:"h-3 w-full bg-white/20"})]})}),(0,d.jsx)("div",{className:"absolute top-3 right-3",children:(0,d.jsx)(f,{className:"w-8 h-8 rounded-full bg-white/20"})})]})},`masonry-${c}`)})})}function o(){return(0,d.jsx)(g.Card,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-lg",children:(0,d.jsxs)(g.CardContent,{className:"p-8",children:[(0,d.jsx)("div",{className:"flex gap-1 mb-6",children:Array.from({length:5}).map((a,b)=>(0,d.jsx)(f,{className:"w-5 h-5"},b))}),(0,d.jsxs)("div",{className:"space-y-3 mb-8",children:[(0,d.jsx)(f,{className:"h-4 w-full"}),(0,d.jsx)(f,{className:"h-4 w-full"}),(0,d.jsx)(f,{className:"h-4 w-3/4"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)(f,{className:"w-12 h-12 rounded-full"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)(f,{className:"h-4 w-24"}),(0,d.jsx)(f,{className:"h-3 w-32"})]})]})]})})}function p(){return(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"max-w-4xl mx-auto mb-12",children:(0,d.jsx)(o,{})}),(0,d.jsxs)("div",{className:"flex justify-center items-center gap-4",children:[(0,d.jsx)(f,{className:"w-10 h-10 rounded-full"}),(0,d.jsx)("div",{className:"flex gap-2",children:Array.from({length:3}).map((a,b)=>(0,d.jsx)(f,{className:"w-3 h-3 rounded-full"},b))}),(0,d.jsx)(f,{className:"w-10 h-10 rounded-full"})]})]})}function q(){return(0,d.jsxs)(g.Card,{className:"group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm relative overflow-hidden",children:[(0,d.jsx)("div",{className:"absolute top-4 right-4 z-10",children:(0,d.jsx)(f,{className:"h-6 w-20 rounded-full"})}),(0,d.jsx)("div",{className:"relative aspect-[4/3] overflow-hidden rounded-t-xl",children:(0,d.jsx)(f,{className:"w-full h-full"})}),(0,d.jsxs)(g.aR,{className:"pb-3",children:[(0,d.jsx)(f,{className:"h-7 w-3/4 mb-2"}),(0,d.jsx)(f,{className:"h-4 w-full"}),(0,d.jsx)(f,{className:"h-4 w-2/3"})]}),(0,d.jsxs)(g.CardContent,{className:"space-y-6",children:[(0,d.jsx)("div",{className:"space-y-2",children:Array.from({length:4}).map((a,b)=>(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(f,{className:"w-4 h-4 rounded-full"}),(0,d.jsx)(f,{className:"h-3 w-32"})]},b))}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(f,{className:"w-4 h-4"}),(0,d.jsx)(f,{className:"h-4 w-24"})]}),(0,d.jsxs)("div",{className:"bg-gradient-to-r from-rose-gold/10 to-blush-pink/10 rounded-xl p-4",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,d.jsx)(f,{className:"h-4 w-20"}),(0,d.jsx)(f,{className:"h-5 w-24"})]}),(0,d.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,d.jsx)(f,{className:"h-6 w-28"}),(0,d.jsx)(f,{className:"h-4 w-16"})]}),(0,d.jsx)(f,{className:"h-10 w-full rounded-md"})]})]})]})}function r(){return(0,d.jsx)(h.gN,{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:Array.from({length:6}).map((a,b)=>(0,d.jsx)(h.MU,{children:(0,d.jsx)(q,{})},b))})}c(71134)},70334:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])}};