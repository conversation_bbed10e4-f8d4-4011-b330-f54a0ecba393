"use strict";exports.id=4040,exports.ids=[4040],exports.modules={13861:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},15079:(a,b,c)=>{c.d(b,{bq:()=>l,eb:()=>n,gC:()=>m,l6:()=>j,yv:()=>k});var d=c(60687);c(43210);var e=c(72951),f=c(78272),g=c(13964),h=c(3589),i=c(4780);function j({...a}){return(0,d.jsx)(e.bL,{"data-slot":"select",...a})}function k({...a}){return(0,d.jsx)(e.WT,{"data-slot":"select-value",...a})}function l({className:a,size:b="default",children:c,...g}){return(0,d.jsxs)(e.l9,{"data-slot":"select-trigger","data-size":b,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...g,children:[c,(0,d.jsx)(e.In,{asChild:!0,children:(0,d.jsx)(f.A,{className:"size-4 opacity-50"})})]})}function m({className:a,children:b,position:c="popper",...f}){return(0,d.jsx)(e.ZL,{children:(0,d.jsxs)(e.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...f,children:[(0,d.jsx)(o,{}),(0,d.jsx)(e.LM,{className:(0,i.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:b}),(0,d.jsx)(p,{})]})})}function n({className:a,children:b,...c}){return(0,d.jsxs)(e.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...c,children:[(0,d.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,d.jsx)(e.VF,{children:(0,d.jsx)(g.A,{className:"size-4"})})}),(0,d.jsx)(e.p4,{children:b})]})}function o({className:a,...b}){return(0,d.jsx)(e.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(h.A,{className:"size-4"})})}function p({className:a,...b}){return(0,d.jsx)(e.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(f.A,{className:"size-4"})})}},40211:(a,b,c)=>{c.d(b,{C1:()=>x,bL:()=>v});var d=c(43210),e=c(98599),f=c(11273),g=c(70569),h=c(65551),i=c(83721),j=c(18853),k=c(46059),l=c(14163),m=c(60687),n="Checkbox",[o,p]=(0,f.A)(n),[q,r]=o(n);function s(a){let{__scopeCheckbox:b,checked:c,children:e,defaultChecked:f,disabled:g,form:i,name:j,onCheckedChange:k,required:l,value:o="on",internal_do_not_use_render:p}=a,[r,s]=(0,h.i)({prop:c,defaultProp:f??!1,onChange:k,caller:n}),[t,u]=d.useState(null),[v,w]=d.useState(null),x=d.useRef(!1),y=!t||!!i||!!t.closest("form"),z={checked:r,disabled:g,setChecked:s,control:t,setControl:u,name:j,form:i,value:o,hasConsumerStoppedPropagationRef:x,required:l,defaultChecked:!A(f)&&f,isFormControl:y,bubbleInput:v,setBubbleInput:w};return(0,m.jsx)(q,{scope:b,...z,children:"function"==typeof p?p(z):e})}var t="CheckboxTrigger",u=d.forwardRef(({__scopeCheckbox:a,onKeyDown:b,onClick:c,...f},h)=>{let{control:i,value:j,disabled:k,checked:n,required:o,setControl:p,setChecked:q,hasConsumerStoppedPropagationRef:s,isFormControl:u,bubbleInput:v}=r(t,a),w=(0,e.s)(h,p),x=d.useRef(n);return d.useEffect(()=>{let a=i?.form;if(a){let b=()=>q(x.current);return a.addEventListener("reset",b),()=>a.removeEventListener("reset",b)}},[i,q]),(0,m.jsx)(l.sG.button,{type:"button",role:"checkbox","aria-checked":A(n)?"mixed":n,"aria-required":o,"data-state":B(n),"data-disabled":k?"":void 0,disabled:k,value:j,...f,ref:w,onKeyDown:(0,g.m)(b,a=>{"Enter"===a.key&&a.preventDefault()}),onClick:(0,g.m)(c,a=>{q(a=>!!A(a)||!a),v&&u&&(s.current=a.isPropagationStopped(),s.current||a.stopPropagation())})})});u.displayName=t;var v=d.forwardRef((a,b)=>{let{__scopeCheckbox:c,name:d,checked:e,defaultChecked:f,required:g,disabled:h,value:i,onCheckedChange:j,form:k,...l}=a;return(0,m.jsx)(s,{__scopeCheckbox:c,checked:e,defaultChecked:f,disabled:h,required:g,onCheckedChange:j,name:d,form:k,value:i,internal_do_not_use_render:({isFormControl:a})=>(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(u,{...l,ref:b,__scopeCheckbox:c}),a&&(0,m.jsx)(z,{__scopeCheckbox:c})]})})});v.displayName=n;var w="CheckboxIndicator",x=d.forwardRef((a,b)=>{let{__scopeCheckbox:c,forceMount:d,...e}=a,f=r(w,c);return(0,m.jsx)(k.C,{present:d||A(f.checked)||!0===f.checked,children:(0,m.jsx)(l.sG.span,{"data-state":B(f.checked),"data-disabled":f.disabled?"":void 0,...e,ref:b,style:{pointerEvents:"none",...a.style}})})});x.displayName=w;var y="CheckboxBubbleInput",z=d.forwardRef(({__scopeCheckbox:a,...b},c)=>{let{control:f,hasConsumerStoppedPropagationRef:g,checked:h,defaultChecked:k,required:n,disabled:o,name:p,value:q,form:s,bubbleInput:t,setBubbleInput:u}=r(y,a),v=(0,e.s)(c,u),w=(0,i.Z)(h),x=(0,j.X)(f);d.useEffect(()=>{if(!t)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,b=!g.current;if(w!==h&&a){let c=new Event("click",{bubbles:b});t.indeterminate=A(h),a.call(t,!A(h)&&h),t.dispatchEvent(c)}},[t,w,h,g]);let z=d.useRef(!A(h)&&h);return(0,m.jsx)(l.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:k??z.current,required:n,disabled:o,name:p,value:q,form:s,...b,tabIndex:-1,ref:v,style:{...b.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function A(a){return"indeterminate"===a}function B(a){return A(a)?"indeterminate":a?"checked":"unchecked"}z.displayName=y},44493:(a,b,c)=>{c.d(b,{BT:()=>i,Wu:()=>j,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,e.cn)("text-muted-foreground text-sm",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},56896:(a,b,c)=>{c.d(b,{S:()=>h});var d=c(60687);c(43210);var e=c(40211),f=c(13964),g=c(4780);function h({className:a,...b}){return(0,d.jsx)(e.bL,{"data-slot":"checkbox",className:(0,g.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...b,children:(0,d.jsx)(e.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,d.jsx)(f.A,{className:"size-3.5"})})})}},63143:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},64398:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},88233:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]])},93661:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("ellipsis",[["circle",{cx:"12",cy:"12",r:"1",key:"41hilf"}],["circle",{cx:"19",cy:"12",r:"1",key:"1wjl8i"}],["circle",{cx:"5",cy:"12",r:"1",key:"1pcz8c"}]])},96474:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]])},96834:(a,b,c)=>{c.d(b,{E:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}},99270:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])}};