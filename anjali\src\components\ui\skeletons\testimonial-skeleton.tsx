import { Card, CardContent } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

export function TestimonialCardSkeleton() {
  return (
    <Card className="bg-white/80 backdrop-blur-sm border-0 shadow-lg">
      <CardContent className="p-8">
        {/* Stars skeleton */}
        <div className="flex gap-1 mb-6">
          {Array.from({ length: 5 }).map((_, i) => (
            <Skeleton key={i} className="w-5 h-5" />
          ))}
        </div>
        
        {/* Quote skeleton */}
        <div className="space-y-3 mb-8">
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>
        
        {/* Author info skeleton */}
        <div className="flex items-center gap-4">
          <Skeleton className="w-12 h-12 rounded-full" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-24" />
            <Skeleton className="h-3 w-32" />
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

export function TestimonialsSkeleton() {
  return (
    <div className="relative">
      {/* Main testimonial skeleton */}
      <div className="max-w-4xl mx-auto mb-12">
        <TestimonialCardSkeleton />
      </div>
      
      {/* Navigation skeleton */}
      <div className="flex justify-center items-center gap-4">
        <Skeleton className="w-10 h-10 rounded-full" />
        <div className="flex gap-2">
          {Array.from({ length: 3 }).map((_, i) => (
            <Skeleton key={i} className="w-3 h-3 rounded-full" />
          ))}
        </div>
        <Skeleton className="w-10 h-10 rounded-full" />
      </div>
    </div>
  )
}
