(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[977],{488:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("facebook",[["path",{d:"M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z",key:"1jg4f8"}]])},1264:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},3110:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var a=s(5155),r=s(4090),i=s(6425),n=s(8292);function l(){return(0,a.jsxs)(r.Section,{background:"cream",children:[(0,a.jsx)(i.a6,{animation:"fadeIn",children:(0,a.jsx)(r.SectionHeader,{subtitle:"Send Us a Message",title:"Book Your Consultation",description:"Fill out the form below with your details and requirements. We'll get back to you within 24 hours to discuss your makeup needs and schedule your session."})}),(0,a.jsx)(i.a6,{animation:"slideUp",delay:.3,children:(0,a.jsx)(n.A,{})})]})}},4090:(e,t,s)=>{"use strict";s.r(t),s.d(t,{Section:()=>i,SectionHeader:()=>n});var a=s(5155),r=s(9434);function i(e){let{children:t,className:s,id:i,background:n="default"}=e;return(0,a.jsx)("section",{id:i,className:(0,r.cn)("py-16 md:py-24",{default:"bg-white",cream:"bg-cream","soft-gray":"bg-soft-gray",gradient:"bg-gradient-to-br from-cream to-soft-gray"}[n],s),children:(0,a.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:t})})}function n(e){let{title:t,subtitle:s,description:i,centered:n=!0,className:l}=e;return(0,a.jsxs)("div",{className:(0,r.cn)("mb-12 md:mb-16",n&&"text-center",l),children:[s&&(0,a.jsx)("p",{className:"text-rose-gold-dark font-medium text-sm uppercase tracking-wide mb-2",children:s}),(0,a.jsx)("h2",{className:"font-display text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary mb-4",children:t}),i&&(0,a.jsx)("p",{className:"text-text-secondary text-lg max-w-3xl mx-auto leading-relaxed",children:i})]})}},5684:(e,t,s)=>{"use strict";s.d(t,{A:()=>a});let a=(0,s(9946).A)("instagram",[["rect",{width:"20",height:"20",x:"2",y:"2",rx:"5",ry:"5",key:"2e1cvw"}],["path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z",key:"9exkf1"}],["line",{x1:"17.5",x2:"17.51",y1:"6.5",y2:"6.5",key:"r4j83e"}]])},6126:(e,t,s)=>{"use strict";s.d(t,{Badge:()=>l});var a=s(5155);s(2115);var r=s(2085),i=s(9434);let n=(0,r.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-rose-gold text-white shadow hover:bg-rose-gold-dark",secondary:"border-transparent bg-blush-pink text-text-primary hover:bg-blush-pink-dark",destructive:"border-transparent bg-red-500 text-white shadow hover:bg-red-600",outline:"text-text-primary border-gray-300",success:"border-transparent bg-green-500 text-white shadow hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white shadow hover:bg-yellow-600",lavender:"border-transparent bg-lavender text-text-primary hover:bg-lavender-dark"}},defaultVariants:{variant:"default"}});function l(e){let{className:t,variant:s,...r}=e;return(0,a.jsx)("div",{className:(0,i.cn)(n({variant:s}),t),...r})}},6425:(e,t,s)=>{"use strict";s.d(t,{MU:()=>c,a6:()=>l,gN:()=>d});var a=s(5155),r=s(2605),i=s(9434);let n={fadeIn:{initial:{opacity:0},animate:{opacity:1}},slideUp:{initial:{opacity:0,y:50},animate:{opacity:1,y:0}},slideLeft:{initial:{opacity:0,x:50},animate:{opacity:1,x:0}},slideRight:{initial:{opacity:0,x:-50},animate:{opacity:1,x:0}},scale:{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1}},bounce:{initial:{opacity:0,y:-20},animate:{opacity:1,y:0}}};function l(e){let{children:t,className:s,animation:l="fadeIn",delay:d=0,duration:c=.6,once:o=!0}=e,x=n[l];return(0,a.jsx)(r.P.div,{className:(0,i.cn)(s),initial:x.initial,whileInView:x.animate,viewport:{once:o,margin:"-100px"},transition:{duration:c,delay:d,ease:"easeOut"},children:t})}function d(e){let{children:t,className:s,staggerDelay:n=.1}=e;return(0,a.jsx)(r.P.div,{className:(0,i.cn)(s),initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},variants:{hidden:{},visible:{transition:{staggerChildren:n}}},children:t})}function c(e){let{children:t,className:s,animation:l="slideUp"}=e,d=n[l];return(0,a.jsx)(r.P.div,{className:(0,i.cn)(s),variants:{hidden:d.initial,visible:d.animate},transition:{duration:.6,ease:"easeOut"},children:t})}},6695:(e,t,s)=>{"use strict";s.d(t,{BT:()=>c,Card:()=>n,CardContent:()=>o,ZB:()=>d,aR:()=>l});var a=s(5155),r=s(2115),i=s(9434);let n=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("rounded-xl border border-gray-200 bg-white text-text-primary shadow-sm transition-shadow hover:shadow-md",s),...r})});n.displayName="Card";let l=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",s),...r})});l.displayName="CardHeader";let d=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("h3",{ref:t,className:(0,i.cn)("font-display text-2xl font-semibold leading-none tracking-tight",s),...r})});d.displayName="CardTitle";let c=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("p",{ref:t,className:(0,i.cn)("text-sm text-text-secondary",s),...r})});c.displayName="CardDescription";let o=r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("p-6 pt-0",s),...r})});o.displayName="CardContent",r.forwardRef((e,t)=>{let{className:s,...r}=e;return(0,a.jsx)("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",s),...r})}).displayName="CardFooter"},6902:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(5155),r=s(9420),i=s(1366),n=s(4516),l=s(6126),d=s(4090),c=s(6425);function o(){return(0,a.jsx)(d.Section,{className:"pt-24 pb-16 bg-gradient-to-br from-cream via-white to-blush-pink-light",children:(0,a.jsxs)("div",{className:"text-center space-y-8",children:[(0,a.jsxs)(c.a6,{animation:"slideUp",children:[(0,a.jsxs)(l.Badge,{variant:"secondary",className:"text-sm px-4 py-2 mb-4",children:[(0,a.jsx)(r.A,{className:"w-4 h-4 mr-2"}),"Contact & Booking"]}),(0,a.jsxs)("h1",{className:"font-display text-4xl md:text-5xl lg:text-6xl font-bold text-text-primary leading-tight",children:["Let's Create Your",(0,a.jsx)("span",{className:"block text-transparent bg-gradient-to-r from-rose-gold to-blush-pink bg-clip-text",children:"Perfect Look"})]}),(0,a.jsx)("p",{className:"text-xl text-text-secondary leading-relaxed max-w-3xl mx-auto",children:"Ready to book your makeup session? Get in touch with us today for consultations, bookings, or any questions about our services. We're here to help you look and feel absolutely stunning."})]}),(0,a.jsx)(c.a6,{animation:"slideUp",delay:.3,children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-3",children:(0,a.jsx)(r.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("div",{className:"text-lg font-semibold text-text-primary mb-1",children:"Call Us"}),(0,a.jsx)("div",{className:"text-text-secondary",children:"+977-9800000000"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto mb-3",children:(0,a.jsx)(i.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("div",{className:"text-lg font-semibold text-text-primary mb-1",children:"WhatsApp"}),(0,a.jsx)("div",{className:"text-text-secondary",children:"Quick Response"})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto mb-3",children:(0,a.jsx)(n.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("div",{className:"text-lg font-semibold text-text-primary mb-1",children:"Visit Us"}),(0,a.jsx)("div",{className:"text-text-secondary",children:"Biratnagar, Nepal"})]})]})}),(0,a.jsx)(c.a6,{animation:"slideUp",delay:.5,children:(0,a.jsx)("div",{className:"flex flex-wrap justify-center gap-3 max-w-2xl mx-auto",children:["Quick Response","Professional Service","Flexible Scheduling","Free Consultation"].map(e=>(0,a.jsx)(l.Badge,{variant:"outline",className:"text-sm px-4 py-2",children:e},e))})})]})})}},7658:(e,t,s)=>{"use strict";s.d(t,{default:()=>y});var a=s(5155),r=s(6874),i=s.n(r),n=s(9420),l=s(1366),d=s(1264),c=s(4516),o=s(4186),x=s(5684),m=s(488),h=s(285),u=s(6695),p=s(4090),j=s(6425),f=s(1710),g=s(9434);function y(){let e=(0,f.Q2)(),t=(0,f.PZ)(),s=(0,g.ec)(e.contact.whatsapp,e.whatsappMessage);return(0,a.jsxs)(p.Section,{children:[(0,a.jsx)(j.a6,{animation:"fadeIn",children:(0,a.jsx)(p.SectionHeader,{subtitle:"Get In Touch",title:"Contact Information",description:"Multiple ways to reach us for bookings, consultations, and inquiries. We're here to help you with all your makeup needs."})}),(0,a.jsxs)(j.gN,{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:[(0,a.jsx)(j.MU,{children:(0,a.jsxs)(u.Card,{className:"h-full hover:shadow-lg transition-shadow duration-300",children:[(0,a.jsxs)(u.aR,{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(n.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)(u.ZB,{className:"text-xl",children:"Phone & WhatsApp"})]}),(0,a.jsxs)(u.CardContent,{className:"text-center space-y-4",children:[(0,a.jsx)("p",{className:"text-text-secondary",children:"Call or message us directly for immediate assistance"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"font-semibold text-text-primary",children:e.contact.phone}),(0,a.jsx)("p",{className:"text-text-secondary text-sm",children:"Available during business hours"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(h.$,{asChild:!0,variant:"gradient",size:"sm",className:"w-full",children:(0,a.jsxs)(i(),{href:s,target:"_blank",rel:"noopener noreferrer",children:[(0,a.jsx)(l.A,{className:"w-4 h-4 mr-2"}),"WhatsApp Now"]})}),(0,a.jsx)(h.$,{asChild:!0,variant:"outline",size:"sm",className:"w-full",children:(0,a.jsxs)(i(),{href:"tel:".concat(e.contact.phone),children:[(0,a.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Call Now"]})})]})]})]})}),(0,a.jsx)(j.MU,{children:(0,a.jsxs)(u.Card,{className:"h-full hover:shadow-lg transition-shadow duration-300",children:[(0,a.jsxs)(u.aR,{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(d.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)(u.ZB,{className:"text-xl",children:"Email"})]}),(0,a.jsxs)(u.CardContent,{className:"text-center space-y-4",children:[(0,a.jsx)("p",{className:"text-text-secondary",children:"Send us detailed inquiries and we'll respond promptly"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"font-semibold text-text-primary",children:e.contact.email}),(0,a.jsx)("p",{className:"text-text-secondary text-sm",children:"Response within 24 hours"})]}),(0,a.jsx)(h.$,{asChild:!0,variant:"outline",size:"sm",className:"w-full",children:(0,a.jsxs)(i(),{href:"mailto:".concat(e.contact.email),children:[(0,a.jsx)(d.A,{className:"w-4 h-4 mr-2"}),"Send Email"]})})]})]})}),(0,a.jsx)(j.MU,{children:(0,a.jsxs)(u.Card,{className:"h-full hover:shadow-lg transition-shadow duration-300",children:[(0,a.jsxs)(u.aR,{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(c.A,{className:"w-8 h-8 text-white"})}),(0,a.jsx)(u.ZB,{className:"text-xl",children:"Location"})]}),(0,a.jsxs)(u.CardContent,{className:"text-center space-y-4",children:[(0,a.jsx)("p",{className:"text-text-secondary",children:"Visit our studio or we can come to you"}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"font-semibold text-text-primary",children:e.contact.address.street}),(0,a.jsxs)("p",{className:"text-text-secondary",children:[e.contact.address.city,", ",e.contact.address.state]}),(0,a.jsx)("p",{className:"text-text-secondary",children:e.contact.address.country})]}),(0,a.jsxs)(h.$,{variant:"outline",size:"sm",className:"w-full",disabled:!0,children:[(0,a.jsx)(c.A,{className:"w-4 h-4 mr-2"}),"View on Map"]})]})]})})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-8 mt-16",children:[(0,a.jsx)(j.a6,{animation:"slideRight",delay:.6,children:(0,a.jsxs)(u.Card,{className:"bg-gradient-to-br from-cream to-soft-gray border-0",children:[(0,a.jsx)(u.aR,{children:(0,a.jsxs)("div",{className:"flex items-center gap-3",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center",children:(0,a.jsx)(o.A,{className:"w-6 h-6 text-white"})}),(0,a.jsx)(u.ZB,{className:"text-xl",children:"Business Hours"})]})}),(0,a.jsxs)(u.CardContent,{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-text-secondary",children:"Monday - Friday"}),(0,a.jsx)("span",{className:"font-semibold text-text-primary",children:"9:00 AM - 6:00 PM"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-text-secondary",children:"Saturday"}),(0,a.jsx)("span",{className:"font-semibold text-text-primary",children:"9:00 AM - 6:00 PM"})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-text-secondary",children:"Sunday"}),(0,a.jsx)("span",{className:"font-semibold text-text-primary",children:"10:00 AM - 4:00 PM"})]}),(0,a.jsx)("div",{className:"pt-3 border-t border-gray-200",children:(0,a.jsx)("p",{className:"text-sm text-text-muted",children:"Emergency bookings available with advance notice. Early morning and evening appointments can be arranged."})})]})]})}),(0,a.jsx)(j.a6,{animation:"slideLeft",delay:.8,children:(0,a.jsxs)(u.Card,{className:"bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0",children:[(0,a.jsx)(u.aR,{children:(0,a.jsx)(u.ZB,{className:"text-xl",children:"Follow Us"})}),(0,a.jsxs)(u.CardContent,{className:"space-y-6",children:[(0,a.jsx)("p",{className:"text-text-secondary",children:"Stay connected with us on social media for daily beauty tips, behind-the-scenes content, and our latest work."}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)(i(),{href:t.instagram,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors group",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-gradient-to-br from-pink-500 to-purple-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(x.A,{className:"w-5 h-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-semibold text-text-primary group-hover:text-rose-gold-dark transition-colors",children:"Instagram"}),(0,a.jsx)("div",{className:"text-text-secondary text-sm",children:"@anjalimakeup"})]})]}),(0,a.jsxs)(i(),{href:t.facebook,target:"_blank",rel:"noopener noreferrer",className:"flex items-center gap-4 p-3 rounded-lg hover:bg-white/50 transition-colors group",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,a.jsx)(m.A,{className:"w-5 h-5 text-white"})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"font-semibold text-text-primary group-hover:text-rose-gold-dark transition-colors",children:"Facebook"}),(0,a.jsx)("div",{className:"text-text-secondary text-sm",children:"Anjali Makeup Artist"})]})]})]}),(0,a.jsx)("div",{className:"pt-4 border-t border-gray-200",children:(0,a.jsx)("p",{className:"text-sm text-text-muted",children:"Follow us for makeup tutorials, client transformations, and exclusive behind-the-scenes content."})})]})]})})]})]})}},9493:(e,t,s)=>{Promise.resolve().then(s.bind(s,3110)),Promise.resolve().then(s.bind(s,6902)),Promise.resolve().then(s.bind(s,7658))}},e=>{e.O(0,[277,699,605,196,578,292,441,964,358],()=>e(e.s=9493)),_N_E=e.O()}]);