(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[962],{285:(e,r,a)=>{"use strict";a.d(r,{$:()=>l});var t=a(5155);a(2115);var s=a(9708),i=a(2085),n=a(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:r,variant:a,size:i,asChild:l=!1,...c}=e,o=l?s.DX:"button";return(0,t.jsx)(o,{"data-slot":"button",className:(0,n.cn)(d({variant:a,size:i,className:r})),...c})}},799:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>b});var t=a(5155),s=a(2115),i=a(5695),n=a(6874),d=a.n(n),l=a(6766),c=a(285),o=a(6126),u=a(6695),h=a(2346),x=a(5169),v=a(8564),m=a(9074),g=a(3717),f=a(3332),p=a(9434),j=a(6671);function b(){let e=(0,i.useParams)(),r=(0,i.useRouter)(),[a,n]=(0,s.useState)(null),[b,N]=(0,s.useState)(!0);if((0,s.useEffect)(()=>{let a=async()=>{try{let a=await fetch("/api/gallery/".concat(e.id));if(a.ok){let e=await a.json();n(e)}else j.oR.error("Gallery item not found"),r.push("/dashboard/gallery")}catch(e){j.oR.error("Error fetching gallery item"),r.push("/dashboard/gallery")}finally{N(!1)}};e.id&&a()},[e.id,r]),b)return(0,t.jsx)("div",{children:"Loading..."});if(!a)return(0,t.jsx)("div",{children:"Gallery item not found"});let w=e=>(0,t.jsx)(o.E,{variant:{ACTIVE:"default",INACTIVE:"secondary",ARCHIVED:"outline"}[e],children:e});return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)(c.$,{variant:"ghost",size:"sm",onClick:()=>r.back(),children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-2",children:[a.title,a.featured&&(0,t.jsx)(v.A,{className:"h-6 w-6 text-yellow-500 fill-current"})]}),(0,t.jsx)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground mt-2",children:(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(m.A,{className:"h-4 w-4"}),"Added ",(0,p.Yq)(a.createdAt)]})})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[w(a.status),a.category&&(0,t.jsx)(o.E,{variant:"outline",children:a.category}),(0,t.jsx)(c.$,{asChild:!0,children:(0,t.jsxs)(d(),{href:"/dashboard/gallery/".concat(a.id,"/edit"),children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Edit"]})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsx)(u.Zp,{children:(0,t.jsx)(u.Wu,{className:"p-0",children:(0,t.jsx)("div",{className:"relative aspect-square w-full overflow-hidden rounded-lg",children:(0,t.jsx)(l.default,{src:a.image,alt:a.title,fill:!0,className:"object-cover"})})})}),a.description&&(0,t.jsxs)(u.Zp,{children:[(0,t.jsx)(u.aR,{children:(0,t.jsx)(u.ZB,{children:"Description"})}),(0,t.jsx)(u.Wu,{children:(0,t.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:a.description})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(u.Zp,{children:[(0,t.jsx)(u.aR,{children:(0,t.jsx)(u.ZB,{children:"Details"})}),(0,t.jsxs)(u.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(y,{className:"text-sm font-medium",children:"Status"}),(0,t.jsx)("div",{className:"mt-1",children:w(a.status)})]}),a.category&&(0,t.jsxs)("div",{children:[(0,t.jsx)(y,{className:"text-sm font-medium",children:"Category"}),(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsx)(o.E,{variant:"outline",children:a.category})})]}),a.tags.length>0&&(0,t.jsxs)("div",{children:[(0,t.jsx)(y,{className:"text-sm font-medium",children:"Tags"}),(0,t.jsx)("div",{className:"flex flex-wrap gap-2 mt-1",children:a.tags.map((e,r)=>(0,t.jsxs)(o.E,{variant:"outline",children:[(0,t.jsx)(f.A,{className:"h-3 w-3 mr-1"}),e]},r))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(y,{className:"text-sm font-medium",children:"Featured"}),(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsx)(o.E,{variant:a.featured?"default":"outline",children:a.featured?"Yes":"No"})})]}),(0,t.jsx)(h.w,{}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Added:"})," ",(0,p.Yq)(a.createdAt)]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Updated:"})," ",(0,p.Yq)(a.updatedAt)]})]})]})]}),(0,t.jsxs)(u.Zp,{children:[(0,t.jsx)(u.aR,{children:(0,t.jsx)(u.ZB,{children:"Quick Actions"})}),(0,t.jsxs)(u.Wu,{className:"space-y-2",children:[(0,t.jsx)(c.$,{asChild:!0,className:"w-full",children:(0,t.jsxs)(d(),{href:"/dashboard/gallery/".concat(a.id,"/edit"),children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Edit Image"]})}),(0,t.jsx)(c.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,t.jsx)(d(),{href:"/dashboard/gallery",children:"View All Gallery"})}),(0,t.jsx)(c.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,t.jsx)(d(),{href:"/dashboard/gallery/new",children:"Add New Image"})})]})]}),(0,t.jsxs)(u.Zp,{children:[(0,t.jsx)(u.aR,{children:(0,t.jsx)(u.ZB,{children:"Image Info"})}),(0,t.jsxs)(u.Wu,{className:"space-y-2 text-sm text-muted-foreground",children:[(0,t.jsx)("p",{children:"• This image is part of your portfolio gallery"}),(0,t.jsxs)("p",{children:["• ",a.featured?"Featured images appear prominently":"Mark as featured to highlight"]}),(0,t.jsx)("p",{children:"• Only active images are visible to visitors"}),(0,t.jsx)("p",{children:"• Use tags and categories for better organization"})]})]})]})]})]})}function y(e){let{children:r,className:a}=e;return(0,t.jsx)("div",{className:a,children:r})}},2346:(e,r,a)=>{"use strict";a.d(r,{w:()=>n});var t=a(5155);a(2115);var s=a(7489),i=a(9434);function n(e){let{className:r,orientation:a="horizontal",decorative:n=!0,...d}=e;return(0,t.jsx)(s.b,{"data-slot":"separator",decorative:n,orientation:a,className:(0,i.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",r),...d})}},2348:(e,r,a)=>{Promise.resolve().then(a.bind(a,799))},3332:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(9946).A)("tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},3717:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},5169:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5695:(e,r,a)=>{"use strict";var t=a(8999);a.o(t,"redirect")&&a.d(r,{redirect:function(){return t.redirect}}),a.o(t,"useParams")&&a.d(r,{useParams:function(){return t.useParams}}),a.o(t,"usePathname")&&a.d(r,{usePathname:function(){return t.usePathname}}),a.o(t,"useRouter")&&a.d(r,{useRouter:function(){return t.useRouter}})},6126:(e,r,a)=>{"use strict";a.d(r,{E:()=>l});var t=a(5155);a(2115);var s=a(9708),i=a(2085),n=a(9434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function l(e){let{className:r,variant:a,asChild:i=!1,...l}=e,c=i?s.DX:"span";return(0,t.jsx)(c,{"data-slot":"badge",className:(0,n.cn)(d({variant:a}),r),...l})}},6695:(e,r,a)=>{"use strict";a.d(r,{BT:()=>l,Wu:()=>c,ZB:()=>d,Zp:()=>i,aR:()=>n});var t=a(5155);a(2115);var s=a(9434);function i(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,s.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",r),...a})}function n(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,s.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",r),...a})}function d(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,s.cn)("leading-none font-semibold",r),...a})}function l(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,s.cn)("text-muted-foreground text-sm",r),...a})}function c(e){let{className:r,...a}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,s.cn)("px-6",r),...a})}},7489:(e,r,a)=>{"use strict";a.d(r,{b:()=>c});var t=a(2115),s=a(3655),i=a(5155),n="horizontal",d=["horizontal","vertical"],l=t.forwardRef((e,r)=>{var a;let{decorative:t,orientation:l=n,...c}=e,o=(a=l,d.includes(a))?l:n;return(0,i.jsx)(s.sG.div,{"data-orientation":o,...t?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...c,ref:r})});l.displayName="Separator";var c=l},8564:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9074:(e,r,a)=>{"use strict";a.d(r,{A:()=>t});let t=(0,a(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9434:(e,r,a)=>{"use strict";a.d(r,{EJ:()=>d,Yq:()=>n,cn:()=>i});var t=a(2596),s=a(9688);function i(){for(var e=arguments.length,r=Array(e),a=0;a<e;a++)r[a]=arguments[a];return(0,s.QP)((0,t.$)(r))}function n(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function d(e,r){return e.length<=r?e:e.substring(0,r).trim()+"..."}}},e=>{e.O(0,[5389,6671,651,6874,8441,5964,7358],()=>e(e.s=2348)),_N_E=e.O()}]);