"use strict";exports.id=2827,exports.ids=[2827],exports.modules={56896:(a,b,c)=>{c.d(b,{S:()=>h});var d=c(60687);c(43210);var e=c(40211),f=c(13964),g=c(4780);function h({className:a,...b}){return(0,d.jsx)(e.bL,{"data-slot":"checkbox",className:(0,g.cn)("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",a),...b,children:(0,d.jsx)(e.C1,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:(0,d.jsx)(f.A,{className:"size-3.5"})})})}},92827:(a,b,c)=>{c.d(b,{BlogForm:()=>U});var d=c(60687),e=c(43210),f=c(16189),g=c(27605),h=c(63442),i=c(37566),j=c(29523),k=c(89667),l=c(80013),m=c(34729),n=c(56896),o=c(15079),p=c(71669),q=c(44493),r=c(96834),s=c(7203),t=c(31425),u=c(79801),v=c(2188),w=c(36293),x=c(44731),y=c(68324),z=c(68174),A=c(35950),B=c(27777),C=c(11922),D=c(75687),E=c(80375),F=c(45984),G=c(69169),H=c(21782),I=c(25366),J=c(14290),K=c(98916),L=c(47342),M=c(9005),N=c(54388),O=c(31110);function P({content:a,onChange:b,placeholder:c}){let e=(0,s.hG)({extensions:[t.A,u.Ay.configure({HTMLAttributes:{class:"max-w-full h-auto rounded-lg"}}),v.Ay.configure({openOnClick:!1,HTMLAttributes:{class:"text-blue-500 underline"}}),w.xJ,w.Q1,x.A.configure({HTMLAttributes:{class:"list-disc list-inside"}}),y.A.configure({HTMLAttributes:{class:"list-decimal list-inside"}}),z.A],content:a,onUpdate:({editor:a})=>{b(a.getHTML())},editorProps:{attributes:{class:"prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none min-h-[200px] p-4"}},immediatelyRender:!1});return e?(0,d.jsxs)("div",{className:"border rounded-lg",children:[(0,d.jsxs)("div",{className:"border-b p-2 flex flex-wrap gap-1",children:[(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>e.chain().focus().toggleBold().run(),className:e.isActive("bold")?"bg-muted":"",children:(0,d.jsx)(B.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>e.chain().focus().toggleItalic().run(),className:e.isActive("italic")?"bg-muted":"",children:(0,d.jsx)(C.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>e.chain().focus().toggleStrike().run(),className:e.isActive("strike")?"bg-muted":"",children:(0,d.jsx)(D.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>e.chain().focus().toggleCode().run(),className:e.isActive("code")?"bg-muted":"",children:(0,d.jsx)(E.A,{className:"h-4 w-4"})}),(0,d.jsx)(A.w,{orientation:"vertical",className:"h-8"}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>e.chain().focus().toggleHeading({level:1}).run(),className:e.isActive("heading",{level:1})?"bg-muted":"",children:(0,d.jsx)(F.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>e.chain().focus().toggleHeading({level:2}).run(),className:e.isActive("heading",{level:2})?"bg-muted":"",children:(0,d.jsx)(G.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>e.chain().focus().toggleHeading({level:3}).run(),className:e.isActive("heading",{level:3})?"bg-muted":"",children:(0,d.jsx)(H.A,{className:"h-4 w-4"})}),(0,d.jsx)(A.w,{orientation:"vertical",className:"h-8"}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>e.chain().focus().toggleBulletList().run(),className:e.isActive("bulletList")?"bg-muted":"",children:(0,d.jsx)(I.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>e.chain().focus().toggleOrderedList().run(),className:e.isActive("orderedList")?"bg-muted":"",children:(0,d.jsx)(J.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>e.chain().focus().toggleBlockquote().run(),className:e.isActive("blockquote")?"bg-muted":"",children:(0,d.jsx)(K.A,{className:"h-4 w-4"})}),(0,d.jsx)(A.w,{orientation:"vertical",className:"h-8"}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>{let a=window.prompt("Enter URL:");a&&e.chain().focus().setLink({href:a}).run()},children:(0,d.jsx)(L.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>{let a=window.prompt("Enter image URL:");a&&e.chain().focus().setImage({src:a}).run()},children:(0,d.jsx)(M.A,{className:"h-4 w-4"})}),(0,d.jsx)(A.w,{orientation:"vertical",className:"h-8"}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>e.chain().focus().undo().run(),disabled:!e.can().undo(),children:(0,d.jsx)(N.A,{className:"h-4 w-4"})}),(0,d.jsx)(j.$,{variant:"ghost",size:"sm",onClick:()=>e.chain().focus().redo().run(),disabled:!e.can().redo(),children:(0,d.jsx)(O.A,{className:"h-4 w-4"})})]}),(0,d.jsx)(s.$Z,{editor:e})]}):null}var Q=c(19312),R=c(52581),S=c(11860);let T=i.Ik({title:i.Yj().min(1,"Title is required"),excerpt:i.Yj().optional(),content:i.Yj().min(1,"Content is required"),author:i.Yj().min(1,"Author is required"),featured:i.zM().default(!1),image:i.Yj().optional(),readTime:i.Yj().optional(),status:i.k5(["DRAFT","PUBLISHED","ARCHIVED"]).default("DRAFT"),metaTitle:i.Yj().optional(),metaDescription:i.Yj().optional(),keywords:i.Yj().optional(),categoryId:i.Yj().optional(),tagIds:i.YO(i.Yj()).default([])});function U({initialData:a,isEditing:b=!1}){let c=(0,f.useRouter)(),[i,s]=(0,e.useState)([]),[t,u]=(0,e.useState)([]),[v,w]=(0,e.useState)([]),[x,y]=(0,e.useState)(""),[z,A]=(0,e.useState)(!1),B=(0,g.mN)({resolver:(0,h.u)(T),defaultValues:{title:a?.title||"",excerpt:a?.excerpt||"",content:a?.content||"",author:a?.author||"",featured:a?.featured||!1,image:a?.image||"",readTime:a?.readTime||"",status:a?.status||"DRAFT",metaTitle:a?.metaTitle||"",metaDescription:a?.metaDescription||"",keywords:a?.keywords||"",categoryId:a?.categoryId||"none",tagIds:a?.tags?.map(a=>a.tag.id)||[]}}),C=async()=>{if(x.trim())try{let a=await fetch("/api/tags",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({name:x.trim()})});if(a.ok){let b=await a.json();u(a=>[...a,b]),w(a=>[...a,b.id]),y(""),R.oR.success("Tag created successfully")}else R.oR.error("Failed to create tag")}catch(a){R.oR.error("Error creating tag")}},D=async d=>{A(!0);try{let e={...d,keywords:d.keywords?d.keywords.split(",").map(a=>a.trim()):[],tagIds:v,categoryId:"none"===d.categoryId?null:d.categoryId},f=b?`/api/blogs/${a?.id}`:"/api/blogs",g=await fetch(f,{method:b?"PUT":"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(g.ok)R.oR.success(b?"Blog updated successfully":"Blog created successfully"),c.push("/dashboard/blogs");else{let a=await g.json();R.oR.error(a.error||"Something went wrong")}}catch(a){R.oR.error("Error saving blog")}finally{A(!1)}};return(0,d.jsx)(p.lV,{...B,children:(0,d.jsx)("form",{onSubmit:B.handleSubmit(D),className:"space-y-6",children:(0,d.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,d.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,d.jsxs)(q.Zp,{children:[(0,d.jsxs)(q.aR,{children:[(0,d.jsx)(q.ZB,{children:"Basic Information"}),(0,d.jsx)(q.BT,{children:"Enter the basic details for your blog post"})]}),(0,d.jsxs)(q.Wu,{className:"space-y-4",children:[(0,d.jsx)(p.zB,{control:B.control,name:"title",render:({field:a})=>(0,d.jsxs)(p.eI,{children:[(0,d.jsx)(p.lR,{children:"Title"}),(0,d.jsx)(p.MJ,{children:(0,d.jsx)(k.p,{placeholder:"Enter blog title",...a})}),(0,d.jsx)(p.C5,{})]})}),(0,d.jsx)(p.zB,{control:B.control,name:"excerpt",render:({field:a})=>(0,d.jsxs)(p.eI,{children:[(0,d.jsx)(p.lR,{children:"Excerpt"}),(0,d.jsx)(p.MJ,{children:(0,d.jsx)(m.T,{placeholder:"Brief description of the blog post",...a})}),(0,d.jsx)(p.Rr,{children:"A short summary that will appear in blog listings"}),(0,d.jsx)(p.C5,{})]})}),(0,d.jsx)(p.zB,{control:B.control,name:"author",render:({field:a})=>(0,d.jsxs)(p.eI,{children:[(0,d.jsx)(p.lR,{children:"Author"}),(0,d.jsx)(p.MJ,{children:(0,d.jsx)(k.p,{placeholder:"Author name",...a})}),(0,d.jsx)(p.C5,{})]})}),(0,d.jsx)(p.zB,{control:B.control,name:"readTime",render:({field:a})=>(0,d.jsxs)(p.eI,{children:[(0,d.jsx)(p.lR,{children:"Read Time"}),(0,d.jsx)(p.MJ,{children:(0,d.jsx)(k.p,{placeholder:"e.g., 5 min read",...a})}),(0,d.jsx)(p.C5,{})]})})]})]}),(0,d.jsxs)(q.Zp,{children:[(0,d.jsxs)(q.aR,{children:[(0,d.jsx)(q.ZB,{children:"Content"}),(0,d.jsx)(q.BT,{children:"Write your blog post content using the rich text editor"})]}),(0,d.jsx)(q.Wu,{children:(0,d.jsx)(p.zB,{control:B.control,name:"content",render:({field:a})=>(0,d.jsxs)(p.eI,{children:[(0,d.jsx)(p.MJ,{children:(0,d.jsx)(P,{content:a.value,onChange:a.onChange,placeholder:"Start writing your blog post..."})}),(0,d.jsx)(p.C5,{})]})})})]})]}),(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)(q.Zp,{children:[(0,d.jsx)(q.aR,{children:(0,d.jsx)(q.ZB,{children:"Publishing"})}),(0,d.jsxs)(q.Wu,{className:"space-y-4",children:[(0,d.jsx)(p.zB,{control:B.control,name:"status",render:({field:a})=>(0,d.jsxs)(p.eI,{children:[(0,d.jsx)(p.lR,{children:"Status"}),(0,d.jsxs)(o.l6,{onValueChange:a.onChange,defaultValue:a.value,children:[(0,d.jsx)(p.MJ,{children:(0,d.jsx)(o.bq,{children:(0,d.jsx)(o.yv,{placeholder:"Select status"})})}),(0,d.jsxs)(o.gC,{children:[(0,d.jsx)(o.eb,{value:"DRAFT",children:"Draft"}),(0,d.jsx)(o.eb,{value:"PUBLISHED",children:"Published"}),(0,d.jsx)(o.eb,{value:"ARCHIVED",children:"Archived"})]})]}),(0,d.jsx)(p.C5,{})]})}),(0,d.jsx)(p.zB,{control:B.control,name:"featured",render:({field:a})=>(0,d.jsxs)(p.eI,{className:"flex flex-row items-start space-x-3 space-y-0",children:[(0,d.jsx)(p.MJ,{children:(0,d.jsx)(n.S,{checked:a.value,onCheckedChange:a.onChange})}),(0,d.jsxs)("div",{className:"space-y-1 leading-none",children:[(0,d.jsx)(p.lR,{children:"Featured Post"}),(0,d.jsx)(p.Rr,{children:"Mark this post as featured"})]})]})}),(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(j.$,{type:"submit",disabled:z,children:z?"Saving...":b?"Update":"Create"}),(0,d.jsx)(j.$,{type:"button",variant:"outline",onClick:()=>c.back(),children:"Cancel"})]})]})]}),(0,d.jsxs)(q.Zp,{children:[(0,d.jsx)(q.aR,{children:(0,d.jsx)(q.ZB,{children:"Featured Image"})}),(0,d.jsx)(q.Wu,{children:(0,d.jsx)(p.zB,{control:B.control,name:"image",render:({field:a})=>(0,d.jsxs)(p.eI,{children:[(0,d.jsx)(p.MJ,{children:(0,d.jsx)(Q.B,{value:a.value,onChange:a.onChange,onRemove:()=>a.onChange(""),folder:"blogs"})}),(0,d.jsx)(p.C5,{})]})})})]}),(0,d.jsxs)(q.Zp,{children:[(0,d.jsx)(q.aR,{children:(0,d.jsx)(q.ZB,{children:"Category & Tags"})}),(0,d.jsxs)(q.Wu,{className:"space-y-4",children:[(0,d.jsx)(p.zB,{control:B.control,name:"categoryId",render:({field:a})=>(0,d.jsxs)(p.eI,{children:[(0,d.jsx)(p.lR,{children:"Category"}),(0,d.jsxs)(o.l6,{onValueChange:a.onChange,defaultValue:a.value,children:[(0,d.jsx)(p.MJ,{children:(0,d.jsx)(o.bq,{children:(0,d.jsx)(o.yv,{placeholder:"Select category"})})}),(0,d.jsxs)(o.gC,{children:[(0,d.jsx)(o.eb,{value:"none",children:"No category"}),i.map(a=>(0,d.jsx)(o.eb,{value:a.id,children:a.name},a.id))]})]}),(0,d.jsx)(p.C5,{})]})}),(0,d.jsxs)("div",{children:[(0,d.jsx)(l.J,{children:"Tags"}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(k.p,{placeholder:"Add new tag",value:x,onChange:a=>y(a.target.value),onKeyPress:a=>"Enter"===a.key&&(a.preventDefault(),C())}),(0,d.jsx)(j.$,{type:"button",onClick:C,size:"sm",children:"Add"})]}),(0,d.jsx)("div",{className:"flex flex-wrap gap-2",children:t.map(a=>(0,d.jsxs)(r.E,{variant:v.includes(a.id)?"default":"outline",className:"cursor-pointer",onClick:()=>{var b;return b=a.id,void w(a=>a.includes(b)?a.filter(a=>a!==b):[...a,b])},children:[a.name,v.includes(a.id)&&(0,d.jsx)(S.A,{className:"h-3 w-3 ml-1"})]},a.id))})]})]})]})]}),(0,d.jsxs)(q.Zp,{children:[(0,d.jsxs)(q.aR,{children:[(0,d.jsx)(q.ZB,{children:"SEO Settings"}),(0,d.jsx)(q.BT,{children:"Optimize your post for search engines"})]}),(0,d.jsxs)(q.Wu,{className:"space-y-4",children:[(0,d.jsx)(p.zB,{control:B.control,name:"metaTitle",render:({field:a})=>(0,d.jsxs)(p.eI,{children:[(0,d.jsx)(p.lR,{children:"Meta Title"}),(0,d.jsx)(p.MJ,{children:(0,d.jsx)(k.p,{placeholder:"SEO title",...a})}),(0,d.jsx)(p.Rr,{children:"Leave empty to use the post title"}),(0,d.jsx)(p.C5,{})]})}),(0,d.jsx)(p.zB,{control:B.control,name:"metaDescription",render:({field:a})=>(0,d.jsxs)(p.eI,{children:[(0,d.jsx)(p.lR,{children:"Meta Description"}),(0,d.jsx)(p.MJ,{children:(0,d.jsx)(m.T,{placeholder:"SEO description",...a})}),(0,d.jsx)(p.Rr,{children:"Brief description for search engines"}),(0,d.jsx)(p.C5,{})]})}),(0,d.jsx)(p.zB,{control:B.control,name:"keywords",render:({field:a})=>(0,d.jsxs)(p.eI,{children:[(0,d.jsx)(p.lR,{children:"Keywords"}),(0,d.jsx)(p.MJ,{children:(0,d.jsx)(k.p,{placeholder:"keyword1, keyword2, keyword3",...a})}),(0,d.jsx)(p.Rr,{children:"Comma-separated keywords for SEO"}),(0,d.jsx)(p.C5,{})]})})]})]})]})]})})})}},96834:(a,b,c)=>{c.d(b,{E:()=>i});var d=c(60687);c(43210);var e=c(8730),f=c(24224),g=c(4780);let h=(0,f.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function i({className:a,variant:b,asChild:c=!1,...f}){let i=c?e.DX:"span";return(0,d.jsx)(i,{"data-slot":"badge",className:(0,g.cn)(h({variant:b}),a),...f})}}};