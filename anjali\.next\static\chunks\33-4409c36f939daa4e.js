"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[33],{1469:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return h},getImageProps:function(){return a}});let s=r(8229),i=r(8883),n=r(3063),u=s._(r(1193));function a(e){let{props:t}=(0,i.getImgProps)(e,{defaultLoader:u.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let h=n.Image},2960:(e,t,r)=>{r.d(t,{I:()=>m});var s=r(920),i=r(7165),n=r(9853),u=r(5910),a=r(3504),h=r(2020),l=class extends u.Q{constructor(e,t){super(),this.options=t,this.#e=e,this.#t=null,this.#r=(0,a.T)(),this.options.experimental_prefetchInRender||this.#r.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#e;#s=void 0;#i=void 0;#n=void 0;#u;#a;#r;#t;#h;#l;#c;#o;#d;#p;#f=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#s.addObserver(this),c(this.#s,this.options)?this.#y():this.updateResult(),this.#R())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return o(this.#s,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return o(this.#s,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#v(),this.#b(),this.#s.removeObserver(this)}setOptions(e){let t=this.options,r=this.#s;if(this.options=this.#e.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,h.Eh)(this.options.enabled,this.#s))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#m(),this.#s.setOptions(this.options),t._defaulted&&!(0,h.f8)(this.options,t)&&this.#e.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#s,observer:this});let s=this.hasListeners();s&&d(this.#s,r,this.options,t)&&this.#y(),this.updateResult(),s&&(this.#s!==r||(0,h.Eh)(this.options.enabled,this.#s)!==(0,h.Eh)(t.enabled,this.#s)||(0,h.d2)(this.options.staleTime,this.#s)!==(0,h.d2)(t.staleTime,this.#s))&&this.#Q();let i=this.#g();s&&(this.#s!==r||(0,h.Eh)(this.options.enabled,this.#s)!==(0,h.Eh)(t.enabled,this.#s)||i!==this.#p)&&this.#I(i)}getOptimisticResult(e){var t,r;let s=this.#e.getQueryCache().build(this.#e,e),i=this.createResult(s,e);return t=this,r=i,(0,h.f8)(t.getCurrentResult(),r)||(this.#n=i,this.#a=this.options,this.#u=this.#s.state),i}getCurrentResult(){return this.#n}trackResult(e,t){return new Proxy(e,{get:(e,r)=>(this.trackProp(r),t?.(r),Reflect.get(e,r))})}trackProp(e){this.#f.add(e)}getCurrentQuery(){return this.#s}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#e.defaultQueryOptions(e),r=this.#e.getQueryCache().build(this.#e,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#y({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#n))}#y(e){this.#m();let t=this.#s.fetch(this.options,e);return e?.throwOnError||(t=t.catch(h.lQ)),t}#Q(){this.#v();let e=(0,h.d2)(this.options.staleTime,this.#s);if(h.S$||this.#n.isStale||!(0,h.gn)(e))return;let t=(0,h.j3)(this.#n.dataUpdatedAt,e);this.#o=setTimeout(()=>{this.#n.isStale||this.updateResult()},t+1)}#g(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#s):this.options.refetchInterval)??!1}#I(e){this.#b(),this.#p=e,!h.S$&&!1!==(0,h.Eh)(this.options.enabled,this.#s)&&(0,h.gn)(this.#p)&&0!==this.#p&&(this.#d=setInterval(()=>{(this.options.refetchIntervalInBackground||s.m.isFocused())&&this.#y()},this.#p))}#R(){this.#Q(),this.#I(this.#g())}#v(){this.#o&&(clearTimeout(this.#o),this.#o=void 0)}#b(){this.#d&&(clearInterval(this.#d),this.#d=void 0)}createResult(e,t){let r,s=this.#s,i=this.options,u=this.#n,l=this.#u,o=this.#a,f=e!==s?e.state:this.#i,{state:y}=e,R={...y},v=!1;if(t._optimisticResults){let r=this.hasListeners(),u=!r&&c(e,t),a=r&&d(e,s,t,i);(u||a)&&(R={...R,...(0,n.k)(y.data,e.options)}),"isRestoring"===t._optimisticResults&&(R.fetchStatus="idle")}let{error:b,errorUpdatedAt:m,status:Q}=R;r=R.data;let g=!1;if(void 0!==t.placeholderData&&void 0===r&&"pending"===Q){let e;u?.isPlaceholderData&&t.placeholderData===o?.placeholderData?(e=u.data,g=!0):e="function"==typeof t.placeholderData?t.placeholderData(this.#c?.state.data,this.#c):t.placeholderData,void 0!==e&&(Q="success",r=(0,h.pl)(u?.data,e,t),v=!0)}if(t.select&&void 0!==r&&!g)if(u&&r===l?.data&&t.select===this.#h)r=this.#l;else try{this.#h=t.select,r=t.select(r),r=(0,h.pl)(u?.data,r,t),this.#l=r,this.#t=null}catch(e){this.#t=e}this.#t&&(b=this.#t,r=this.#l,m=Date.now(),Q="error");let I="fetching"===R.fetchStatus,O="pending"===Q,S="error"===Q,E=O&&I,T=void 0!==r,C={status:Q,fetchStatus:R.fetchStatus,isPending:O,isSuccess:"success"===Q,isError:S,isInitialLoading:E,isLoading:E,data:r,dataUpdatedAt:R.dataUpdatedAt,error:b,errorUpdatedAt:m,failureCount:R.fetchFailureCount,failureReason:R.fetchFailureReason,errorUpdateCount:R.errorUpdateCount,isFetched:R.dataUpdateCount>0||R.errorUpdateCount>0,isFetchedAfterMount:R.dataUpdateCount>f.dataUpdateCount||R.errorUpdateCount>f.errorUpdateCount,isFetching:I,isRefetching:I&&!O,isLoadingError:S&&!T,isPaused:"paused"===R.fetchStatus,isPlaceholderData:v,isRefetchError:S&&T,isStale:p(e,t),refetch:this.refetch,promise:this.#r,isEnabled:!1!==(0,h.Eh)(t.enabled,e)};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===C.status?e.reject(C.error):void 0!==C.data&&e.resolve(C.data)},r=()=>{t(this.#r=C.promise=(0,a.T)())},i=this.#r;switch(i.status){case"pending":e.queryHash===s.queryHash&&t(i);break;case"fulfilled":("error"===C.status||C.data!==i.value)&&r();break;case"rejected":("error"!==C.status||C.error!==i.reason)&&r()}}return C}updateResult(){let e=this.#n,t=this.createResult(this.#s,this.options);this.#u=this.#s.state,this.#a=this.options,void 0!==this.#u.data&&(this.#c=this.#s),(0,h.f8)(t,e)||(this.#n=t,this.#O({listeners:(()=>{if(!e)return!0;let{notifyOnChangeProps:t}=this.options,r="function"==typeof t?t():t;if("all"===r||!r&&!this.#f.size)return!0;let s=new Set(r??this.#f);return this.options.throwOnError&&s.add("error"),Object.keys(this.#n).some(t=>this.#n[t]!==e[t]&&s.has(t))})()}))}#m(){let e=this.#e.getQueryCache().build(this.#e,this.options);if(e===this.#s)return;let t=this.#s;this.#s=e,this.#i=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#R()}#O(e){i.jG.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#n)}),this.#e.getQueryCache().notify({query:this.#s,type:"observerResultsUpdated"})})}};function c(e,t){return!1!==(0,h.Eh)(t.enabled,e)&&void 0===e.state.data&&("error"!==e.state.status||!1!==t.retryOnMount)||void 0!==e.state.data&&o(e,t,t.refetchOnMount)}function o(e,t,r){if(!1!==(0,h.Eh)(t.enabled,e)&&"static"!==(0,h.d2)(t.staleTime,e)){let s="function"==typeof r?r(e):r;return"always"===s||!1!==s&&p(e,t)}return!1}function d(e,t,r,s){return(e!==t||!1===(0,h.Eh)(s.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&p(e,r)}function p(e,t){return!1!==(0,h.Eh)(t.enabled,e)&&e.isStaleByTime((0,h.d2)(t.staleTime,e))}var f=r(2115),y=r(6715);r(5155);var R=f.createContext(function(){let e=!1;return{clearReset:()=>{e=!1},reset:()=>{e=!0},isReset:()=>e}}()),v=f.createContext(!1);v.Provider;var b=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function m(e,t){return function(e,t,r){var s,n,u,a,l;let c=f.useContext(v),o=f.useContext(R),d=(0,y.jE)(r),p=d.defaultQueryOptions(e);if(null==(n=d.getDefaultOptions().queries)||null==(s=n._experimental_beforeQuery)||s.call(n,p),p._optimisticResults=c?"isRestoring":"optimistic",p.suspense){let e=e=>"static"===e?e:Math.max(e??1e3,1e3),t=p.staleTime;p.staleTime="function"==typeof t?(...r)=>e(t(...r)):e(t),"number"==typeof p.gcTime&&(p.gcTime=Math.max(p.gcTime,1e3))}(p.suspense||p.throwOnError||p.experimental_prefetchInRender)&&!o.isReset()&&(p.retryOnMount=!1),f.useEffect(()=>{o.clearReset()},[o]);let m=!d.getQueryCache().get(p.queryHash),[Q]=f.useState(()=>new t(d,p)),g=Q.getOptimisticResult(p),I=!c&&!1!==e.subscribed;if(f.useSyncExternalStore(f.useCallback(e=>{let t=I?Q.subscribe(i.jG.batchCalls(e)):h.lQ;return Q.updateResult(),t},[Q,I]),()=>Q.getCurrentResult(),()=>Q.getCurrentResult()),f.useEffect(()=>{Q.setOptions(p)},[p,Q]),p?.suspense&&g.isPending)throw b(p,Q,o);if((e=>{let{result:t,errorResetBoundary:r,throwOnError:s,query:i,suspense:n}=e;return t.isError&&!r.isReset()&&!t.isFetching&&i&&(n&&void 0===t.data||(0,h.GU)(s,[t.error,i]))})({result:g,errorResetBoundary:o,throwOnError:p.throwOnError,query:d.getQueryCache().get(p.queryHash),suspense:p.suspense}))throw g.error;if(null==(a=d.getDefaultOptions().queries)||null==(u=a._experimental_afterQuery)||u.call(a,p,g),p.experimental_prefetchInRender&&!h.S$&&g.isLoading&&g.isFetching&&!c){let e=m?b(p,Q,o):null==(l=d.getQueryCache().get(p.queryHash))?void 0:l.promise;null==e||e.catch(h.lQ).finally(()=>{Q.updateResult()})}return p.notifyOnChangeProps?g:Q.trackResult(g)}(e,l,t)}},6766:(e,t,r)=>{r.d(t,{default:()=>i.a});var s=r(1469),i=r.n(s)}}]);