(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9076],{38:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var i=s(5155),n=s(2115),o=s(5695),r=s(2825),l=s(6671);function a(){let e=(0,o.useParams)(),[t,s]=(0,n.useState)(null),[a,d]=(0,n.useState)(!0);if((0,n.useEffect)(()=>{let t=async()=>{try{let t=await fetch("/api/blogs/".concat(e.id));if(t.ok){let e=await t.json();s(e)}else l.oR.error("Blog not found")}catch(e){l.oR.error("Error fetching blog")}finally{d(!1)}};e.id&&t()},[e.id]),a)return(0,i.jsx)("div",{children:"Loading..."});if(!t)return(0,i.jsx)("div",{children:"Blog not found"});let c={...t,keywords:t.keywords.join(", ")};return(0,i.jsxs)("div",{className:"space-y-6",children:[(0,i.jsxs)("div",{children:[(0,i.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Blog Post"}),(0,i.jsx)("p",{className:"text-muted-foreground",children:"Update your blog post content and settings"})]}),(0,i.jsx)(r.BlogForm,{initialData:c,isEditing:!0})]})}},1673:(e,t,s)=>{Promise.resolve().then(s.bind(s,38))}},e=>{e.O(0,[5004,277,5389,6671,651,7536,7764,8062,2804,7166,9304,2825,8441,5964,7358],()=>e(e.s=1673)),_N_E=e.O()}]);