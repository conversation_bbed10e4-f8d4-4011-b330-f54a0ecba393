{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/lib/utils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: string): string {\n  return price.replace(/NPR\\s*/g, 'NPR ')\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function generateWhatsAppLink(phone: string, message: string): string {\n  const cleanPhone = phone.replace(/[^\\d+]/g, '')\n  const encodedMessage = encodeURIComponent(message)\n  return `https://wa.me/${cleanPhone}?text=${encodedMessage}`\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength).replace(/\\s+\\S*$/, '') + '...'\n}\n\nexport function getImageUrl(imagePath: string): string {\n  // Handle placeholder images for development\n  if (imagePath.startsWith('/images/')) {\n    return `https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=800&h=600&fit=crop&crop=face`\n  }\n  return imagePath\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function validatePhone(phone: string): boolean {\n  const phoneRegex = /^(\\+977)?[0-9]{10}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function scrollToElement(elementId: string, offset: number = 80): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const elementPosition = element.getBoundingClientRect().top\n    const offsetPosition = elementPosition + window.pageYOffset - offset\n    \n    window.scrollTo({\n      top: offsetPosition,\n      behavior: 'smooth'\n    })\n  }\n}\n\nexport function isInViewport(element: Element): boolean {\n  const rect = element.getBoundingClientRect()\n  return (\n    rect.top >= 0 &&\n    rect.left >= 0 &&\n    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&\n    rect.right <= (window.innerWidth || document.documentElement.clientWidth)\n  )\n}\n\nexport function getReadingTime(content: string): string {\n  const wordsPerMinute = 200\n  const words = content.trim().split(/\\s+/).length\n  const minutes = Math.ceil(words / wordsPerMinute)\n  return `${minutes} min read`\n}\n\nexport function generateSEOSchema(type: 'Organization' | 'LocalBusiness' | 'Article', data: any) {\n  const baseSchema = {\n    '@context': 'https://schema.org',\n    '@type': type\n  }\n\n  switch (type) {\n    case 'LocalBusiness':\n      return {\n        ...baseSchema,\n        name: data.name,\n        description: data.description,\n        url: data.url,\n        telephone: data.phone,\n        address: {\n          '@type': 'PostalAddress',\n          streetAddress: data.address.street,\n          addressLocality: data.address.city,\n          addressRegion: data.address.state,\n          addressCountry: data.address.country,\n          postalCode: data.address.zipCode\n        },\n        geo: data.geo,\n        openingHours: data.openingHours,\n        priceRange: data.priceRange,\n        serviceArea: data.serviceArea\n      }\n    \n    case 'Article':\n      return {\n        ...baseSchema,\n        headline: data.title,\n        description: data.description,\n        author: {\n          '@type': 'Person',\n          name: data.author\n        },\n        datePublished: data.publishedAt,\n        dateModified: data.updatedAt,\n        image: data.image,\n        url: data.url\n      }\n    \n    default:\n      return baseSchema\n  }\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;;;;;;;;;;;AACrD;AACA;;;AAEO,SAAS;IAAG,IAAA,IAAA,OAAA,UAAA,QAAA,AAAG,SAAH,UAAA,OAAA,OAAA,GAAA,OAAA,MAAA;QAAG,OAAH,QAAA,SAAA,CAAA,KAAuB;;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,MAAM,OAAO,CAAC,WAAW;AAClC;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,qBAAqB,KAAa,EAAE,OAAe;IACjE,MAAM,aAAa,MAAM,OAAO,CAAC,WAAW;IAC5C,MAAM,iBAAiB,mBAAmB;IAC1C,OAAO,AAAC,iBAAmC,OAAnB,YAAW,UAAuB,OAAf;AAC7C;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;AAC3D;AAEO,SAAS,YAAY,SAAiB;IAC3C,4CAA4C;IAC5C,IAAI,UAAU,UAAU,CAAC,aAAa;QACpC,OAAQ;IACV;IACA,OAAO;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO;yCAAI;YAAA;;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,gBAAgB,SAAiB;QAAE,SAAA,iEAAiB;IAClE,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;QAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;QAE9D,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;AACF;AAEO,SAAS,aAAa,OAAgB;IAC3C,MAAM,OAAO,QAAQ,qBAAqB;IAC1C,OACE,KAAK,GAAG,IAAI,KACZ,KAAK,IAAI,IAAI,KACb,KAAK,MAAM,IAAI,CAAC,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,YAAY,KAC3E,KAAK,KAAK,IAAI,CAAC,OAAO,UAAU,IAAI,SAAS,eAAe,CAAC,WAAW;AAE5E;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,iBAAiB;IACvB,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAChD,MAAM,UAAU,KAAK,IAAI,CAAC,QAAQ;IAClC,OAAO,AAAC,GAAU,OAAR,SAAQ;AACpB;AAEO,SAAS,kBAAkB,IAAkD,EAAE,IAAS;IAC7F,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;IACX;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,GAAG,UAAU;gBACb,MAAM,KAAK,IAAI;gBACf,aAAa,KAAK,WAAW;gBAC7B,KAAK,KAAK,GAAG;gBACb,WAAW,KAAK,KAAK;gBACrB,SAAS;oBACP,SAAS;oBACT,eAAe,KAAK,OAAO,CAAC,MAAM;oBAClC,iBAAiB,KAAK,OAAO,CAAC,IAAI;oBAClC,eAAe,KAAK,OAAO,CAAC,KAAK;oBACjC,gBAAgB,KAAK,OAAO,CAAC,OAAO;oBACpC,YAAY,KAAK,OAAO,CAAC,OAAO;gBAClC;gBACA,KAAK,KAAK,GAAG;gBACb,cAAc,KAAK,YAAY;gBAC/B,YAAY,KAAK,UAAU;gBAC3B,aAAa,KAAK,WAAW;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,UAAU;gBACb,UAAU,KAAK,KAAK;gBACpB,aAAa,KAAK,WAAW;gBAC7B,QAAQ;oBACN,SAAS;oBACT,MAAM,KAAK,MAAM;gBACnB;gBACA,eAAe,KAAK,WAAW;gBAC/B,cAAc,KAAK,SAAS;gBAC5B,OAAO,KAAK,KAAK;gBACjB,KAAK,KAAK,GAAG;YACf;QAEF;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/button.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-rose-gold text-white shadow hover:bg-rose-gold-dark\",\n        destructive: \"bg-red-500 text-white shadow-sm hover:bg-red-600\",\n        outline: \"border border-rose-gold text-rose-gold-dark bg-transparent shadow-sm hover:bg-rose-gold hover:text-white\",\n        secondary: \"bg-blush-pink text-text-primary shadow-sm hover:bg-blush-pink-dark\",\n        ghost: \"hover:bg-rose-gold-light hover:text-rose-gold-dark\",\n        link: \"text-rose-gold-dark underline-offset-4 hover:underline\",\n        gradient: \"bg-gradient-to-r from-rose-gold to-blush-pink text-white shadow hover:from-rose-gold-dark hover:to-blush-pink-dark\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2\",\n        sm: \"h-8 rounded-md px-3 text-xs\",\n        lg: \"h-10 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-9 w-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\n    const Comp = asChild ? Slot : \"button\"\n    return (\n      <Comp\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        {...props}\n      />\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,ySACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aAAa;YACb,SAAS;YACT,WAAW;YACX,OAAO;YACP,MAAM;YACN,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,6JAAA,CAAA,aAAgB,MAC7B,QAA0D;QAAzD,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO;IACtD,MAAM,OAAO,UAAU,mKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/lib/data.ts"], "sourcesContent": ["import servicesData from '@/data/services.json'\nimport packagesData from '@/data/packages.json'\nimport testimonialsData from '@/data/testimonials.json'\nimport galleryData from '@/data/gallery.json'\nimport blogData from '@/data/blog.json'\nimport siteConfigData from '@/data/site-config.json'\nimport { formatPrice, formatDate, generateWhatsAppLink } from '@/lib/utils'\n\nexport interface Service {\n  id: string\n  title: string\n  description: string\n  features: string[]\n  duration: string\n  price: string\n  image: string\n  category: string\n  popular: boolean\n}\n\nexport interface Package {\n  id: string\n  title: string\n  description: string\n  services: string[]\n  duration: string\n  originalPrice: string\n  discountedPrice: string\n  savings: string\n  image: string\n  popular: boolean\n  badge: string | null\n}\n\nexport interface Testimonial {\n  id: string\n  name: string\n  location: string\n  service: string\n  rating: number\n  text: string\n  image: string\n  date: string\n  featured: boolean\n}\n\nexport interface GalleryItem {\n  id: string\n  title: string\n  description: string\n  image: string\n  category: string\n  featured: boolean\n  tags: string[]\n}\n\nexport interface BlogPost {\n  id: string\n  title: string\n  slug: string\n  excerpt: string\n  content: string\n  author: string\n  publishedAt: string\n  updatedAt: string\n  featured: boolean\n  image: string\n  tags: string[]\n  category: string\n  readTime: string\n  seo: {\n    metaTitle: string\n    metaDescription: string\n    keywords: string[]\n  }\n}\n\nexport interface SiteConfig {\n  site: {\n    name: string\n    tagline: string\n    description: string\n    url: string\n    logo: string\n    favicon: string\n  }\n  contact: {\n    phone: string\n    whatsapp: string\n    email: string\n    address: {\n      street: string\n      city: string\n      state: string\n      country: string\n      zipCode: string\n    }\n    workingHours: Record<string, string>\n  }\n  social: {\n    facebook: string\n    instagram: string\n    tiktok: string\n    youtube: string\n  }\n  serviceAreas: Array<{\n    name: string\n    primary: boolean\n    travelFee: number\n  }>\n  whatsappMessage: string\n  seo: {\n    defaultTitle: string\n    defaultDescription: string\n    keywords: string[]\n    author: string\n    twitterHandle: string\n  }\n  analytics: {\n    googleAnalyticsId: string\n  }\n}\n\n// Services\nexport function getServices(): Service[] {\n  return servicesData.services\n}\n\nexport function getService(id: string): Service | undefined {\n  return servicesData.services.find(service => service.id === id)\n}\n\nexport function getPopularServices(): Service[] {\n  return servicesData.services.filter(service => service.popular)\n}\n\nexport function getServicesByCategory(category: string): Service[] {\n  return servicesData.services.filter(service => service.category === category)\n}\n\n// Packages\nexport function getPackages(): Package[] {\n  return packagesData.packages\n}\n\nexport function getPackage(id: string): Package | undefined {\n  return packagesData.packages.find(pkg => pkg.id === id)\n}\n\nexport function getPopularPackages(): Package[] {\n  return packagesData.packages.filter(pkg => pkg.popular)\n}\n\n// Testimonials\nexport function getTestimonials(): Testimonial[] {\n  return testimonialsData.testimonials\n}\n\nexport function getFeaturedTestimonials(): Testimonial[] {\n  return testimonialsData.testimonials.filter(testimonial => testimonial.featured)\n}\n\nexport function getTestimonialsByService(service: string): Testimonial[] {\n  return testimonialsData.testimonials.filter(testimonial => \n    testimonial.service.toLowerCase().includes(service.toLowerCase())\n  )\n}\n\n// Gallery\nexport function getGalleryItems(): GalleryItem[] {\n  return galleryData.gallery\n}\n\nexport function getFeaturedGalleryItems(): GalleryItem[] {\n  return galleryData.gallery.filter(item => item.featured)\n}\n\nexport function getGalleryItemsByCategory(category: string): GalleryItem[] {\n  if (category === 'all') return galleryData.gallery\n  return galleryData.gallery.filter(item => item.category === category)\n}\n\nexport function getGalleryCategories() {\n  return galleryData.categories\n}\n\n// Blog\nexport function getBlogPosts(): BlogPost[] {\n  return blogData.posts\n}\n\nexport function getBlogPost(slug: string): BlogPost | undefined {\n  return blogData.posts.find(post => post.slug === slug)\n}\n\nexport function getFeaturedBlogPosts(): BlogPost[] {\n  return blogData.posts.filter(post => post.featured)\n}\n\nexport function getBlogPostsByCategory(category: string): BlogPost[] {\n  return blogData.posts.filter(post => \n    post.category.toLowerCase().replace(/\\s+/g, '-') === category.toLowerCase()\n  )\n}\n\nexport function getBlogCategories() {\n  return blogData.categories\n}\n\nexport function getRelatedBlogPosts(currentPost: BlogPost, limit: number = 3): BlogPost[] {\n  return blogData.posts\n    .filter(post => \n      post.id !== currentPost.id && \n      (post.category === currentPost.category || \n       post.tags.some(tag => currentPost.tags.includes(tag)))\n    )\n    .slice(0, limit)\n}\n\n// Site Configuration\nexport function getSiteConfig(): SiteConfig {\n  return {\n    ...siteConfigData,\n    analytics: {\n      googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID || siteConfigData.analytics.googleAnalyticsId\n    }\n  } as SiteConfig\n}\n\nexport function getContactInfo() {\n  return siteConfigData.contact\n}\n\nexport function getSocialLinks() {\n  return siteConfigData.social\n}\n\nexport function getServiceAreas() {\n  return siteConfigData.serviceAreas\n}\n\nexport function getWhatsAppMessage() {\n  return siteConfigData.whatsappMessage\n}\n\nexport function getSEODefaults() {\n  return siteConfigData.seo\n}\n\n// Re-export utility functions\nexport { formatPrice, formatDate, generateWhatsAppLink }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgOyB;AAhOzB;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAsHO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ;AAC9B;AAEO,SAAS,WAAW,EAAU;IACnC,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AAC9D;AAEO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO;AAChE;AAEO,SAAS,sBAAsB,QAAgB;IACpD,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACtE;AAGO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ;AAC9B;AAEO,SAAS,WAAW,EAAU;IACnC,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;AACtD;AAEO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,OAAO;AACxD;AAGO,SAAS;IACd,OAAO,mGAAA,CAAA,UAAgB,CAAC,YAAY;AACtC;AAEO,SAAS;IACd,OAAO,mGAAA,CAAA,UAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,cAAe,YAAY,QAAQ;AACjF;AAEO,SAAS,yBAAyB,OAAe;IACtD,OAAO,mGAAA,CAAA,UAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,cAC1C,YAAY,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW;AAElE;AAGO,SAAS;IACd,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO;AAC5B;AAEO,SAAS;IACd,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AACzD;AAEO,SAAS,0BAA0B,QAAgB;IACxD,IAAI,aAAa,OAAO,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO;IAClD,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;AAC9D;AAEO,SAAS;IACd,OAAO,8FAAA,CAAA,UAAW,CAAC,UAAU;AAC/B;AAGO,SAAS;IACd,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK;AACvB;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;AACnD;AAEO,SAAS;IACd,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AACpD;AAEO,SAAS,uBAAuB,QAAgB;IACrD,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,OAC3B,KAAK,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,SAAS,SAAS,WAAW;AAE7E;AAEO,SAAS;IACd,OAAO,2FAAA,CAAA,UAAQ,CAAC,UAAU;AAC5B;AAEO,SAAS,oBAAoB,WAAqB;QAAE,QAAA,iEAAgB;IACzE,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAClB,MAAM,CAAC,CAAA,OACN,KAAK,EAAE,KAAK,YAAY,EAAE,IAC1B,CAAC,KAAK,QAAQ,KAAK,YAAY,QAAQ,IACtC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,YAAY,IAAI,CAAC,QAAQ,CAAC,KAAK,GAEvD,KAAK,CAAC,GAAG;AACd;AAGO,SAAS;IACd,OAAO;QACL,GAAG,qGAAA,CAAA,UAAc;QACjB,WAAW;YACT,mBAAmB,oDAAiC,qGAAA,CAAA,UAAc,CAAC,SAAS,CAAC,iBAAiB;QAChG;IACF;AACF;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,OAAO;AAC/B;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,MAAM;AAC9B;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,YAAY;AACpC;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,eAAe;AACvC;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,GAAG;AAC3B", "debugId": null}}, {"offset": {"line": 396, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/layout/header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport Link from 'next/link'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { Menu, X, Phone, Instagram, Facebook } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\nimport { getSiteConfig, getSocialLinks } from '@/lib/data'\nimport { generateWhatsAppLink } from '@/lib/utils'\nimport { cn } from '@/lib/utils'\nimport { PiTiktokLogo } from \"react-icons/pi\";\n\nconst navigation = [\n  { name: 'Home', href: '/' },\n  { name: 'About', href: '/about' },\n  { name: 'Services', href: '/services' },\n  { name: 'Packages', href: '/packages' },\n  { name: 'Portfolio', href: '/portfolio' },\n  { name: 'Blog', href: '/blog' },\n  { name: 'Contact', href: '/contact' },\n]\n\nexport default function Header() {\n  const [isOpen, setIsOpen] = useState(false)\n  const [isScrolled, setIsScrolled] = useState(false)\n  const siteConfig = getSiteConfig()\n  const socialLinks = getSocialLinks()\n\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 10)\n    }\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  const whatsappLink = generateWhatsAppLink(\n    siteConfig.contact.whatsapp,\n    siteConfig.whatsappMessage\n  )\n\n  return (\n    <header\n      className={cn(\n        'fixed top-0 left-0 right-0 z-50 transition-all duration-300',\n        isScrolled\n          ? 'bg-white/95 backdrop-blur-md shadow-md'\n          : 'bg-white'\n      )}\n    >\n      <nav id=\"navigation\" className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <div className=\"flex-shrink-0\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-rose-gold to-blush-pink flex items-center justify-center\">\n                <span className=\"text-white font-display font-bold text-lg\">A</span>\n              </div>\n              <span className=\"font-display text-xl font-semibold text-text-primary\">\n                {siteConfig.site.name}\n              </span>\n            </Link>\n          </div>\n\n          {/* Desktop Navigation */}\n          <div className=\"hidden md:block\">\n            <div className=\"ml-10 flex items-baseline space-x-4\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-text-primary hover:text-rose-gold-dark px-3 py-2 rounded-md text-sm font-medium transition-colors\"\n                >\n                  {item.name}\n                </Link>\n              ))}\n            </div>\n          </div>\n\n          {/* Desktop CTA */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            <div className=\"flex items-center space-x-2\">\n              <Link\n                href={socialLinks.instagram}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n              >\n                <Instagram className=\"h-5 w-5\" />\n              </Link>\n              <Link\n                href={socialLinks.facebook}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n              >\n                <Facebook className=\"h-5 w-5\" />\n              </Link>\n              <Link\n                href={socialLinks.tiktok}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n              >\n                <PiTiktokLogo className=\"h-5 w-5\" />\n              </Link>\n            </div>\n            <Button asChild variant=\"gradient\" size=\"sm\">\n              <Link href={whatsappLink} target=\"_blank\" rel=\"noopener noreferrer\">\n                <Phone className=\"h-4 w-4\" />\n                Book Now\n              </Link>\n            </Button>\n          </div>\n\n          {/* Mobile menu button */}\n          <div className=\"md:hidden\">\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={() => setIsOpen(!isOpen)}\n              aria-label=\"Toggle menu\"\n            >\n              {isOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n            </Button>\n          </div>\n        </div>\n      </nav>\n\n      {/* Mobile Navigation */}\n      <AnimatePresence>\n        {isOpen && (\n          <motion.div\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: 'auto' }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n            className=\"md:hidden bg-white border-t border-gray-200\"\n          >\n            <div className=\"px-2 pt-2 pb-3 space-y-1 sm:px-3\">\n              {navigation.map((item) => (\n                <Link\n                  key={item.name}\n                  href={item.href}\n                  className=\"text-text-primary hover:text-rose-gold-dark block px-3 py-2 rounded-md text-base font-medium transition-colors\"\n                  onClick={() => setIsOpen(false)}\n                >\n                  {item.name}\n                </Link>\n              ))}\n              <div className=\"pt-4 pb-2 border-t border-gray-200\">\n                <div className=\"flex items-center justify-between px-3\">\n                  <div className=\"flex items-center space-x-4\">\n                    <Link\n                      href={socialLinks.instagram}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n                    >\n                      <Instagram className=\"h-6 w-6\" />\n                    </Link>\n                    <Link\n                      href={socialLinks.facebook}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n                    >\n                      <Facebook className=\"h-6 w-6\" />\n                    </Link>\n                    <Link\n                      href={socialLinks.tiktok}\n                      target=\"_blank\"\n                      rel=\"noopener noreferrer\"\n                      className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n                    >\n                      <PiTiktokLogo className=\"h-6 w-6\" />\n                    </Link>\n                  </div>\n                  <Button asChild variant=\"gradient\" size=\"sm\">\n                    <Link href={whatsappLink} target=\"_blank\" rel=\"noopener noreferrer\">\n                      <Phone className=\"h-4 w-4\" />\n                      Book Now\n                    </Link>\n                  </Button>\n                </div>\n              </div>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AAEA;;;AAVA;;;;;;;;;;AAYA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAQ,MAAM;IAAI;IAC1B;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAEc,SAAS;;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,aAAa,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,cAAc,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD;IAEjC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EACtC,WAAW,OAAO,CAAC,QAAQ,EAC3B,WAAW,eAAe;IAG5B,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,2CACA;;0BAGN,6LAAC;gBAAI,IAAG;gBAAa,WAAU;0BAC7B,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,+JAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA4C;;;;;;;;;;;kDAE9D,6LAAC;wCAAK,WAAU;kDACb,WAAW,IAAI,CAAC,IAAI;;;;;;;;;;;;;;;;;sCAM3B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;kDAET,KAAK,IAAI;uCAJL,KAAK,IAAI;;;;;;;;;;;;;;;sCAWtB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,YAAY,SAAS;4CAC3B,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,6LAAC,+MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,YAAY,QAAQ;4CAC1B,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,YAAY,MAAM;4CACxB,QAAO;4CACP,KAAI;4CACJ,WAAU;sDAEV,cAAA,6LAAC,iJAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;8CAG5B,6LAAC,qIAAA,CAAA,SAAM;oCAAC,OAAO;oCAAC,SAAQ;oCAAW,MAAK;8CACtC,cAAA,6LAAC,+JAAA,CAAA,UAAI;wCAAC,MAAM;wCAAc,QAAO;wCAAS,KAAI;;0DAC5C,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;;;;;;sCAOnC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,UAAU,CAAC;gCAC1B,cAAW;0CAEV,uBAAS,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;yDAAe,6LAAC,qMAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9D,6LAAC,4LAAA,CAAA,kBAAe;0BACb,wBACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBACjC,SAAS;wBAAE,SAAS;wBAAG,QAAQ;oBAAO;oBACtC,MAAM;wBAAE,SAAS;wBAAG,QAAQ;oBAAE;oBAC9B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;8BAEV,cAAA,6LAAC;wBAAI,WAAU;;4BACZ,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC,+JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;oCACV,SAAS,IAAM,UAAU;8CAExB,KAAK,IAAI;mCALL,KAAK,IAAI;;;;;0CAQlB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,YAAY,SAAS;oDAC3B,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,6LAAC,+MAAA,CAAA,YAAS;wDAAC,WAAU;;;;;;;;;;;8DAEvB,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,YAAY,QAAQ;oDAC1B,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,6LAAC,6MAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;;;;;;8DAEtB,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,YAAY,MAAM;oDACxB,QAAO;oDACP,KAAI;oDACJ,WAAU;8DAEV,cAAA,6LAAC,iJAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAG5B,6LAAC,qIAAA,CAAA,SAAM;4CAAC,OAAO;4CAAC,SAAQ;4CAAW,MAAK;sDACtC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAM;gDAAc,QAAO;gDAAS,KAAI;;kEAC5C,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;oDAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnD;GA1KwB;KAAA", "debugId": null}}, {"offset": {"line": 857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/lib/analytics.ts"], "sourcesContent": ["// Google Analytics 4 Event Tracking Utilities\n\ndeclare global {\n  interface Window {\n    gtag: (command: string, targetId: string, config?: Record<string, unknown>) => void;\n  }\n}\n\n// Check if gtag is available\nconst isGtagAvailable = () => {\n  return typeof window !== 'undefined' && typeof window.gtag === 'function';\n};\n\n// Generic event tracking\nexport const trackEvent = (eventName: string, parameters: Record<string, unknown> = {}) => {\n  if (!isGtagAvailable()) return;\n  \n  window.gtag('event', eventName, {\n    event_category: parameters.category || 'general',\n    event_label: parameters.label,\n    value: parameters.value,\n    ...parameters\n  });\n};\n\n// Business-specific event tracking functions\n\n// Contact & Booking Events\nexport const trackWhatsAppClick = (source: string, message?: string) => {\n  trackEvent('whatsapp_click', {\n    category: 'contact',\n    label: source,\n    custom_parameter_1: 'whatsapp',\n    message_type: message ? 'custom' : 'default'\n  });\n};\n\nexport const trackPhoneClick = (source: string) => {\n  trackEvent('phone_click', {\n    category: 'contact',\n    label: source,\n    custom_parameter_1: 'phone'\n  });\n};\n\nexport const trackEmailClick = (source: string) => {\n  trackEvent('email_click', {\n    category: 'contact',\n    label: source,\n    custom_parameter_1: 'email'\n  });\n};\n\nexport const trackContactFormSubmit = (formData: {\n  service?: string;\n  source?: string;\n}) => {\n  trackEvent('contact_form_submit', {\n    category: 'lead_generation',\n    label: formData.service || 'general_inquiry',\n    custom_parameter_1: formData.service,\n    custom_parameter_2: formData.source || 'contact_page'\n  });\n};\n\n// Service & Package Events\nexport const trackServiceView = (serviceId: string, serviceName: string) => {\n  trackEvent('service_view', {\n    category: 'services',\n    label: serviceName,\n    custom_parameter_1: serviceId,\n    service_name: serviceName\n  });\n};\n\nexport const trackPackageView = (packageId: string, packageName: string, price?: number) => {\n  trackEvent('package_view', {\n    category: 'packages',\n    label: packageName,\n    custom_parameter_1: packageId,\n    custom_parameter_2: 'package',\n    package_name: packageName,\n    value: price\n  });\n};\n\nexport const trackServiceInquiry = (serviceId: string, serviceName: string, source: string) => {\n  trackEvent('service_inquiry', {\n    category: 'lead_generation',\n    label: serviceName,\n    custom_parameter_1: serviceId,\n    custom_parameter_2: source,\n    service_name: serviceName\n  });\n};\n\n// Portfolio & Gallery Events\nexport const trackPortfolioImageView = (imageId: string, category: string) => {\n  trackEvent('portfolio_image_view', {\n    category: 'portfolio',\n    label: category,\n    custom_parameter_1: imageId,\n    custom_parameter_2: category\n  });\n};\n\nexport const trackGalleryFilter = (filterCategory: string) => {\n  trackEvent('gallery_filter', {\n    category: 'portfolio',\n    label: filterCategory,\n    custom_parameter_1: filterCategory\n  });\n};\n\n// Blog Events\nexport const trackBlogPostView = (postId: string, postTitle: string, category?: string) => {\n  trackEvent('blog_post_view', {\n    category: 'content',\n    label: postTitle,\n    custom_parameter_1: postId,\n    custom_parameter_2: category || 'blog',\n    post_title: postTitle\n  });\n};\n\nexport const trackBlogCategoryView = (category: string) => {\n  trackEvent('blog_category_view', {\n    category: 'content',\n    label: category,\n    custom_parameter_1: category\n  });\n};\n\n// Social Media Events\nexport const trackSocialClick = (platform: string, source: string) => {\n  trackEvent('social_click', {\n    category: 'social_media',\n    label: platform,\n    custom_parameter_1: platform,\n    custom_parameter_2: source\n  });\n};\n\n// Navigation Events\nexport const trackPageView = (pageName: string, pageTitle?: string) => {\n  if (!isGtagAvailable()) return;\n  \n  window.gtag('config', process.env.NEXT_PUBLIC_GA_ID || '', {\n    page_title: pageTitle || document.title,\n    page_location: window.location.href,\n    page_path: window.location.pathname,\n    custom_parameter_1: pageName\n  });\n};\n\nexport const trackMenuClick = (menuItem: string, source: string = 'header') => {\n  trackEvent('menu_click', {\n    category: 'navigation',\n    label: menuItem,\n    custom_parameter_1: menuItem,\n    custom_parameter_2: source\n  });\n};\n\n// Conversion Events\nexport const trackBookingIntent = (service: string, source: string) => {\n  trackEvent('booking_intent', {\n    category: 'conversion',\n    label: service,\n    custom_parameter_1: service,\n    custom_parameter_2: source,\n    value: 1\n  });\n};\n\nexport const trackQuoteRequest = (services: string[], totalValue?: number) => {\n  trackEvent('quote_request', {\n    category: 'conversion',\n    label: services.join(', '),\n    custom_parameter_1: services[0] || 'multiple',\n    custom_parameter_2: 'quote',\n    value: totalValue || services.length,\n    services_count: services.length\n  });\n};\n\n// File Download Events\nexport const trackFileDownload = (fileName: string, fileType: string) => {\n  trackEvent('file_download', {\n    category: 'downloads',\n    label: fileName,\n    custom_parameter_1: fileType,\n    custom_parameter_2: fileName\n  });\n};\n\n// Search Events\nexport const trackSiteSearch = (searchTerm: string, resultsCount?: number) => {\n  trackEvent('search', {\n    category: 'site_search',\n    label: searchTerm,\n    custom_parameter_1: searchTerm,\n    value: resultsCount || 0,\n    search_term: searchTerm\n  });\n};\n\n// Error Tracking\nexport const trackError = (errorType: string, errorMessage: string, page: string) => {\n  trackEvent('error', {\n    category: 'errors',\n    label: errorType,\n    custom_parameter_1: errorType,\n    custom_parameter_2: page,\n    error_message: errorMessage,\n    page: page\n  });\n};\n\n// Performance Tracking\nexport const trackPerformance = (metric: string, value: number, page: string) => {\n  trackEvent('performance', {\n    category: 'performance',\n    label: metric,\n    custom_parameter_1: metric,\n    custom_parameter_2: page,\n    value: Math.round(value)\n  });\n};\n\n// User Engagement\nexport const trackVideoPlay = (videoId: string, videoTitle: string) => {\n  trackEvent('video_play', {\n    category: 'engagement',\n    label: videoTitle,\n    custom_parameter_1: videoId,\n    custom_parameter_2: 'video'\n  });\n};\n\nexport const trackImageZoom = (imageId: string, source: string) => {\n  trackEvent('image_zoom', {\n    category: 'engagement',\n    label: source,\n    custom_parameter_1: imageId,\n    custom_parameter_2: source\n  });\n};\n"], "names": [], "mappings": "AAAA,8CAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;AAmJtB;AA3IxB,6BAA6B;AAC7B,MAAM,kBAAkB;IACtB,OAAO,aAAkB,eAAe,OAAO,OAAO,IAAI,KAAK;AACjE;AAGO,MAAM,aAAa,SAAC;QAAmB,8EAAsC,CAAC;IACnF,IAAI,CAAC,mBAAmB;IAExB,OAAO,IAAI,CAAC,SAAS,WAAW;QAC9B,gBAAgB,WAAW,QAAQ,IAAI;QACvC,aAAa,WAAW,KAAK;QAC7B,OAAO,WAAW,KAAK;QACvB,GAAG,UAAU;IACf;AACF;AAKO,MAAM,qBAAqB,CAAC,QAAgB;IACjD,WAAW,kBAAkB;QAC3B,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,cAAc,UAAU,WAAW;IACrC;AACF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,WAAW,eAAe;QACxB,UAAU;QACV,OAAO;QACP,oBAAoB;IACtB;AACF;AAEO,MAAM,kBAAkB,CAAC;IAC9B,WAAW,eAAe;QACxB,UAAU;QACV,OAAO;QACP,oBAAoB;IACtB;AACF;AAEO,MAAM,yBAAyB,CAAC;IAIrC,WAAW,uBAAuB;QAChC,UAAU;QACV,OAAO,SAAS,OAAO,IAAI;QAC3B,oBAAoB,SAAS,OAAO;QACpC,oBAAoB,SAAS,MAAM,IAAI;IACzC;AACF;AAGO,MAAM,mBAAmB,CAAC,WAAmB;IAClD,WAAW,gBAAgB;QACzB,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,cAAc;IAChB;AACF;AAEO,MAAM,mBAAmB,CAAC,WAAmB,aAAqB;IACvE,WAAW,gBAAgB;QACzB,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,oBAAoB;QACpB,cAAc;QACd,OAAO;IACT;AACF;AAEO,MAAM,sBAAsB,CAAC,WAAmB,aAAqB;IAC1E,WAAW,mBAAmB;QAC5B,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,oBAAoB;QACpB,cAAc;IAChB;AACF;AAGO,MAAM,0BAA0B,CAAC,SAAiB;IACvD,WAAW,wBAAwB;QACjC,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,oBAAoB;IACtB;AACF;AAEO,MAAM,qBAAqB,CAAC;IACjC,WAAW,kBAAkB;QAC3B,UAAU;QACV,OAAO;QACP,oBAAoB;IACtB;AACF;AAGO,MAAM,oBAAoB,CAAC,QAAgB,WAAmB;IACnE,WAAW,kBAAkB;QAC3B,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,oBAAoB,YAAY;QAChC,YAAY;IACd;AACF;AAEO,MAAM,wBAAwB,CAAC;IACpC,WAAW,sBAAsB;QAC/B,UAAU;QACV,OAAO;QACP,oBAAoB;IACtB;AACF;AAGO,MAAM,mBAAmB,CAAC,UAAkB;IACjD,WAAW,gBAAgB;QACzB,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,oBAAoB;IACtB;AACF;AAGO,MAAM,gBAAgB,CAAC,UAAkB;IAC9C,IAAI,CAAC,mBAAmB;IAExB,OAAO,IAAI,CAAC,UAAU,oDAAiC,IAAI;QACzD,YAAY,aAAa,SAAS,KAAK;QACvC,eAAe,OAAO,QAAQ,CAAC,IAAI;QACnC,WAAW,OAAO,QAAQ,CAAC,QAAQ;QACnC,oBAAoB;IACtB;AACF;AAEO,MAAM,iBAAiB,SAAC;QAAkB,0EAAiB;IAChE,WAAW,cAAc;QACvB,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,oBAAoB;IACtB;AACF;AAGO,MAAM,qBAAqB,CAAC,SAAiB;IAClD,WAAW,kBAAkB;QAC3B,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,oBAAoB;QACpB,OAAO;IACT;AACF;AAEO,MAAM,oBAAoB,CAAC,UAAoB;IACpD,WAAW,iBAAiB;QAC1B,UAAU;QACV,OAAO,SAAS,IAAI,CAAC;QACrB,oBAAoB,QAAQ,CAAC,EAAE,IAAI;QACnC,oBAAoB;QACpB,OAAO,cAAc,SAAS,MAAM;QACpC,gBAAgB,SAAS,MAAM;IACjC;AACF;AAGO,MAAM,oBAAoB,CAAC,UAAkB;IAClD,WAAW,iBAAiB;QAC1B,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,oBAAoB;IACtB;AACF;AAGO,MAAM,kBAAkB,CAAC,YAAoB;IAClD,WAAW,UAAU;QACnB,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,OAAO,gBAAgB;QACvB,aAAa;IACf;AACF;AAGO,MAAM,aAAa,CAAC,WAAmB,cAAsB;IAClE,WAAW,SAAS;QAClB,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,oBAAoB;QACpB,eAAe;QACf,MAAM;IACR;AACF;AAGO,MAAM,mBAAmB,CAAC,QAAgB,OAAe;IAC9D,WAAW,eAAe;QACxB,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,oBAAoB;QACpB,OAAO,KAAK,KAAK,CAAC;IACpB;AACF;AAGO,MAAM,iBAAiB,CAAC,SAAiB;IAC9C,WAAW,cAAc;QACvB,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,oBAAoB;IACtB;AACF;AAEO,MAAM,iBAAiB,CAAC,SAAiB;IAC9C,WAAW,cAAc;QACvB,UAAU;QACV,OAAO;QACP,oBAAoB;QACpB,oBAAoB;IACtB;AACF", "debugId": null}}, {"offset": {"line": 1092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/layout/footer.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { Facebook, Instagram, Phone, Mail, MapPin, Clock } from 'lucide-react'\nimport { getSiteConfig, getSocialLinks } from '@/lib/data'\nimport { generateWhatsAppLink } from '@/lib/utils'\nimport { PiTiktokLogo } from 'react-icons/pi'\nimport { trackSocialClick, trackEmailClick, trackWhatsAppClick } from '@/lib/analytics'\n\nconst quickLinks = [\n  { name: 'About', href: '/about' },\n  { name: 'Services', href: '/services' },\n  { name: 'Packages', href: '/packages' },\n  { name: 'Portfolio', href: '/portfolio' },\n  { name: 'Blog', href: '/blog' },\n  { name: 'Contact', href: '/contact' },\n]\n\nconst services = [\n  { name: 'Bridal Makeup', href: '/services#bridal-makeup' },\n  { name: 'Party Makeup', href: '/services#party-makeup' },\n  { name: 'Engagement Makeup', href: '/services#engagement-makeup' },\n  { name: 'Traditional Makeup', href: '/services#traditional-makeup' },\n  { name: 'Photoshoot Makeup', href: '/services#photoshoot-makeup' },\n  { name: 'Makeup Lessons', href: '/services#makeup-lessons' },\n]\n\nexport default function Footer() {\n  const siteConfig = getSiteConfig()\n  const socialLinks = getSocialLinks()\n  const whatsappLink = generateWhatsAppLink(\n    siteConfig.contact.whatsapp,\n    siteConfig.whatsappMessage\n  )\n\n  return (\n    <footer className=\"bg-gradient-to-br from-cream to-soft-gray\">\n      <div className=\"mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8\">\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\">\n          {/* Company Info */}\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"h-8 w-8 rounded-full bg-gradient-to-r from-rose-gold to-blush-pink flex items-center justify-center\">\n                <span className=\"text-white font-display font-bold text-lg\">A</span>\n              </div>\n              <span className=\"font-display text-xl font-semibold text-text-primary\">\n                {siteConfig.site.name}\n              </span>\n            </div>\n            <p className=\"text-text-secondary text-sm leading-relaxed\">\n              {siteConfig.site.description}\n            </p>\n            <div className=\"flex space-x-4\">\n              <Link\n                href={socialLinks.facebook}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n                aria-label=\"Facebook\"\n                onClick={() => trackSocialClick('facebook', 'footer')}\n              >\n                <Facebook className=\"h-5 w-5\" />\n              </Link>\n              <Link\n                href={socialLinks.instagram}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n                aria-label=\"Instagram\"\n                onClick={() => trackSocialClick('instagram', 'footer')}\n              >\n                <Instagram className=\"h-5 w-5\" />\n              </Link>\n              <Link\n                href={socialLinks.tiktok}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-text-secondary hover:text-rose-gold-dark transition-colors\"\n                aria-label=\"Tiktok\"\n                onClick={() => trackSocialClick('tiktok', 'footer')}\n              >\n                <PiTiktokLogo className=\"h-5 w-5\" />\n              </Link>\n            </div>\n          </div>\n\n          {/* Quick Links */}\n          <div>\n            <h3 className=\"font-display text-lg font-semibold text-text-primary mb-4\">\n              Quick Links\n            </h3>\n            <ul className=\"space-y-2\">\n              {quickLinks.map((link) => (\n                <li key={link.name}>\n                  <Link\n                    href={link.href}\n                    className=\"text-text-secondary hover:text-rose-gold-dark transition-colors text-sm\"\n                  >\n                    {link.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Services */}\n          <div>\n            <h3 className=\"font-display text-lg font-semibold text-text-primary mb-4\">\n              Services\n            </h3>\n            <ul className=\"space-y-2\">\n              {services.map((service) => (\n                <li key={service.name}>\n                  <Link\n                    href={service.href}\n                    className=\"text-text-secondary hover:text-rose-gold-dark transition-colors text-sm\"\n                  >\n                    {service.name}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Contact Info */}\n          <div>\n            <h3 className=\"font-display text-lg font-semibold text-text-primary mb-4\">\n              Contact Info\n            </h3>\n            <div className=\"space-y-3\">\n              <div className=\"flex items-start space-x-3\">\n                <MapPin className=\"h-4 w-4 text-rose-gold-dark mt-1 flex-shrink-0\" />\n                <div className=\"text-sm text-text-secondary\">\n                  <p>{siteConfig.contact.address.street}</p>\n                  <p>{siteConfig.contact.address.city}, {siteConfig.contact.address.state}</p>\n                  <p>{siteConfig.contact.address.country}</p>\n                </div>\n              </div>\n              \n              <div className=\"flex items-center space-x-3\">\n                <Phone className=\"h-4 w-4 text-rose-gold-dark flex-shrink-0\" />\n                <Link\n                  href={whatsappLink}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"text-sm text-text-secondary hover:text-rose-gold-dark transition-colors\"\n                  onClick={() => trackWhatsAppClick('footer', 'contact_info')}\n                >\n                  {siteConfig.contact.phone}\n                </Link>\n              </div>\n\n              <div className=\"flex items-center space-x-3\">\n                <Mail className=\"h-4 w-4 text-rose-gold-dark flex-shrink-0\" />\n                <Link\n                  href={`mailto:${siteConfig.contact.email}`}\n                  className=\"text-sm text-text-secondary hover:text-rose-gold-dark transition-colors\"\n                  onClick={() => trackEmailClick('footer')}\n                >\n                  {siteConfig.contact.email}\n                </Link>\n              </div>\n              \n              <div className=\"flex items-start space-x-3\">\n                <Clock className=\"h-4 w-4 text-rose-gold-dark mt-1 flex-shrink-0\" />\n                <div className=\"text-sm text-text-secondary\">\n                  <p>Sun-Fri: 9:00 AM - 6:00 PM</p>\n                  <p>Sat: 10:00 AM - 4:00 PM</p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Service Areas */}\n        <div className=\"mt-8 pt-8 border-t border-gray-200\">\n          <div className=\"text-center\">\n            <h4 className=\"font-display text-lg font-semibold text-text-primary mb-3\">\n              Service Areas\n            </h4>\n            <p className=\"text-text-secondary text-sm\">\n              Serving clients across{' '}\n              {siteConfig.serviceAreas.map((area, index) => (\n                <span key={area.name}>\n                  {area.name}\n                  {index < siteConfig.serviceAreas.length - 1 && ', '}\n                </span>\n              ))}\n            </p>\n          </div>\n        </div>\n\n        {/* Copyright */}\n        <div className=\"mt-8 pt-8 border-t border-gray-200\">\n          <div className=\"text-center space-y-2\">\n            <p className=\"text-text-muted text-sm\">\n              © {new Date().getFullYear()} {siteConfig.site.name}. All rights reserved.\n            </p>\n            <p className=\"text-text-muted text-xs\">\n              Designed and developed with ❤️ by{' '}\n              <Link\n                href=\"https://ashishkamat.com.np/\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"text-rose-gold-dark hover:text-rose-gold transition-colors underline underline-offset-2\"\n              >\n                Ashish Kamat\n              </Link>\n            </p>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,aAAa;IACjB;QAAE,MAAM;QAAS,MAAM;IAAS;IAChC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAY,MAAM;IAAY;IACtC;QAAE,MAAM;QAAa,MAAM;IAAa;IACxC;QAAE,MAAM;QAAQ,MAAM;IAAQ;IAC9B;QAAE,MAAM;QAAW,MAAM;IAAW;CACrC;AAED,MAAM,WAAW;IACf;QAAE,MAAM;QAAiB,MAAM;IAA0B;IACzD;QAAE,MAAM;QAAgB,MAAM;IAAyB;IACvD;QAAE,MAAM;QAAqB,MAAM;IAA8B;IACjE;QAAE,MAAM;QAAsB,MAAM;IAA+B;IACnE;QAAE,MAAM;QAAqB,MAAM;IAA8B;IACjE;QAAE,MAAM;QAAkB,MAAM;IAA2B;CAC5D;AAEc,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,cAAc,CAAA,GAAA,qIAAA,CAAA,iBAAc,AAAD;IACjC,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,uBAAoB,AAAD,EACtC,WAAW,OAAO,CAAC,QAAQ,EAC3B,WAAW,eAAe;IAG5B,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;0DAA4C;;;;;;;;;;;sDAE9D,6LAAC;4CAAK,WAAU;sDACb,WAAW,IAAI,CAAC,IAAI;;;;;;;;;;;;8CAGzB,6LAAC;oCAAE,WAAU;8CACV,WAAW,IAAI,CAAC,WAAW;;;;;;8CAE9B,6LAAC;oCAAI,WAAU;;sDACb,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,YAAY,QAAQ;4CAC1B,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAW;4CACX,SAAS,IAAM,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE,YAAY;sDAE5C,cAAA,6LAAC,6MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;;;;;;sDAEtB,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,YAAY,SAAS;4CAC3B,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAW;4CACX,SAAS,IAAM,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE,aAAa;sDAE7C,cAAA,6LAAC,+MAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;;;;;;sDAEvB,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAM,YAAY,MAAM;4CACxB,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAW;4CACX,SAAS,IAAM,CAAA,GAAA,0HAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU;sDAE1C,cAAA,6LAAC,iJAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;sCAM9B,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,6LAAC;oCAAG,WAAU;8CACX,WAAW,GAAG,CAAC,CAAC,qBACf,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,IAAI;;;;;;2CALL,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,6LAAC;oCAAG,WAAU;8CACX,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,QAAQ,IAAI;gDAClB,WAAU;0DAET,QAAQ,IAAI;;;;;;2CALR,QAAQ,IAAI;;;;;;;;;;;;;;;;sCAa3B,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAA4D;;;;;;8CAG1E,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAG,WAAW,OAAO,CAAC,OAAO,CAAC,MAAM;;;;;;sEACrC,6LAAC;;gEAAG,WAAW,OAAO,CAAC,OAAO,CAAC,IAAI;gEAAC;gEAAG,WAAW,OAAO,CAAC,OAAO,CAAC,KAAK;;;;;;;sEACvE,6LAAC;sEAAG,WAAW,OAAO,CAAC,OAAO,CAAC,OAAO;;;;;;;;;;;;;;;;;;sDAI1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM;oDACN,QAAO;oDACP,KAAI;oDACJ,WAAU;oDACV,SAAS,IAAM,CAAA,GAAA,0HAAA,CAAA,qBAAkB,AAAD,EAAE,UAAU;8DAE3C,WAAW,OAAO,CAAC,KAAK;;;;;;;;;;;;sDAI7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,6LAAC,+JAAA,CAAA,UAAI;oDACH,MAAM,AAAC,UAAkC,OAAzB,WAAW,OAAO,CAAC,KAAK;oDACxC,WAAU;oDACV,SAAS,IAAM,CAAA,GAAA,0HAAA,CAAA,kBAAe,AAAD,EAAE;8DAE9B,WAAW,OAAO,CAAC,KAAK;;;;;;;;;;;;sDAI7B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAE;;;;;;sEACH,6LAAC;sEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAG1E,6LAAC;gCAAE,WAAU;;oCAA8B;oCAClB;oCACtB,WAAW,YAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBAClC,6LAAC;;gDACE,KAAK,IAAI;gDACT,QAAQ,WAAW,YAAY,CAAC,MAAM,GAAG,KAAK;;2CAFtC,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;8BAU5B,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;;oCAA0B;oCAClC,IAAI,OAAO,WAAW;oCAAG;oCAAE,WAAW,IAAI,CAAC,IAAI;oCAAC;;;;;;;0CAErD,6LAAC;gCAAE,WAAU;;oCAA0B;oCACH;kDAClC,6LAAC,+JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,QAAO;wCACP,KAAI;wCACJ,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;KA3LwB", "debugId": null}}, {"offset": {"line": 1666, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/seo/analytics.tsx"], "sourcesContent": ["'use client'\n\nimport Script from 'next/script'\nimport { getSiteConfig } from '@/lib/data'\n\nexport default function Analytics() {\n  const siteConfig = getSiteConfig()\n  const gaId = siteConfig.analytics.googleAnalyticsId\n\n  // Only load analytics in production or if GA_ID is provided\n  if (!gaId || gaId === 'G-XXXXXXXXXX') {\n    return null\n  }\n\n  return (\n    <>\n      <Script\n        src={`https://www.googletagmanager.com/gtag/js?id=${gaId}`}\n        strategy=\"afterInteractive\"\n      />\n      <Script id=\"google-analytics\" strategy=\"afterInteractive\">\n        {`\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n\n          gtag('config', '${gaId}', {\n            page_title: document.title,\n            page_location: window.location.href,\n            send_page_view: true,\n            // Enhanced ecommerce and engagement tracking\n            custom_map: {\n              'custom_parameter_1': 'service_type',\n              'custom_parameter_2': 'package_type'\n            },\n            // User engagement tracking\n            engagement_time_msec: 100,\n            // Conversion tracking\n            allow_enhanced_conversions: true,\n            // Privacy settings\n            anonymize_ip: true,\n            allow_google_signals: true,\n            allow_ad_personalization_signals: true\n          });\n\n          // Track scroll depth\n          let scrollDepth = 0;\n          window.addEventListener('scroll', function() {\n            const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);\n            if (scrollPercent > scrollDepth && scrollPercent % 25 === 0) {\n              scrollDepth = scrollPercent;\n              gtag('event', 'scroll_depth', {\n                event_category: 'engagement',\n                event_label: scrollPercent + '%',\n                value: scrollPercent\n              });\n            }\n          });\n\n          // Track time on page\n          let startTime = Date.now();\n          window.addEventListener('beforeunload', function() {\n            const timeOnPage = Math.round((Date.now() - startTime) / 1000);\n            gtag('event', 'time_on_page', {\n              event_category: 'engagement',\n              event_label: 'seconds',\n              value: timeOnPage\n            });\n          });\n        `}\n      </Script>\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,qIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,OAAO,WAAW,SAAS,CAAC,iBAAiB;IAEnD,4DAA4D;IAC5D,IAAI,CAAC,QAAQ,SAAS,gBAAgB;QACpC,OAAO;IACT;IAEA,qBACE;;0BACE,6LAAC,iIAAA,CAAA,UAAM;gBACL,KAAK,AAAC,+CAAmD,OAAL;gBACpD,UAAS;;;;;;0BAEX,6LAAC,iIAAA,CAAA,UAAM;gBAAC,IAAG;gBAAmB,UAAS;0BACpC,AAAC,iLAKuB,OAAL,MAAK;;;;;;;;AA+CjC;KApEwB", "debugId": null}}, {"offset": {"line": 1718, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/scroll-to-top.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { ArrowUp } from 'lucide-react'\nimport { Button } from '@/components/ui/button'\n\nexport default function ScrollToTop() {\n  const [isVisible, setIsVisible] = useState(false)\n\n  useEffect(() => {\n    const toggleVisibility = () => {\n      if (window.pageYOffset > 300) {\n        setIsVisible(true)\n      } else {\n        setIsVisible(false)\n      }\n    }\n\n    window.addEventListener('scroll', toggleVisibility)\n    return () => window.removeEventListener('scroll', toggleVisibility)\n  }, [])\n\n  const scrollToTop = () => {\n    window.scrollTo({\n      top: 0,\n      behavior: 'smooth'\n    })\n  }\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          exit={{ opacity: 0, scale: 0.8 }}\n          transition={{ duration: 0.2 }}\n          className=\"fixed bottom-8 right-8 z-50\"\n        >\n          <Button\n            onClick={scrollToTop}\n            size=\"icon\"\n            variant=\"gradient\"\n            className=\"w-12 h-12 rounded-full shadow-lg hover:shadow-xl transition-shadow duration-300\"\n            aria-label=\"Scroll to top\"\n          >\n            <ArrowUp className=\"w-5 h-5\" />\n          </Button>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;0DAAmB;oBACvB,IAAI,OAAO,WAAW,GAAG,KAAK;wBAC5B,aAAa;oBACf,OAAO;wBACL,aAAa;oBACf;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;gCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;IAEA,qBACE,6LAAC,4LAAA,CAAA,kBAAe;kBACb,2BACC,6LAAC,6LAAA,CAAA,SAAM,CAAC,GAAG;YACT,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAClC,SAAS;gBAAE,SAAS;gBAAG,OAAO;YAAE;YAChC,MAAM;gBAAE,SAAS;gBAAG,OAAO;YAAI;YAC/B,YAAY;gBAAE,UAAU;YAAI;YAC5B,WAAU;sBAEV,cAAA,6LAAC,qIAAA,CAAA,SAAM;gBACL,SAAS;gBACT,MAAK;gBACL,SAAQ;gBACR,WAAU;gBACV,cAAW;0BAEX,cAAA,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;;;;;;;;;;;;;;;;AAM/B;GA9CwB;KAAA", "debugId": null}}, {"offset": {"line": 1820, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/providers/query-provider.tsx"], "sourcesContent": ["'use client'\n/* eslint-disable @typescript-eslint/no-explicit-any */\n\nimport { QueryClient, QueryClientProvider } from '@tanstack/react-query'\nimport { ReactQueryDevtools } from '@tanstack/react-query-devtools'\nimport { useState } from 'react'\n\nexport function QueryProvider({ children }: { children: React.ReactNode }) {\n  const [queryClient] = useState(\n    () =>\n      new QueryClient({\n        defaultOptions: {\n          queries: {\n            // With SSR, we usually want to set some default staleTime\n            // above 0 to avoid refetching immediately on the client\n            staleTime: 60 * 1000, // 1 minute\n            gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)\n            retry: (failureCount, error: any) => {\n              // Don't retry on 4xx errors\n              if (error?.status >= 400 && error?.status < 500) {\n                return false\n              }\n              // Retry up to 3 times for other errors\n              return failureCount < 3\n            },\n            refetchOnWindowFocus: false,\n            refetchOnMount: true,\n            refetchOnReconnect: true,\n          },\n          mutations: {\n            retry: false,\n          },\n        },\n      })\n  )\n\n  return (\n    <QueryClientProvider client={queryClient}>\n      {children}\n      <ReactQueryDevtools initialIsOpen={false} />\n    </QueryClientProvider>\n  )\n}\n"], "names": [], "mappings": ";;;;AACA,qDAAqD,GAErD;AAAA;AACA;AACA;;;AALA;;;;AAOO,SAAS,cAAc,KAA2C;QAA3C,EAAE,QAAQ,EAAiC,GAA3C;;IAC5B,MAAM,CAAC,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;kCAC3B,IACE,IAAI,gLAAA,CAAA,cAAW,CAAC;gBACd,gBAAgB;oBACd,SAAS;wBACP,0DAA0D;wBAC1D,wDAAwD;wBACxD,WAAW,KAAK;wBAChB,QAAQ,KAAK,KAAK;wBAClB,KAAK;sDAAE,CAAC,cAAc;gCACpB,4BAA4B;gCAC5B,IAAI,CAAA,kBAAA,4BAAA,MAAO,MAAM,KAAI,OAAO,CAAA,kBAAA,4BAAA,MAAO,MAAM,IAAG,KAAK;oCAC/C,OAAO;gCACT;gCACA,uCAAuC;gCACvC,OAAO,eAAe;4BACxB;;wBACA,sBAAsB;wBACtB,gBAAgB;wBAChB,oBAAoB;oBACtB;oBACA,WAAW;wBACT,OAAO;oBACT;gBACF;YACF;;IAGJ,qBACE,6LAAC,yLAAA,CAAA,sBAAmB;QAAC,QAAQ;;YAC1B;0BACD,6LAAC,uLAAA,CAAA,qBAAkB;gBAAC,eAAe;;;;;;;;;;;;AAGzC;GAnCgB;KAAA", "debugId": null}}]}