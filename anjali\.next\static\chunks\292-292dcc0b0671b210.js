"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[292],{1335:(e,s,a)=>{a.d(s,{BD:()=>m,KA:()=>l,S0:()=>d,Tr:()=>x,cl:()=>i,gf:()=>o,iF:()=>c,iw:()=>n,uJ:()=>r});let t=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"function"==typeof window.gtag&&window.gtag("event",e,{event_category:s.category||"general",event_label:s.label,value:s.value,...s})},r=(e,s)=>{t("whatsapp_click",{category:"contact",label:e,custom_parameter_1:"whatsapp",message_type:s?"custom":"default"})},l=e=>{t("phone_click",{category:"contact",label:e,custom_parameter_1:"phone"})},i=e=>{t("email_click",{category:"contact",label:e,custom_parameter_1:"email"})},c=e=>{t("contact_form_submit",{category:"lead_generation",label:e.service||"general_inquiry",custom_parameter_1:e.service,custom_parameter_2:e.source||"contact_page"})},n=(e,s,a)=>{t("service_inquiry",{category:"lead_generation",label:s,custom_parameter_1:e,custom_parameter_2:a,service_name:s})},o=(e,s)=>{t("portfolio_image_view",{category:"portfolio",label:s,custom_parameter_1:e,custom_parameter_2:s})},d=e=>{t("gallery_filter",{category:"portfolio",label:e,custom_parameter_1:e})},m=(e,s,a)=>{t("blog_post_view",{category:"content",label:s,custom_parameter_1:e,custom_parameter_2:a||"blog",post_title:s})},x=(e,s)=>{t("social_click",{category:"social_media",label:e,custom_parameter_1:e,custom_parameter_2:s})}},8292:(e,s,a)=>{a.d(s,{A:()=>N});var t=a(5155),r=a(2115),l=a(2177),i=a(221),c=a(8309),n=a(2486),o=a(9420),d=a(1366),m=a(285),x=a(9434);let u=r.forwardRef((e,s)=>{let{className:a,type:r,...l}=e;return(0,t.jsx)("input",{type:r,className:(0,x.cn)("flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-text-primary placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...l})});u.displayName="Input";let h=r.forwardRef((e,s)=>{let{className:a,...r}=e;return(0,t.jsx)("textarea",{className:(0,x.cn)("flex min-h-[60px] w-full rounded-md border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm placeholder:text-text-muted focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold disabled:cursor-not-allowed disabled:opacity-50",a),ref:s,...r})});h.displayName="Textarea";var p=a(6695),g=a(6126),b=a(1710),f=a(1335);let j=c.Ik({name:c.Yj().min(2,"Name must be at least 2 characters"),email:c.Yj().email("Please enter a valid email address"),phone:c.Yj().min(10,"Please enter a valid phone number"),service:c.Yj().min(1,"Please select a service"),date:c.Yj().min(1,"Please select a preferred date"),message:c.Yj().min(10,"Message must be at least 10 characters")}),y=["Bridal Makeup","Party Makeup","Engagement Makeup","Traditional Makeup","Photoshoot Makeup","Makeup Lessons","Other"];function N(){let[e,s]=(0,r.useState)(!1),[a,c]=(0,r.useState)(!1),N=(0,b.Q2)(),{register:v,handleSubmit:_,formState:{errors:w},reset:k}=(0,l.mN)({resolver:(0,i.u)(j)}),C=async e=>{s(!0);try{(0,f.iF)({service:e.service,source:"contact_page"}),await new Promise(e=>setTimeout(e,2e3)),c(!0),k()}catch(e){console.error("Error submitting form:",e)}finally{s(!1)}},M=(0,x.ec)(N.contact.whatsapp,N.whatsappMessage);return a?(0,t.jsx)(p.Card,{className:"max-w-md mx-auto text-center",children:(0,t.jsxs)(p.CardContent,{className:"pt-6",children:[(0,t.jsx)("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,t.jsx)(n.A,{className:"w-8 h-8 text-green-600"})}),(0,t.jsx)("h3",{className:"font-display text-xl font-semibold text-text-primary mb-2",children:"Message Sent Successfully!"}),(0,t.jsx)("p",{className:"text-text-secondary mb-6",children:"Thank you for your inquiry. We'll get back to you within 24 hours."}),(0,t.jsx)(m.$,{onClick:()=>c(!1),variant:"outline",className:"w-full",children:"Send Another Message"})]})}):(0,t.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 max-w-4xl mx-auto",children:[(0,t.jsxs)(p.Card,{children:[(0,t.jsxs)(p.aR,{children:[(0,t.jsx)(p.ZB,{className:"font-display text-2xl",children:"Send us a Message"}),(0,t.jsx)(p.BT,{children:"Fill out the form below and we'll get back to you as soon as possible."})]}),(0,t.jsx)(p.CardContent,{children:(0,t.jsxs)("form",{onSubmit:_(C),className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"name",className:"text-sm font-medium text-text-primary",children:"Full Name *"}),(0,t.jsx)(u,{id:"name",placeholder:"Your full name",...v("name"),className:w.name?"border-red-500":""}),w.name&&(0,t.jsx)("p",{className:"text-red-500 text-xs",children:w.name.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"email",className:"text-sm font-medium text-text-primary",children:"Email Address *"}),(0,t.jsx)(u,{id:"email",type:"email",placeholder:"<EMAIL>",...v("email"),className:w.email?"border-red-500":""}),w.email&&(0,t.jsx)("p",{className:"text-red-500 text-xs",children:w.email.message})]})]}),(0,t.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"phone",className:"text-sm font-medium text-text-primary",children:"Phone Number *"}),(0,t.jsx)(u,{id:"phone",placeholder:"+977-9800000000",...v("phone"),className:w.phone?"border-red-500":""}),w.phone&&(0,t.jsx)("p",{className:"text-red-500 text-xs",children:w.phone.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"date",className:"text-sm font-medium text-text-primary",children:"Preferred Date *"}),(0,t.jsx)(u,{id:"date",type:"date",...v("date"),className:w.date?"border-red-500":""}),w.date&&(0,t.jsx)("p",{className:"text-red-500 text-xs",children:w.date.message})]})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"service",className:"text-sm font-medium text-text-primary",children:"Service Interested In *"}),(0,t.jsxs)("select",{id:"service",...v("service"),className:"flex h-9 w-full rounded-md border border-gray-300 bg-white px-3 py-1 text-sm shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-rose-gold focus-visible:border-rose-gold ".concat(w.service?"border-red-500":""),children:[(0,t.jsx)("option",{value:"",children:"Select a service"}),y.map(e=>(0,t.jsx)("option",{value:e,children:e},e))]}),w.service&&(0,t.jsx)("p",{className:"text-red-500 text-xs",children:w.service.message})]}),(0,t.jsxs)("div",{className:"space-y-2",children:[(0,t.jsx)("label",{htmlFor:"message",className:"text-sm font-medium text-text-primary",children:"Message *"}),(0,t.jsx)(h,{id:"message",placeholder:"Tell us about your requirements, occasion, and any specific preferences...",rows:4,...v("message"),className:w.message?"border-red-500":""}),w.message&&(0,t.jsx)("p",{className:"text-red-500 text-xs",children:w.message.message})]}),(0,t.jsx)(m.$,{type:"submit",variant:"gradient",size:"lg",className:"w-full",disabled:e,children:e?(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"}),"Sending..."]}):(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(n.A,{className:"w-4 h-4 mr-2"}),"Send Message"]})})]})})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(p.Card,{className:"bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0",children:[(0,t.jsxs)(p.aR,{children:[(0,t.jsxs)(p.ZB,{className:"font-display text-xl flex items-center gap-2",children:[(0,t.jsx)(o.A,{className:"w-5 h-5 text-rose-gold-dark"}),"Quick Contact"]}),(0,t.jsx)(p.BT,{children:"Need immediate assistance? Contact us directly via WhatsApp or phone."})]}),(0,t.jsxs)(p.CardContent,{className:"space-y-4",children:[(0,t.jsx)(m.$,{asChild:!0,variant:"gradient",size:"lg",className:"w-full",children:(0,t.jsxs)("a",{href:M,target:"_blank",rel:"noopener noreferrer",onClick:()=>(0,f.uJ)("contact_form","quick_contact"),children:[(0,t.jsx)(d.A,{className:"w-5 h-5 mr-2"}),"WhatsApp Now"]})}),(0,t.jsx)(m.$,{asChild:!0,variant:"outline",size:"lg",className:"w-full",children:(0,t.jsxs)("a",{href:"tel:".concat(N.contact.phone),onClick:()=>(0,f.KA)("contact_form"),children:[(0,t.jsx)(o.A,{className:"w-5 h-5 mr-2"}),"Call Now"]})})]})]}),(0,t.jsxs)(p.Card,{children:[(0,t.jsx)(p.aR,{children:(0,t.jsx)(p.ZB,{className:"font-display text-xl",children:"Business Hours"})}),(0,t.jsxs)(p.CardContent,{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-text-secondary",children:"Monday - Saturday"}),(0,t.jsx)(g.Badge,{variant:"outline",children:"9:00 AM - 6:00 PM"})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("span",{className:"text-text-secondary",children:"Sunday"}),(0,t.jsx)(g.Badge,{variant:"outline",children:"10:00 AM - 4:00 PM"})]}),(0,t.jsx)("div",{className:"pt-2 border-t border-gray-100",children:(0,t.jsx)("p",{className:"text-sm text-text-muted",children:"Emergency bookings available with advance notice"})})]})]})]})]})}}}]);