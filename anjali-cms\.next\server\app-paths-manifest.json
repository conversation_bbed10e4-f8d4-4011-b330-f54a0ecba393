{"/api/auth/[...nextauth]/route": "app/api/auth/[...nextauth]/route.js", "/api/blogs/[id]/route": "app/api/blogs/[id]/route.js", "/api/blogs/route": "app/api/blogs/route.js", "/api/blogs/slug/[slug]/route": "app/api/blogs/slug/[slug]/route.js", "/api/categories/route": "app/api/categories/route.js", "/api/cloudinary-signature/route": "app/api/cloudinary-signature/route.js", "/api/gallery/[id]/route": "app/api/gallery/[id]/route.js", "/api/export/route": "app/api/export/route.js", "/api/gallery/route": "app/api/gallery/route.js", "/api/gallery/bulk/route": "app/api/gallery/bulk/route.js", "/api/health/route": "app/api/health/route.js", "/api/import/route": "app/api/import/route.js", "/api/packages/[id]/route": "app/api/packages/[id]/route.js", "/api/packages/route": "app/api/packages/route.js", "/api/services/[id]/route": "app/api/services/[id]/route.js", "/api/services/slug/[slug]/route": "app/api/services/slug/[slug]/route.js", "/api/services/route": "app/api/services/route.js", "/api/settings/[key]/route": "app/api/settings/[key]/route.js", "/api/settings/route": "app/api/settings/route.js", "/api/tags/route": "app/api/tags/route.js", "/api/test-cloudinary/route": "app/api/test-cloudinary/route.js", "/api/testimonials/[id]/route": "app/api/testimonials/[id]/route.js", "/api/testimonials/bulk/route": "app/api/testimonials/bulk/route.js", "/api/testimonials/route": "app/api/testimonials/route.js", "/api/upload/route": "app/api/upload/route.js", "/api/packages/slug/[slug]/route": "app/api/packages/slug/[slug]/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/_not-found/page": "app/_not-found/page.js", "/auth/signin/page": "app/auth/signin/page.js", "/page": "app/page.js", "/dashboard/blogs/[id]/edit/page": "app/dashboard/blogs/[id]/edit/page.js", "/dashboard/blogs/new/page": "app/dashboard/blogs/new/page.js", "/dashboard/blogs/[id]/page": "app/dashboard/blogs/[id]/page.js", "/dashboard/gallery/[id]/edit/page": "app/dashboard/gallery/[id]/edit/page.js", "/dashboard/blogs/page": "app/dashboard/blogs/page.js", "/dashboard/data/page": "app/dashboard/data/page.js", "/dashboard/gallery/page": "app/dashboard/gallery/page.js", "/dashboard/gallery/[id]/page": "app/dashboard/gallery/[id]/page.js", "/dashboard/packages/[id]/edit/page": "app/dashboard/packages/[id]/edit/page.js", "/dashboard/packages/new/page": "app/dashboard/packages/new/page.js", "/dashboard/gallery/new/page": "app/dashboard/gallery/new/page.js", "/dashboard/packages/[id]/page": "app/dashboard/packages/[id]/page.js", "/dashboard/page": "app/dashboard/page.js", "/dashboard/services/[id]/edit/page": "app/dashboard/services/[id]/edit/page.js", "/dashboard/services/[id]/page": "app/dashboard/services/[id]/page.js", "/dashboard/packages/page": "app/dashboard/packages/page.js", "/dashboard/services/new/page": "app/dashboard/services/new/page.js", "/dashboard/services/page": "app/dashboard/services/page.js", "/dashboard/settings/page": "app/dashboard/settings/page.js", "/dashboard/testimonials/[id]/edit/page": "app/dashboard/testimonials/[id]/edit/page.js", "/dashboard/testimonials/[id]/page": "app/dashboard/testimonials/[id]/page.js", "/dashboard/testimonials/new/page": "app/dashboard/testimonials/new/page.js", "/dashboard/testimonials/page": "app/dashboard/testimonials/page.js"}