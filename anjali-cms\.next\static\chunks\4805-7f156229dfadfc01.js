"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4805],{704:(e,t,n)=>{n.d(t,{B8:()=>D,UC:()=>M,bL:()=>x,l9:()=>F});var r=n(2115),o=n(5185),a=n(6081),i=n(9196),l=n(8905),u=n(3655),s=n(4315),c=n(5845),d=n(1285),f=n(5155),m="Tabs",[p,v]=(0,a.A)(m,[i.RG]),w=(0,i.RG)(),[b,h]=p(m),g=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:o,defaultValue:a,orientation:i="horizontal",dir:l,activationMode:p="automatic",...v}=e,w=(0,s.jH)(l),[h,g]=(0,c.i)({prop:r,onChange:o,defaultProp:null!=a?a:"",caller:m});return(0,f.jsx)(b,{scope:n,baseId:(0,d.B)(),value:h,onValueChange:g,orientation:i,dir:w,activationMode:p,children:(0,f.jsx)(u.sG.div,{dir:w,"data-orientation":i,...v,ref:t})})});g.displayName=m;var y="TabsList",N=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...o}=e,a=h(y,n),l=w(n);return(0,f.jsx)(i.bL,{asChild:!0,...l,orientation:a.orientation,dir:a.dir,loop:r,children:(0,f.jsx)(u.sG.div,{role:"tablist","aria-orientation":a.orientation,...o,ref:t})})});N.displayName=y;var A="TabsTrigger",R=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:a=!1,...l}=e,s=h(A,n),c=w(n),d=C(s.baseId,r),m=E(s.baseId,r),p=r===s.value;return(0,f.jsx)(i.q7,{asChild:!0,...c,focusable:!a,active:p,children:(0,f.jsx)(u.sG.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":m,"data-state":p?"active":"inactive","data-disabled":a?"":void 0,disabled:a,id:d,...l,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a||0!==e.button||!1!==e.ctrlKey?e.preventDefault():s.onValueChange(r)}),onKeyDown:(0,o.m)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&s.onValueChange(r)}),onFocus:(0,o.m)(e.onFocus,()=>{let e="manual"!==s.activationMode;p||a||!e||s.onValueChange(r)})})})});R.displayName=A;var T="TabsContent",I=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:o,forceMount:a,children:i,...s}=e,c=h(T,n),d=C(c.baseId,o),m=E(c.baseId,o),p=o===c.value,v=r.useRef(p);return r.useEffect(()=>{let e=requestAnimationFrame(()=>v.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(l.C,{present:a||p,children:n=>{let{present:r}=n;return(0,f.jsx)(u.sG.div,{"data-state":p?"active":"inactive","data-orientation":c.orientation,role:"tabpanel","aria-labelledby":d,hidden:!r,id:m,tabIndex:0,...s,ref:t,style:{...e.style,animationDuration:v.current?"0s":void 0},children:r&&i})}})});function C(e,t){return"".concat(e,"-trigger-").concat(t)}function E(e,t){return"".concat(e,"-content-").concat(t)}I.displayName=T;var x=g,D=N,F=R,M=I},8905:(e,t,n)=>{n.d(t,{C:()=>i});var r=n(2115),o=n(6101),a=n(2712),i=e=>{let{present:t,children:n}=e,i=function(e){var t,n;let[o,i]=r.useState(),u=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[d,f]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(u.current);c.current="mounted"===d?e:"none"},[d]),(0,a.N)(()=>{let t=u.current,n=s.current;if(n!==e){let r=c.current,o=l(t);e?f("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):n&&r!==o?f("ANIMATION_OUT"):f("UNMOUNT"),s.current=e}},[e,f]),(0,a.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=l(u.current).includes(e.animationName);if(e.target===o&&r&&(f("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},a=e=>{e.target===o&&(c.current=l(u.current))};return o.addEventListener("animationstart",a),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",a),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}f("ANIMATION_END")},[o,f]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,i(e)},[])}}(t),u="function"==typeof n?n({present:i.isPresent}):r.Children.only(n),s=(0,o.s)(i.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||i.isPresent?r.cloneElement(u,{ref:s}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}i.displayName="Presence"},9196:(e,t,n)=>{n.d(t,{RG:()=>N,bL:()=>F,q7:()=>M});var r=n(2115),o=n(5185),a=n(7328),i=n(6101),l=n(6081),u=n(1285),s=n(3655),c=n(9033),d=n(5845),f=n(4315),m=n(5155),p="rovingFocusGroup.onEntryFocus",v={bubbles:!1,cancelable:!0},w="RovingFocusGroup",[b,h,g]=(0,a.N)(w),[y,N]=(0,l.A)(w,[g]),[A,R]=y(w),T=r.forwardRef((e,t)=>(0,m.jsx)(b.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(b.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,m.jsx)(I,{...e,ref:t})})}));T.displayName=w;var I=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:a,loop:l=!1,dir:u,currentTabStopId:b,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:y,onEntryFocus:N,preventScrollOnEntryFocus:R=!1,...T}=e,I=r.useRef(null),C=(0,i.s)(t,I),E=(0,f.jH)(u),[x,F]=(0,d.i)({prop:b,defaultProp:null!=g?g:null,onChange:y,caller:w}),[M,j]=r.useState(!1),L=(0,c.c)(N),O=h(n),k=r.useRef(!1),[U,G]=r.useState(0);return r.useEffect(()=>{let e=I.current;if(e)return e.addEventListener(p,L),()=>e.removeEventListener(p,L)},[L]),(0,m.jsx)(A,{scope:n,orientation:a,dir:E,loop:l,currentTabStopId:x,onItemFocus:r.useCallback(e=>F(e),[F]),onItemShiftTab:r.useCallback(()=>j(!0),[]),onFocusableItemAdd:r.useCallback(()=>G(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>G(e=>e-1),[]),children:(0,m.jsx)(s.sG.div,{tabIndex:M||0===U?-1:0,"data-orientation":a,...T,ref:C,style:{outline:"none",...e.style},onMouseDown:(0,o.m)(e.onMouseDown,()=>{k.current=!0}),onFocus:(0,o.m)(e.onFocus,e=>{let t=!k.current;if(e.target===e.currentTarget&&t&&!M){let t=new CustomEvent(p,v);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=O().filter(e=>e.focusable);D([e.find(e=>e.active),e.find(e=>e.id===x),...e].filter(Boolean).map(e=>e.ref.current),R)}}k.current=!1}),onBlur:(0,o.m)(e.onBlur,()=>j(!1))})})}),C="RovingFocusGroupItem",E=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:a=!0,active:i=!1,tabStopId:l,children:c,...d}=e,f=(0,u.B)(),p=l||f,v=R(C,n),w=v.currentTabStopId===p,g=h(n),{onFocusableItemAdd:y,onFocusableItemRemove:N,currentTabStopId:A}=v;return r.useEffect(()=>{if(a)return y(),()=>N()},[a,y,N]),(0,m.jsx)(b.ItemSlot,{scope:n,id:p,focusable:a,active:i,children:(0,m.jsx)(s.sG.span,{tabIndex:w?0:-1,"data-orientation":v.orientation,...d,ref:t,onMouseDown:(0,o.m)(e.onMouseDown,e=>{a?v.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.m)(e.onFocus,()=>v.onItemFocus(p)),onKeyDown:(0,o.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void v.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return x[o]}(e,v.orientation,v.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=g().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=v.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>D(n))}}),children:"function"==typeof c?c({isCurrentTabStop:w,hasTabStop:null!=A}):c})})});E.displayName=C;var x={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function D(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var F=T,M=E},9946:(e,t,n)=>{n.d(t,{A:()=>u});var r=n(2115);let o=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,n)=>n?n.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter((e,t,n)=>!!e&&""!==e.trim()&&n.indexOf(e)===t).join(" ").trim()};var i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,r.forwardRef)((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:l=2,absoluteStrokeWidth:u,className:s="",children:c,iconNode:d,...f}=e;return(0,r.createElement)("svg",{ref:t,...i,width:o,height:o,stroke:n,strokeWidth:u?24*Number(l)/Number(o):l,className:a("lucide",s),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"},...f},[...d.map(e=>{let[t,n]=e;return(0,r.createElement)(t,n)}),...Array.isArray(c)?c:[c]])}),u=(e,t)=>{let n=(0,r.forwardRef)((n,i)=>{let{className:u,...s}=n;return(0,r.createElement)(l,{ref:i,iconNode:t,className:a("lucide-".concat(o(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),u),...s})});return n.displayName=o(e),n}}}]);