import { Calendar, Award, Users, Palette } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Section, SectionHeader } from '@/components/ui/section'

const milestones = [
  {
    year: '2019',
    title: 'Started Professional Journey',
    description: 'Began my career as a professional makeup artist in Biratnagar, focusing on bridal and special occasion makeup.',
    icon: Calendar,
    color: 'from-rose-gold to-blush-pink'
  },
  {
    year: '2020',
    title: 'Expanded Service Areas',
    description: 'Extended services to Itahari, Dharan, and surrounding areas, building a strong client base across the region.',
    icon: Users,
    color: 'from-blush-pink to-lavender'
  },
  {
    year: '2021',
    title: 'Advanced Training & Certification',
    description: 'Completed advanced makeup courses and obtained professional certifications in modern makeup techniques.',
    icon: Award,
    color: 'from-lavender to-rose-gold'
  },
  {
    year: '2022',
    title: 'Specialized in Traditional Makeup',
    description: 'Developed expertise in traditional Nepali makeup styles, becoming known for authentic cultural looks.',
    icon: Palette,
    color: 'from-rose-gold to-blush-pink'
  },
  {
    year: '2023',
    title: 'Reached 100+ Happy Clients',
    description: 'Celebrated serving over 100 satisfied clients with consistently high ratings and positive reviews.',
    icon: Users,
    color: 'from-blush-pink to-lavender'
  },
  {
    year: '2024',
    title: 'Expanded to Kathmandu',
    description: 'Extended services to Nepal\'s capital, bringing professional makeup artistry to a wider audience.',
    icon: Calendar,
    color: 'from-lavender to-rose-gold'
  }
]

const skills = [
  { name: 'Bridal Makeup', level: 95 },
  { name: 'Traditional Makeup', level: 90 },
  { name: 'Party & Event Makeup', level: 92 },
  { name: 'Photoshoot Makeup', level: 88 },
  { name: 'Color Matching', level: 94 },
  { name: 'Contouring & Highlighting', level: 91 }
]

export default function AboutExperience() {
  return (
    <Section>
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Experience & Expertise"
          title="My Professional Journey"
          description="A timeline of growth, learning, and achievements in the makeup artistry field."
        />
      </AnimatedElement>

      {/* Timeline */}
      <div className="relative">
        {/* Timeline Line */}
        <div className="absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-rose-gold via-blush-pink to-lavender rounded-full hidden lg:block"></div>

        <StaggeredContainer className="space-y-12">
          {milestones.map((milestone, index) => (
            <StaggeredItem key={milestone.year}>
              <div className={`flex items-center ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} flex-col lg:gap-8 gap-4`}>
                {/* Content Card */}
                <div className="flex-1 lg:max-w-md">
                  <Card className="bg-white/80 backdrop-blur-sm border-0 hover:shadow-xl transition-all duration-300">
                    <CardContent className="p-6">
                      <div className="flex items-center gap-3 mb-3">
                        <Badge variant="secondary" className="text-sm">
                          {milestone.year}
                        </Badge>
                        <div className={`w-8 h-8 bg-gradient-to-r ${milestone.color} rounded-full flex items-center justify-center`}>
                          <milestone.icon className="w-4 h-4 text-white" />
                        </div>
                      </div>
                      <h3 className="font-display text-xl font-semibold text-text-primary mb-2">
                        {milestone.title}
                      </h3>
                      <p className="text-text-secondary leading-relaxed">
                        {milestone.description}
                      </p>
                    </CardContent>
                  </Card>
                </div>

                {/* Timeline Node */}
                <div className="hidden lg:block relative">
                  <div className={`w-4 h-4 bg-gradient-to-r ${milestone.color} rounded-full border-4 border-white shadow-lg`}></div>
                </div>

                {/* Spacer for alternating layout */}
                <div className="flex-1 lg:max-w-md hidden lg:block"></div>
              </div>
            </StaggeredItem>
          ))}
        </StaggeredContainer>
      </div>

      {/* Skills Section */}
      <div className="mt-20">
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Skills & Expertise"
            title="Areas of Specialization"
            description="My core competencies and areas of expertise in makeup artistry."
          />
        </AnimatedElement>

        <div className="grid md:grid-cols-2 gap-8">
          <AnimatedElement animation="slideRight" delay={0.3}>
            <Card className="bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0">
              <CardContent className="p-6">
                <h3 className="font-display text-xl font-semibold text-text-primary mb-6">
                  Technical Skills
                </h3>
                <div className="space-y-4">
                  {skills.map((skill) => (
                    <div key={skill.name} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-text-primary font-medium">{skill.name}</span>
                        <span className="text-text-secondary text-sm">{skill.level}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-gradient-to-r from-rose-gold to-blush-pink h-2 rounded-full transition-all duration-1000"
                          style={{ width: `${skill.level}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </AnimatedElement>

          <AnimatedElement animation="slideLeft" delay={0.5}>
            <Card className="bg-gradient-to-br from-lavender/10 to-blush-pink/10 border-0">
              <CardContent className="p-6">
                <h3 className="font-display text-xl font-semibold text-text-primary mb-6">
                  Professional Highlights
                </h3>
                <div className="space-y-4">
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-rose-gold rounded-full"></div>
                    <span className="text-text-secondary">5+ Years Professional Experience</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blush-pink rounded-full"></div>
                    <span className="text-text-secondary">100+ Successful Makeup Sessions</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-lavender rounded-full"></div>
                    <span className="text-text-secondary">5.0 Star Average Rating</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-rose-gold rounded-full"></div>
                    <span className="text-text-secondary">Certified in Advanced Techniques</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-blush-pink rounded-full"></div>
                    <span className="text-text-secondary">Specialized in Traditional & Modern Styles</span>
                  </div>
                  <div className="flex items-center gap-3">
                    <div className="w-2 h-2 bg-lavender rounded-full"></div>
                    <span className="text-text-secondary">Serving 7 Cities Across Nepal</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </AnimatedElement>
        </div>
      </div>
    </Section>
  )
}
