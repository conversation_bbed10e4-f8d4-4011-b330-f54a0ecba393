(()=>{var a={};a.id=984,a.ids=[984],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},12177:(a,b,c)=>{Promise.resolve().then(c.bind(c,20063))},19121:a=>{"use strict";a.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20063:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>A});var d=c(60687),e=c(43210),f=c(85814),g=c.n(f),h=c(30474),i=c(29523),j=c(89667),k=c(96834),l=c(56896),m=c(15079),n=c(21342),o=c(44493),p=c(96474);let q=(0,c(62688).A)("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]);var r=c(25366),s=c(99270),t=c(64398),u=c(88233),v=c(13861),w=c(63143),x=c(93661),y=c(4780),z=c(52581);function A(){let[a,b]=(0,e.useState)([]),[c,f]=(0,e.useState)(!0),[A,B]=(0,e.useState)(""),[C,D]=(0,e.useState)("all"),[E,F]=(0,e.useState)(""),[G,H]=(0,e.useState)("all"),[I,J]=(0,e.useState)("grid"),[K,L]=(0,e.useState)(1),[M,N]=(0,e.useState)(1),[O,P]=(0,e.useState)([]),Q=async()=>{try{let a=new URLSearchParams({page:K.toString(),limit:"grid"===I?"12":"10",...A&&{search:A},...C&&"all"!==C&&{status:C},...E&&{category:E},...G&&"all"!==G&&{featured:G}}),c=await fetch(`/api/gallery?${a}`);if(c.ok){let a=await c.json();b(a.gallery),N(a.pagination.pages)}else z.oR.error("Failed to fetch gallery")}catch(a){z.oR.error("Error fetching gallery")}finally{f(!1)}},R=async a=>{if(confirm("Are you sure you want to delete this gallery item?"))try{(await fetch(`/api/gallery/${a}`,{method:"DELETE"})).ok?(z.oR.success("Gallery item deleted successfully"),Q()):z.oR.error("Failed to delete gallery item")}catch(a){z.oR.error("Error deleting gallery item")}},S=async a=>{if(0===O.length)return void z.oR.error("Please select items first");let b=a.replace(/([A-Z])/g," $1").toLowerCase();if(confirm(`Are you sure you want to ${b} ${O.length} item(s)?`))try{let b=await fetch("/api/gallery/bulk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({ids:O,action:a})});if(b.ok){let a=await b.json();z.oR.success(a.message),P([]),Q()}else z.oR.error("Failed to perform bulk action")}catch(a){z.oR.error("Error performing bulk action")}},T=a=>{P(b=>b.includes(a)?b.filter(b=>b!==a):[...b,a])},U=a=>(0,d.jsx)(k.E,{variant:{ACTIVE:"default",INACTIVE:"secondary",ARCHIVED:"outline"}[a],children:a});return c?(0,d.jsx)("div",{children:"Loading..."}):(0,d.jsxs)("div",{className:"space-y-6",children:[(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)("h1",{className:"text-3xl font-bold",children:"Gallery"}),(0,d.jsx)("p",{className:"text-muted-foreground",children:"Manage your portfolio images and showcase your work"})]}),(0,d.jsx)(i.$,{asChild:!0,children:(0,d.jsxs)(g(),{href:"/dashboard/gallery/new",children:[(0,d.jsx)(p.A,{className:"h-4 w-4 mr-2"}),"Add Images"]})})]}),(0,d.jsxs)(o.Zp,{children:[(0,d.jsx)(o.aR,{children:(0,d.jsxs)("div",{className:"flex items-center justify-between",children:[(0,d.jsxs)("div",{children:[(0,d.jsx)(o.ZB,{children:"Filters"}),(0,d.jsx)(o.BT,{children:"Filter and search through your gallery"})]}),(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)(i.$,{variant:"grid"===I?"default":"outline",size:"sm",onClick:()=>J("grid"),children:(0,d.jsx)(q,{className:"h-4 w-4"})}),(0,d.jsx)(i.$,{variant:"list"===I?"default":"outline",size:"sm",onClick:()=>J("list"),children:(0,d.jsx)(r.A,{className:"h-4 w-4"})})]})]})}),(0,d.jsx)(o.Wu,{children:(0,d.jsxs)("div",{className:"flex gap-4",children:[(0,d.jsx)("div",{className:"flex-1",children:(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)(s.A,{className:"absolute left-2 top-2.5 h-4 w-4 text-muted-foreground"}),(0,d.jsx)(j.p,{placeholder:"Search gallery...",value:A,onChange:a=>B(a.target.value),className:"pl-8"})]})}),(0,d.jsxs)(m.l6,{value:C,onValueChange:D,children:[(0,d.jsx)(m.bq,{className:"w-[140px]",children:(0,d.jsx)(m.yv,{placeholder:"Status"})}),(0,d.jsxs)(m.gC,{children:[(0,d.jsx)(m.eb,{value:"all",children:"All"}),(0,d.jsx)(m.eb,{value:"ACTIVE",children:"Active"}),(0,d.jsx)(m.eb,{value:"INACTIVE",children:"Inactive"}),(0,d.jsx)(m.eb,{value:"ARCHIVED",children:"Archived"})]})]}),(0,d.jsx)(j.p,{placeholder:"Category",value:E,onChange:a=>F(a.target.value),className:"w-[140px]"}),(0,d.jsxs)(m.l6,{value:G,onValueChange:H,children:[(0,d.jsx)(m.bq,{className:"w-[140px]",children:(0,d.jsx)(m.yv,{placeholder:"Featured"})}),(0,d.jsxs)(m.gC,{children:[(0,d.jsx)(m.eb,{value:"all",children:"All"}),(0,d.jsx)(m.eb,{value:"true",children:"Featured"}),(0,d.jsx)(m.eb,{value:"false",children:"Not Featured"})]})]})]})})]}),O.length>0&&(0,d.jsx)(o.Zp,{children:(0,d.jsx)(o.Wu,{className:"pt-6",children:(0,d.jsxs)("div",{className:"flex items-center gap-2 flex-wrap",children:[(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:[O.length," item(s) selected"]}),(0,d.jsxs)(i.$,{size:"sm",onClick:()=>S("feature"),children:[(0,d.jsx)(t.A,{className:"h-4 w-4 mr-2"}),"Feature"]}),(0,d.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>S("unfeature"),children:"Unfeature"}),(0,d.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>S("activate"),children:"Activate"}),(0,d.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>S("deactivate"),children:"Deactivate"}),(0,d.jsx)(i.$,{size:"sm",variant:"outline",onClick:()=>S("archive"),children:"Archive"}),(0,d.jsxs)(i.$,{size:"sm",variant:"destructive",onClick:()=>S("delete"),children:[(0,d.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})})}),0===a.length?(0,d.jsx)(o.Zp,{children:(0,d.jsx)(o.Wu,{className:"text-center py-12",children:(0,d.jsxs)("div",{className:"text-muted-foreground",children:["No gallery items found.",(0,d.jsx)(g(),{href:"/dashboard/gallery/new",className:"text-primary hover:underline ml-1",children:"Add your first images"})]})})}):"grid"===I?(0,d.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:a.map(a=>(0,d.jsxs)(o.Zp,{className:"group overflow-hidden",children:[(0,d.jsxs)("div",{className:"relative",children:[(0,d.jsx)("div",{className:"absolute top-2 left-2 z-10",children:(0,d.jsx)(l.S,{checked:O.includes(a.id),onCheckedChange:()=>T(a.id),className:"bg-white/80 border-white"})}),(0,d.jsxs)("div",{className:"absolute top-2 right-2 z-10 flex gap-1",children:[a.featured&&(0,d.jsx)(k.E,{className:"bg-yellow-500",children:(0,d.jsx)(t.A,{className:"h-3 w-3"})}),U(a.status)]}),(0,d.jsx)("div",{className:"relative aspect-square overflow-hidden",children:(0,d.jsx)(h.default,{src:a.image,alt:a.title,fill:!0,className:"object-cover transition-transform group-hover:scale-105"})}),(0,d.jsx)("div",{className:"absolute inset-0 bg-black/0 group-hover:bg-black/20 transition-colors flex items-center justify-center opacity-0 group-hover:opacity-100",children:(0,d.jsxs)("div",{className:"flex gap-2",children:[(0,d.jsx)(i.$,{size:"sm",variant:"secondary",asChild:!0,children:(0,d.jsx)(g(),{href:`/dashboard/gallery/${a.id}`,children:(0,d.jsx)(v.A,{className:"h-4 w-4"})})}),(0,d.jsx)(i.$,{size:"sm",variant:"secondary",asChild:!0,children:(0,d.jsx)(g(),{href:`/dashboard/gallery/${a.id}/edit`,children:(0,d.jsx)(w.A,{className:"h-4 w-4"})})}),(0,d.jsx)(i.$,{size:"sm",variant:"destructive",onClick:()=>R(a.id),children:(0,d.jsx)(u.A,{className:"h-4 w-4"})})]})})]}),(0,d.jsxs)(o.Wu,{className:"p-4",children:[(0,d.jsx)("h3",{className:"font-medium truncate",children:a.title}),a.description&&(0,d.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:(0,y.EJ)(a.description,60)}),a.category&&(0,d.jsx)(k.E,{variant:"outline",className:"mt-2",children:a.category}),(0,d.jsx)("div",{className:"text-xs text-muted-foreground mt-2",children:(0,y.Yq)(a.createdAt)})]})]},a.id))}):(0,d.jsx)(o.Zp,{children:(0,d.jsx)(o.Wu,{className:"p-0",children:(0,d.jsxs)("div",{className:"space-y-4 p-6",children:[(0,d.jsxs)("div",{className:"flex items-center gap-4 pb-2 border-b",children:[(0,d.jsx)(l.S,{checked:O.length===a.length&&a.length>0,onCheckedChange:()=>{P(b=>b.length===a.length?[]:a.map(a=>a.id))}}),(0,d.jsx)("span",{className:"text-sm font-medium",children:"Select All"})]}),a.map(a=>(0,d.jsxs)("div",{className:"flex items-center gap-4 p-4 border rounded-lg",children:[(0,d.jsx)(l.S,{checked:O.includes(a.id),onCheckedChange:()=>T(a.id)}),(0,d.jsx)("div",{className:"relative w-16 h-16 rounded-md overflow-hidden flex-shrink-0",children:(0,d.jsx)(h.default,{src:a.image,alt:a.title,fill:!0,className:"object-cover"})}),(0,d.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,d.jsxs)("div",{className:"flex items-center gap-2",children:[(0,d.jsx)("h3",{className:"font-medium truncate",children:a.title}),a.featured&&(0,d.jsx)(t.A,{className:"h-4 w-4 text-yellow-500 fill-current"})]}),a.description&&(0,d.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:(0,y.EJ)(a.description,100)}),(0,d.jsxs)("div",{className:"flex items-center gap-2 mt-2",children:[U(a.status),a.category&&(0,d.jsx)(k.E,{variant:"outline",children:a.category}),(0,d.jsx)("span",{className:"text-xs text-muted-foreground",children:(0,y.Yq)(a.createdAt)})]})]}),(0,d.jsxs)(n.rI,{children:[(0,d.jsx)(n.ty,{asChild:!0,children:(0,d.jsx)(i.$,{variant:"ghost",size:"sm",children:(0,d.jsx)(x.A,{className:"h-4 w-4"})})}),(0,d.jsxs)(n.SQ,{align:"end",children:[(0,d.jsx)(n._2,{asChild:!0,children:(0,d.jsxs)(g(),{href:`/dashboard/gallery/${a.id}`,children:[(0,d.jsx)(v.A,{className:"h-4 w-4 mr-2"}),"View"]})}),(0,d.jsx)(n._2,{asChild:!0,children:(0,d.jsxs)(g(),{href:`/dashboard/gallery/${a.id}/edit`,children:[(0,d.jsx)(w.A,{className:"h-4 w-4 mr-2"}),"Edit"]})}),(0,d.jsxs)(n._2,{onClick:()=>R(a.id),className:"text-destructive",children:[(0,d.jsx)(u.A,{className:"h-4 w-4 mr-2"}),"Delete"]})]})]})]},a.id))]})})}),M>1&&(0,d.jsxs)("div",{className:"flex items-center justify-center gap-2",children:[(0,d.jsx)(i.$,{variant:"outline",onClick:()=>L(K-1),disabled:1===K,children:"Previous"}),(0,d.jsxs)("span",{className:"text-sm text-muted-foreground",children:["Page ",K," of ",M]}),(0,d.jsx)(i.$,{variant:"outline",onClick:()=>L(K+1),disabled:K===M,children:"Next"})]})]})}},21905:(a,b,c)=>{Promise.resolve().then(c.bind(c,77850))},25366:(a,b,c)=>{"use strict";c.d(b,{A:()=>d});let d=(0,c(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},26713:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/is-bot")},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:a=>{"use strict";a.exports=require("path")},41025:a=>{"use strict";a.exports=require("next/dist/server/app-render/dynamic-access-async-storage.external.js")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},77850:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>d});let d=(0,c(61369).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\ashish\\\\anjali-portfolio\\\\anjali-cms\\\\src\\\\app\\\\dashboard\\\\gallery\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali-cms\\src\\app\\dashboard\\gallery\\page.tsx","default")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},89878:(a,b,c)=>{"use strict";c.r(b),c.d(b,{GlobalError:()=>C.a,__next_app__:()=>I,handler:()=>K,pages:()=>H,routeModule:()=>J,tree:()=>G});var d=c(65239),e=c(48088),f=c(47220),g=c(81289),h=c(26191),i=c(14823),j=c(71998),k=c(92603),l=c(54649),m=c(32781),n=c(82602),o=c(61268),p=c(4853),q=c(261),r=c(5052),s=c(9977),t=c(26713),u=c(43365),v=c(71454),w=c(67778),x=c(46143),y=c(39105),z=c(38171),A=c(86439),B=c(16133),C=c.n(B),D=c(30893),E=c(52836),F={};for(let a in D)0>["default","tree","pages","GlobalError","__next_app__","routeModule","handler"].indexOf(a)&&(F[a]=()=>D[a]);c.d(b,F);let G={children:["",{children:["dashboard",{children:["gallery",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(c.bind(c,77850)),"C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali-cms\\src\\app\\dashboard\\gallery\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(c.bind(c,63144)),"C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali-cms\\src\\app\\dashboard\\layout.tsx"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(c.bind(c,94431)),"C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali-cms\\src\\app\\layout.tsx"],"global-error":[()=>Promise.resolve().then(c.t.bind(c,16133,23)),"next/dist/client/components/builtin/global-error.js"],"not-found":[()=>Promise.resolve().then(c.t.bind(c,80849,23)),"next/dist/client/components/builtin/not-found.js"],forbidden:[()=>Promise.resolve().then(c.t.bind(c,29868,23)),"next/dist/client/components/builtin/forbidden.js"],unauthorized:[()=>Promise.resolve().then(c.t.bind(c,79615,23)),"next/dist/client/components/builtin/unauthorized.js"],metadata:{icon:[async a=>(await Promise.resolve().then(c.bind(c,70440))).default(a)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,H=["C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali-cms\\src\\app\\dashboard\\gallery\\page.tsx"],I={require:c,loadChunk:()=>Promise.resolve()},J=new d.AppPageRouteModule({definition:{kind:e.RouteKind.APP_PAGE,page:"/dashboard/gallery/page",pathname:"/dashboard/gallery",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:G},distDir:".next",projectDir:""});async function K(a,b,c){var d;let B="/dashboard/gallery/page";"/index"===B&&(B="/");let F="false",L=(0,h.getRequestMeta)(a,"postponed"),M=(0,h.getRequestMeta)(a,"minimalMode"),N=await J.prepare(a,b,{srcPage:B,multiZoneDraftMode:F});if(!N)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:O,query:P,params:Q,parsedUrl:R,pageIsDynamic:S,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,serverActionsManifest:W,clientReferenceManifest:X,subresourceIntegrityManifest:Y,prerenderManifest:Z,isDraftMode:$,resolvedPathname:_,revalidateOnlyGenerated:aa,routerServerContext:ab,nextConfig:ac}=N,ad=R.pathname||"/",ae=(0,q.normalizeAppPath)(B),{isOnDemandRevalidate:af}=N,ag=Z.dynamicRoutes[ae],ah=Z.routes[_],ai=!!(ag||ah||Z.routes[ae]),aj=a.headers["user-agent"]||"",ak=(0,t.getBotType)(aj),al=(0,o.isHtmlBotRequest)(a),am=(0,h.getRequestMeta)(a,"isPrefetchRSCRequest")??!!a.headers[s.NEXT_ROUTER_PREFETCH_HEADER],an=(0,h.getRequestMeta)(a,"isRSCRequest")??!!a.headers[s.RSC_HEADER],ao=(0,r.getIsPossibleServerAction)(a),ap=(0,l.checkIsAppPPREnabled)(ac.experimental.ppr)&&(null==(d=Z.routes[ae]??Z.dynamicRoutes[ae])?void 0:d.renderingMode)==="PARTIALLY_STATIC",aq=!1,ar=!1,as=ap?L:void 0,at=ap&&an&&!am,au=(0,h.getRequestMeta)(a,"segmentPrefetchRSCRequest"),av=!aj||(0,o.shouldServeStreamingMetadata)(aj,ac.htmlLimitedBots);al&&ap&&(ai=!1,av=!1);let aw=!0===J.isDev||!ai||"string"==typeof L||at,ax=al&&ap,ay=null;$||!ai||aw||ao||as||at||(ay=_);let az=ay;!az&&J.isDev&&(az=_);let aA={...D,tree:G,pages:H,GlobalError:C(),handler:K,routeModule:J,__next_app__:I};W&&X&&(0,n.setReferenceManifestsSingleton)({page:B,clientReferenceManifest:X,serverActionsManifest:W,serverModuleMap:(0,p.createServerModuleMap)({serverActionsManifest:W})});let aB=a.method||"GET",aC=(0,g.getTracer)(),aD=aC.getActiveScopeSpan();try{let d=async(c,d)=>{let e=new k.NodeNextRequest(a),f=new k.NodeNextResponse(b);return J.render(e,f,d).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=aC.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==i.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${aB} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${aB} ${a.url}`)})},f=async({span:e,postponed:f,fallbackRouteParams:g})=>{let i={query:P,params:Q,page:ae,sharedContext:{buildId:O},serverComponentsHmrCache:(0,h.getRequestMeta)(a,"serverComponentsHmrCache"),fallbackRouteParams:g,renderOpts:{App:()=>null,Document:()=>null,pageConfig:{},ComponentMod:aA,Component:(0,j.T)(aA),params:Q,routeModule:J,page:B,postponed:f,shouldWaitOnAllReady:ax,serveStreamingMetadata:av,supportsDynamicResponse:"string"==typeof f||aw,buildManifest:T,nextFontManifest:U,reactLoadableManifest:V,subresourceIntegrityManifest:Y,serverActionsManifest:W,clientReferenceManifest:X,setIsrStatus:null==ab?void 0:ab.setIsrStatus,dir:J.projectDir,isDraftMode:$,isRevalidate:ai&&!f&&!at,botType:ak,isOnDemandRevalidate:af,isPossibleServerAction:ao,assetPrefix:ac.assetPrefix,nextConfigOutput:ac.output,crossOrigin:ac.crossOrigin,trailingSlash:ac.trailingSlash,previewProps:Z.preview,deploymentId:ac.deploymentId,enableTainting:ac.experimental.taint,htmlLimitedBots:ac.htmlLimitedBots,devtoolSegmentExplorer:ac.experimental.devtoolSegmentExplorer,reactMaxHeadersLength:ac.reactMaxHeadersLength,multiZoneDraftMode:F,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:ac.experimental.cacheLife,basePath:ac.basePath,serverActions:ac.experimental.serverActions,...aq?{nextExport:!0,supportsDynamicResponse:!1,isStaticGeneration:!0,isRevalidate:!0,isDebugDynamicAccesses:aq}:{},experimental:{isRoutePPREnabled:ap,expireTime:ac.expireTime,staleTimes:ac.experimental.staleTimes,dynamicIO:!!ac.experimental.dynamicIO,clientSegmentCache:!!ac.experimental.clientSegmentCache,dynamicOnHover:!!ac.experimental.dynamicOnHover,inlineCss:!!ac.experimental.inlineCss,authInterrupts:!!ac.experimental.authInterrupts,clientTraceMetadata:ac.experimental.clientTraceMetadata||[]},waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:()=>{},onInstrumentationRequestError:(b,c,d)=>J.onRequestError(a,b,d,ab),err:(0,h.getRequestMeta)(a,"invokeError"),dev:J.isDev}},k=await d(e,i),{metadata:l}=k,{cacheControl:m,headers:n={},fetchTags:o}=l;if(o&&(n[x.NEXT_CACHE_TAGS_HEADER]=o),a.fetchMetrics=l.fetchMetrics,ai&&(null==m?void 0:m.revalidate)===0&&!J.isDev&&!ap){let a=l.staticBailoutInfo,b=Object.defineProperty(Error(`Page changed from static to dynamic at runtime ${_}${(null==a?void 0:a.description)?`, reason: ${a.description}`:""}
see more here https://nextjs.org/docs/messages/app-static-to-dynamic-error`),"__NEXT_ERROR_CODE",{value:"E132",enumerable:!1,configurable:!0});if(null==a?void 0:a.stack){let c=a.stack;b.stack=b.message+c.substring(c.indexOf("\n"))}throw b}return{value:{kind:u.CachedRouteKind.APP_PAGE,html:k,headers:n,rscData:l.flightData,postponed:l.postponed,status:l.statusCode,segmentData:l.segmentData},cacheControl:m}},l=async({hasResolved:d,previousCacheEntry:g,isRevalidating:i,span:j})=>{let k,l=!1===J.isDev,n=d||b.writableEnded;if(af&&aa&&!g&&!M)return(null==ab?void 0:ab.render404)?await ab.render404(a,b):(b.statusCode=404,b.end("This page could not be found")),null;if(ag&&(k=(0,v.parseFallbackField)(ag.fallback)),k===v.FallbackMode.PRERENDER&&(0,t.isBot)(aj)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),(null==g?void 0:g.isStale)===-1&&(af=!0),af&&(k!==v.FallbackMode.NOT_FOUND||g)&&(k=v.FallbackMode.BLOCKING_STATIC_RENDER),!M&&k!==v.FallbackMode.BLOCKING_STATIC_RENDER&&az&&!n&&!$&&S&&(l||!ah)){let b;if((l||ag)&&k===v.FallbackMode.NOT_FOUND)throw new A.NoFallbackError;if(ap&&!an){if(b=await J.handleResponse({cacheKey:l?ae:null,req:a,nextConfig:ac,routeKind:e.RouteKind.APP_PAGE,isFallback:!0,prerenderManifest:Z,isRoutePPREnabled:ap,responseGenerator:async()=>f({span:j,postponed:void 0,fallbackRouteParams:l||ar?(0,m.u)(ae):null}),waitUntil:c.waitUntil}),null===b)return null;if(b)return delete b.cacheControl,b}}let o=af||i||!as?void 0:as;if(aq&&void 0!==o)return{cacheControl:{revalidate:1,expire:void 0},value:{kind:u.CachedRouteKind.PAGES,html:w.default.fromStatic(""),pageData:{},headers:void 0,status:void 0}};let p=S&&ap&&((0,h.getRequestMeta)(a,"renderFallbackShell")||ar)?(0,m.u)(ad):null;return f({span:j,postponed:o,fallbackRouteParams:p})},n=async d=>{var g,i,j,k,m;let n,o=await J.handleResponse({cacheKey:ay,responseGenerator:a=>l({span:d,...a}),routeKind:e.RouteKind.APP_PAGE,isOnDemandRevalidate:af,isRoutePPREnabled:ap,req:a,nextConfig:ac,prerenderManifest:Z,waitUntil:c.waitUntil});if($&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate"),J.isDev&&b.setHeader("Cache-Control","no-store, must-revalidate"),!o){if(ay)throw Object.defineProperty(Error("invariant: cache entry required but not generated"),"__NEXT_ERROR_CODE",{value:"E62",enumerable:!1,configurable:!0});return null}if((null==(g=o.value)?void 0:g.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant app-page handler received invalid cache entry ${null==(j=o.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E707",enumerable:!1,configurable:!0});let p="string"==typeof o.value.postponed;ai&&!at&&(!p||am)&&(M||b.setHeader("x-nextjs-cache",af?"REVALIDATED":o.isMiss?"MISS":o.isStale?"STALE":"HIT"),b.setHeader(s.NEXT_IS_PRERENDER_HEADER,"1"));let{value:q}=o;if(as)n={revalidate:0,expire:void 0};else if(M&&an&&!am&&ap)n={revalidate:0,expire:void 0};else if(!J.isDev)if($)n={revalidate:0,expire:void 0};else if(ai){if(o.cacheControl)if("number"==typeof o.cacheControl.revalidate){if(o.cacheControl.revalidate<1)throw Object.defineProperty(Error(`Invalid revalidate configuration provided: ${o.cacheControl.revalidate} < 1`),"__NEXT_ERROR_CODE",{value:"E22",enumerable:!1,configurable:!0});n={revalidate:o.cacheControl.revalidate,expire:(null==(k=o.cacheControl)?void 0:k.expire)??ac.expireTime}}else n={revalidate:x.CACHE_ONE_YEAR,expire:void 0}}else b.getHeader("Cache-Control")||(n={revalidate:0,expire:void 0});if(o.cacheControl=n,"string"==typeof au&&(null==q?void 0:q.kind)===u.CachedRouteKind.APP_PAGE&&q.segmentData){b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"2");let c=null==(m=q.headers)?void 0:m[x.NEXT_CACHE_TAGS_HEADER];M&&ai&&c&&"string"==typeof c&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,c);let d=q.segmentData.get(au);return void 0!==d?(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(d),cacheControl:o.cacheControl}):(b.statusCode=204,(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(""),cacheControl:o.cacheControl}))}let r=(0,h.getRequestMeta)(a,"onCacheEntry");if(r&&await r({...o,value:{...o.value,kind:"PAGE"}},{url:(0,h.getRequestMeta)(a,"initURL")}))return null;if(p&&as)throw Object.defineProperty(Error("Invariant: postponed state should not be present on a resume request"),"__NEXT_ERROR_CODE",{value:"E396",enumerable:!1,configurable:!0});if(q.headers){let a={...q.headers};for(let[c,d]of(M&&ai||delete a[x.NEXT_CACHE_TAGS_HEADER],Object.entries(a)))if(void 0!==d)if(Array.isArray(d))for(let a of d)b.appendHeader(c,a);else"number"==typeof d&&(d=d.toString()),b.appendHeader(c,d)}let t=null==(i=q.headers)?void 0:i[x.NEXT_CACHE_TAGS_HEADER];if(M&&ai&&t&&"string"==typeof t&&b.setHeader(x.NEXT_CACHE_TAGS_HEADER,t),!q.status||an&&ap||(b.statusCode=q.status),!M&&q.status&&E.RedirectStatusCode[q.status]&&an&&(b.statusCode=200),p&&b.setHeader(s.NEXT_DID_POSTPONE_HEADER,"1"),an&&!$){if(void 0===q.rscData){if(q.postponed)throw Object.defineProperty(Error("Invariant: Expected postponed to be undefined"),"__NEXT_ERROR_CODE",{value:"E372",enumerable:!1,configurable:!0});return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:q.html,cacheControl:at?{revalidate:0,expire:void 0}:o.cacheControl})}return(0,z.sendRenderResult)({req:a,res:b,type:"rsc",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:w.default.fromStatic(q.rscData),cacheControl:o.cacheControl})}let v=q.html;if(!p||M)return(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:o.cacheControl});if(aq)return v.chain(new ReadableStream({start(a){a.enqueue(y.ENCODED_TAGS.CLOSED.BODY_AND_HTML),a.close()}})),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}});let A=new TransformStream;return v.chain(A.readable),f({span:d,postponed:q.postponed,fallbackRouteParams:null}).then(async a=>{var b,c;if(!a)throw Object.defineProperty(Error("Invariant: expected a result to be returned"),"__NEXT_ERROR_CODE",{value:"E463",enumerable:!1,configurable:!0});if((null==(b=a.value)?void 0:b.kind)!==u.CachedRouteKind.APP_PAGE)throw Object.defineProperty(Error(`Invariant: expected a page response, got ${null==(c=a.value)?void 0:c.kind}`),"__NEXT_ERROR_CODE",{value:"E305",enumerable:!1,configurable:!0});await a.value.html.pipeTo(A.writable)}).catch(a=>{A.writable.abort(a).catch(a=>{console.error("couldn't abort transformer",a)})}),(0,z.sendRenderResult)({req:a,res:b,type:"html",generateEtags:ac.generateEtags,poweredByHeader:ac.poweredByHeader,result:v,cacheControl:{revalidate:0,expire:void 0}})};if(!aD)return await aC.withPropagatedContext(a.headers,()=>aC.trace(i.BaseServerSpan.handleRequest,{spanName:`${aB} ${a.url}`,kind:g.SpanKind.SERVER,attributes:{"http.method":aB,"http.target":a.url}},n));await n(aD)}catch(b){throw aD||await J.onRequestError(a,b,{routerKind:"App Router",routePath:B,routeType:"render",revalidateReason:(0,f.c)({isRevalidate:ai,isOnDemandRevalidate:af})},ab),b}}}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[2073,7840,1999,8316,474,5555,6926,4040],()=>b(b.s=89878));module.exports=c})();