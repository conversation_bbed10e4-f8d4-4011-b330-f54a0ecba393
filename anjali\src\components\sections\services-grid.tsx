'use client'

import Image from 'next/image'
import Link from 'next/link'
import { <PERSON>, <PERSON>, ArrowR<PERSON>, Check, Loader2, AlertCircle } from 'lucide-react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Section, SectionHeader } from '@/components/ui/section'
import { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'
import { useActiveServices, useContactInfo } from '@/hooks/use-api'
import { generateWhatsAppLink } from '@/lib/utils'
import { trackServiceInquiry, trackWhatsAppClick } from '@/lib/analytics'

export default function ServicesGrid() {
  const { data: servicesData, isLoading, error } = useActiveServices()
  const { data: contactInfo } = useContactInfo()

  const services = servicesData?.services || []

  // Loading state
  if (isLoading) {
    return (
      <Section>
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Our Services"
            title="Complete Makeup Solutions"
            description="Professional makeup services tailored to your unique style and occasion. Each service includes consultation, application, and touch-up guidance."
          />
        </AnimatedElement>
        <div className="flex justify-center items-center py-20">
          <Loader2 className="w-8 h-8 animate-spin text-rose-gold" />
          <span className="ml-3 text-text-secondary">Loading services...</span>
        </div>
      </Section>
    )
  }

  // Error state
  if (error) {
    return (
      <Section>
        <AnimatedElement animation="fadeIn">
          <SectionHeader
            subtitle="Our Services"
            title="Complete Makeup Solutions"
            description="Professional makeup services tailored to your unique style and occasion. Each service includes consultation, application, and touch-up guidance."
          />
        </AnimatedElement>
        <div className="text-center py-20">
          <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <AlertCircle className="w-8 h-8 text-red-600" />
          </div>
          <h3 className="font-display text-xl font-semibold text-text-primary mb-2">
            Unable to Load Services
          </h3>
          <p className="text-text-secondary mb-6">
            We&apos;re having trouble loading our services. Please try again later.
          </p>
          <Button variant="outline" onClick={() => window.location.reload()}>
            Try Again
          </Button>
        </div>
      </Section>
    )
  }

  return (
    <Section>
      <AnimatedElement animation="fadeIn">
        <SectionHeader
          subtitle="Our Services"
          title="Complete Makeup Solutions"
          description="Professional makeup services tailored to your unique style and occasion. Each service includes consultation, application, and touch-up guidance."
        />
      </AnimatedElement>

      <StaggeredContainer className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
        {services.map((service) => (
          <StaggeredItem key={service.id}>
            <Card 
              id={service.id}
              className="group h-full hover:shadow-2xl transition-all duration-300 border-0 bg-white overflow-hidden"
            >
              {/* Service Image */}
              <div className="relative aspect-[4/3] overflow-hidden">
                <Image
                  src={service.image || `https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=400&h=300&fit=crop&crop=face&q=80`}
                  alt={service.title}
                  fill
                  className="object-cover group-hover:scale-105 transition-transform duration-300"
                />
                
                {/* Overlay with badges */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="flex items-center gap-2 mb-2">
                      {service.popular && (
                        <Badge variant="default" className="text-xs">
                          <Star className="w-3 h-3 mr-1 fill-current" />
                          Popular
                        </Badge>
                      )}
                      <Badge variant="secondary" className="text-xs">
                        {service.category}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Top badges */}
                <div className="absolute top-4 left-4 right-4 flex justify-between items-start">
                  {service.popular && (
                    <Badge variant="default" className="text-xs">
                      <Star className="w-3 h-3 mr-1 fill-current" />
                      Popular
                    </Badge>
                  )}
                  <Badge variant="secondary" className="text-xs bg-white/90 text-text-primary">
                    {service.category}
                  </Badge>
                </div>
              </div>
              
              <CardHeader className="pb-3">
                <CardTitle className="text-xl group-hover:text-rose-gold-dark transition-colors">
                  {service.title}
                </CardTitle>
                <CardDescription className="text-text-secondary">
                  {service.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4 flex-1 flex flex-col">
                {/* Service Details */}
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2 text-text-secondary">
                    <Clock className="w-4 h-4" />
                    <span>{service.duration}</span>
                  </div>
                  <div className="font-semibold text-rose-gold-dark text-lg">
                    {service.price}
                  </div>
                </div>
                
                {/* Features */}
                <div className="space-y-2 flex-1">
                  <h4 className="font-semibold text-text-primary text-sm">What&apos;s Included:</h4>
                  <ul className="space-y-1">
                    {service.features.map((feature, idx) => (
                      <li key={idx} className="text-sm text-text-secondary flex items-start gap-2">
                        <Check className="w-3 h-3 text-rose-gold-dark mt-1 flex-shrink-0" />
                        <span>{feature}</span>
                      </li>
                    ))}
                  </ul>
                </div>
                
                {/* CTA Button */}
                <div className="pt-4 mt-auto">
                  <Button 
                    asChild 
                    variant="gradient" 
                    className="w-full group"
                    size="sm"
                  >
                    <Link
                      href={generateWhatsAppLink(
                        contactInfo?.phone || '',
                        `Hi! I'm interested in ${service.title}. Could you provide more details about pricing and availability?`
                      )}
                      target="_blank"
                      rel="noopener noreferrer"
                      onClick={() => {
                        trackServiceInquiry(service.id, service.title, 'services_grid')
                        trackWhatsAppClick('services_grid', `service_inquiry_${service.id}`)
                      }}
                    >
                      Book This Service
                      <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </StaggeredItem>
        ))}
      </StaggeredContainer>

      {/* Service Information */}
      <AnimatedElement animation="slideUp" delay={0.6} className="mt-16">
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card className="bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-3">
                <Check className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-text-primary mb-2">Quality Products</h3>
              <p className="text-text-secondary text-sm">Premium makeup brands and professional-grade products</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-blush-pink/10 to-lavender/10 border-0">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto mb-3">
                <Clock className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-text-primary mb-2">Punctual Service</h3>
              <p className="text-text-secondary text-sm">Always on time for your important events</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-lavender/10 to-rose-gold/10 border-0">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto mb-3">
                <Star className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-text-primary mb-2">Expert Technique</h3>
              <p className="text-text-secondary text-sm">5+ years of professional experience and training</p>
            </CardContent>
          </Card>

          <Card className="bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0">
            <CardContent className="p-6 text-center">
              <div className="w-12 h-12 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto mb-3">
                <ArrowRight className="w-6 h-6 text-white" />
              </div>
              <h3 className="font-semibold text-text-primary mb-2">Custom Approach</h3>
              <p className="text-text-secondary text-sm">Personalized looks tailored to your style and occasion</p>
            </CardContent>
          </Card>
        </div>
      </AnimatedElement>
    </Section>
  )
}
