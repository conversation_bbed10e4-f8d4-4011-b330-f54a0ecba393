(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1104],{285:(e,s,r)=>{"use strict";r.d(s,{$:()=>c});var t=r(5155);r(2115);var a=r(9708),i=r(2085),n=r(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function c(e){let{className:s,variant:r,size:i,asChild:c=!1,...l}=e,o=c?a.DX:"button";return(0,t.jsx)(o,{"data-slot":"button",className:(0,n.cn)(d({variant:r,size:i,className:s})),...l})}},646:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},2346:(e,s,r)=>{"use strict";r.d(s,{w:()=>n});var t=r(5155);r(2115);var a=r(7489),i=r(9434);function n(e){let{className:s,orientation:r="horizontal",decorative:n=!0,...d}=e;return(0,t.jsx)(a.b,{"data-slot":"separator",decorative:n,orientation:r,className:(0,i.cn)("bg-border shrink-0 data-[orientation=horizontal]:h-px data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-px",s),...d})}},2962:(e,s,r)=>{Promise.resolve().then(r.bind(r,5170))},3717:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},4186:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("clock",[["path",{d:"M12 6v6l4 2",key:"mmk7yg"}],["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},5169:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},5170:(e,s,r)=>{"use strict";r.r(s),r.d(s,{default:()=>N});var t=r(5155),a=r(2115),i=r(5695),n=r(6874),d=r.n(n),c=r(6766),l=r(285),o=r(6126),u=r(6695),h=r(2346),x=r(5169),m=r(8564),v=r(9074),p=r(4186),f=r(5868),g=r(3717),j=r(646),b=r(9434),y=r(6671);function N(){let e=(0,i.useParams)(),s=(0,i.useRouter)(),[r,n]=(0,a.useState)(null),[N,w]=(0,a.useState)(!0);if((0,a.useEffect)(()=>{let r=async()=>{try{let r=await fetch("/api/services/".concat(e.id));if(r.ok){let e=await r.json();n(e)}else y.oR.error("Service not found"),s.push("/dashboard/services")}catch(e){y.oR.error("Error fetching service"),s.push("/dashboard/services")}finally{w(!1)}};e.id&&r()},[e.id,s]),N)return(0,t.jsx)("div",{children:"Loading..."});if(!r)return(0,t.jsx)("div",{children:"Service not found"});let A=e=>(0,t.jsx)(o.E,{variant:{ACTIVE:"default",INACTIVE:"secondary",ARCHIVED:"outline"}[e],children:e});return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center gap-4",children:[(0,t.jsxs)(l.$,{variant:"ghost",size:"sm",onClick:()=>s.back(),children:[(0,t.jsx)(x.A,{className:"h-4 w-4 mr-2"}),"Back"]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold flex items-center gap-2",children:[r.title,r.popular&&(0,t.jsx)(m.A,{className:"h-6 w-6 text-yellow-500 fill-current"})]}),(0,t.jsxs)("div",{className:"flex items-center gap-4 text-sm text-muted-foreground mt-2",children:[(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(v.A,{className:"h-4 w-4"}),"Created ",(0,b.Yq)(r.createdAt)]}),r.duration&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(p.A,{className:"h-4 w-4"}),r.duration]}),r.price&&(0,t.jsxs)("div",{className:"flex items-center gap-1",children:[(0,t.jsx)(f.A,{className:"h-4 w-4"}),r.price]})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center gap-2",children:[A(r.status),r.category&&(0,t.jsx)(o.E,{variant:"outline",children:r.category}),(0,t.jsx)(l.$,{asChild:!0,children:(0,t.jsxs)(d(),{href:"/dashboard/services/".concat(r.id,"/edit"),children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Edit"]})})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[r.image&&(0,t.jsx)(u.Zp,{children:(0,t.jsx)(u.Wu,{className:"p-0",children:(0,t.jsx)("div",{className:"relative aspect-video w-full overflow-hidden rounded-lg",children:(0,t.jsx)(c.default,{src:r.image,alt:r.title,fill:!0,className:"object-cover"})})})}),(0,t.jsxs)(u.Zp,{children:[(0,t.jsx)(u.aR,{children:(0,t.jsx)(u.ZB,{children:"Description"})}),(0,t.jsx)(u.Wu,{children:(0,t.jsx)("p",{className:"text-muted-foreground leading-relaxed",children:r.description})})]}),r.features.length>0&&(0,t.jsxs)(u.Zp,{children:[(0,t.jsxs)(u.aR,{children:[(0,t.jsx)(u.ZB,{children:"Features & Benefits"}),(0,t.jsx)(u.BT,{children:"What's included in this service"})]}),(0,t.jsx)(u.Wu,{children:(0,t.jsx)("ul",{className:"space-y-2",children:r.features.map((e,s)=>(0,t.jsxs)("li",{className:"flex items-start gap-2",children:[(0,t.jsx)(j.A,{className:"h-5 w-5 text-green-500 mt-0.5 flex-shrink-0"}),(0,t.jsx)("span",{children:e})]},s))})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(u.Zp,{children:[(0,t.jsx)(u.aR,{children:(0,t.jsx)(u.ZB,{children:"Service Details"})}),(0,t.jsxs)(u.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)(k,{className:"text-sm font-medium",children:"Status"}),(0,t.jsx)("div",{className:"mt-1",children:A(r.status)})]}),r.category&&(0,t.jsxs)("div",{children:[(0,t.jsx)(k,{className:"text-sm font-medium",children:"Category"}),(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsx)(o.E,{variant:"outline",children:r.category})})]}),r.duration&&(0,t.jsxs)("div",{children:[(0,t.jsx)(k,{className:"text-sm font-medium",children:"Duration"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:r.duration})]}),r.price&&(0,t.jsxs)("div",{children:[(0,t.jsx)(k,{className:"text-sm font-medium",children:"Price"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground mt-1",children:r.price})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)(k,{className:"text-sm font-medium",children:"Popular Service"}),(0,t.jsx)("div",{className:"mt-1",children:(0,t.jsx)(o.E,{variant:r.popular?"default":"outline",children:r.popular?"Yes":"No"})})]}),(0,t.jsx)(h.w,{}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Created:"})," ",(0,b.Yq)(r.createdAt)]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Updated:"})," ",(0,b.Yq)(r.updatedAt)]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("span",{className:"font-medium",children:"Slug:"})," ",(0,t.jsx)("code",{className:"text-xs bg-muted px-1 py-0.5 rounded",children:r.slug})]})]})]})]}),(0,t.jsxs)(u.Zp,{children:[(0,t.jsx)(u.aR,{children:(0,t.jsx)(u.ZB,{children:"Quick Actions"})}),(0,t.jsxs)(u.Wu,{className:"space-y-2",children:[(0,t.jsx)(l.$,{asChild:!0,className:"w-full",children:(0,t.jsxs)(d(),{href:"/dashboard/services/".concat(r.id,"/edit"),children:[(0,t.jsx)(g.A,{className:"h-4 w-4 mr-2"}),"Edit Service"]})}),(0,t.jsx)(l.$,{variant:"outline",className:"w-full",asChild:!0,children:(0,t.jsx)(d(),{href:"/dashboard/services",children:"View All Services"})})]})]})]})]})]})}function k(e){let{children:s,className:r}=e;return(0,t.jsx)("div",{className:r,children:s})}},5695:(e,s,r)=>{"use strict";var t=r(8999);r.o(t,"redirect")&&r.d(s,{redirect:function(){return t.redirect}}),r.o(t,"useParams")&&r.d(s,{useParams:function(){return t.useParams}}),r.o(t,"usePathname")&&r.d(s,{usePathname:function(){return t.usePathname}}),r.o(t,"useRouter")&&r.d(s,{useRouter:function(){return t.useRouter}})},5868:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]])},6126:(e,s,r)=>{"use strict";r.d(s,{E:()=>c});var t=r(5155);r(2115);var a=r(9708),i=r(2085),n=r(9434);let d=(0,i.F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function c(e){let{className:s,variant:r,asChild:i=!1,...c}=e,l=i?a.DX:"span";return(0,t.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(d({variant:r}),s),...c})}},6695:(e,s,r)=>{"use strict";r.d(s,{BT:()=>c,Wu:()=>l,ZB:()=>d,Zp:()=>i,aR:()=>n});var t=r(5155);r(2115);var a=r(9434);function i(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card",className:(0,a.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",s),...r})}function n(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-header",className:(0,a.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",s),...r})}function d(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-title",className:(0,a.cn)("leading-none font-semibold",s),...r})}function c(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-description",className:(0,a.cn)("text-muted-foreground text-sm",s),...r})}function l(e){let{className:s,...r}=e;return(0,t.jsx)("div",{"data-slot":"card-content",className:(0,a.cn)("px-6",s),...r})}},7489:(e,s,r)=>{"use strict";r.d(s,{b:()=>l});var t=r(2115),a=r(3655),i=r(5155),n="horizontal",d=["horizontal","vertical"],c=t.forwardRef((e,s)=>{var r;let{decorative:t,orientation:c=n,...l}=e,o=(r=c,d.includes(r))?c:n;return(0,i.jsx)(a.sG.div,{"data-orientation":o,...t?{role:"none"}:{"aria-orientation":"vertical"===o?o:void 0,role:"separator"},...l,ref:s})});c.displayName="Separator";var l=c},8564:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("star",[["path",{d:"M11.525 2.295a.53.53 0 0 1 .95 0l2.31 4.679a2.123 2.123 0 0 0 1.595 1.16l5.166.756a.53.53 0 0 1 .294.904l-3.736 3.638a2.123 2.123 0 0 0-.611 1.878l.882 5.14a.53.53 0 0 1-.771.56l-4.618-2.428a2.122 2.122 0 0 0-1.973 0L6.396 21.01a.53.53 0 0 1-.77-.56l.881-5.139a2.122 2.122 0 0 0-.611-1.879L2.16 9.795a.53.53 0 0 1 .294-.906l5.165-.755a2.122 2.122 0 0 0 1.597-1.16z",key:"r04s7s"}]])},9074:(e,s,r)=>{"use strict";r.d(s,{A:()=>t});let t=(0,r(9946).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},9434:(e,s,r)=>{"use strict";r.d(s,{EJ:()=>d,Yq:()=>n,cn:()=>i});var t=r(2596),a=r(9688);function i(){for(var e=arguments.length,s=Array(e),r=0;r<e;r++)s[r]=arguments[r];return(0,a.QP)((0,t.$)(s))}function n(e){return new Date(e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function d(e,s){return e.length<=s?e:e.substring(0,s).trim()+"..."}}},e=>{e.O(0,[5389,6671,651,6874,8441,5964,7358],()=>e(e.s=2962)),_N_E=e.O()}]);