{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/playfair_display_c4ae686b.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"playfair_display_c4ae686b-module__xLrk2G__className\",\n  \"variable\": \"playfair_display_c4ae686b-module__xLrk2G__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 12, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/playfair_display_c4ae686b.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Playfair_Display%22,%22arguments%22:[{%22variable%22:%22--font-display%22,%22subsets%22:[%22latin%22],%22display%22:%22swap%22}],%22variableName%22:%22playfairDisplay%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Playfair Display', 'Playfair Display Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,gKAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,gKAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,gKAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_80f34f63.module.css [app-rsc] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"poppins_80f34f63-module__xyMXYq__className\",\n  \"variable\": \"poppins_80f34f63-module__xyMXYq__variable\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA", "ignoreList": [0]}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[next]/internal/font/google/poppins_80f34f63.js"], "sourcesContent": ["import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Poppins%22,%22arguments%22:[{%22variable%22:%22--font-sans%22,%22subsets%22:[%22latin%22],%22weight%22:[%22300%22,%22400%22,%22500%22,%22600%22,%22700%22],%22display%22:%22swap%22}],%22variableName%22:%22poppins%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Poppins', 'Poppins Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM,WAAW;IACb,WAAW,uJAAA,CAAA,UAAS,CAAC,SAAS;IAC9B,OAAO;QACH,YAAY;QACZ,WAAW;IAEf;AACJ;AAEA,IAAI,uJAAA,CAAA,UAAS,CAAC,QAAQ,IAAI,MAAM;IAC5B,SAAS,QAAQ,GAAG,uJAAA,CAAA,UAAS,CAAC,QAAQ;AAC1C;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 59, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 71, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/layout/header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 83, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/layout/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/footer.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/footer.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 103, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/layout/footer.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/layout/footer.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/layout/footer.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 115, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/lib/utils.ts"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: string): string {\n  return price.replace(/NPR\\s*/g, 'NPR ')\n}\n\nexport function formatDate(dateString: string): string {\n  const date = new Date(dateString)\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  })\n}\n\nexport function generateWhatsAppLink(phone: string, message: string): string {\n  const cleanPhone = phone.replace(/[^\\d+]/g, '')\n  const encodedMessage = encodeURIComponent(message)\n  return `https://wa.me/${cleanPhone}?text=${encodedMessage}`\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w\\s-]/g, '')\n    .replace(/[\\s_-]+/g, '-')\n    .replace(/^-+|-+$/g, '')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength).replace(/\\s+\\S*$/, '') + '...'\n}\n\nexport function getImageUrl(imagePath: string): string {\n  // Handle placeholder images for development\n  if (imagePath.startsWith('/images/')) {\n    return `https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=800&h=600&fit=crop&crop=face`\n  }\n  return imagePath\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function validatePhone(phone: string): boolean {\n  const phoneRegex = /^(\\+977)?[0-9]{10}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function scrollToElement(elementId: string, offset: number = 80): void {\n  const element = document.getElementById(elementId)\n  if (element) {\n    const elementPosition = element.getBoundingClientRect().top\n    const offsetPosition = elementPosition + window.pageYOffset - offset\n    \n    window.scrollTo({\n      top: offsetPosition,\n      behavior: 'smooth'\n    })\n  }\n}\n\nexport function isInViewport(element: Element): boolean {\n  const rect = element.getBoundingClientRect()\n  return (\n    rect.top >= 0 &&\n    rect.left >= 0 &&\n    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&\n    rect.right <= (window.innerWidth || document.documentElement.clientWidth)\n  )\n}\n\nexport function getReadingTime(content: string): string {\n  const wordsPerMinute = 200\n  const words = content.trim().split(/\\s+/).length\n  const minutes = Math.ceil(words / wordsPerMinute)\n  return `${minutes} min read`\n}\n\nexport function generateSEOSchema(type: 'Organization' | 'LocalBusiness' | 'Article', data: any) {\n  const baseSchema = {\n    '@context': 'https://schema.org',\n    '@type': type\n  }\n\n  switch (type) {\n    case 'LocalBusiness':\n      return {\n        ...baseSchema,\n        name: data.name,\n        description: data.description,\n        url: data.url,\n        telephone: data.phone,\n        address: {\n          '@type': 'PostalAddress',\n          streetAddress: data.address.street,\n          addressLocality: data.address.city,\n          addressRegion: data.address.state,\n          addressCountry: data.address.country,\n          postalCode: data.address.zipCode\n        },\n        geo: data.geo,\n        openingHours: data.openingHours,\n        priceRange: data.priceRange,\n        serviceArea: data.serviceArea\n      }\n    \n    case 'Article':\n      return {\n        ...baseSchema,\n        headline: data.title,\n        description: data.description,\n        author: {\n          '@type': 'Person',\n          name: data.author\n        },\n        datePublished: data.publishedAt,\n        dateModified: data.updatedAt,\n        image: data.image,\n        url: data.url\n      }\n    \n    default:\n      return baseSchema\n  }\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;;;;;;;;;;;;;AACrD;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,MAAM,OAAO,CAAC,WAAW;AAClC;AAEO,SAAS,WAAW,UAAkB;IAC3C,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAEO,SAAS,qBAAqB,KAAa,EAAE,OAAe;IACjE,MAAM,aAAa,MAAM,OAAO,CAAC,WAAW;IAC5C,MAAM,iBAAiB,mBAAmB;IAC1C,OAAO,CAAC,cAAc,EAAE,WAAW,MAAM,EAAE,gBAAgB;AAC7D;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,aAAa,IACrB,OAAO,CAAC,YAAY,KACpB,OAAO,CAAC,YAAY;AACzB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,WAAW,OAAO,CAAC,WAAW,MAAM;AAC3D;AAEO,SAAS,YAAY,SAAiB;IAC3C,4CAA4C;IAC5C,IAAI,UAAU,UAAU,CAAC,aAAa;QACpC,OAAO,CAAC,2FAA2F,CAAC;IACtG;IACA,OAAO;AACT;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,gBAAgB,SAAiB,EAAE,SAAiB,EAAE;IACpE,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;QAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;QAE9D,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;AACF;AAEO,SAAS,aAAa,OAAgB;IAC3C,MAAM,OAAO,QAAQ,qBAAqB;IAC1C,OACE,KAAK,GAAG,IAAI,KACZ,KAAK,IAAI,IAAI,KACb,KAAK,MAAM,IAAI,CAAC,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,YAAY,KAC3E,KAAK,KAAK,IAAI,CAAC,OAAO,UAAU,IAAI,SAAS,eAAe,CAAC,WAAW;AAE5E;AAEO,SAAS,eAAe,OAAe;IAC5C,MAAM,iBAAiB;IACvB,MAAM,QAAQ,QAAQ,IAAI,GAAG,KAAK,CAAC,OAAO,MAAM;IAChD,MAAM,UAAU,KAAK,IAAI,CAAC,QAAQ;IAClC,OAAO,GAAG,QAAQ,SAAS,CAAC;AAC9B;AAEO,SAAS,kBAAkB,IAAkD,EAAE,IAAS;IAC7F,MAAM,aAAa;QACjB,YAAY;QACZ,SAAS;IACX;IAEA,OAAQ;QACN,KAAK;YACH,OAAO;gBACL,GAAG,UAAU;gBACb,MAAM,KAAK,IAAI;gBACf,aAAa,KAAK,WAAW;gBAC7B,KAAK,KAAK,GAAG;gBACb,WAAW,KAAK,KAAK;gBACrB,SAAS;oBACP,SAAS;oBACT,eAAe,KAAK,OAAO,CAAC,MAAM;oBAClC,iBAAiB,KAAK,OAAO,CAAC,IAAI;oBAClC,eAAe,KAAK,OAAO,CAAC,KAAK;oBACjC,gBAAgB,KAAK,OAAO,CAAC,OAAO;oBACpC,YAAY,KAAK,OAAO,CAAC,OAAO;gBAClC;gBACA,KAAK,KAAK,GAAG;gBACb,cAAc,KAAK,YAAY;gBAC/B,YAAY,KAAK,UAAU;gBAC3B,aAAa,KAAK,WAAW;YAC/B;QAEF,KAAK;YACH,OAAO;gBACL,GAAG,UAAU;gBACb,UAAU,KAAK,KAAK;gBACpB,aAAa,KAAK,WAAW;gBAC7B,QAAQ;oBACN,SAAS;oBACT,MAAM,KAAK,MAAM;gBACnB;gBACA,eAAe,KAAK,WAAW;gBAC/B,cAAc,KAAK,SAAS;gBAC5B,OAAO,KAAK,KAAK;gBACjB,KAAK,KAAK,GAAG;YACf;QAEF;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 278, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/lib/data.ts"], "sourcesContent": ["import servicesData from '@/data/services.json'\nimport packagesData from '@/data/packages.json'\nimport testimonialsData from '@/data/testimonials.json'\nimport galleryData from '@/data/gallery.json'\nimport blogData from '@/data/blog.json'\nimport siteConfigData from '@/data/site-config.json'\nimport { formatPrice, formatDate, generateWhatsAppLink } from '@/lib/utils'\n\nexport interface Service {\n  id: string\n  title: string\n  description: string\n  features: string[]\n  duration: string\n  price: string\n  image: string\n  category: string\n  popular: boolean\n}\n\nexport interface Package {\n  id: string\n  title: string\n  description: string\n  services: string[]\n  duration: string\n  originalPrice: string\n  discountedPrice: string\n  savings: string\n  image: string\n  popular: boolean\n  badge: string | null\n}\n\nexport interface Testimonial {\n  id: string\n  name: string\n  location: string\n  service: string\n  rating: number\n  text: string\n  image: string\n  date: string\n  featured: boolean\n}\n\nexport interface GalleryItem {\n  id: string\n  title: string\n  description: string\n  image: string\n  category: string\n  featured: boolean\n  tags: string[]\n}\n\nexport interface BlogPost {\n  id: string\n  title: string\n  slug: string\n  excerpt: string\n  content: string\n  author: string\n  publishedAt: string\n  updatedAt: string\n  featured: boolean\n  image: string\n  tags: string[]\n  category: string\n  readTime: string\n  seo: {\n    metaTitle: string\n    metaDescription: string\n    keywords: string[]\n  }\n}\n\nexport interface SiteConfig {\n  site: {\n    name: string\n    tagline: string\n    description: string\n    url: string\n    logo: string\n    favicon: string\n  }\n  contact: {\n    phone: string\n    whatsapp: string\n    email: string\n    address: {\n      street: string\n      city: string\n      state: string\n      country: string\n      zipCode: string\n    }\n    workingHours: Record<string, string>\n  }\n  social: {\n    facebook: string\n    instagram: string\n    tiktok: string\n    youtube: string\n  }\n  serviceAreas: Array<{\n    name: string\n    primary: boolean\n    travelFee: number\n  }>\n  whatsappMessage: string\n  seo: {\n    defaultTitle: string\n    defaultDescription: string\n    keywords: string[]\n    author: string\n    twitterHandle: string\n  }\n  analytics: {\n    googleAnalyticsId: string\n  }\n}\n\n// Services\nexport function getServices(): Service[] {\n  return servicesData.services\n}\n\nexport function getService(id: string): Service | undefined {\n  return servicesData.services.find(service => service.id === id)\n}\n\nexport function getPopularServices(): Service[] {\n  return servicesData.services.filter(service => service.popular)\n}\n\nexport function getServicesByCategory(category: string): Service[] {\n  return servicesData.services.filter(service => service.category === category)\n}\n\n// Packages\nexport function getPackages(): Package[] {\n  return packagesData.packages\n}\n\nexport function getPackage(id: string): Package | undefined {\n  return packagesData.packages.find(pkg => pkg.id === id)\n}\n\nexport function getPopularPackages(): Package[] {\n  return packagesData.packages.filter(pkg => pkg.popular)\n}\n\n// Testimonials\nexport function getTestimonials(): Testimonial[] {\n  return testimonialsData.testimonials\n}\n\nexport function getFeaturedTestimonials(): Testimonial[] {\n  return testimonialsData.testimonials.filter(testimonial => testimonial.featured)\n}\n\nexport function getTestimonialsByService(service: string): Testimonial[] {\n  return testimonialsData.testimonials.filter(testimonial => \n    testimonial.service.toLowerCase().includes(service.toLowerCase())\n  )\n}\n\n// Gallery\nexport function getGalleryItems(): GalleryItem[] {\n  return galleryData.gallery\n}\n\nexport function getFeaturedGalleryItems(): GalleryItem[] {\n  return galleryData.gallery.filter(item => item.featured)\n}\n\nexport function getGalleryItemsByCategory(category: string): GalleryItem[] {\n  if (category === 'all') return galleryData.gallery\n  return galleryData.gallery.filter(item => item.category === category)\n}\n\nexport function getGalleryCategories() {\n  return galleryData.categories\n}\n\n// Blog\nexport function getBlogPosts(): BlogPost[] {\n  return blogData.posts\n}\n\nexport function getBlogPost(slug: string): BlogPost | undefined {\n  return blogData.posts.find(post => post.slug === slug)\n}\n\nexport function getFeaturedBlogPosts(): BlogPost[] {\n  return blogData.posts.filter(post => post.featured)\n}\n\nexport function getBlogPostsByCategory(category: string): BlogPost[] {\n  return blogData.posts.filter(post => \n    post.category.toLowerCase().replace(/\\s+/g, '-') === category.toLowerCase()\n  )\n}\n\nexport function getBlogCategories() {\n  return blogData.categories\n}\n\nexport function getRelatedBlogPosts(currentPost: BlogPost, limit: number = 3): BlogPost[] {\n  return blogData.posts\n    .filter(post => \n      post.id !== currentPost.id && \n      (post.category === currentPost.category || \n       post.tags.some(tag => currentPost.tags.includes(tag)))\n    )\n    .slice(0, limit)\n}\n\n// Site Configuration\nexport function getSiteConfig(): SiteConfig {\n  return {\n    ...siteConfigData,\n    analytics: {\n      googleAnalyticsId: process.env.NEXT_PUBLIC_GA_ID || siteConfigData.analytics.googleAnalyticsId\n    }\n  } as SiteConfig\n}\n\nexport function getContactInfo() {\n  return siteConfigData.contact\n}\n\nexport function getSocialLinks() {\n  return siteConfigData.social\n}\n\nexport function getServiceAreas() {\n  return siteConfigData.serviceAreas\n}\n\nexport function getWhatsAppMessage() {\n  return siteConfigData.whatsappMessage\n}\n\nexport function getSEODefaults() {\n  return siteConfigData.seo\n}\n\n// Re-export utility functions\nexport { formatPrice, formatDate, generateWhatsAppLink }\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAsHO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ;AAC9B;AAEO,SAAS,WAAW,EAAU;IACnC,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,UAAW,QAAQ,EAAE,KAAK;AAC9D;AAEO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,OAAO;AAChE;AAEO,SAAS,sBAAsB,QAAgB;IACpD,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACtE;AAGO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ;AAC9B;AAEO,SAAS,WAAW,EAAU;IACnC,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;AACtD;AAEO,SAAS;IACd,OAAO,+FAAA,CAAA,UAAY,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAA,MAAO,IAAI,OAAO;AACxD;AAGO,SAAS;IACd,OAAO,mGAAA,CAAA,UAAgB,CAAC,YAAY;AACtC;AAEO,SAAS;IACd,OAAO,mGAAA,CAAA,UAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,cAAe,YAAY,QAAQ;AACjF;AAEO,SAAS,yBAAyB,OAAe;IACtD,OAAO,mGAAA,CAAA,UAAgB,CAAC,YAAY,CAAC,MAAM,CAAC,CAAA,cAC1C,YAAY,OAAO,CAAC,WAAW,GAAG,QAAQ,CAAC,QAAQ,WAAW;AAElE;AAGO,SAAS;IACd,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO;AAC5B;AAEO,SAAS;IACd,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AACzD;AAEO,SAAS,0BAA0B,QAAgB;IACxD,IAAI,aAAa,OAAO,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO;IAClD,OAAO,8FAAA,CAAA,UAAW,CAAC,OAAO,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,KAAK;AAC9D;AAEO,SAAS;IACd,OAAO,8FAAA,CAAA,UAAW,CAAC,UAAU;AAC/B;AAGO,SAAS;IACd,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK;AACvB;AAEO,SAAS,YAAY,IAAY;IACtC,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;AACnD;AAEO,SAAS;IACd,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ;AACpD;AAEO,SAAS,uBAAuB,QAAgB;IACrD,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAA,OAC3B,KAAK,QAAQ,CAAC,WAAW,GAAG,OAAO,CAAC,QAAQ,SAAS,SAAS,WAAW;AAE7E;AAEO,SAAS;IACd,OAAO,2FAAA,CAAA,UAAQ,CAAC,UAAU;AAC5B;AAEO,SAAS,oBAAoB,WAAqB,EAAE,QAAgB,CAAC;IAC1E,OAAO,2FAAA,CAAA,UAAQ,CAAC,KAAK,CAClB,MAAM,CAAC,CAAA,OACN,KAAK,EAAE,KAAK,YAAY,EAAE,IAC1B,CAAC,KAAK,QAAQ,KAAK,YAAY,QAAQ,IACtC,KAAK,IAAI,CAAC,IAAI,CAAC,CAAA,MAAO,YAAY,IAAI,CAAC,QAAQ,CAAC,KAAK,GAEvD,KAAK,CAAC,GAAG;AACd;AAGO,SAAS;IACd,OAAO;QACL,GAAG,qGAAA,CAAA,UAAc;QACjB,WAAW;YACT,mBAAmB,oDAAiC,qGAAA,CAAA,UAAc,CAAC,SAAS,CAAC,iBAAiB;QAChG;IACF;AACF;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,OAAO;AAC/B;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,MAAM;AAC9B;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,YAAY;AACpC;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,eAAe;AACvC;AAEO,SAAS;IACd,OAAO,qGAAA,CAAA,UAAc,CAAC,GAAG;AAC3B", "debugId": null}}, {"offset": {"line": 422, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/seo/json-ld.tsx"], "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { getSiteConfig } from '@/lib/data'\n\ninterface JsonLdProps {\n  type: 'Organization' | 'LocalBusiness' | 'Article' | 'WebSite'\n  data?: Record<string, unknown>\n}\n\nexport default function JsonLd({ type, data }: JsonLdProps) {\n  const siteConfig = getSiteConfig()\n\n  const generateSchema = () => {\n    const baseSchema = {\n      '@context': 'https://schema.org',\n      '@type': type\n    }\n\n    switch (type) {\n      case 'Organization':\n        return {\n          ...baseSchema,\n          name: siteConfig.site.name,\n          description: siteConfig.site.description,\n          url: siteConfig.site.url,\n          logo: `${siteConfig.site.url}/images/logo.png`,\n          contactPoint: {\n            '@type': 'ContactPoint',\n            telephone: siteConfig.contact.phone,\n            contactType: 'customer service',\n            availableLanguage: ['English', 'Nepali']\n          },\n          address: {\n            '@type': 'PostalAddress',\n            streetAddress: siteConfig.contact.address.street,\n            addressLocality: siteConfig.contact.address.city,\n            addressRegion: siteConfig.contact.address.state,\n            addressCountry: siteConfig.contact.address.country,\n            postalCode: siteConfig.contact.address.zipCode\n          },\n          sameAs: [\n            siteConfig.social.facebook,\n            siteConfig.social.instagram,\n            siteConfig.social.tiktok\n          ]\n        }\n\n      case 'LocalBusiness':\n        return {\n          ...baseSchema,\n          '@type': 'BeautySalon',\n          name: siteConfig.site.name,\n          description: siteConfig.site.description,\n          url: siteConfig.site.url,\n          telephone: siteConfig.contact.phone,\n          email: siteConfig.contact.email,\n          address: {\n            '@type': 'PostalAddress',\n            streetAddress: siteConfig.contact.address.street,\n            addressLocality: siteConfig.contact.address.city,\n            addressRegion: siteConfig.contact.address.state,\n            addressCountry: siteConfig.contact.address.country,\n            postalCode: siteConfig.contact.address.zipCode\n          },\n          geo: {\n            '@type': 'GeoCoordinates',\n            latitude: '26.4525', // Biratnagar coordinates\n            longitude: '87.2718'\n          },\n          openingHours: [\n            'Mo-Sa 09:00-18:00',\n            'Su 10:00-16:00'\n          ],\n          priceRange: 'NPR 4,000 - NPR 25,000',\n          serviceArea: siteConfig.serviceAreas.map(area => ({\n            '@type': 'City',\n            name: area.name\n          })),\n          hasOfferCatalog: {\n            '@type': 'OfferCatalog',\n            name: 'Makeup Services',\n            itemListElement: [\n              {\n                '@type': 'Offer',\n                itemOffered: {\n                  '@type': 'Service',\n                  name: 'Bridal Makeup',\n                  description: 'Complete bridal transformation with traditional and modern looks'\n                }\n              },\n              {\n                '@type': 'Offer',\n                itemOffered: {\n                  '@type': 'Service',\n                  name: 'Party Makeup',\n                  description: 'Glamorous looks for special occasions and celebrations'\n                }\n              },\n              {\n                '@type': 'Offer',\n                itemOffered: {\n                  '@type': 'Service',\n                  name: 'Traditional Makeup',\n                  description: 'Authentic traditional looks for cultural ceremonies'\n                }\n              }\n            ]\n          },\n          aggregateRating: {\n            '@type': 'AggregateRating',\n            ratingValue: '5.0',\n            reviewCount: '50',\n            bestRating: '5',\n            worstRating: '1'\n          },\n          sameAs: [\n            siteConfig.social.facebook,\n            siteConfig.social.instagram,\n            siteConfig.social.tiktok\n          ]\n        }\n\n      case 'WebSite':\n        return {\n          ...baseSchema,\n          name: siteConfig.site.name,\n          description: siteConfig.site.description,\n          url: siteConfig.site.url,\n          potentialAction: {\n            '@type': 'SearchAction',\n            target: {\n              '@type': 'EntryPoint',\n              urlTemplate: `${siteConfig.site.url}/search?q={search_term_string}`\n            },\n            'query-input': 'required name=search_term_string'\n          },\n          publisher: {\n            '@type': 'Organization',\n            name: siteConfig.site.name,\n            logo: {\n              '@type': 'ImageObject',\n              url: `${siteConfig.site.url}/images/logo.png`\n            }\n          }\n        }\n\n      case 'Article':\n        if (!data) return baseSchema\n        return {\n          ...baseSchema,\n          headline: (data as any).title,\n          description: (data as any).excerpt,\n          author: {\n            '@type': 'Person',\n            name: (data as any).author\n          },\n          publisher: {\n            '@type': 'Organization',\n            name: siteConfig.site.name,\n            logo: {\n              '@type': 'ImageObject',\n              url: `${siteConfig.site.url}/images/logo.png`\n            }\n          },\n          datePublished: (data as any).publishedAt,\n          dateModified: (data as any).updatedAt,\n          image: (data as any).image ? `${siteConfig.site.url}${(data as any).image}` : `${siteConfig.site.url}/images/blog-default.jpg`,\n          url: `${siteConfig.site.url}/blog/${(data as any).slug}`,\n          mainEntityOfPage: {\n            '@type': 'WebPage',\n            '@id': `${siteConfig.site.url}/blog/${(data as any).slug}`\n          },\n          keywords: (data as any).tags?.join?.(', ') || '',\n          articleSection: (data as any).category,\n          wordCount: (data as any).wordCount || 1000\n        }\n\n      default:\n        return baseSchema\n    }\n  }\n\n  const schema = generateSchema()\n\n  return (\n    <script\n      type=\"application/ld+json\"\n      dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}\n    />\n  )\n}\n"], "names": [], "mappings": "AAAA,qDAAqD;;;;AACrD;AAAA;;;AAOe,SAAS,OAAO,EAAE,IAAI,EAAE,IAAI,EAAe;IACxD,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;IAE/B,MAAM,iBAAiB;QACrB,MAAM,aAAa;YACjB,YAAY;YACZ,SAAS;QACX;QAEA,OAAQ;YACN,KAAK;gBACH,OAAO;oBACL,GAAG,UAAU;oBACb,MAAM,WAAW,IAAI,CAAC,IAAI;oBAC1B,aAAa,WAAW,IAAI,CAAC,WAAW;oBACxC,KAAK,WAAW,IAAI,CAAC,GAAG;oBACxB,MAAM,GAAG,WAAW,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;oBAC9C,cAAc;wBACZ,SAAS;wBACT,WAAW,WAAW,OAAO,CAAC,KAAK;wBACnC,aAAa;wBACb,mBAAmB;4BAAC;4BAAW;yBAAS;oBAC1C;oBACA,SAAS;wBACP,SAAS;wBACT,eAAe,WAAW,OAAO,CAAC,OAAO,CAAC,MAAM;wBAChD,iBAAiB,WAAW,OAAO,CAAC,OAAO,CAAC,IAAI;wBAChD,eAAe,WAAW,OAAO,CAAC,OAAO,CAAC,KAAK;wBAC/C,gBAAgB,WAAW,OAAO,CAAC,OAAO,CAAC,OAAO;wBAClD,YAAY,WAAW,OAAO,CAAC,OAAO,CAAC,OAAO;oBAChD;oBACA,QAAQ;wBACN,WAAW,MAAM,CAAC,QAAQ;wBAC1B,WAAW,MAAM,CAAC,SAAS;wBAC3B,WAAW,MAAM,CAAC,MAAM;qBACzB;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,UAAU;oBACb,SAAS;oBACT,MAAM,WAAW,IAAI,CAAC,IAAI;oBAC1B,aAAa,WAAW,IAAI,CAAC,WAAW;oBACxC,KAAK,WAAW,IAAI,CAAC,GAAG;oBACxB,WAAW,WAAW,OAAO,CAAC,KAAK;oBACnC,OAAO,WAAW,OAAO,CAAC,KAAK;oBAC/B,SAAS;wBACP,SAAS;wBACT,eAAe,WAAW,OAAO,CAAC,OAAO,CAAC,MAAM;wBAChD,iBAAiB,WAAW,OAAO,CAAC,OAAO,CAAC,IAAI;wBAChD,eAAe,WAAW,OAAO,CAAC,OAAO,CAAC,KAAK;wBAC/C,gBAAgB,WAAW,OAAO,CAAC,OAAO,CAAC,OAAO;wBAClD,YAAY,WAAW,OAAO,CAAC,OAAO,CAAC,OAAO;oBAChD;oBACA,KAAK;wBACH,SAAS;wBACT,UAAU;wBACV,WAAW;oBACb;oBACA,cAAc;wBACZ;wBACA;qBACD;oBACD,YAAY;oBACZ,aAAa,WAAW,YAAY,CAAC,GAAG,CAAC,CAAA,OAAQ,CAAC;4BAChD,SAAS;4BACT,MAAM,KAAK,IAAI;wBACjB,CAAC;oBACD,iBAAiB;wBACf,SAAS;wBACT,MAAM;wBACN,iBAAiB;4BACf;gCACE,SAAS;gCACT,aAAa;oCACX,SAAS;oCACT,MAAM;oCACN,aAAa;gCACf;4BACF;4BACA;gCACE,SAAS;gCACT,aAAa;oCACX,SAAS;oCACT,MAAM;oCACN,aAAa;gCACf;4BACF;4BACA;gCACE,SAAS;gCACT,aAAa;oCACX,SAAS;oCACT,MAAM;oCACN,aAAa;gCACf;4BACF;yBACD;oBACH;oBACA,iBAAiB;wBACf,SAAS;wBACT,aAAa;wBACb,aAAa;wBACb,YAAY;wBACZ,aAAa;oBACf;oBACA,QAAQ;wBACN,WAAW,MAAM,CAAC,QAAQ;wBAC1B,WAAW,MAAM,CAAC,SAAS;wBAC3B,WAAW,MAAM,CAAC,MAAM;qBACzB;gBACH;YAEF,KAAK;gBACH,OAAO;oBACL,GAAG,UAAU;oBACb,MAAM,WAAW,IAAI,CAAC,IAAI;oBAC1B,aAAa,WAAW,IAAI,CAAC,WAAW;oBACxC,KAAK,WAAW,IAAI,CAAC,GAAG;oBACxB,iBAAiB;wBACf,SAAS;wBACT,QAAQ;4BACN,SAAS;4BACT,aAAa,GAAG,WAAW,IAAI,CAAC,GAAG,CAAC,8BAA8B,CAAC;wBACrE;wBACA,eAAe;oBACjB;oBACA,WAAW;wBACT,SAAS;wBACT,MAAM,WAAW,IAAI,CAAC,IAAI;wBAC1B,MAAM;4BACJ,SAAS;4BACT,KAAK,GAAG,WAAW,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;wBAC/C;oBACF;gBACF;YAEF,KAAK;gBACH,IAAI,CAAC,MAAM,OAAO;gBAClB,OAAO;oBACL,GAAG,UAAU;oBACb,UAAU,AAAC,KAAa,KAAK;oBAC7B,aAAa,AAAC,KAAa,OAAO;oBAClC,QAAQ;wBACN,SAAS;wBACT,MAAM,AAAC,KAAa,MAAM;oBAC5B;oBACA,WAAW;wBACT,SAAS;wBACT,MAAM,WAAW,IAAI,CAAC,IAAI;wBAC1B,MAAM;4BACJ,SAAS;4BACT,KAAK,GAAG,WAAW,IAAI,CAAC,GAAG,CAAC,gBAAgB,CAAC;wBAC/C;oBACF;oBACA,eAAe,AAAC,KAAa,WAAW;oBACxC,cAAc,AAAC,KAAa,SAAS;oBACrC,OAAO,AAAC,KAAa,KAAK,GAAG,GAAG,WAAW,IAAI,CAAC,GAAG,GAAG,AAAC,KAAa,KAAK,EAAE,GAAG,GAAG,WAAW,IAAI,CAAC,GAAG,CAAC,wBAAwB,CAAC;oBAC9H,KAAK,GAAG,WAAW,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,AAAC,KAAa,IAAI,EAAE;oBACxD,kBAAkB;wBAChB,SAAS;wBACT,OAAO,GAAG,WAAW,IAAI,CAAC,GAAG,CAAC,MAAM,EAAE,AAAC,KAAa,IAAI,EAAE;oBAC5D;oBACA,UAAU,AAAC,KAAa,IAAI,EAAE,OAAO,SAAS;oBAC9C,gBAAgB,AAAC,KAAa,QAAQ;oBACtC,WAAW,AAAC,KAAa,SAAS,IAAI;gBACxC;YAEF;gBACE,OAAO;QACX;IACF;IAEA,MAAM,SAAS;IAEf,qBACE,8OAAC;QACC,MAAK;QACL,yBAAyB;YAAE,QAAQ,KAAK,SAAS,CAAC;QAAQ;;;;;;AAGhE", "debugId": null}}, {"offset": {"line": 615, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/seo/analytics.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/seo/analytics.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/seo/analytics.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/seo/analytics.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/seo/analytics.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/seo/analytics.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAgR,GAC7S,8CACA", "debugId": null}}, {"offset": {"line": 639, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 647, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/scroll-to-top.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/scroll-to-top.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/scroll-to-top.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAuS,GACpU,qEACA", "debugId": null}}, {"offset": {"line": 659, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/scroll-to-top.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/ui/scroll-to-top.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/scroll-to-top.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAmR,GAChT,iDACA", "debugId": null}}, {"offset": {"line": 671, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 679, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/skip-links.tsx"], "sourcesContent": ["export default function SkipLinks() {\n  return (\n    <div className=\"sr-only focus-within:not-sr-only\">\n      <a\n        href=\"#main-content\"\n        className=\"fixed top-4 left-4 z-50 bg-text-primary text-white px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-rose-gold\"\n      >\n        Skip to main content\n      </a>\n      <a\n        href=\"#navigation\"\n        className=\"fixed top-4 left-32 z-50 bg-text-primary text-white px-4 py-2 rounded-md focus:outline-none focus:ring-2 focus:ring-rose-gold\"\n      >\n        Skip to navigation\n      </a>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAAe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBACC,MAAK;gBACL,WAAU;0BACX;;;;;;0BAGD,8OAAC;gBACC,MAAK;gBACL,WAAU;0BACX;;;;;;;;;;;;AAKP", "debugId": null}}, {"offset": {"line": 717, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/providers/query-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const QueryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/query-provider.tsx <module evaluation>\",\n    \"QueryProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,kEACA", "debugId": null}}, {"offset": {"line": 729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/providers/query-provider.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const QueryProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call QueryProvider() from the server but QueryProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/providers/query-provider.tsx\",\n    \"QueryProvider\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,8CACA", "debugId": null}}, {"offset": {"line": 741, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 749, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from \"next\";\nimport { Playfair_Display, Poppins } from \"next/font/google\";\nimport \"./globals.css\";\nimport Header from \"@/components/layout/header\";\nimport Footer from \"@/components/layout/footer\";\nimport JsonLd from \"@/components/seo/json-ld\";\nimport Analytics from \"@/components/seo/analytics\";\nimport ScrollToTop from \"@/components/ui/scroll-to-top\";\nimport SkipLinks from \"@/components/ui/skip-links\";\nimport { getSiteConfig } from \"@/lib/data\";\nimport { QueryProvider } from \"@/providers/query-provider\";\n\nconst playfairDisplay = Playfair_Display({\n  variable: \"--font-display\",\n  subsets: [\"latin\"],\n  display: \"swap\",\n});\n\nconst poppins = Poppins({\n  variable: \"--font-sans\",\n  subsets: [\"latin\"],\n  weight: [\"300\", \"400\", \"500\", \"600\", \"700\"],\n  display: \"swap\",\n});\n\nconst siteConfig = getSiteConfig();\n\nexport const metadata: Metadata = {\n  title: siteConfig.seo.defaultTitle,\n  description: siteConfig.seo.defaultDescription,\n  keywords: siteConfig.seo.keywords,\n  authors: [{ name: siteConfig.seo.author }],\n  creator: siteConfig.seo.author,\n  manifest: '/manifest.json',\n  icons: {\n    icon: '/favicon.ico',\n    apple: '/apple-touch-icon.png',\n  },\n  openGraph: {\n    type: \"website\",\n    locale: \"en_US\",\n    url: siteConfig.site.url,\n    title: siteConfig.seo.defaultTitle,\n    description: siteConfig.seo.defaultDescription,\n    siteName: siteConfig.site.name,\n    images: [\n      {\n        url: `${siteConfig.site.url}/og-image.jpg`,\n        width: 1200,\n        height: 630,\n        alt: siteConfig.site.name,\n      },\n    ],\n  },\n  twitter: {\n    card: \"summary_large_image\",\n    title: siteConfig.seo.defaultTitle,\n    description: siteConfig.seo.defaultDescription,\n    creator: siteConfig.seo.twitterHandle,\n    images: [`${siteConfig.site.url}/og-image.jpg`],\n  },\n  robots: {\n    index: true,\n    follow: true,\n    googleBot: {\n      index: true,\n      follow: true,\n      \"max-video-preview\": -1,\n      \"max-image-preview\": \"large\",\n      \"max-snippet\": -1,\n    },\n  },\n  verification: {\n    google: 'your-google-verification-code',\n    // Add other verification codes as needed\n  },\n  alternates: {\n    canonical: siteConfig.site.url,\n  },\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" className={`${playfairDisplay.variable} ${poppins.variable}`}>\n      <head>\n        <JsonLd type=\"Organization\" />\n        <JsonLd type=\"LocalBusiness\" />\n        <JsonLd type=\"WebSite\" />\n      </head>\n      <body className=\"antialiased\">\n        <QueryProvider>\n          <SkipLinks />\n          <Analytics />\n          <Header />\n          <main id=\"main-content\" className=\"pt-16\">\n            {children}\n          </main>\n          <Footer />\n          <ScrollToTop />\n        </QueryProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;;;;;AAeA,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;AAExB,MAAM,WAAqB;IAChC,OAAO,WAAW,GAAG,CAAC,YAAY;IAClC,aAAa,WAAW,GAAG,CAAC,kBAAkB;IAC9C,UAAU,WAAW,GAAG,CAAC,QAAQ;IACjC,SAAS;QAAC;YAAE,MAAM,WAAW,GAAG,CAAC,MAAM;QAAC;KAAE;IAC1C,SAAS,WAAW,GAAG,CAAC,MAAM;IAC9B,UAAU;IACV,OAAO;QACL,MAAM;QACN,OAAO;IACT;IACA,WAAW;QACT,MAAM;QACN,QAAQ;QACR,KAAK,WAAW,IAAI,CAAC,GAAG;QACxB,OAAO,WAAW,GAAG,CAAC,YAAY;QAClC,aAAa,WAAW,GAAG,CAAC,kBAAkB;QAC9C,UAAU,WAAW,IAAI,CAAC,IAAI;QAC9B,QAAQ;YACN;gBACE,KAAK,GAAG,WAAW,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;gBAC1C,OAAO;gBACP,QAAQ;gBACR,KAAK,WAAW,IAAI,CAAC,IAAI;YAC3B;SACD;IACH;IACA,SAAS;QACP,MAAM;QACN,OAAO,WAAW,GAAG,CAAC,YAAY;QAClC,aAAa,WAAW,GAAG,CAAC,kBAAkB;QAC9C,SAAS,WAAW,GAAG,CAAC,aAAa;QACrC,QAAQ;YAAC,GAAG,WAAW,IAAI,CAAC,GAAG,CAAC,aAAa,CAAC;SAAC;IACjD;IACA,QAAQ;QACN,OAAO;QACP,QAAQ;QACR,WAAW;YACT,OAAO;YACP,QAAQ;YACR,qBAAqB,CAAC;YACtB,qBAAqB;YACrB,eAAe,CAAC;QAClB;IACF;IACA,cAAc;QACZ,QAAQ;IAEV;IACA,YAAY;QACV,WAAW,WAAW,IAAI,CAAC,GAAG;IAChC;AACF;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,8OAAC;QAAK,MAAK;QAAK,WAAW,GAAG,oJAAA,CAAA,UAAe,CAAC,QAAQ,CAAC,CAAC,EAAE,2IAAA,CAAA,UAAO,CAAC,QAAQ,EAAE;;0BAC1E,8OAAC;;kCACC,8OAAC,uIAAA,CAAA,UAAM;wBAAC,MAAK;;;;;;kCACb,8OAAC,uIAAA,CAAA,UAAM;wBAAC,MAAK;;;;;;kCACb,8OAAC,uIAAA,CAAA,UAAM;wBAAC,MAAK;;;;;;;;;;;;0BAEf,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,sIAAA,CAAA,gBAAa;;sCACZ,8OAAC,yIAAA,CAAA,UAAS;;;;;sCACV,8OAAC,sIAAA,CAAA,UAAS;;;;;sCACV,8OAAC,sIAAA,CAAA,UAAM;;;;;sCACP,8OAAC;4BAAK,IAAG;4BAAe,WAAU;sCAC/B;;;;;;sCAEH,8OAAC,sIAAA,CAAA,UAAM;;;;;sCACP,8OAAC,+IAAA,CAAA,UAAW;;;;;;;;;;;;;;;;;;;;;;AAKtB", "debugId": null}}]}