// Google Analytics 4 Event Tracking Utilities

declare global {
  interface Window {
    gtag: (command: string, targetId: string, config?: Record<string, unknown>) => void;
  }
}

// Check if gtag is available
const isGtagAvailable = () => {
  return typeof window !== 'undefined' && typeof window.gtag === 'function';
};

// Generic event tracking
export const trackEvent = (eventName: string, parameters: Record<string, unknown> = {}) => {
  if (!isGtagAvailable()) return;
  
  window.gtag('event', eventName, {
    event_category: parameters.category || 'general',
    event_label: parameters.label,
    value: parameters.value,
    ...parameters
  });
};

// Business-specific event tracking functions

// Contact & Booking Events
export const trackWhatsAppClick = (source: string, message?: string) => {
  trackEvent('whatsapp_click', {
    category: 'contact',
    label: source,
    custom_parameter_1: 'whatsapp',
    message_type: message ? 'custom' : 'default'
  });
};

export const trackPhoneClick = (source: string) => {
  trackEvent('phone_click', {
    category: 'contact',
    label: source,
    custom_parameter_1: 'phone'
  });
};

export const trackEmailClick = (source: string) => {
  trackEvent('email_click', {
    category: 'contact',
    label: source,
    custom_parameter_1: 'email'
  });
};

export const trackContactFormSubmit = (formData: {
  service?: string;
  source?: string;
}) => {
  trackEvent('contact_form_submit', {
    category: 'lead_generation',
    label: formData.service || 'general_inquiry',
    custom_parameter_1: formData.service,
    custom_parameter_2: formData.source || 'contact_page'
  });
};

// Service & Package Events
export const trackServiceView = (serviceId: string, serviceName: string) => {
  trackEvent('service_view', {
    category: 'services',
    label: serviceName,
    custom_parameter_1: serviceId,
    service_name: serviceName
  });
};

export const trackPackageView = (packageId: string, packageName: string, price?: number) => {
  trackEvent('package_view', {
    category: 'packages',
    label: packageName,
    custom_parameter_1: packageId,
    custom_parameter_2: 'package',
    package_name: packageName,
    value: price
  });
};

export const trackServiceInquiry = (serviceId: string, serviceName: string, source: string) => {
  trackEvent('service_inquiry', {
    category: 'lead_generation',
    label: serviceName,
    custom_parameter_1: serviceId,
    custom_parameter_2: source,
    service_name: serviceName
  });
};

// Portfolio & Gallery Events
export const trackPortfolioImageView = (imageId: string, category: string) => {
  trackEvent('portfolio_image_view', {
    category: 'portfolio',
    label: category,
    custom_parameter_1: imageId,
    custom_parameter_2: category
  });
};

export const trackGalleryFilter = (filterCategory: string) => {
  trackEvent('gallery_filter', {
    category: 'portfolio',
    label: filterCategory,
    custom_parameter_1: filterCategory
  });
};

// Blog Events
export const trackBlogPostView = (postId: string, postTitle: string, category?: string) => {
  trackEvent('blog_post_view', {
    category: 'content',
    label: postTitle,
    custom_parameter_1: postId,
    custom_parameter_2: category || 'blog',
    post_title: postTitle
  });
};

export const trackBlogCategoryView = (category: string) => {
  trackEvent('blog_category_view', {
    category: 'content',
    label: category,
    custom_parameter_1: category
  });
};

// Social Media Events
export const trackSocialClick = (platform: string, source: string) => {
  trackEvent('social_click', {
    category: 'social_media',
    label: platform,
    custom_parameter_1: platform,
    custom_parameter_2: source
  });
};

// Navigation Events
export const trackPageView = (pageName: string, pageTitle?: string) => {
  if (!isGtagAvailable()) return;
  
  window.gtag('config', process.env.NEXT_PUBLIC_GA_ID || '', {
    page_title: pageTitle || document.title,
    page_location: window.location.href,
    page_path: window.location.pathname,
    custom_parameter_1: pageName
  });
};

export const trackMenuClick = (menuItem: string, source: string = 'header') => {
  trackEvent('menu_click', {
    category: 'navigation',
    label: menuItem,
    custom_parameter_1: menuItem,
    custom_parameter_2: source
  });
};

// Conversion Events
export const trackBookingIntent = (service: string, source: string) => {
  trackEvent('booking_intent', {
    category: 'conversion',
    label: service,
    custom_parameter_1: service,
    custom_parameter_2: source,
    value: 1
  });
};

export const trackQuoteRequest = (services: string[], totalValue?: number) => {
  trackEvent('quote_request', {
    category: 'conversion',
    label: services.join(', '),
    custom_parameter_1: services[0] || 'multiple',
    custom_parameter_2: 'quote',
    value: totalValue || services.length,
    services_count: services.length
  });
};

// File Download Events
export const trackFileDownload = (fileName: string, fileType: string) => {
  trackEvent('file_download', {
    category: 'downloads',
    label: fileName,
    custom_parameter_1: fileType,
    custom_parameter_2: fileName
  });
};

// Search Events
export const trackSiteSearch = (searchTerm: string, resultsCount?: number) => {
  trackEvent('search', {
    category: 'site_search',
    label: searchTerm,
    custom_parameter_1: searchTerm,
    value: resultsCount || 0,
    search_term: searchTerm
  });
};

// Error Tracking
export const trackError = (errorType: string, errorMessage: string, page: string) => {
  trackEvent('error', {
    category: 'errors',
    label: errorType,
    custom_parameter_1: errorType,
    custom_parameter_2: page,
    error_message: errorMessage,
    page: page
  });
};

// Performance Tracking
export const trackPerformance = (metric: string, value: number, page: string) => {
  trackEvent('performance', {
    category: 'performance',
    label: metric,
    custom_parameter_1: metric,
    custom_parameter_2: page,
    value: Math.round(value)
  });
};

// User Engagement
export const trackVideoPlay = (videoId: string, videoTitle: string) => {
  trackEvent('video_play', {
    category: 'engagement',
    label: videoTitle,
    custom_parameter_1: videoId,
    custom_parameter_2: 'video'
  });
};

export const trackImageZoom = (imageId: string, source: string) => {
  trackEvent('image_zoom', {
    category: 'engagement',
    label: source,
    custom_parameter_1: imageId,
    custom_parameter_2: source
  });
};
