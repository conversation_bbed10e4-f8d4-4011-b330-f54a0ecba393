"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8062],{4582:(e,t,r)=>{r.d(t,{UC:()=>eM,In:()=>eP,q7:()=>eL,VF:()=>eH,p4:()=>eA,ZL:()=>eE,bL:()=>eN,wn:()=>eB,PP:()=>e_,l9:()=>eR,WT:()=>eI,LM:()=>eD});var n=r(2115),l=r(7650);function o(e,[t,r]){return Math.min(r,Math.max(t,e))}var a=r(5185),i=r(7328),s=r(6101),u=r(6081),d=r(4315),c=r(9178),p=r(2293),f=r(7900),v=r(1285),h=r(5152),m=r(4378),g=r(3655),w=r(9708),x=r(9033),y=r(5845),S=r(2712),b=r(5503),C=r(2564),j=r(8168),k=r(3795),T=r(5155),N=[" ","Enter","ArrowUp","ArrowDown"],R=[" ","Enter"],I="Select",[P,E,M]=(0,i.N)(I),[D,L]=(0,u.A)(I,[M,h.Bk]),A=(0,h.Bk)(),[H,_]=D(I),[B,V]=D(I),G=e=>{let{__scopeSelect:t,children:r,open:l,defaultOpen:o,onOpenChange:a,value:i,defaultValue:s,onValueChange:u,dir:c,name:p,autoComplete:f,disabled:m,required:g,form:w}=e,x=A(t),[S,b]=n.useState(null),[C,j]=n.useState(null),[k,N]=n.useState(!1),R=(0,d.jH)(c),[E,M]=(0,y.i)({prop:l,defaultProp:null!=o&&o,onChange:a,caller:I}),[D,L]=(0,y.i)({prop:i,defaultProp:s,onChange:u,caller:I}),_=n.useRef(null),V=!S||w||!!S.closest("form"),[G,F]=n.useState(new Set),K=Array.from(G).map(e=>e.props.value).join(";");return(0,T.jsx)(h.bL,{...x,children:(0,T.jsxs)(H,{required:g,scope:t,trigger:S,onTriggerChange:b,valueNode:C,onValueNodeChange:j,valueNodeHasChildren:k,onValueNodeHasChildrenChange:N,contentId:(0,v.B)(),value:D,onValueChange:L,open:E,onOpenChange:M,dir:R,triggerPointerDownPosRef:_,disabled:m,children:[(0,T.jsx)(P.Provider,{scope:t,children:(0,T.jsx)(B,{scope:e.__scopeSelect,onNativeOptionAdd:n.useCallback(e=>{F(t=>new Set(t).add(e))},[]),onNativeOptionRemove:n.useCallback(e=>{F(t=>{let r=new Set(t);return r.delete(e),r})},[]),children:r})}),V?(0,T.jsxs)(eC,{"aria-hidden":!0,required:g,tabIndex:-1,name:p,autoComplete:f,value:D,onChange:e=>L(e.target.value),disabled:m,form:w,children:[void 0===D?(0,T.jsx)("option",{value:""}):null,Array.from(G)]},K):null]})})};G.displayName=I;var F="SelectTrigger",K=n.forwardRef((e,t)=>{let{__scopeSelect:r,disabled:l=!1,...o}=e,i=A(r),u=_(F,r),d=u.disabled||l,c=(0,s.s)(t,u.onTriggerChange),p=E(r),f=n.useRef("touch"),[v,m,w]=ek(e=>{let t=p().filter(e=>!e.disabled),r=t.find(e=>e.value===u.value),n=eT(t,e,r);void 0!==n&&u.onValueChange(n.value)}),x=e=>{d||(u.onOpenChange(!0),w()),e&&(u.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,T.jsx)(h.Mz,{asChild:!0,...i,children:(0,T.jsx)(g.sG.button,{type:"button",role:"combobox","aria-controls":u.contentId,"aria-expanded":u.open,"aria-required":u.required,"aria-autocomplete":"none",dir:u.dir,"data-state":u.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":ej(u.value)?"":void 0,...o,ref:c,onClick:(0,a.m)(o.onClick,e=>{e.currentTarget.focus(),"mouse"!==f.current&&x(e)}),onPointerDown:(0,a.m)(o.onPointerDown,e=>{f.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,a.m)(o.onKeyDown,e=>{let t=""!==v.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||m(e.key),(!t||" "!==e.key)&&N.includes(e.key)&&(x(),e.preventDefault())})})})});K.displayName=F;var O="SelectValue",W=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:n,style:l,children:o,placeholder:a="",...i}=e,u=_(O,r),{onValueNodeHasChildrenChange:d}=u,c=void 0!==o,p=(0,s.s)(t,u.onValueNodeChange);return(0,S.N)(()=>{d(c)},[d,c]),(0,T.jsx)(g.sG.span,{...i,ref:p,style:{pointerEvents:"none"},children:ej(u.value)?(0,T.jsx)(T.Fragment,{children:a}):o})});W.displayName=O;var U=n.forwardRef((e,t)=>{let{__scopeSelect:r,children:n,...l}=e;return(0,T.jsx)(g.sG.span,{"aria-hidden":!0,...l,ref:t,children:n||"▼"})});U.displayName="SelectIcon";var z=e=>(0,T.jsx)(m.Z,{asChild:!0,...e});z.displayName="SelectPortal";var q="SelectContent",Z=n.forwardRef((e,t)=>{let r=_(q,e.__scopeSelect),[o,a]=n.useState();return((0,S.N)(()=>{a(new DocumentFragment)},[]),r.open)?(0,T.jsx)(J,{...e,ref:t}):o?l.createPortal((0,T.jsx)(X,{scope:e.__scopeSelect,children:(0,T.jsx)(P.Slot,{scope:e.__scopeSelect,children:(0,T.jsx)("div",{children:e.children})})}),o):null});Z.displayName=q;var[X,Y]=D(q),Q=(0,w.TL)("SelectContent.RemoveScroll"),J=n.forwardRef((e,t)=>{let{__scopeSelect:r,position:l="item-aligned",onCloseAutoFocus:o,onEscapeKeyDown:i,onPointerDownOutside:u,side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:w,collisionPadding:x,sticky:y,hideWhenDetached:S,avoidCollisions:b,...C}=e,N=_(q,r),[R,I]=n.useState(null),[P,M]=n.useState(null),D=(0,s.s)(t,e=>I(e)),[L,A]=n.useState(null),[H,B]=n.useState(null),V=E(r),[G,F]=n.useState(!1),K=n.useRef(!1);n.useEffect(()=>{if(R)return(0,j.Eq)(R)},[R]),(0,p.Oh)();let O=n.useCallback(e=>{let[t,...r]=V().map(e=>e.ref.current),[n]=r.slice(-1),l=document.activeElement;for(let r of e)if(r===l||(null==r||r.scrollIntoView({block:"nearest"}),r===t&&P&&(P.scrollTop=0),r===n&&P&&(P.scrollTop=P.scrollHeight),null==r||r.focus(),document.activeElement!==l))return},[V,P]),W=n.useCallback(()=>O([L,R]),[O,L,R]);n.useEffect(()=>{G&&W()},[G,W]);let{onOpenChange:U,triggerPointerDownPosRef:z}=N;n.useEffect(()=>{if(R){let e={x:0,y:0},t=t=>{var r,n,l,o;e={x:Math.abs(Math.round(t.pageX)-(null!=(l=null==(r=z.current)?void 0:r.x)?l:0)),y:Math.abs(Math.round(t.pageY)-(null!=(o=null==(n=z.current)?void 0:n.y)?o:0))}},r=r=>{e.x<=10&&e.y<=10?r.preventDefault():R.contains(r.target)||U(!1),document.removeEventListener("pointermove",t),z.current=null};return null!==z.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",r,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",r,{capture:!0})}}},[R,U,z]),n.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[Z,Y]=ek(e=>{let t=V().filter(e=>!e.disabled),r=t.find(e=>e.ref.current===document.activeElement),n=eT(t,e,r);n&&setTimeout(()=>n.ref.current.focus())}),J=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==N.value&&N.value===t||n)&&(A(e),n&&(K.current=!0))},[N.value]),et=n.useCallback(()=>null==R?void 0:R.focus(),[R]),er=n.useCallback((e,t,r)=>{let n=!K.current&&!r;(void 0!==N.value&&N.value===t||n)&&B(e)},[N.value]),en="popper"===l?ee:$,el=en===ee?{side:d,sideOffset:v,align:h,alignOffset:m,arrowPadding:g,collisionBoundary:w,collisionPadding:x,sticky:y,hideWhenDetached:S,avoidCollisions:b}:{};return(0,T.jsx)(X,{scope:r,content:R,viewport:P,onViewportChange:M,itemRefCallback:J,selectedItem:L,onItemLeave:et,itemTextRefCallback:er,focusSelectedItem:W,selectedItemText:H,position:l,isPositioned:G,searchRef:Z,children:(0,T.jsx)(k.A,{as:Q,allowPinchZoom:!0,children:(0,T.jsx)(f.n,{asChild:!0,trapped:N.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,a.m)(o,e=>{var t;null==(t=N.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,T.jsx)(c.qW,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:i,onPointerDownOutside:u,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>N.onOpenChange(!1),children:(0,T.jsx)(en,{role:"listbox",id:N.contentId,"data-state":N.open?"open":"closed",dir:N.dir,onContextMenu:e=>e.preventDefault(),...C,...el,onPlaced:()=>F(!0),ref:D,style:{display:"flex",flexDirection:"column",outline:"none",...C.style},onKeyDown:(0,a.m)(C.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=V().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let r=e.target,n=t.indexOf(r);t=t.slice(n+1)}setTimeout(()=>O(t)),e.preventDefault()}})})})})})})});J.displayName="SelectContentImpl";var $=n.forwardRef((e,t)=>{let{__scopeSelect:r,onPlaced:l,...a}=e,i=_(q,r),u=Y(q,r),[d,c]=n.useState(null),[p,f]=n.useState(null),v=(0,s.s)(t,e=>f(e)),h=E(r),m=n.useRef(!1),w=n.useRef(!0),{viewport:x,selectedItem:y,selectedItemText:b,focusSelectedItem:C}=u,j=n.useCallback(()=>{if(i.trigger&&i.valueNode&&d&&p&&x&&y&&b){let e=i.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),r=i.valueNode.getBoundingClientRect(),n=b.getBoundingClientRect();if("rtl"!==i.dir){let l=n.left-t.left,a=r.left-l,i=e.left-a,s=e.width+i,u=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.left=c+"px"}else{let l=t.right-n.right,a=window.innerWidth-r.right-l,i=window.innerWidth-e.right-a,s=e.width+i,u=Math.max(s,t.width),c=o(a,[10,Math.max(10,window.innerWidth-10-u)]);d.style.minWidth=s+"px",d.style.right=c+"px"}let a=h(),s=window.innerHeight-20,u=x.scrollHeight,c=window.getComputedStyle(p),f=parseInt(c.borderTopWidth,10),v=parseInt(c.paddingTop,10),g=parseInt(c.borderBottomWidth,10),w=f+v+u+parseInt(c.paddingBottom,10)+g,S=Math.min(5*y.offsetHeight,w),C=window.getComputedStyle(x),j=parseInt(C.paddingTop,10),k=parseInt(C.paddingBottom,10),T=e.top+e.height/2-10,N=y.offsetHeight/2,R=f+v+(y.offsetTop+N);if(R<=T){let e=a.length>0&&y===a[a.length-1].ref.current;d.style.bottom="0px";let t=Math.max(s-T,N+(e?k:0)+(p.clientHeight-x.offsetTop-x.offsetHeight)+g);d.style.height=R+t+"px"}else{let e=a.length>0&&y===a[0].ref.current;d.style.top="0px";let t=Math.max(T,f+x.offsetTop+(e?j:0)+N);d.style.height=t+(w-R)+"px",x.scrollTop=R-T+x.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=S+"px",d.style.maxHeight=s+"px",null==l||l(),requestAnimationFrame(()=>m.current=!0)}},[h,i.trigger,i.valueNode,d,p,x,y,b,i.dir,l]);(0,S.N)(()=>j(),[j]);let[k,N]=n.useState();(0,S.N)(()=>{p&&N(window.getComputedStyle(p).zIndex)},[p]);let R=n.useCallback(e=>{e&&!0===w.current&&(j(),null==C||C(),w.current=!1)},[j,C]);return(0,T.jsx)(et,{scope:r,contentWrapper:d,shouldExpandOnScrollRef:m,onScrollButtonChange:R,children:(0,T.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:k},children:(0,T.jsx)(g.sG.div,{...a,ref:v,style:{boxSizing:"border-box",maxHeight:"100%",...a.style}})})})});$.displayName="SelectItemAlignedPosition";var ee=n.forwardRef((e,t)=>{let{__scopeSelect:r,align:n="start",collisionPadding:l=10,...o}=e,a=A(r);return(0,T.jsx)(h.UC,{...a,...o,ref:t,align:n,collisionPadding:l,style:{boxSizing:"border-box",...o.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ee.displayName="SelectPopperPosition";var[et,er]=D(q,{}),en="SelectViewport",el=n.forwardRef((e,t)=>{let{__scopeSelect:r,nonce:l,...o}=e,i=Y(en,r),u=er(en,r),d=(0,s.s)(t,i.onViewportChange),c=n.useRef(0);return(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:l}),(0,T.jsx)(P.Slot,{scope:r,children:(0,T.jsx)(g.sG.div,{"data-radix-select-viewport":"",role:"presentation",...o,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...o.style},onScroll:(0,a.m)(o.onScroll,e=>{let t=e.currentTarget,{contentWrapper:r,shouldExpandOnScrollRef:n}=u;if((null==n?void 0:n.current)&&r){let e=Math.abs(c.current-t.scrollTop);if(e>0){let n=window.innerHeight-20,l=Math.max(parseFloat(r.style.minHeight),parseFloat(r.style.height));if(l<n){let o=l+e,a=Math.min(n,o),i=o-a;r.style.height=a+"px","0px"===r.style.bottom&&(t.scrollTop=i>0?i:0,r.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});el.displayName=en;var eo="SelectGroup",[ea,ei]=D(eo);n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=(0,v.B)();return(0,T.jsx)(ea,{scope:r,id:l,children:(0,T.jsx)(g.sG.div,{role:"group","aria-labelledby":l,...n,ref:t})})}).displayName=eo;var es="SelectLabel";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=ei(es,r);return(0,T.jsx)(g.sG.div,{id:l.id,...n,ref:t})}).displayName=es;var eu="SelectItem",[ed,ec]=D(eu),ep=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,disabled:o=!1,textValue:i,...u}=e,d=_(eu,r),c=Y(eu,r),p=d.value===l,[f,h]=n.useState(null!=i?i:""),[m,w]=n.useState(!1),x=(0,s.s)(t,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,l,o)}),y=(0,v.B)(),S=n.useRef("touch"),b=()=>{o||(d.onValueChange(l),d.onOpenChange(!1))};if(""===l)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,T.jsx)(ed,{scope:r,value:l,disabled:o,textId:y,isSelected:p,onItemTextChange:n.useCallback(e=>{h(t=>{var r;return t||(null!=(r=null==e?void 0:e.textContent)?r:"").trim()})},[]),children:(0,T.jsx)(P.ItemSlot,{scope:r,value:l,disabled:o,textValue:f,children:(0,T.jsx)(g.sG.div,{role:"option","aria-labelledby":y,"data-highlighted":m?"":void 0,"aria-selected":p&&m,"data-state":p?"checked":"unchecked","aria-disabled":o||void 0,"data-disabled":o?"":void 0,tabIndex:o?void 0:-1,...u,ref:x,onFocus:(0,a.m)(u.onFocus,()=>w(!0)),onBlur:(0,a.m)(u.onBlur,()=>w(!1)),onClick:(0,a.m)(u.onClick,()=>{"mouse"!==S.current&&b()}),onPointerUp:(0,a.m)(u.onPointerUp,()=>{"mouse"===S.current&&b()}),onPointerDown:(0,a.m)(u.onPointerDown,e=>{S.current=e.pointerType}),onPointerMove:(0,a.m)(u.onPointerMove,e=>{if(S.current=e.pointerType,o){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===S.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,a.m)(u.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,a.m)(u.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(R.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});ep.displayName=eu;var ef="SelectItemText",ev=n.forwardRef((e,t)=>{let{__scopeSelect:r,className:o,style:a,...i}=e,u=_(ef,r),d=Y(ef,r),c=ec(ef,r),p=V(ef,r),[f,v]=n.useState(null),h=(0,s.s)(t,e=>v(e),c.onItemTextChange,e=>{var t;return null==(t=d.itemTextRefCallback)?void 0:t.call(d,e,c.value,c.disabled)}),m=null==f?void 0:f.textContent,w=n.useMemo(()=>(0,T.jsx)("option",{value:c.value,disabled:c.disabled,children:m},c.value),[c.disabled,c.value,m]),{onNativeOptionAdd:x,onNativeOptionRemove:y}=p;return(0,S.N)(()=>(x(w),()=>y(w)),[x,y,w]),(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)(g.sG.span,{id:c.textId,...i,ref:h}),c.isSelected&&u.valueNode&&!u.valueNodeHasChildren?l.createPortal(i.children,u.valueNode):null]})});ev.displayName=ef;var eh="SelectItemIndicator",em=n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return ec(eh,r).isSelected?(0,T.jsx)(g.sG.span,{"aria-hidden":!0,...n,ref:t}):null});em.displayName=eh;var eg="SelectScrollUpButton",ew=n.forwardRef((e,t)=>{let r=Y(eg,e.__scopeSelect),l=er(eg,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,S.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){a(t.scrollTop>0)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,T.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});ew.displayName=eg;var ex="SelectScrollDownButton",ey=n.forwardRef((e,t)=>{let r=Y(ex,e.__scopeSelect),l=er(ex,e.__scopeSelect),[o,a]=n.useState(!1),i=(0,s.s)(t,l.onScrollButtonChange);return(0,S.N)(()=>{if(r.viewport&&r.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;a(Math.ceil(t.scrollTop)<e)},t=r.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[r.viewport,r.isPositioned]),o?(0,T.jsx)(eS,{...e,ref:i,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=r;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});ey.displayName=ex;var eS=n.forwardRef((e,t)=>{let{__scopeSelect:r,onAutoScroll:l,...o}=e,i=Y("SelectScrollButton",r),s=n.useRef(null),u=E(r),d=n.useCallback(()=>{null!==s.current&&(window.clearInterval(s.current),s.current=null)},[]);return n.useEffect(()=>()=>d(),[d]),(0,S.N)(()=>{var e;let t=u().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[u]),(0,T.jsx)(g.sG.div,{"aria-hidden":!0,...o,ref:t,style:{flexShrink:0,...o.style},onPointerDown:(0,a.m)(o.onPointerDown,()=>{null===s.current&&(s.current=window.setInterval(l,50))}),onPointerMove:(0,a.m)(o.onPointerMove,()=>{var e;null==(e=i.onItemLeave)||e.call(i),null===s.current&&(s.current=window.setInterval(l,50))}),onPointerLeave:(0,a.m)(o.onPointerLeave,()=>{d()})})});n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e;return(0,T.jsx)(g.sG.div,{"aria-hidden":!0,...n,ref:t})}).displayName="SelectSeparator";var eb="SelectArrow";n.forwardRef((e,t)=>{let{__scopeSelect:r,...n}=e,l=A(r),o=_(eb,r),a=Y(eb,r);return o.open&&"popper"===a.position?(0,T.jsx)(h.i3,{...l,...n,ref:t}):null}).displayName=eb;var eC=n.forwardRef((e,t)=>{let{__scopeSelect:r,value:l,...o}=e,a=n.useRef(null),i=(0,s.s)(t,a),u=(0,b.Z)(l);return n.useEffect(()=>{let e=a.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(u!==l&&t){let r=new Event("change",{bubbles:!0});t.call(e,l),e.dispatchEvent(r)}},[u,l]),(0,T.jsx)(g.sG.select,{...o,style:{...C.Qg,...o.style},ref:i,defaultValue:l})});function ej(e){return""===e||void 0===e}function ek(e){let t=(0,x.c)(e),r=n.useRef(""),l=n.useRef(0),o=n.useCallback(e=>{let n=r.current+e;t(n),function e(t){r.current=t,window.clearTimeout(l.current),""!==t&&(l.current=window.setTimeout(()=>e(""),1e3))}(n)},[t]),a=n.useCallback(()=>{r.current="",window.clearTimeout(l.current)},[]);return n.useEffect(()=>()=>window.clearTimeout(l.current),[]),[r,o,a]}function eT(e,t,r){var n,l;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,a=r?e.indexOf(r):-1,i=(n=e,l=Math.max(a,0),n.map((e,t)=>n[(l+t)%n.length]));1===o.length&&(i=i.filter(e=>e!==r));let s=i.find(e=>e.textValue.toLowerCase().startsWith(o.toLowerCase()));return s!==r?s:void 0}eC.displayName="SelectBubbleInput";var eN=G,eR=K,eI=W,eP=U,eE=z,eM=Z,eD=el,eL=ep,eA=ev,eH=em,e_=ew,eB=ey},5196:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},5503:(e,t,r)=>{r.d(t,{Z:()=>l});var n=r(2115);function l(e){let t=n.useRef({value:e,previous:e});return n.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}},6474:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},7863:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])}}]);