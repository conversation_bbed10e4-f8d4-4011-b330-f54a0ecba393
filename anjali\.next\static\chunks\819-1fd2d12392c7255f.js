"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[819],{2138:(s,e,a)=>{a.d(e,{A:()=>l});let l=(0,a(9946).A)("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},6425:(s,e,a)=>{a.d(e,{MU:()=>d,a6:()=>n,gN:()=>t});var l=a(5155),i=a(2605),r=a(9434);let c={fadeIn:{initial:{opacity:0},animate:{opacity:1}},slideUp:{initial:{opacity:0,y:50},animate:{opacity:1,y:0}},slideLeft:{initial:{opacity:0,x:50},animate:{opacity:1,x:0}},slideRight:{initial:{opacity:0,x:-50},animate:{opacity:1,x:0}},scale:{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1}},bounce:{initial:{opacity:0,y:-20},animate:{opacity:1,y:0}}};function n(s){let{children:e,className:a,animation:n="fadeIn",delay:t=0,duration:d=.6,once:m=!0}=s,h=c[n];return(0,l.jsx)(i.P.div,{className:(0,r.cn)(a),initial:h.initial,whileInView:h.animate,viewport:{once:m,margin:"-100px"},transition:{duration:d,delay:t,ease:"easeOut"},children:e})}function t(s){let{children:e,className:a,staggerDelay:c=.1}=s;return(0,l.jsx)(i.P.div,{className:(0,r.cn)(a),initial:"hidden",whileInView:"visible",viewport:{once:!0,margin:"-100px"},variants:{hidden:{},visible:{transition:{staggerChildren:c}}},children:e})}function d(s){let{children:e,className:a,animation:n="slideUp"}=s,t=c[n];return(0,l.jsx)(i.P.div,{className:(0,r.cn)(a),variants:{hidden:t.initial,visible:t.animate},transition:{duration:.6,ease:"easeOut"},children:e})}},7492:(s,e,a)=>{a.d(e,{qp:()=>o,gO:()=>h,bY:()=>x,t3:()=>g,pI:()=>d,Oz:()=>j});var l=a(5155),i=a(9434);function r(s){let{className:e,...a}=s;return(0,l.jsx)("div",{className:(0,i.cn)("animate-pulse rounded-md bg-muted",e),...a})}var c=a(6695),n=a(6425);function t(){return(0,l.jsxs)(c.Card,{className:"group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm",children:[(0,l.jsx)("div",{className:"relative aspect-[4/3] overflow-hidden rounded-t-xl",children:(0,l.jsx)(r,{className:"w-full h-full"})}),(0,l.jsxs)(c.aR,{className:"pb-3",children:[(0,l.jsx)(r,{className:"h-6 w-3/4 mb-2"}),(0,l.jsx)(r,{className:"h-4 w-full"}),(0,l.jsx)(r,{className:"h-4 w-2/3"})]}),(0,l.jsxs)(c.CardContent,{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between text-sm",children:[(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(r,{className:"w-4 h-4 rounded-full"}),(0,l.jsx)(r,{className:"h-4 w-16"})]}),(0,l.jsx)(r,{className:"h-4 w-20"})]}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(r,{className:"h-3 w-full"}),(0,l.jsx)(r,{className:"h-3 w-4/5"}),(0,l.jsx)(r,{className:"h-3 w-3/5"})]}),(0,l.jsx)(r,{className:"h-10 w-full rounded-md"})]})]})}function d(){return(0,l.jsx)(n.gN,{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12",children:Array.from({length:6}).map((s,e)=>(0,l.jsx)(n.MU,{children:(0,l.jsx)(t,{})},e))})}function m(){return(0,l.jsxs)("div",{className:"group relative aspect-square overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10",children:[(0,l.jsx)(r,{className:"w-full h-full"}),(0,l.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent",children:(0,l.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-6",children:[(0,l.jsx)(r,{className:"h-4 w-16 mb-3 bg-white/20"}),(0,l.jsx)(r,{className:"h-5 w-3/4 mb-2 bg-white/30"}),(0,l.jsx)(r,{className:"h-3 w-full mb-3 bg-white/20"}),(0,l.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,l.jsx)(r,{className:"h-5 w-12 bg-white/20"}),(0,l.jsx)(r,{className:"h-5 w-16 bg-white/20"}),(0,l.jsx)(r,{className:"h-5 w-14 bg-white/20"})]})]})}),(0,l.jsx)("div",{className:"absolute top-4 right-4",children:(0,l.jsx)(r,{className:"w-10 h-10 rounded-full bg-white/20"})})]})}function h(){return(0,l.jsx)(n.gN,{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:Array.from({length:12}).map((s,e)=>(0,l.jsx)(n.MU,{children:(0,l.jsx)(m,{})},e))})}function o(){return(0,l.jsx)("div",{className:"flex flex-wrap justify-center gap-3 mb-12",children:Array.from({length:6}).map((s,e)=>(0,l.jsx)("div",{className:"flex items-center gap-2",children:(0,l.jsx)(r,{className:"h-8 w-20 rounded-full"})},e))})}function x(){let s=["aspect-[3/4]","aspect-square","aspect-[4/5]","aspect-[3/5]"];return(0,l.jsx)(n.gN,{className:"columns-1 md:columns-2 lg:columns-3 xl:columns-4 gap-6 space-y-6",children:Array.from({length:8}).map((e,a)=>{let i=s[a%s.length];return(0,l.jsx)(n.MU,{children:(0,l.jsxs)("div",{className:"group relative ".concat(i," overflow-hidden rounded-xl bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 break-inside-avoid"),children:[(0,l.jsx)(r,{className:"w-full h-full"}),(0,l.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent",children:(0,l.jsxs)("div",{className:"absolute bottom-0 left-0 right-0 p-4",children:[(0,l.jsx)(r,{className:"h-3 w-12 mb-2 bg-white/20"}),(0,l.jsx)(r,{className:"h-4 w-2/3 mb-1 bg-white/30"}),(0,l.jsx)(r,{className:"h-3 w-full bg-white/20"})]})}),(0,l.jsx)("div",{className:"absolute top-3 right-3",children:(0,l.jsx)(r,{className:"w-8 h-8 rounded-full bg-white/20"})})]})},"masonry-".concat(a))})})}function u(){return(0,l.jsx)(c.Card,{className:"bg-white/80 backdrop-blur-sm border-0 shadow-lg",children:(0,l.jsxs)(c.CardContent,{className:"p-8",children:[(0,l.jsx)("div",{className:"flex gap-1 mb-6",children:Array.from({length:5}).map((s,e)=>(0,l.jsx)(r,{className:"w-5 h-5"},e))}),(0,l.jsxs)("div",{className:"space-y-3 mb-8",children:[(0,l.jsx)(r,{className:"h-4 w-full"}),(0,l.jsx)(r,{className:"h-4 w-full"}),(0,l.jsx)(r,{className:"h-4 w-3/4"})]}),(0,l.jsxs)("div",{className:"flex items-center gap-4",children:[(0,l.jsx)(r,{className:"w-12 h-12 rounded-full"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsx)(r,{className:"h-4 w-24"}),(0,l.jsx)(r,{className:"h-3 w-32"})]})]})]})})}function j(){return(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("div",{className:"max-w-4xl mx-auto mb-12",children:(0,l.jsx)(u,{})}),(0,l.jsxs)("div",{className:"flex justify-center items-center gap-4",children:[(0,l.jsx)(r,{className:"w-10 h-10 rounded-full"}),(0,l.jsx)("div",{className:"flex gap-2",children:Array.from({length:3}).map((s,e)=>(0,l.jsx)(r,{className:"w-3 h-3 rounded-full"},e))}),(0,l.jsx)(r,{className:"w-10 h-10 rounded-full"})]})]})}function N(){return(0,l.jsxs)(c.Card,{className:"group hover:shadow-xl transition-all duration-300 border-0 bg-white/80 backdrop-blur-sm relative overflow-hidden",children:[(0,l.jsx)("div",{className:"absolute top-4 right-4 z-10",children:(0,l.jsx)(r,{className:"h-6 w-20 rounded-full"})}),(0,l.jsx)("div",{className:"relative aspect-[4/3] overflow-hidden rounded-t-xl",children:(0,l.jsx)(r,{className:"w-full h-full"})}),(0,l.jsxs)(c.aR,{className:"pb-3",children:[(0,l.jsx)(r,{className:"h-7 w-3/4 mb-2"}),(0,l.jsx)(r,{className:"h-4 w-full"}),(0,l.jsx)(r,{className:"h-4 w-2/3"})]}),(0,l.jsxs)(c.CardContent,{className:"space-y-6",children:[(0,l.jsx)("div",{className:"space-y-2",children:Array.from({length:4}).map((s,e)=>(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(r,{className:"w-4 h-4 rounded-full"}),(0,l.jsx)(r,{className:"h-3 w-32"})]},e))}),(0,l.jsxs)("div",{className:"flex items-center gap-2",children:[(0,l.jsx)(r,{className:"w-4 h-4"}),(0,l.jsx)(r,{className:"h-4 w-24"})]}),(0,l.jsxs)("div",{className:"bg-gradient-to-r from-rose-gold/10 to-blush-pink/10 rounded-xl p-4",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,l.jsx)(r,{className:"h-4 w-20"}),(0,l.jsx)(r,{className:"h-5 w-24"})]}),(0,l.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,l.jsx)(r,{className:"h-6 w-28"}),(0,l.jsx)(r,{className:"h-4 w-16"})]}),(0,l.jsx)(r,{className:"h-10 w-full rounded-md"})]})]})]})}function g(){return(0,l.jsx)(n.gN,{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:Array.from({length:6}).map((s,e)=>(0,l.jsx)(n.MU,{children:(0,l.jsx)(N,{})},e))})}a(4090)}}]);