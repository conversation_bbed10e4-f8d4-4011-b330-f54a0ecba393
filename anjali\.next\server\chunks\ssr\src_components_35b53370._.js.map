{"version": 3, "sources": [], "sections": [{"offset": {"line": 5, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/badge.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default: \"border-transparent bg-rose-gold text-white shadow hover:bg-rose-gold-dark\",\n        secondary: \"border-transparent bg-blush-pink text-text-primary hover:bg-blush-pink-dark\",\n        destructive: \"border-transparent bg-red-500 text-white shadow hover:bg-red-600\",\n        outline: \"text-text-primary border-gray-300\",\n        success: \"border-transparent bg-green-500 text-white shadow hover:bg-green-600\",\n        warning: \"border-transparent bg-yellow-500 text-white shadow hover:bg-yellow-600\",\n        lavender: \"border-transparent bg-lavender text-text-primary hover:bg-lavender-dark\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AAGA;AACA;AAJA;;;;AAMA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,wKACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,aAAa;YACb,SAAS;YACT,SAAS;YACT,SAAS;YACT,UAAU;QACZ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 49, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/section.tsx"], "sourcesContent": ["'use client'\n\nimport { cn } from '@/lib/utils'\n\ninterface SectionProps {\n  children: React.ReactNode\n  className?: string\n  id?: string\n  background?: 'default' | 'cream' | 'soft-gray' | 'gradient'\n}\n\nexport function Section({ \n  children, \n  className, \n  id, \n  background = 'default' \n}: SectionProps) {\n  const backgroundClasses = {\n    default: 'bg-white',\n    cream: 'bg-cream',\n    'soft-gray': 'bg-soft-gray',\n    gradient: 'bg-gradient-to-br from-cream to-soft-gray'\n  }\n\n  return (\n    <section \n      id={id}\n      className={cn(\n        'py-16 md:py-24',\n        backgroundClasses[background],\n        className\n      )}\n    >\n      <div className=\"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8\">\n        {children}\n      </div>\n    </section>\n  )\n}\n\ninterface SectionHeaderProps {\n  title: string\n  subtitle?: string\n  description?: string\n  centered?: boolean\n  className?: string\n}\n\nexport function SectionHeader({ \n  title, \n  subtitle, \n  description, \n  centered = true,\n  className \n}: SectionHeaderProps) {\n  return (\n    <div className={cn(\n      'mb-12 md:mb-16',\n      centered && 'text-center',\n      className\n    )}>\n      {subtitle && (\n        <p className=\"text-rose-gold-dark font-medium text-sm uppercase tracking-wide mb-2\">\n          {subtitle}\n        </p>\n      )}\n      <h2 className=\"font-display text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary mb-4\">\n        {title}\n      </h2>\n      {description && (\n        <p className=\"text-text-secondary text-lg max-w-3xl mx-auto leading-relaxed\">\n          {description}\n        </p>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;AAWO,SAAS,QAAQ,EACtB,QAAQ,EACR,SAAS,EACT,EAAE,EACF,aAAa,SAAS,EACT;IACb,MAAM,oBAAoB;QACxB,SAAS;QACT,OAAO;QACP,aAAa;QACb,UAAU;IACZ;IAEA,qBACE,8OAAC;QACC,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kBACA,iBAAiB,CAAC,WAAW,EAC7B;kBAGF,cAAA,8OAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;AAUO,SAAS,cAAc,EAC5B,KAAK,EACL,QAAQ,EACR,WAAW,EACX,WAAW,IAAI,EACf,SAAS,EACU;IACnB,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACf,kBACA,YAAY,eACZ;;YAEC,0BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;0BAGL,8OAAC;gBAAG,WAAU;0BACX;;;;;;YAEF,6BACC,8OAAC;gBAAE,WAAU;0BACV;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 121, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/card.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Card = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"rounded-xl border border-gray-200 bg-white text-text-primary shadow-sm transition-shadow hover:shadow-md\",\n      className\n    )}\n    {...props}\n  />\n))\nCard.displayName = \"Card\"\n\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex flex-col space-y-1.5 p-6\", className)} {...props} />\n))\nCardHeader.displayName = \"CardHeader\"\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\"font-display text-2xl font-semibold leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nCardTitle.displayName = \"CardTitle\"\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p\n    ref={ref}\n    className={cn(\"text-sm text-text-secondary\", className)}\n    {...props}\n  />\n))\nCardDescription.displayName = \"CardDescription\"\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n))\nCardContent.displayName = \"CardContent\"\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"flex items-center p-6 pt-0\", className)} {...props} />\n))\nCardFooter.displayName = \"CardFooter\"\n\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,qBAAO,qMAAA,CAAA,aAAgB,CAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4GACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAAa,GAAG,KAAK;;;;;;AAErF,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,qMAAA,CAAA,aAAgB,CAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,qMAAA,CAAA,aAAgB,CAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,+BAA+B;QAC5C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,qMAAA,CAAA,aAAgB,CAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,qMAAA,CAAA,aAAgB,CAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAAa,GAAG,KAAK;;;;;;AAElF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/animated-element.tsx"], "sourcesContent": ["'use client'\n\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\ninterface AnimatedElementProps {\n  children: React.ReactNode\n  className?: string\n  animation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'slideRight' | 'scale' | 'bounce'\n  delay?: number\n  duration?: number\n  once?: boolean\n}\n\nconst animations = {\n  fadeIn: {\n    initial: { opacity: 0 },\n    animate: { opacity: 1 },\n  },\n  slideUp: {\n    initial: { opacity: 0, y: 50 },\n    animate: { opacity: 1, y: 0 },\n  },\n  slideLeft: {\n    initial: { opacity: 0, x: 50 },\n    animate: { opacity: 1, x: 0 },\n  },\n  slideRight: {\n    initial: { opacity: 0, x: -50 },\n    animate: { opacity: 1, x: 0 },\n  },\n  scale: {\n    initial: { opacity: 0, scale: 0.8 },\n    animate: { opacity: 1, scale: 1 },\n  },\n  bounce: {\n    initial: { opacity: 0, y: -20 },\n    animate: { opacity: 1, y: 0 },\n  },\n}\n\nexport function AnimatedElement({\n  children,\n  className,\n  animation = 'fadeIn',\n  delay = 0,\n  duration = 0.6,\n  once = true,\n}: AnimatedElementProps) {\n  const selectedAnimation = animations[animation]\n\n  return (\n    <motion.div\n      className={cn(className)}\n      initial={selectedAnimation.initial}\n      whileInView={selectedAnimation.animate}\n      viewport={{ once, margin: '-100px' }}\n      transition={{\n        duration,\n        delay,\n        ease: 'easeOut',\n      }}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\ninterface StaggeredContainerProps {\n  children: React.ReactNode\n  className?: string\n  staggerDelay?: number\n}\n\nexport function StaggeredContainer({\n  children,\n  className,\n  staggerDelay = 0.1,\n}: StaggeredContainerProps) {\n  return (\n    <motion.div\n      className={cn(className)}\n      initial=\"hidden\"\n      whileInView=\"visible\"\n      viewport={{ once: true, margin: '-100px' }}\n      variants={{\n        hidden: {},\n        visible: {\n          transition: {\n            staggerChildren: staggerDelay,\n          },\n        },\n      }}\n    >\n      {children}\n    </motion.div>\n  )\n}\n\ninterface StaggeredItemProps {\n  children: React.ReactNode\n  className?: string\n  animation?: 'fadeIn' | 'slideUp' | 'slideLeft' | 'slideRight' | 'scale'\n}\n\nexport function StaggeredItem({\n  children,\n  className,\n  animation = 'slideUp',\n}: StaggeredItemProps) {\n  const selectedAnimation = animations[animation]\n\n  return (\n    <motion.div\n      className={cn(className)}\n      variants={{\n        hidden: selectedAnimation.initial,\n        visible: selectedAnimation.animate,\n      }}\n      transition={{ duration: 0.6, ease: 'easeOut' }}\n    >\n      {children}\n    </motion.div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AAHA;;;;AAcA,MAAM,aAAa;IACjB,QAAQ;QACN,SAAS;YAAE,SAAS;QAAE;QACtB,SAAS;YAAE,SAAS;QAAE;IACxB;IACA,SAAS;QACP,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,WAAW;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,YAAY;QACV,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;IACA,OAAO;QACL,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;IAClC;IACA,QAAQ;QACN,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;IAC9B;AACF;AAEO,SAAS,gBAAgB,EAC9B,QAAQ,EACR,SAAS,EACT,YAAY,QAAQ,EACpB,QAAQ,CAAC,EACT,WAAW,GAAG,EACd,OAAO,IAAI,EACU;IACrB,MAAM,oBAAoB,UAAU,CAAC,UAAU;IAE/C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAS,kBAAkB,OAAO;QAClC,aAAa,kBAAkB,OAAO;QACtC,UAAU;YAAE;YAAM,QAAQ;QAAS;QACnC,YAAY;YACV;YACA;YACA,MAAM;QACR;kBAEC;;;;;;AAGP;AAQO,SAAS,mBAAmB,EACjC,QAAQ,EACR,SAAS,EACT,eAAe,GAAG,EACM;IACxB,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,SAAQ;QACR,aAAY;QACZ,UAAU;YAAE,MAAM;YAAM,QAAQ;QAAS;QACzC,UAAU;YACR,QAAQ,CAAC;YACT,SAAS;gBACP,YAAY;oBACV,iBAAiB;gBACnB;YACF;QACF;kBAEC;;;;;;AAGP;AAQO,SAAS,cAAc,EAC5B,QAAQ,EACR,SAAS,EACT,YAAY,SAAS,EACF;IACnB,MAAM,oBAAoB,UAAU,CAAC,UAAU;IAE/C,qBACE,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;QACd,UAAU;YACR,QAAQ,kBAAkB,OAAO;YACjC,SAAS,kBAAkB,OAAO;QACpC;QACA,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;kBAE5C;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 342, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-experience.tsx"], "sourcesContent": ["'use client'\n\nimport { Calendar, Award, Users, Palette } from 'lucide-react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Section, SectionHeader } from '@/components/ui/section'\nimport { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'\n\nconst milestones = [\n  {\n    year: '2019',\n    title: 'Started Professional Journey',\n    description: 'Began my career as a professional makeup artist in Biratnagar, focusing on bridal and special occasion makeup.',\n    icon: Calendar,\n    color: 'from-rose-gold to-blush-pink'\n  },\n  {\n    year: '2020',\n    title: 'Expanded Service Areas',\n    description: 'Extended services to Itahari, Dharan, and surrounding areas, building a strong client base across the region.',\n    icon: Users,\n    color: 'from-blush-pink to-lavender'\n  },\n  {\n    year: '2021',\n    title: 'Advanced Training & Certification',\n    description: 'Completed advanced makeup courses and obtained professional certifications in modern makeup techniques.',\n    icon: Award,\n    color: 'from-lavender to-rose-gold'\n  },\n  {\n    year: '2022',\n    title: 'Specialized in Traditional Makeup',\n    description: 'Developed expertise in traditional Nepali makeup styles, becoming known for authentic cultural looks.',\n    icon: Palette,\n    color: 'from-rose-gold to-blush-pink'\n  },\n  {\n    year: '2023',\n    title: 'Reached 100+ Happy Clients',\n    description: 'Celebrated serving over 100 satisfied clients with consistently high ratings and positive reviews.',\n    icon: Users,\n    color: 'from-blush-pink to-lavender'\n  },\n  {\n    year: '2024',\n    title: 'Expanded to Kathmandu',\n    description: 'Extended services to Nepal\\'s capital, bringing professional makeup artistry to a wider audience.',\n    icon: Calendar,\n    color: 'from-lavender to-rose-gold'\n  }\n]\n\nconst skills = [\n  { name: 'Bridal Makeup', level: 95 },\n  { name: 'Traditional Makeup', level: 90 },\n  { name: 'Party & Event Makeup', level: 92 },\n  { name: 'Photoshoot Makeup', level: 88 },\n  { name: 'Color Matching', level: 94 },\n  { name: 'Contouring & Highlighting', level: 91 }\n]\n\nexport default function AboutExperience() {\n  return (\n    <Section>\n      <AnimatedElement animation=\"fadeIn\">\n        <SectionHeader\n          subtitle=\"Experience & Expertise\"\n          title=\"My Professional Journey\"\n          description=\"A timeline of growth, learning, and achievements in the makeup artistry field.\"\n        />\n      </AnimatedElement>\n\n      {/* Timeline */}\n      <div className=\"relative\">\n        {/* Timeline Line */}\n        <div className=\"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-rose-gold via-blush-pink to-lavender rounded-full hidden lg:block\"></div>\n\n        <StaggeredContainer className=\"space-y-12\">\n          {milestones.map((milestone, index) => (\n            <StaggeredItem key={milestone.year}>\n              <div className={`flex items-center ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} flex-col lg:gap-8 gap-4`}>\n                {/* Content Card */}\n                <div className=\"flex-1 lg:max-w-md\">\n                  <Card className=\"bg-white/80 backdrop-blur-sm border-0 hover:shadow-xl transition-all duration-300\">\n                    <CardContent className=\"p-6\">\n                      <div className=\"flex items-center gap-3 mb-3\">\n                        <Badge variant=\"secondary\" className=\"text-sm\">\n                          {milestone.year}\n                        </Badge>\n                        <div className={`w-8 h-8 bg-gradient-to-r ${milestone.color} rounded-full flex items-center justify-center`}>\n                          <milestone.icon className=\"w-4 h-4 text-white\" />\n                        </div>\n                      </div>\n                      <h3 className=\"font-display text-xl font-semibold text-text-primary mb-2\">\n                        {milestone.title}\n                      </h3>\n                      <p className=\"text-text-secondary leading-relaxed\">\n                        {milestone.description}\n                      </p>\n                    </CardContent>\n                  </Card>\n                </div>\n\n                {/* Timeline Node */}\n                <div className=\"hidden lg:block relative\">\n                  <div className={`w-4 h-4 bg-gradient-to-r ${milestone.color} rounded-full border-4 border-white shadow-lg`}></div>\n                </div>\n\n                {/* Spacer for alternating layout */}\n                <div className=\"flex-1 lg:max-w-md hidden lg:block\"></div>\n              </div>\n            </StaggeredItem>\n          ))}\n        </StaggeredContainer>\n      </div>\n\n      {/* Skills Section */}\n      <div className=\"mt-20\">\n        <AnimatedElement animation=\"fadeIn\">\n          <SectionHeader\n            subtitle=\"Skills & Expertise\"\n            title=\"Areas of Specialization\"\n            description=\"My core competencies and areas of expertise in makeup artistry.\"\n          />\n        </AnimatedElement>\n\n        <div className=\"grid md:grid-cols-2 gap-8\">\n          <AnimatedElement animation=\"slideRight\" delay={0.3}>\n            <Card className=\"bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0\">\n              <CardContent className=\"p-6\">\n                <h3 className=\"font-display text-xl font-semibold text-text-primary mb-6\">\n                  Technical Skills\n                </h3>\n                <div className=\"space-y-4\">\n                  {skills.map((skill) => (\n                    <div key={skill.name} className=\"space-y-2\">\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"text-text-primary font-medium\">{skill.name}</span>\n                        <span className=\"text-text-secondary text-sm\">{skill.level}%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                        <div \n                          className=\"bg-gradient-to-r from-rose-gold to-blush-pink h-2 rounded-full transition-all duration-1000\"\n                          style={{ width: `${skill.level}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </AnimatedElement>\n\n          <AnimatedElement animation=\"slideLeft\" delay={0.5}>\n            <Card className=\"bg-gradient-to-br from-lavender/10 to-blush-pink/10 border-0\">\n              <CardContent className=\"p-6\">\n                <h3 className=\"font-display text-xl font-semibold text-text-primary mb-6\">\n                  Professional Highlights\n                </h3>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-2 h-2 bg-rose-gold rounded-full\"></div>\n                    <span className=\"text-text-secondary\">5+ Years Professional Experience</span>\n                  </div>\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-2 h-2 bg-blush-pink rounded-full\"></div>\n                    <span className=\"text-text-secondary\">100+ Successful Makeup Sessions</span>\n                  </div>\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-2 h-2 bg-lavender rounded-full\"></div>\n                    <span className=\"text-text-secondary\">5.0 Star Average Rating</span>\n                  </div>\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-2 h-2 bg-rose-gold rounded-full\"></div>\n                    <span className=\"text-text-secondary\">Certified in Advanced Techniques</span>\n                  </div>\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-2 h-2 bg-blush-pink rounded-full\"></div>\n                    <span className=\"text-text-secondary\">Specialized in Traditional & Modern Styles</span>\n                  </div>\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-2 h-2 bg-lavender rounded-full\"></div>\n                    <span className=\"text-text-secondary\">Serving 7 Cities Across Nepal</span>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </AnimatedElement>\n        </div>\n      </div>\n    </Section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAQA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;CACD;AAED,MAAM,SAAS;IACb;QAAE,MAAM;QAAiB,OAAO;IAAG;IACnC;QAAE,MAAM;QAAsB,OAAO;IAAG;IACxC;QAAE,MAAM;QAAwB,OAAO;IAAG;IAC1C;QAAE,MAAM;QAAqB,OAAO;IAAG;IACvC;QAAE,MAAM;QAAkB,OAAO;IAAG;IACpC;QAAE,MAAM;QAA6B,OAAO;IAAG;CAChD;AAEc,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC,+IAAA,CAAA,kBAAe;gBAAC,WAAU;0BACzB,cAAA,8OAAC,mIAAA,CAAA,gBAAa;oBACZ,UAAS;oBACT,OAAM;oBACN,aAAY;;;;;;;;;;;0BAKhB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC,+IAAA,CAAA,qBAAkB;wBAAC,WAAU;kCAC3B,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC,+IAAA,CAAA,gBAAa;0CACZ,cAAA,8OAAC;oCAAI,WAAW,CAAC,kBAAkB,EAAE,QAAQ,MAAM,IAAI,gBAAgB,sBAAsB,wBAAwB,CAAC;;sDAEpH,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;0DACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;8EAClC,UAAU,IAAI;;;;;;8EAEjB,8OAAC;oEAAI,WAAW,CAAC,yBAAyB,EAAE,UAAU,KAAK,CAAC,8CAA8C,CAAC;8EACzG,cAAA,8OAAC,UAAU,IAAI;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAG9B,8OAAC;4DAAG,WAAU;sEACX,UAAU,KAAK;;;;;;sEAElB,8OAAC;4DAAE,WAAU;sEACV,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;sDAO9B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAW,CAAC,yBAAyB,EAAE,UAAU,KAAK,CAAC,6CAA6C,CAAC;;;;;;;;;;;sDAI5G,8OAAC;4CAAI,WAAU;;;;;;;;;;;;+BA9BC,UAAU,IAAI;;;;;;;;;;;;;;;;0BAsCxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,+IAAA,CAAA,kBAAe;wBAAC,WAAU;kCACzB,cAAA,8OAAC,mIAAA,CAAA,gBAAa;4BACZ,UAAS;4BACT,OAAM;4BACN,aAAY;;;;;;;;;;;kCAIhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,+IAAA,CAAA,kBAAe;gCAAC,WAAU;gCAAa,OAAO;0CAC7C,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAI,WAAU;0DACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;wDAAqB,WAAU;;0EAC9B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAiC,MAAM,IAAI;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;;4EAA+B,MAAM,KAAK;4EAAC;;;;;;;;;;;;;0EAE7D,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;oEAAC;;;;;;;;;;;;uDAR9B,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0CAkB9B,8OAAC,+IAAA,CAAA,kBAAe;gCAAC,WAAU;gCAAY,OAAO;0CAC5C,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1D", "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-cities.tsx"], "sourcesContent": ["'use client'\n\nimport { Map<PERSON><PERSON>, <PERSON>, Car } from 'lucide-react'\nimport { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Section, SectionHeader } from '@/components/ui/section'\nimport { AnimatedElement, StaggeredContainer, StaggeredItem } from '@/components/ui/animated-element'\nimport { getServiceAreas } from '@/lib/data'\nimport { formatPrice } from '@/lib/utils'\n\nexport default function AboutCities() {\n  const serviceAreas = getServiceAreas()\n\n  return (\n    <Section background=\"gradient\">\n      <AnimatedElement animation=\"fadeIn\">\n        <SectionHeader\n          subtitle=\"Service Areas\"\n          title=\"Cities We Serve\"\n          description=\"Professional makeup services available across multiple cities in Nepal. Travel fees may apply for locations outside Biratnagar.\"\n        />\n      </AnimatedElement>\n\n      <StaggeredContainer className=\"grid md:grid-cols-2 lg:grid-cols-3 gap-6\">\n        {serviceAreas.map((area) => (\n          <StaggeredItem key={area.name}>\n            <Card className={`h-full transition-all duration-300 hover:shadow-xl ${\n              area.primary \n                ? 'bg-gradient-to-br from-rose-gold/20 to-blush-pink/20 border-rose-gold/30' \n                : 'bg-white/80 backdrop-blur-sm border-0'\n            }`}>\n              <CardHeader className=\"pb-3\">\n                <div className=\"flex items-center justify-between\">\n                  <CardTitle className=\"flex items-center gap-2 text-lg\">\n                    <MapPin className={`w-5 h-5 ${area.primary ? 'text-rose-gold-dark' : 'text-text-secondary'}`} />\n                    {area.name}\n                  </CardTitle>\n                  {area.primary && (\n                    <Badge variant=\"default\" className=\"text-xs\">\n                      Home Base\n                    </Badge>\n                  )}\n                </div>\n              </CardHeader>\n              \n              <CardContent className=\"space-y-3\">\n                <div className=\"flex items-center gap-2 text-sm text-text-secondary\">\n                  <Car className=\"w-4 h-4\" />\n                  <span>\n                    {area.travelFee === 0 \n                      ? 'No travel fee' \n                      : `Travel fee: ${formatPrice(`NPR ${area.travelFee}`)}`\n                    }\n                  </span>\n                </div>\n                \n                <div className=\"flex items-center gap-2 text-sm text-text-secondary\">\n                  <Clock className=\"w-4 h-4\" />\n                  <span>\n                    {area.primary \n                      ? 'Same-day booking available' \n                      : 'Advance booking recommended'\n                    }\n                  </span>\n                </div>\n\n                {area.primary && (\n                  <div className=\"pt-2 border-t border-rose-gold/20\">\n                    <p className=\"text-sm text-text-secondary\">\n                      Our main studio location with full equipment and product range available.\n                    </p>\n                  </div>\n                )}\n              </CardContent>\n            </Card>\n          </StaggeredItem>\n        ))}\n      </StaggeredContainer>\n\n      {/* Additional Information */}\n      <AnimatedElement animation=\"slideUp\" delay={0.6} className=\"mt-12\">\n        <div className=\"grid md:grid-cols-3 gap-8 text-center\">\n          <div className=\"space-y-2\">\n            <div className=\"w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto\">\n              <MapPin className=\"w-8 h-8 text-white\" />\n            </div>\n            <h3 className=\"font-display text-lg font-semibold text-text-primary\">\n              Wide Coverage\n            </h3>\n            <p className=\"text-text-secondary text-sm\">\n              Serving 7 major cities across Nepal with professional makeup services\n            </p>\n          </div>\n          \n          <div className=\"space-y-2\">\n            <div className=\"w-16 h-16 bg-gradient-to-br from-blush-pink to-lavender rounded-full flex items-center justify-center mx-auto\">\n              <Car className=\"w-8 h-8 text-white\" />\n            </div>\n            <h3 className=\"font-display text-lg font-semibold text-text-primary\">\n              Travel Services\n            </h3>\n            <p className=\"text-text-secondary text-sm\">\n              We come to you! On-location services available with minimal travel fees\n            </p>\n          </div>\n          \n          <div className=\"space-y-2\">\n            <div className=\"w-16 h-16 bg-gradient-to-br from-lavender to-rose-gold rounded-full flex items-center justify-center mx-auto\">\n              <Clock className=\"w-8 h-8 text-white\" />\n            </div>\n            <h3 className=\"font-display text-lg font-semibold text-text-primary\">\n              Flexible Scheduling\n            </h3>\n            <p className=\"text-text-secondary text-sm\">\n              Early morning and evening appointments available for your convenience\n            </p>\n          </div>\n        </div>\n      </AnimatedElement>\n\n      {/* Service Notes */}\n      <AnimatedElement animation=\"fadeIn\" delay={0.8} className=\"mt-12\">\n        <Card className=\"bg-white/60 backdrop-blur-sm border-0\">\n          <CardContent className=\"p-6\">\n            <h3 className=\"font-display text-lg font-semibold text-text-primary mb-4 text-center\">\n              Service Information\n            </h3>\n            <div className=\"grid md:grid-cols-2 gap-6 text-sm text-text-secondary\">\n              <div>\n                <h4 className=\"font-semibold text-text-primary mb-2\">Booking Requirements:</h4>\n                <ul className=\"space-y-1\">\n                  <li>• Advance booking recommended for all locations</li>\n                  <li>• Minimum 24-hour notice for travel bookings</li>\n                  <li>• Emergency bookings subject to availability</li>\n                </ul>\n              </div>\n              <div>\n                <h4 className=\"font-semibold text-text-primary mb-2\">What&apos;s Included:</h4>\n                <ul className=\"space-y-1\">\n                  <li>• Professional makeup application</li>\n                  <li>• High-quality products and tools</li>\n                  <li>• Touch-up kit for events</li>\n                  <li>• Consultation and color matching</li>\n                </ul>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </AnimatedElement>\n    </Section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AARA;;;;;;;;;AAUe,SAAS;IACtB,MAAM,eAAe,CAAA,GAAA,kIAAA,CAAA,kBAAe,AAAD;IAEnC,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,YAAW;;0BAClB,8OAAC,+IAAA,CAAA,kBAAe;gBAAC,WAAU;0BACzB,cAAA,8OAAC,mIAAA,CAAA,gBAAa;oBACZ,UAAS;oBACT,OAAM;oBACN,aAAY;;;;;;;;;;;0BAIhB,8OAAC,+IAAA,CAAA,qBAAkB;gBAAC,WAAU;0BAC3B,aAAa,GAAG,CAAC,CAAC,qBACjB,8OAAC,+IAAA,CAAA,gBAAa;kCACZ,cAAA,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAW,CAAC,mDAAmD,EACnE,KAAK,OAAO,GACR,6EACA,yCACJ;;8CACA,8OAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDAAC,WAAU;;kEACnB,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAW,CAAC,QAAQ,EAAE,KAAK,OAAO,GAAG,wBAAwB,uBAAuB;;;;;;oDAC3F,KAAK,IAAI;;;;;;;4CAEX,KAAK,OAAO,kBACX,8OAAC,iIAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAAU;;;;;;;;;;;;;;;;;8CAOnD,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,8OAAC;8DACE,KAAK,SAAS,KAAK,IAChB,kBACA,CAAC,YAAY,EAAE,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,CAAC,IAAI,EAAE,KAAK,SAAS,EAAE,GAAG;;;;;;;;;;;;sDAK7D,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,8OAAC;8DACE,KAAK,OAAO,GACT,+BACA;;;;;;;;;;;;wCAKP,KAAK,OAAO,kBACX,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAE,WAAU;0DAA8B;;;;;;;;;;;;;;;;;;;;;;;uBA3CjC,KAAK,IAAI;;;;;;;;;;0BAuDjC,8OAAC,+IAAA,CAAA,kBAAe;gBAAC,WAAU;gBAAU,OAAO;gBAAK,WAAU;0BACzD,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,0MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,8OAAC;oCAAG,WAAU;8CAAuD;;;;;;8CAGrE,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;sCAK7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,8OAAC;oCAAG,WAAU;8CAAuD;;;;;;8CAGrE,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;sCAK7C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;;;;;;8CAEnB,8OAAC;oCAAG,WAAU;8CAAuD;;;;;;8CAGrE,8OAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;0BAQjD,8OAAC,+IAAA,CAAA,kBAAe;gBAAC,WAAU;gBAAS,OAAO;gBAAK,WAAU;0BACxD,cAAA,8OAAC,gIAAA,CAAA,OAAI;oBAAC,WAAU;8BACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,8OAAC;gCAAG,WAAU;0CAAwE;;;;;;0CAGtF,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;kDAGR,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAuC;;;;;;0DACrD,8OAAC;gDAAG,WAAU;;kEACZ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;kEACJ,8OAAC;kEAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStB", "debugId": null}}, {"offset": {"line": 1368, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-cta.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { ArrowRight, Phone, Calendar, MessageCircle } from 'lucide-react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Section } from '@/components/ui/section'\nimport { AnimatedElement } from '@/components/ui/animated-element'\nimport { getSiteConfig } from '@/lib/data'\nimport { generateWhatsAppLink } from '@/lib/utils'\n\nexport default function AboutCTA() {\n  const siteConfig = getSiteConfig()\n  const whatsappLink = generateWhatsAppLink(\n    siteConfig.contact.whatsapp,\n    \"Hi! I&apos;d like to book a makeup consultation. Could you please provide more details about your services and availability?\"\n  )\n\n  return (\n    <Section>\n      <div className=\"relative overflow-hidden rounded-3xl bg-gradient-to-br from-rose-gold via-blush-pink to-lavender p-8 md:p-12 text-white\">\n        {/* Background Pattern */}\n        <div className=\"absolute inset-0 opacity-10\">\n          <div className=\"absolute top-10 left-10 w-32 h-32 bg-white rounded-full blur-3xl\"></div>\n          <div className=\"absolute bottom-10 right-10 w-40 h-40 bg-white rounded-full blur-3xl\"></div>\n          <div className=\"absolute top-1/2 left-1/3 w-24 h-24 bg-white rounded-full blur-2xl\"></div>\n        </div>\n\n        <div className=\"relative grid lg:grid-cols-2 gap-12 items-center\">\n          {/* Content */}\n          <AnimatedElement animation=\"slideRight\" className=\"space-y-6\">\n            <h2 className=\"font-display text-3xl md:text-4xl lg:text-5xl font-bold leading-tight\">\n              Ready to Look & Feel\n              <span className=\"block\">Amazing?</span>\n            </h2>\n            \n            <p className=\"text-lg text-white/90 leading-relaxed\">\n              Let&apos;s create the perfect look for your special occasion. Book a consultation \n              today and discover how professional makeup artistry can enhance your natural beauty.\n            </p>\n\n            <div className=\"flex flex-col sm:flex-row gap-4\">\n              <Button asChild size=\"lg\" variant=\"secondary\" className=\"group\">\n                <Link href={whatsappLink} target=\"_blank\" rel=\"noopener noreferrer\">\n                  <MessageCircle className=\"w-5 h-5 mr-2\" />\n                  WhatsApp Consultation\n                  <ArrowRight className=\"w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform\" />\n                </Link>\n              </Button>\n              \n              <Button asChild size=\"lg\" variant=\"outline\" className=\"border-white text-white hover:bg-white hover:text-rose-gold-dark\">\n                <Link href=\"/contact\">\n                  <Calendar className=\"w-5 h-5 mr-2\" />\n                  Book Appointment\n                </Link>\n              </Button>\n            </div>\n          </AnimatedElement>\n\n          {/* Quick Contact Cards */}\n          <AnimatedElement animation=\"slideLeft\" delay={0.3} className=\"space-y-4\">\n            <Card className=\"bg-white/20 backdrop-blur-sm border-white/30\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center\">\n                    <Phone className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-white mb-1\">Call or WhatsApp</h3>\n                    <p className=\"text-white/80 text-sm\">{siteConfig.contact.phone}</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card className=\"bg-white/20 backdrop-blur-sm border-white/30\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center\">\n                    <Calendar className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-white mb-1\">Business Hours</h3>\n                    <p className=\"text-white/80 text-sm\">Mon-Sat: 9AM-6PM, Sun: 10AM-4PM</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n\n            <Card className=\"bg-white/20 backdrop-blur-sm border-white/30\">\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center gap-4\">\n                  <div className=\"w-12 h-12 bg-white/20 rounded-full flex items-center justify-center\">\n                    <MessageCircle className=\"w-6 h-6 text-white\" />\n                  </div>\n                  <div>\n                    <h3 className=\"font-semibold text-white mb-1\">Quick Response</h3>\n                    <p className=\"text-white/80 text-sm\">Usually respond within 2 hours</p>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </AnimatedElement>\n        </div>\n      </div>\n\n      {/* Additional Links */}\n      <AnimatedElement animation=\"slideUp\" delay={0.6} className=\"mt-12\">\n        <div className=\"grid md:grid-cols-3 gap-6 text-center\">\n          <Card className=\"hover:shadow-lg transition-shadow duration-300\">\n            <CardContent className=\"p-6\">\n              <h3 className=\"font-display text-lg font-semibold text-text-primary mb-2\">\n                View Our Work\n              </h3>\n              <p className=\"text-text-secondary text-sm mb-4\">\n                Browse our portfolio of stunning makeup transformations\n              </p>\n              <Button asChild variant=\"outline\" size=\"sm\">\n                <Link href=\"/portfolio\">\n                  Portfolio\n                  <ArrowRight className=\"w-4 h-4 ml-2\" />\n                </Link>\n              </Button>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-300\">\n            <CardContent className=\"p-6\">\n              <h3 className=\"font-display text-lg font-semibold text-text-primary mb-2\">\n                Our Services\n              </h3>\n              <p className=\"text-text-secondary text-sm mb-4\">\n                Explore our comprehensive range of makeup services\n              </p>\n              <Button asChild variant=\"outline\" size=\"sm\">\n                <Link href=\"/services\">\n                  Services\n                  <ArrowRight className=\"w-4 h-4 ml-2\" />\n                </Link>\n              </Button>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-300\">\n            <CardContent className=\"p-6\">\n              <h3 className=\"font-display text-lg font-semibold text-text-primary mb-2\">\n                Special Packages\n              </h3>\n              <p className=\"text-text-secondary text-sm mb-4\">\n                Save more with our curated service packages\n              </p>\n              <Button asChild variant=\"outline\" size=\"sm\">\n                <Link href=\"/packages\">\n                  Packages\n                  <ArrowRight className=\"w-4 h-4 ml-2\" />\n                </Link>\n              </Button>\n            </CardContent>\n          </Card>\n        </div>\n      </AnimatedElement>\n    </Section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;AATA;;;;;;;;;;AAWe,SAAS;IACtB,MAAM,aAAa,CAAA,GAAA,kIAAA,CAAA,gBAAa,AAAD;IAC/B,MAAM,eAAe,CAAA,GAAA,mHAAA,CAAA,uBAAoB,AAAD,EACtC,WAAW,OAAO,CAAC,QAAQ,EAC3B;IAGF,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;0CACf,8OAAC;gCAAI,WAAU;;;;;;;;;;;;kCAGjB,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC,+IAAA,CAAA,kBAAe;gCAAC,WAAU;gCAAa,WAAU;;kDAChD,8OAAC;wCAAG,WAAU;;4CAAwE;0DAEpF,8OAAC;gDAAK,WAAU;0DAAQ;;;;;;;;;;;;kDAG1B,8OAAC;wCAAE,WAAU;kDAAwC;;;;;;kDAKrD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,MAAK;gDAAK,SAAQ;gDAAY,WAAU;0DACtD,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAM;oDAAc,QAAO;oDAAS,KAAI;;sEAC5C,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;wDAAiB;sEAE1C,8OAAC,kNAAA,CAAA,aAAU;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAI1B,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAC,MAAK;gDAAK,SAAQ;gDAAU,WAAU;0DACpD,cAAA,8OAAC,4JAAA,CAAA,UAAI;oDAAC,MAAK;;sEACT,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;;;;;;;0CAQ7C,8OAAC,+IAAA,CAAA,kBAAe;gCAAC,WAAU;gCAAY,OAAO;gCAAK,WAAU;;kDAC3D,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;4DAAC,WAAU;;;;;;;;;;;kEAEnB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAgC;;;;;;0EAC9C,8OAAC;gEAAE,WAAU;0EAAyB,WAAW,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAMtE,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;4DAAC,WAAU;;;;;;;;;;;kEAEtB,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAgC;;;;;;0EAC9C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;kDAM7C,8OAAC,gIAAA,CAAA,OAAI;wCAAC,WAAU;kDACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;sDACrB,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;kEACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;4DAAC,WAAU;;;;;;;;;;;kEAE3B,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAgC;;;;;;0EAC9C,8OAAC;gEAAE,WAAU;0EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUnD,8OAAC,+IAAA,CAAA,kBAAe;gBAAC,WAAU;gBAAU,OAAO;gBAAK,WAAU;0BACzD,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAGhD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,SAAQ;wCAAU,MAAK;kDACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;gDAAa;8DAEtB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM9B,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAGhD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,SAAQ;wCAAU,MAAK;kDACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;gDAAY;8DAErB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAM9B,8OAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,8OAAC;wCAAE,WAAU;kDAAmC;;;;;;kDAGhD,8OAAC,kIAAA,CAAA,SAAM;wCAAC,OAAO;wCAAC,SAAQ;wCAAU,MAAK;kDACrC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;;gDAAY;8DAErB,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxC", "debugId": null}}]}