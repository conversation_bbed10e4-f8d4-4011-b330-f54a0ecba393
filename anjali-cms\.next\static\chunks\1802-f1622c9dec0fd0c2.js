"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[1802],{381:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("settings",[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1007:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1414:(e,t,r)=>{e.exports=r(2436)},1497:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("message-square",[["path",{d:"M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z",key:"1lielz"}]])},2432:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("panel-left",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}]])},2436:(e,t,r)=>{var n=r(2115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},a=n.useState,l=n.useEffect,i=n.useLayoutEffect,s=n.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var r=t();return!o(e,r)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var r=t(),n=a({inst:{value:r,getSnapshot:t}}),o=n[0].inst,c=n[1];return i(function(){o.value=r,o.getSnapshot=t,u(o)&&c({inst:o})},[e,r,t]),l(function(){return u(o)&&c({inst:o}),e(function(){u(o)&&c({inst:o})})},[e]),s(r),r};t.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:c},3783:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("layout-dashboard",[["rect",{width:"7",height:"9",x:"3",y:"3",rx:"1",key:"10lvy0"}],["rect",{width:"7",height:"5",x:"14",y:"3",rx:"1",key:"16une8"}],["rect",{width:"7",height:"9",x:"14",y:"12",rx:"1",key:"1hutg5"}],["rect",{width:"7",height:"5",x:"3",y:"16",rx:"1",key:"ldoo1y"}]])},4011:(e,t,r)=>{r.d(t,{H4:()=>k,bL:()=>b});var n=r(2115),o=r(6081),a=r(9033),l=r(2712),i=r(3655),s=r(1414);function u(){return()=>{}}var c=r(5155),d="Avatar",[p,f]=(0,o.A)(d),[h,y]=p(d),v=n.forwardRef((e,t)=>{let{__scopeAvatar:r,...o}=e,[a,l]=n.useState("idle");return(0,c.jsx)(h,{scope:r,imageLoadingStatus:a,onImageLoadingStatusChange:l,children:(0,c.jsx)(i.sG.span,{...o,ref:t})})});v.displayName=d;var g="AvatarImage";n.forwardRef((e,t)=>{let{__scopeAvatar:r,src:o,onLoadingStatusChange:d=()=>{},...p}=e,f=y(g,r),h=function(e,t){let{referrerPolicy:r,crossOrigin:o}=t,a=(0,s.useSyncExternalStore)(u,()=>!0,()=>!1),i=n.useRef(null),c=a?(i.current||(i.current=new window.Image),i.current):null,[d,p]=n.useState(()=>w(c,e));return(0,l.N)(()=>{p(w(c,e))},[c,e]),(0,l.N)(()=>{let e=e=>()=>{p(e)};if(!c)return;let t=e("loaded"),n=e("error");return c.addEventListener("load",t),c.addEventListener("error",n),r&&(c.referrerPolicy=r),"string"==typeof o&&(c.crossOrigin=o),()=>{c.removeEventListener("load",t),c.removeEventListener("error",n)}},[c,o,r]),d}(o,p),v=(0,a.c)(e=>{d(e),f.onImageLoadingStatusChange(e)});return(0,l.N)(()=>{"idle"!==h&&v(h)},[h,v]),"loaded"===h?(0,c.jsx)(i.sG.img,{...p,ref:t,src:o}):null}).displayName=g;var m="AvatarFallback",x=n.forwardRef((e,t)=>{let{__scopeAvatar:r,delayMs:o,...a}=e,l=y(m,r),[s,u]=n.useState(void 0===o);return n.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>u(!0),o);return()=>window.clearTimeout(e)}},[o]),s&&"loaded"!==l.imageLoadingStatus?(0,c.jsx)(i.sG.span,{...a,ref:t}):null});function w(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}x.displayName=m;var b=v,k=x},4213:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]])},4416:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4835:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("log-out",[["path",{d:"m16 17 5-5-5-5",key:"1bji2h"}],["path",{d:"M21 12H9",key:"dn1m92"}],["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}]])},5452:(e,t,r)=>{r.d(t,{UC:()=>ee,VY:()=>er,ZL:()=>J,bL:()=>$,bm:()=>en,hE:()=>et,hJ:()=>Q});var n=r(2115),o=r(5185),a=r(6101),l=r(6081),i=r(1285),s=r(5845),u=r(9178),c=r(7900),d=r(4378),p=r(8905),f=r(3655),h=r(2293),y=r(3795),v=r(8168),g=r(9708),m=r(5155),x="Dialog",[w,b]=(0,l.A)(x),[k,C]=w(x),j=e=>{let{__scopeDialog:t,children:r,open:o,defaultOpen:a,onOpenChange:l,modal:u=!0}=e,c=n.useRef(null),d=n.useRef(null),[p,f]=(0,s.i)({prop:o,defaultProp:null!=a&&a,onChange:l,caller:x});return(0,m.jsx)(k,{scope:t,triggerRef:c,contentRef:d,contentId:(0,i.B)(),titleId:(0,i.B)(),descriptionId:(0,i.B)(),open:p,onOpenChange:f,onOpenToggle:n.useCallback(()=>f(e=>!e),[f]),modal:u,children:r})};j.displayName=x;var E="DialogTrigger";n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,l=C(E,r),i=(0,a.s)(t,l.triggerRef);return(0,m.jsx)(f.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":W(l.open),...n,ref:i,onClick:(0,o.m)(e.onClick,l.onOpenToggle)})}).displayName=E;var R="DialogPortal",[A,T]=w(R,{forceMount:void 0}),M=e=>{let{__scopeDialog:t,forceMount:r,children:o,container:a}=e,l=C(R,t);return(0,m.jsx)(A,{scope:t,forceMount:r,children:n.Children.map(o,e=>(0,m.jsx)(p.C,{present:r||l.open,children:(0,m.jsx)(d.Z,{asChild:!0,container:a,children:e})}))})};M.displayName=R;var D="DialogOverlay",L=n.forwardRef((e,t)=>{let r=T(D,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=C(D,e.__scopeDialog);return a.modal?(0,m.jsx)(p.C,{present:n||a.open,children:(0,m.jsx)(_,{...o,ref:t})}):null});L.displayName=D;var N=(0,g.TL)("DialogOverlay.RemoveScroll"),_=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(D,r);return(0,m.jsx)(y.A,{as:N,allowPinchZoom:!0,shards:[o.contentRef],children:(0,m.jsx)(f.sG.div,{"data-state":W(o.open),...n,ref:t,style:{pointerEvents:"auto",...n.style}})})}),O="DialogContent",P=n.forwardRef((e,t)=>{let r=T(O,e.__scopeDialog),{forceMount:n=r.forceMount,...o}=e,a=C(O,e.__scopeDialog);return(0,m.jsx)(p.C,{present:n||a.open,children:a.modal?(0,m.jsx)(I,{...o,ref:t}):(0,m.jsx)(S,{...o,ref:t})})});P.displayName=O;var I=n.forwardRef((e,t)=>{let r=C(O,e.__scopeDialog),l=n.useRef(null),i=(0,a.s)(t,r.contentRef,l);return n.useEffect(()=>{let e=l.current;if(e)return(0,v.Eq)(e)},[]),(0,m.jsx)(F,{...e,ref:i,trapFocus:r.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=r.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;(2===t.button||r)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),S=n.forwardRef((e,t)=>{let r=C(O,e.__scopeDialog),o=n.useRef(!1),a=n.useRef(!1);return(0,m.jsx)(F,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,l;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(l=r.triggerRef.current)||l.focus(),t.preventDefault()),o.current=!1,a.current=!1},onInteractOutside:t=>{var n,l;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));let i=t.target;(null==(l=r.triggerRef.current)?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}})}),F=n.forwardRef((e,t)=>{let{__scopeDialog:r,trapFocus:o,onOpenAutoFocus:l,onCloseAutoFocus:i,...s}=e,d=C(O,r),p=n.useRef(null),f=(0,a.s)(t,p);return(0,h.Oh)(),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:l,onUnmountAutoFocus:i,children:(0,m.jsx)(u.qW,{role:"dialog",id:d.contentId,"aria-describedby":d.descriptionId,"aria-labelledby":d.titleId,"data-state":W(d.open),...s,ref:f,onDismiss:()=>d.onOpenChange(!1)})}),(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(K,{titleId:d.titleId}),(0,m.jsx)(X,{contentRef:p,descriptionId:d.descriptionId})]})]})}),H="DialogTitle",V=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(H,r);return(0,m.jsx)(f.sG.h2,{id:o.titleId,...n,ref:t})});V.displayName=H;var q="DialogDescription",B=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,o=C(q,r);return(0,m.jsx)(f.sG.p,{id:o.descriptionId,...n,ref:t})});B.displayName=q;var z="DialogClose",G=n.forwardRef((e,t)=>{let{__scopeDialog:r,...n}=e,a=C(z,r);return(0,m.jsx)(f.sG.button,{type:"button",...n,ref:t,onClick:(0,o.m)(e.onClick,()=>a.onOpenChange(!1))})});function W(e){return e?"open":"closed"}G.displayName=z;var Z="DialogTitleWarning",[U,Y]=(0,l.q)(Z,{contentName:O,titleName:H,docsSlug:"dialog"}),K=e=>{let{titleId:t}=e,r=Y(Z),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return n.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},X=e=>{let{contentRef:t,descriptionId:r}=e,o=Y("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return n.useEffect(()=>{var e;let n=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");r&&n&&(document.getElementById(r)||console.warn(a))},[a,t,r]),null},$=j,J=M,Q=L,ee=P,et=V,er=B,en=G},5562:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("images",[["path",{d:"M18 22H4a2 2 0 0 1-2-2V6",key:"pblm9e"}],["path",{d:"m22 13-1.296-1.296a2.41 2.41 0 0 0-3.408 0L11 18",key:"nf6bnh"}],["circle",{cx:"12",cy:"8",r:"2",key:"1822b1"}],["rect",{width:"16",height:"16",x:"6",y:"2",rx:"2",key:"12espp"}]])},5695:(e,t,r)=>{var n=r(8999);r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}}),r.o(n,"useParams")&&r.d(t,{useParams:function(){return n.useParams}}),r.o(n,"usePathname")&&r.d(t,{usePathname:function(){return n.usePathname}}),r.o(n,"useRouter")&&r.d(t,{useRouter:function(){return n.useRouter}})},6654:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let n=r(2115);function o(e,t){let r=(0,n.useRef)(null),o=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(r.current=a(e,n)),t&&(o.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7108:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},7434:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7489:(e,t,r)=>{r.d(t,{b:()=>u});var n=r(2115),o=r(3655),a=r(5155),l="horizontal",i=["horizontal","vertical"],s=n.forwardRef((e,t)=>{var r;let{decorative:n,orientation:s=l,...u}=e,c=(r=s,i.includes(r))?s:l;return(0,a.jsx)(o.sG.div,{"data-orientation":c,...n?{role:"none"}:{"aria-orientation":"vertical"===c?c:void 0,role:"separator"},...u,ref:t})});s.displayName="Separator";var u=s},7576:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},7580:(e,t,r)=>{r.d(t,{A:()=>n});let n=(0,r(9946).A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},9613:(e,t,r)=>{r.d(t,{Kq:()=>G,UC:()=>Y,ZL:()=>U,bL:()=>W,i3:()=>K,l9:()=>Z});var n=r(2115),o=r(5185),a=r(6101),l=r(6081),i=r(9178),s=r(1285),u=r(5152),c=r(4378),d=r(8905),p=r(3655),f=r(9708),h=r(5845),y=r(2564),v=r(5155),[g,m]=(0,l.A)("Tooltip",[u.Bk]),x=(0,u.Bk)(),w="TooltipProvider",b="tooltip.open",[k,C]=g(w),j=e=>{let{__scopeTooltip:t,delayDuration:r=700,skipDelayDuration:o=300,disableHoverableContent:a=!1,children:l}=e,i=n.useRef(!0),s=n.useRef(!1),u=n.useRef(0);return n.useEffect(()=>{let e=u.current;return()=>window.clearTimeout(e)},[]),(0,v.jsx)(k,{scope:t,isOpenDelayedRef:i,delayDuration:r,onOpen:n.useCallback(()=>{window.clearTimeout(u.current),i.current=!1},[]),onClose:n.useCallback(()=>{window.clearTimeout(u.current),u.current=window.setTimeout(()=>i.current=!0,o)},[o]),isPointerInTransitRef:s,onPointerInTransitChange:n.useCallback(e=>{s.current=e},[]),disableHoverableContent:a,children:l})};j.displayName=w;var E="Tooltip",[R,A]=g(E),T=e=>{let{__scopeTooltip:t,children:r,open:o,defaultOpen:a,onOpenChange:l,disableHoverableContent:i,delayDuration:c}=e,d=C(E,e.__scopeTooltip),p=x(t),[f,y]=n.useState(null),g=(0,s.B)(),m=n.useRef(0),w=null!=i?i:d.disableHoverableContent,k=null!=c?c:d.delayDuration,j=n.useRef(!1),[A,T]=(0,h.i)({prop:o,defaultProp:null!=a&&a,onChange:e=>{e?(d.onOpen(),document.dispatchEvent(new CustomEvent(b))):d.onClose(),null==l||l(e)},caller:E}),M=n.useMemo(()=>A?j.current?"delayed-open":"instant-open":"closed",[A]),D=n.useCallback(()=>{window.clearTimeout(m.current),m.current=0,j.current=!1,T(!0)},[T]),L=n.useCallback(()=>{window.clearTimeout(m.current),m.current=0,T(!1)},[T]),N=n.useCallback(()=>{window.clearTimeout(m.current),m.current=window.setTimeout(()=>{j.current=!0,T(!0),m.current=0},k)},[k,T]);return n.useEffect(()=>()=>{m.current&&(window.clearTimeout(m.current),m.current=0)},[]),(0,v.jsx)(u.bL,{...p,children:(0,v.jsx)(R,{scope:t,contentId:g,open:A,stateAttribute:M,trigger:f,onTriggerChange:y,onTriggerEnter:n.useCallback(()=>{d.isOpenDelayedRef.current?N():D()},[d.isOpenDelayedRef,N,D]),onTriggerLeave:n.useCallback(()=>{w?L():(window.clearTimeout(m.current),m.current=0)},[L,w]),onOpen:D,onClose:L,disableHoverableContent:w,children:r})})};T.displayName=E;var M="TooltipTrigger",D=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...l}=e,i=A(M,r),s=C(M,r),c=x(r),d=n.useRef(null),f=(0,a.s)(t,d,i.onTriggerChange),h=n.useRef(!1),y=n.useRef(!1),g=n.useCallback(()=>h.current=!1,[]);return n.useEffect(()=>()=>document.removeEventListener("pointerup",g),[g]),(0,v.jsx)(u.Mz,{asChild:!0,...c,children:(0,v.jsx)(p.sG.button,{"aria-describedby":i.open?i.contentId:void 0,"data-state":i.stateAttribute,...l,ref:f,onPointerMove:(0,o.m)(e.onPointerMove,e=>{"touch"!==e.pointerType&&(y.current||s.isPointerInTransitRef.current||(i.onTriggerEnter(),y.current=!0))}),onPointerLeave:(0,o.m)(e.onPointerLeave,()=>{i.onTriggerLeave(),y.current=!1}),onPointerDown:(0,o.m)(e.onPointerDown,()=>{i.open&&i.onClose(),h.current=!0,document.addEventListener("pointerup",g,{once:!0})}),onFocus:(0,o.m)(e.onFocus,()=>{h.current||i.onOpen()}),onBlur:(0,o.m)(e.onBlur,i.onClose),onClick:(0,o.m)(e.onClick,i.onClose)})})});D.displayName=M;var L="TooltipPortal",[N,_]=g(L,{forceMount:void 0}),O=e=>{let{__scopeTooltip:t,forceMount:r,children:n,container:o}=e,a=A(L,t);return(0,v.jsx)(N,{scope:t,forceMount:r,children:(0,v.jsx)(d.C,{present:r||a.open,children:(0,v.jsx)(c.Z,{asChild:!0,container:o,children:n})})})};O.displayName=L;var P="TooltipContent",I=n.forwardRef((e,t)=>{let r=_(P,e.__scopeTooltip),{forceMount:n=r.forceMount,side:o="top",...a}=e,l=A(P,e.__scopeTooltip);return(0,v.jsx)(d.C,{present:n||l.open,children:l.disableHoverableContent?(0,v.jsx)(q,{side:o,...a,ref:t}):(0,v.jsx)(S,{side:o,...a,ref:t})})}),S=n.forwardRef((e,t)=>{let r=A(P,e.__scopeTooltip),o=C(P,e.__scopeTooltip),l=n.useRef(null),i=(0,a.s)(t,l),[s,u]=n.useState(null),{trigger:c,onClose:d}=r,p=l.current,{onPointerInTransitChange:f}=o,h=n.useCallback(()=>{u(null),f(!1)},[f]),y=n.useCallback((e,t)=>{let r=e.currentTarget,n={x:e.clientX,y:e.clientY},o=function(e,t){let r=Math.abs(t.top-e.y),n=Math.abs(t.bottom-e.y),o=Math.abs(t.right-e.x),a=Math.abs(t.left-e.x);switch(Math.min(r,n,o,a)){case a:return"left";case o:return"right";case r:return"top";case n:return"bottom";default:throw Error("unreachable")}}(n,r.getBoundingClientRect());u(function(e){let t=e.slice();return t.sort((e,t)=>e.x<t.x?-1:e.x>t.x?1:e.y<t.y?-1:1*!!(e.y>t.y)),function(e){if(e.length<=1)return e.slice();let t=[];for(let r=0;r<e.length;r++){let n=e[r];for(;t.length>=2;){let e=t[t.length-1],r=t[t.length-2];if((e.x-r.x)*(n.y-r.y)>=(e.y-r.y)*(n.x-r.x))t.pop();else break}t.push(n)}t.pop();let r=[];for(let t=e.length-1;t>=0;t--){let n=e[t];for(;r.length>=2;){let e=r[r.length-1],t=r[r.length-2];if((e.x-t.x)*(n.y-t.y)>=(e.y-t.y)*(n.x-t.x))r.pop();else break}r.push(n)}return(r.pop(),1===t.length&&1===r.length&&t[0].x===r[0].x&&t[0].y===r[0].y)?t:t.concat(r)}(t)}([...function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:5,n=[];switch(t){case"top":n.push({x:e.x-r,y:e.y+r},{x:e.x+r,y:e.y+r});break;case"bottom":n.push({x:e.x-r,y:e.y-r},{x:e.x+r,y:e.y-r});break;case"left":n.push({x:e.x+r,y:e.y-r},{x:e.x+r,y:e.y+r});break;case"right":n.push({x:e.x-r,y:e.y-r},{x:e.x-r,y:e.y+r})}return n}(n,o),...function(e){let{top:t,right:r,bottom:n,left:o}=e;return[{x:o,y:t},{x:r,y:t},{x:r,y:n},{x:o,y:n}]}(t.getBoundingClientRect())])),f(!0)},[f]);return n.useEffect(()=>()=>h(),[h]),n.useEffect(()=>{if(c&&p){let e=e=>y(e,p),t=e=>y(e,c);return c.addEventListener("pointerleave",e),p.addEventListener("pointerleave",t),()=>{c.removeEventListener("pointerleave",e),p.removeEventListener("pointerleave",t)}}},[c,p,y,h]),n.useEffect(()=>{if(s){let e=e=>{let t=e.target,r={x:e.clientX,y:e.clientY},n=(null==c?void 0:c.contains(t))||(null==p?void 0:p.contains(t)),o=!function(e,t){let{x:r,y:n}=e,o=!1;for(let e=0,a=t.length-1;e<t.length;a=e++){let l=t[e],i=t[a],s=l.x,u=l.y,c=i.x,d=i.y;u>n!=d>n&&r<(c-s)*(n-u)/(d-u)+s&&(o=!o)}return o}(r,s);n?h():o&&(h(),d())};return document.addEventListener("pointermove",e),()=>document.removeEventListener("pointermove",e)}},[c,p,s,d,h]),(0,v.jsx)(q,{...e,ref:i})}),[F,H]=g(E,{isInside:!1}),V=(0,f.Dc)("TooltipContent"),q=n.forwardRef((e,t)=>{let{__scopeTooltip:r,children:o,"aria-label":a,onEscapeKeyDown:l,onPointerDownOutside:s,...c}=e,d=A(P,r),p=x(r),{onClose:f}=d;return n.useEffect(()=>(document.addEventListener(b,f),()=>document.removeEventListener(b,f)),[f]),n.useEffect(()=>{if(d.trigger){let e=e=>{let t=e.target;(null==t?void 0:t.contains(d.trigger))&&f()};return window.addEventListener("scroll",e,{capture:!0}),()=>window.removeEventListener("scroll",e,{capture:!0})}},[d.trigger,f]),(0,v.jsx)(i.qW,{asChild:!0,disableOutsidePointerEvents:!1,onEscapeKeyDown:l,onPointerDownOutside:s,onFocusOutside:e=>e.preventDefault(),onDismiss:f,children:(0,v.jsxs)(u.UC,{"data-state":d.stateAttribute,...p,...c,ref:t,style:{...c.style,"--radix-tooltip-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-tooltip-content-available-width":"var(--radix-popper-available-width)","--radix-tooltip-content-available-height":"var(--radix-popper-available-height)","--radix-tooltip-trigger-width":"var(--radix-popper-anchor-width)","--radix-tooltip-trigger-height":"var(--radix-popper-anchor-height)"},children:[(0,v.jsx)(V,{children:o}),(0,v.jsx)(F,{scope:r,isInside:!0,children:(0,v.jsx)(y.bL,{id:d.contentId,role:"tooltip",children:a||o})})]})})});I.displayName=P;var B="TooltipArrow",z=n.forwardRef((e,t)=>{let{__scopeTooltip:r,...n}=e,o=x(r);return H(B,r).isInside?null:(0,v.jsx)(u.i3,{...o,...n,ref:t})});z.displayName=B;var G=j,W=T,Z=D,U=O,Y=I,K=z},9946:(e,t,r)=>{r.d(t,{A:()=>s});var n=r(2115);let o=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},a=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let i=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:o=24,strokeWidth:i=2,absoluteStrokeWidth:s,className:u="",children:c,iconNode:d,...p}=e;return(0,n.createElement)("svg",{ref:t,...l,width:o,height:o,stroke:r,strokeWidth:s?24*Number(i)/Number(o):i,className:a("lucide",u),...!c&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(p)&&{"aria-hidden":"true"},...p},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(c)?c:[c]])}),s=(e,t)=>{let r=(0,n.forwardRef)((r,l)=>{let{className:s,...u}=r;return(0,n.createElement)(i,{ref:l,iconNode:t,className:a("lucide-".concat(o(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),s),...u})});return r.displayName=o(e),r}}}]);