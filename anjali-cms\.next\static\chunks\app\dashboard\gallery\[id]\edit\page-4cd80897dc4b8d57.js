(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9347],{1206:(e,t,l)=>{Promise.resolve().then(l.bind(l,4273))},4273:(e,t,l)=>{"use strict";l.r(t),l.d(t,{default:()=>d});var a=l(5155),i=l(2115),s=l(5695),r=l(7605),n=l(6671);function d(){let e=(0,s.useParams)(),[t,l]=(0,i.useState)(null),[d,c]=(0,i.useState)(!0);return((0,i.useEffect)(()=>{let t=async()=>{try{let t=await fetch("/api/gallery/".concat(e.id));if(t.ok){let e=await t.json();l(e)}else n.oR.error("Gallery item not found")}catch(e){n.oR.error("Error fetching gallery item")}finally{c(!1)}};e.id&&t()},[e.id]),d)?(0,a.jsx)("div",{children:"Loading..."}):t?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Gallery Item"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Update the details and settings for this gallery image"})]}),(0,a.jsx)(r.GalleryForm,{initialData:t,isEditing:!0})]}):(0,a.jsx)("div",{children:"Gallery item not found"})}}},e=>{e.O(0,[5389,6671,651,7536,7764,8062,2804,9304,3780,8441,5964,7358],()=>e(e.s=1206)),_N_E=e.O()}]);