(()=>{var a={};a.id=5620,a.ids=[5620],a.modules={261:a=>{"use strict";a.exports=require("next/dist/shared/lib/router/utils/app-paths")},3295:a=>{"use strict";a.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11723:a=>{"use strict";a.exports=require("querystring")},12412:a=>{"use strict";a.exports=require("assert")},12909:(a,b,c)=>{"use strict";c.d(b,{N:()=>h});var d=c(13581),e=c(16467),f=c(94747),g=c(85663);let h={adapter:(0,e.y)(f.z),providers:[(0,d.A)({name:"credentials",credentials:{email:{label:"Email",type:"email"},password:{label:"Password",type:"password"}},async authorize(a){if(!a?.email||!a?.password)return null;let b=await f.z.user.findUnique({where:{email:a.email}});return b&&await g.Ay.compare(a.password,b.password)?{id:b.id,email:b.email,name:b.name,role:b.role}:null}})],session:{strategy:"jwt"},callbacks:{jwt:async({token:a,user:b})=>(b&&(a.id=b.id,a.role=b.role),a),session:async({session:a,token:b})=>(b&&(a.user.id=b.id,a.user.role=b.role),a)},pages:{signIn:"/auth/signin"}}},28354:a=>{"use strict";a.exports=require("util")},29294:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:a=>{"use strict";a.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},54879:(a,b,c)=>{"use strict";c.r(b),c.d(b,{handler:()=>L,patchFetch:()=>K,routeModule:()=>G,serverHooks:()=>J,workAsyncStorage:()=>H,workUnitAsyncStorage:()=>I});var d={};c.r(d),c.d(d,{GET:()=>y});var e=c(96559),f=c(48088),g=c(37719),h=c(26191),i=c(81289),j=c(261),k=c(92603),l=c(39893),m=c(14823),n=c(47220),o=c(66946),p=c(47912),q=c(99786),r=c(46143),s=c(86439),t=c(43365),u=c(32190),v=c(19854),w=c(12909),x=c(94747);async function y(a){try{let b,c;if(!await (0,v.getServerSession)(w.N))return u.NextResponse.json({error:"Unauthorized"},{status:401});let{searchParams:d}=new URL(a.url),e=d.get("type");if(d.get("format"),!e)return u.NextResponse.json({error:"Export type is required"},{status:400});switch(e){case"blogs":b=await z(),c=`blogs-export-${new Date().toISOString().split("T")[0]}.json`;break;case"services":b=await A(),c=`services-export-${new Date().toISOString().split("T")[0]}.json`;break;case"packages":b=await E(),c=`packages-export-${new Date().toISOString().split("T")[0]}.json`;break;case"testimonials":b=await B(),c=`testimonials-export-${new Date().toISOString().split("T")[0]}.json`;break;case"gallery":b=await C(),c=`gallery-export-${new Date().toISOString().split("T")[0]}.json`;break;case"settings":b=await D(),c=`settings-export-${new Date().toISOString().split("T")[0]}.json`;break;case"all":b=await F(),c=`full-backup-${new Date().toISOString().split("T")[0]}.json`;break;default:return u.NextResponse.json({error:"Invalid export type"},{status:400})}let f=new u.NextResponse(JSON.stringify(b,null,2));return f.headers.set("Content-Type","application/json"),f.headers.set("Content-Disposition",`attachment; filename="${c}"`),f}catch(a){return console.error("Export error:",a),u.NextResponse.json({error:"Export failed"},{status:500})}}async function z(){let a=await x.z.blog.findMany({include:{category:!0,tags:{include:{tag:!0}}},orderBy:{createdAt:"desc"}});return{type:"blogs",exportDate:new Date().toISOString(),count:a.length,data:a.map(a=>({id:a.id,title:a.title,slug:a.slug,excerpt:a.excerpt,content:a.content,author:a.author,status:a.status,featured:a.featured,image:a.image,readTime:a.readTime,metaTitle:a.metaTitle,metaDescription:a.metaDescription,keywords:a.keywords,category:a.category?.name,tags:a.tags.map(a=>a.tag.name),publishedAt:a.publishedAt,createdAt:a.createdAt,updatedAt:a.updatedAt}))}}async function A(){let a=await x.z.service.findMany({orderBy:{createdAt:"desc"}});return{type:"services",exportDate:new Date().toISOString(),count:a.length,data:a}}async function B(){let a=await x.z.testimonial.findMany({orderBy:{createdAt:"desc"}});return{type:"testimonials",exportDate:new Date().toISOString(),count:a.length,data:a}}async function C(){let a=await x.z.gallery.findMany({orderBy:{createdAt:"desc"}});return{type:"gallery",exportDate:new Date().toISOString(),count:a.length,data:a}}async function D(){let a=await x.z.siteConfig.findMany({orderBy:{key:"asc"}});return{type:"settings",exportDate:new Date().toISOString(),count:a.length,data:a}}async function E(){let a=await x.z.package.findMany({orderBy:{createdAt:"desc"}});return{type:"packages",exportDate:new Date().toISOString(),count:a.length,data:a}}async function F(){let[a,b,c,d,e,f]=await Promise.all([z(),A(),E(),B(),C(),D()]);return{type:"full_backup",exportDate:new Date().toISOString(),version:"1.0",blogs:a,services:b,packages:c,testimonials:d,gallery:e,settings:f}}let G=new e.AppRouteRouteModule({definition:{kind:f.RouteKind.APP_ROUTE,page:"/api/export/route",pathname:"/api/export",filename:"route",bundlePath:"app/api/export/route"},distDir:".next",projectDir:"",resolvedPagePath:"C:\\Users\\<USER>\\Desktop\\ashish\\anjali-portfolio\\anjali-cms\\src\\app\\api\\export\\route.ts",nextConfigOutput:"",userland:d}),{workAsyncStorage:H,workUnitAsyncStorage:I,serverHooks:J}=G;function K(){return(0,g.patchFetch)({workAsyncStorage:H,workUnitAsyncStorage:I})}async function L(a,b,c){var d;let e="/api/export/route";"/index"===e&&(e="/");let g=await G.prepare(a,b,{srcPage:e,multiZoneDraftMode:"false"});if(!g)return b.statusCode=400,b.end("Bad Request"),null==c.waitUntil||c.waitUntil.call(c,Promise.resolve()),null;let{buildId:u,params:v,nextConfig:w,isDraftMode:x,prerenderManifest:y,routerServerContext:z,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,resolvedPathname:C}=g,D=(0,j.normalizeAppPath)(e),E=!!(y.dynamicRoutes[D]||y.routes[C]);if(E&&!x){let a=!!y.routes[C],b=y.dynamicRoutes[D];if(b&&!1===b.fallback&&!a)throw new s.NoFallbackError}let F=null;!E||G.isDev||x||(F="/index"===(F=C)?"/":F);let H=!0===G.isDev||!E,I=E&&!H,J=a.method||"GET",K=(0,i.getTracer)(),L=K.getActiveScopeSpan(),M={params:v,prerenderManifest:y,renderOpts:{experimental:{dynamicIO:!!w.experimental.dynamicIO,authInterrupts:!!w.experimental.authInterrupts},supportsDynamicResponse:H,incrementalCache:(0,h.getRequestMeta)(a,"incrementalCache"),cacheLifeProfiles:null==(d=w.experimental)?void 0:d.cacheLife,isRevalidate:I,waitUntil:c.waitUntil,onClose:a=>{b.on("close",a)},onAfterTaskError:void 0,onInstrumentationRequestError:(b,c,d)=>G.onRequestError(a,b,d,z)},sharedContext:{buildId:u}},N=new k.NodeNextRequest(a),O=new k.NodeNextResponse(b),P=l.NextRequestAdapter.fromNodeNextRequest(N,(0,l.signalFromNodeResponse)(b));try{let d=async c=>G.handle(P,M).finally(()=>{if(!c)return;c.setAttributes({"http.status_code":b.statusCode,"next.rsc":!1});let d=K.getRootSpanAttributes();if(!d)return;if(d.get("next.span_type")!==m.BaseServerSpan.handleRequest)return void console.warn(`Unexpected root span type '${d.get("next.span_type")}'. Please report this Next.js issue https://github.com/vercel/next.js`);let e=d.get("next.route");if(e){let a=`${J} ${e}`;c.setAttributes({"next.route":e,"http.route":e,"next.span_name":a}),c.updateName(a)}else c.updateName(`${J} ${a.url}`)}),g=async g=>{var i,j;let k=async({previousCacheEntry:f})=>{try{if(!(0,h.getRequestMeta)(a,"minimalMode")&&A&&B&&!f)return b.statusCode=404,b.setHeader("x-nextjs-cache","REVALIDATED"),b.end("This page could not be found"),null;let e=await d(g);a.fetchMetrics=M.renderOpts.fetchMetrics;let i=M.renderOpts.pendingWaitUntil;i&&c.waitUntil&&(c.waitUntil(i),i=void 0);let j=M.renderOpts.collectedTags;if(!E)return await (0,o.I)(N,O,e,M.renderOpts.pendingWaitUntil),null;{let a=await e.blob(),b=(0,p.toNodeOutgoingHttpHeaders)(e.headers);j&&(b[r.NEXT_CACHE_TAGS_HEADER]=j),!b["content-type"]&&a.type&&(b["content-type"]=a.type);let c=void 0!==M.renderOpts.collectedRevalidate&&!(M.renderOpts.collectedRevalidate>=r.INFINITE_CACHE)&&M.renderOpts.collectedRevalidate,d=void 0===M.renderOpts.collectedExpire||M.renderOpts.collectedExpire>=r.INFINITE_CACHE?void 0:M.renderOpts.collectedExpire;return{value:{kind:t.CachedRouteKind.APP_ROUTE,status:e.status,body:Buffer.from(await a.arrayBuffer()),headers:b},cacheControl:{revalidate:c,expire:d}}}}catch(b){throw(null==f?void 0:f.isStale)&&await G.onRequestError(a,b,{routerKind:"App Router",routePath:e,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})},z),b}},l=await G.handleResponse({req:a,nextConfig:w,cacheKey:F,routeKind:f.RouteKind.APP_ROUTE,isFallback:!1,prerenderManifest:y,isRoutePPREnabled:!1,isOnDemandRevalidate:A,revalidateOnlyGenerated:B,responseGenerator:k,waitUntil:c.waitUntil});if(!E)return null;if((null==l||null==(i=l.value)?void 0:i.kind)!==t.CachedRouteKind.APP_ROUTE)throw Object.defineProperty(Error(`Invariant: app-route received invalid cache entry ${null==l||null==(j=l.value)?void 0:j.kind}`),"__NEXT_ERROR_CODE",{value:"E701",enumerable:!1,configurable:!0});(0,h.getRequestMeta)(a,"minimalMode")||b.setHeader("x-nextjs-cache",A?"REVALIDATED":l.isMiss?"MISS":l.isStale?"STALE":"HIT"),x&&b.setHeader("Cache-Control","private, no-cache, no-store, max-age=0, must-revalidate");let m=(0,p.fromNodeOutgoingHttpHeaders)(l.value.headers);return(0,h.getRequestMeta)(a,"minimalMode")&&E||m.delete(r.NEXT_CACHE_TAGS_HEADER),!l.cacheControl||b.getHeader("Cache-Control")||m.get("Cache-Control")||m.set("Cache-Control",(0,q.getCacheControlHeader)(l.cacheControl)),await (0,o.I)(N,O,new Response(l.value.body,{headers:m,status:l.value.status||200})),null};L?await g(L):await K.withPropagatedContext(a.headers,()=>K.trace(m.BaseServerSpan.handleRequest,{spanName:`${J} ${a.url}`,kind:i.SpanKind.SERVER,attributes:{"http.method":J,"http.target":a.url}},g))}catch(b){if(L||await G.onRequestError(a,b,{routerKind:"App Router",routePath:D,routeType:"route",revalidateReason:(0,n.c)({isRevalidate:I,isOnDemandRevalidate:A})}),E)throw b;return await (0,o.I)(N,O,new Response(null,{status:500})),null}}},55511:a=>{"use strict";a.exports=require("crypto")},55591:a=>{"use strict";a.exports=require("https")},63033:a=>{"use strict";a.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74075:a=>{"use strict";a.exports=require("zlib")},78335:()=>{},79428:a=>{"use strict";a.exports=require("buffer")},79551:a=>{"use strict";a.exports=require("url")},81630:a=>{"use strict";a.exports=require("http")},86439:a=>{"use strict";a.exports=require("next/dist/shared/lib/no-fallback-error.external")},94735:a=>{"use strict";a.exports=require("events")},94747:(a,b,c)=>{"use strict";c.d(b,{z:()=>e});let d=require("@prisma/client"),e=globalThis.prisma??new d.PrismaClient},96487:()=>{}};var b=require("../../../webpack-runtime.js");b.C(a);var c=b.X(0,[2073,6055,8896],()=>b(b.s=54879));module.exports=c})();