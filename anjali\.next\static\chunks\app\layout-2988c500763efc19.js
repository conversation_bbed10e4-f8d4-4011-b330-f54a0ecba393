(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},1335:(e,t,s)=>{"use strict";s.d(t,{BD:()=>m,KA:()=>n,S0:()=>d,Tr:()=>x,cl:()=>o,gf:()=>c,iF:()=>l,iw:()=>i,uJ:()=>r});let a=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};"function"==typeof window.gtag&&window.gtag("event",e,{event_category:t.category||"general",event_label:t.label,value:t.value,...t})},r=(e,t)=>{a("whatsapp_click",{category:"contact",label:e,custom_parameter_1:"whatsapp",message_type:t?"custom":"default"})},n=e=>{a("phone_click",{category:"contact",label:e,custom_parameter_1:"phone"})},o=e=>{a("email_click",{category:"contact",label:e,custom_parameter_1:"email"})},l=e=>{a("contact_form_submit",{category:"lead_generation",label:e.service||"general_inquiry",custom_parameter_1:e.service,custom_parameter_2:e.source||"contact_page"})},i=(e,t,s)=>{a("service_inquiry",{category:"lead_generation",label:t,custom_parameter_1:e,custom_parameter_2:s,service_name:t})},c=(e,t)=>{a("portfolio_image_view",{category:"portfolio",label:t,custom_parameter_1:e,custom_parameter_2:t})},d=e=>{a("gallery_filter",{category:"portfolio",label:e,custom_parameter_1:e})},m=(e,t,s)=>{a("blog_post_view",{category:"content",label:t,custom_parameter_1:e,custom_parameter_2:s||"blog",post_title:t})},x=(e,t)=>{a("social_click",{category:"social_media",label:e,custom_parameter_1:e,custom_parameter_2:t})}},1650:(e,t,s)=>{"use strict";s.d(t,{default:()=>o});var a=s(5155),r=s(3554),n=s(1710);function o(){let e=(0,n.Q2)().analytics.googleAnalyticsId;return e&&"G-XXXXXXXXXX"!==e?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.default,{src:"https://www.googletagmanager.com/gtag/js?id=".concat(e),strategy:"afterInteractive"}),(0,a.jsx)(r.default,{id:"google-analytics",strategy:"afterInteractive",children:"\n          window.dataLayer = window.dataLayer || [];\n          function gtag(){dataLayer.push(arguments);}\n          gtag('js', new Date());\n\n          gtag('config', '".concat(e,"', {\n            page_title: document.title,\n            page_location: window.location.href,\n            send_page_view: true,\n            // Enhanced ecommerce and engagement tracking\n            custom_map: {\n              'custom_parameter_1': 'service_type',\n              'custom_parameter_2': 'package_type'\n            },\n            // User engagement tracking\n            engagement_time_msec: 100,\n            // Conversion tracking\n            allow_enhanced_conversions: true,\n            // Privacy settings\n            anonymize_ip: true,\n            allow_google_signals: true,\n            allow_ad_personalization_signals: true\n          });\n\n          // Track scroll depth\n          let scrollDepth = 0;\n          window.addEventListener('scroll', function() {\n            const scrollPercent = Math.round((window.scrollY / (document.body.scrollHeight - window.innerHeight)) * 100);\n            if (scrollPercent > scrollDepth && scrollPercent % 25 === 0) {\n              scrollDepth = scrollPercent;\n              gtag('event', 'scroll_depth', {\n                event_category: 'engagement',\n                event_label: scrollPercent + '%',\n                value: scrollPercent\n              });\n            }\n          });\n\n          // Track time on page\n          let startTime = Date.now();\n          window.addEventListener('beforeunload', function() {\n            const timeOnPage = Math.round((Date.now() - startTime) / 1000);\n            gtag('event', 'time_on_page', {\n              event_category: 'engagement',\n              event_label: 'seconds',\n              value: timeOnPage\n            });\n          });\n        ")})]}):null}},2262:(e,t,s)=>{"use strict";s.d(t,{default:()=>j});var a=s(5155),r=s(2115),n=s(6874),o=s.n(n),l=s(760),i=s(2605),c=s(5684),d=s(488),m=s(9420),x=s(4416),h=s(4783),g=s(285),p=s(1710),f=s(9434),u=s(7509);let v=[{name:"Home",href:"/"},{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Packages",href:"/packages"},{name:"Portfolio",href:"/portfolio"},{name:"Blog",href:"/blog"},{name:"Contact",href:"/contact"}];function j(){let[e,t]=(0,r.useState)(!1),[s,n]=(0,r.useState)(!1),j=(0,p.Q2)(),k=(0,p.PZ)();(0,r.useEffect)(()=>{let e=()=>{n(window.scrollY>10)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]);let b=(0,f.ec)(j.contact.whatsapp,j.whatsappMessage);return(0,a.jsxs)("header",{className:(0,f.cn)("fixed top-0 left-0 right-0 z-50 transition-all duration-300",s?"bg-white/95 backdrop-blur-md shadow-md":"bg-white"),children:[(0,a.jsx)("nav",{id:"navigation",className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex h-16 items-center justify-between",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsxs)(o(),{href:"/",className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-gradient-to-r from-rose-gold to-blush-pink flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-display font-bold text-lg",children:"A"})}),(0,a.jsx)("span",{className:"font-display text-xl font-semibold text-text-primary",children:j.site.name})]})}),(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsx)("div",{className:"ml-10 flex items-baseline space-x-4",children:v.map(e=>(0,a.jsx)(o(),{href:e.href,className:"text-text-primary hover:text-rose-gold-dark px-3 py-2 rounded-md text-sm font-medium transition-colors",children:e.name},e.name))})}),(0,a.jsxs)("div",{className:"hidden md:flex items-center space-x-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)(o(),{href:k.instagram,target:"_blank",rel:"noopener noreferrer",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:(0,a.jsx)(c.A,{className:"h-5 w-5"})}),(0,a.jsx)(o(),{href:k.facebook,target:"_blank",rel:"noopener noreferrer",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:(0,a.jsx)(d.A,{className:"h-5 w-5"})}),(0,a.jsx)(o(),{href:k.tiktok,target:"_blank",rel:"noopener noreferrer",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:(0,a.jsx)(u.H6Z,{className:"h-5 w-5"})})]}),(0,a.jsx)(g.$,{asChild:!0,variant:"gradient",size:"sm",children:(0,a.jsxs)(o(),{href:b,target:"_blank",rel:"noopener noreferrer",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),"Book Now"]})})]}),(0,a.jsx)("div",{className:"md:hidden",children:(0,a.jsx)(g.$,{variant:"ghost",size:"icon",onClick:()=>t(!e),"aria-label":"Toggle menu",children:e?(0,a.jsx)(x.A,{className:"h-6 w-6"}):(0,a.jsx)(h.A,{className:"h-6 w-6"})})})]})}),(0,a.jsx)(l.N,{children:e&&(0,a.jsx)(i.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},className:"md:hidden bg-white border-t border-gray-200",children:(0,a.jsxs)("div",{className:"px-2 pt-2 pb-3 space-y-1 sm:px-3",children:[v.map(e=>(0,a.jsx)(o(),{href:e.href,className:"text-text-primary hover:text-rose-gold-dark block px-3 py-2 rounded-md text-base font-medium transition-colors",onClick:()=>t(!1),children:e.name},e.name)),(0,a.jsx)("div",{className:"pt-4 pb-2 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"flex items-center justify-between px-3",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)(o(),{href:k.instagram,target:"_blank",rel:"noopener noreferrer",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:(0,a.jsx)(c.A,{className:"h-6 w-6"})}),(0,a.jsx)(o(),{href:k.facebook,target:"_blank",rel:"noopener noreferrer",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:(0,a.jsx)(d.A,{className:"h-6 w-6"})}),(0,a.jsx)(o(),{href:k.tiktok,target:"_blank",rel:"noopener noreferrer",className:"text-text-secondary hover:text-rose-gold-dark transition-colors",children:(0,a.jsx)(u.H6Z,{className:"h-6 w-6"})})]}),(0,a.jsx)(g.$,{asChild:!0,variant:"gradient",size:"sm",children:(0,a.jsxs)(o(),{href:b,target:"_blank",rel:"noopener noreferrer",children:[(0,a.jsx)(m.A,{className:"h-4 w-4"}),"Book Now"]})})]})})]})})})]})}},3182:(e,t,s)=>{"use strict";s.d(t,{default:()=>c});var a=s(5155),r=s(2115),n=s(760),o=s(2605),l=s(9881),i=s(285);function c(){let[e,t]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{let e=()=>{window.pageYOffset>300?t(!0):t(!1)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,a.jsx)(n.N,{children:e&&(0,a.jsx)(o.P.div,{initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.2},className:"fixed bottom-8 right-8 z-50",children:(0,a.jsx)(i.$,{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},size:"icon",variant:"gradient",className:"w-12 h-12 rounded-full shadow-lg hover:shadow-xl transition-shadow duration-300","aria-label":"Scroll to top",children:(0,a.jsx)(l.A,{className:"w-5 h-5"})})})})}},4143:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,598,23)),Promise.resolve().then(s.t.bind(s,6253,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,6996)),Promise.resolve().then(s.bind(s,2262)),Promise.resolve().then(s.bind(s,1650)),Promise.resolve().then(s.bind(s,3182)),Promise.resolve().then(s.bind(s,4774))},4774:(e,t,s)=>{"use strict";s.d(t,{QueryProvider:()=>i});var a=s(5155),r=s(432),n=s(6715),o=s(192),l=s(2115);function i(e){let{children:t}=e,[s]=(0,l.useState)(()=>new r.E({defaultOptions:{queries:{staleTime:6e4,gcTime:6e5,retry:(e,t)=>(!((null==t?void 0:t.status)>=400)||!((null==t?void 0:t.status)<500))&&e<3,refetchOnWindowFocus:!1,refetchOnMount:!0,refetchOnReconnect:!0},mutations:{retry:!1}}}));return(0,a.jsxs)(n.Ht,{client:s,children:[t,(0,a.jsx)(o.E,{initialIsOpen:!1})]})}},6996:(e,t,s)=>{"use strict";s.d(t,{default:()=>v});var a=s(5155),r=s(6874),n=s.n(r),o=s(488),l=s(5684),i=s(4516),c=s(9420),d=s(1264),m=s(4186),x=s(1710),h=s(9434),g=s(7509),p=s(1335);let f=[{name:"About",href:"/about"},{name:"Services",href:"/services"},{name:"Packages",href:"/packages"},{name:"Portfolio",href:"/portfolio"},{name:"Blog",href:"/blog"},{name:"Contact",href:"/contact"}],u=[{name:"Bridal Makeup",href:"/services#bridal-makeup"},{name:"Party Makeup",href:"/services#party-makeup"},{name:"Engagement Makeup",href:"/services#engagement-makeup"},{name:"Traditional Makeup",href:"/services#traditional-makeup"},{name:"Photoshoot Makeup",href:"/services#photoshoot-makeup"},{name:"Makeup Lessons",href:"/services#makeup-lessons"}];function v(){let e=(0,x.Q2)(),t=(0,x.PZ)(),s=(0,h.ec)(e.contact.whatsapp,e.whatsappMessage);return(0,a.jsx)("footer",{className:"bg-gradient-to-br from-cream to-soft-gray",children:(0,a.jsxs)("div",{className:"mx-auto max-w-7xl px-4 py-12 sm:px-6 lg:px-8",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"h-8 w-8 rounded-full bg-gradient-to-r from-rose-gold to-blush-pink flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-display font-bold text-lg",children:"A"})}),(0,a.jsx)("span",{className:"font-display text-xl font-semibold text-text-primary",children:e.site.name})]}),(0,a.jsx)("p",{className:"text-text-secondary text-sm leading-relaxed",children:e.site.description}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsx)(n(),{href:t.facebook,target:"_blank",rel:"noopener noreferrer",className:"text-text-secondary hover:text-rose-gold-dark transition-colors","aria-label":"Facebook",onClick:()=>(0,p.Tr)("facebook","footer"),children:(0,a.jsx)(o.A,{className:"h-5 w-5"})}),(0,a.jsx)(n(),{href:t.instagram,target:"_blank",rel:"noopener noreferrer",className:"text-text-secondary hover:text-rose-gold-dark transition-colors","aria-label":"Instagram",onClick:()=>(0,p.Tr)("instagram","footer"),children:(0,a.jsx)(l.A,{className:"h-5 w-5"})}),(0,a.jsx)(n(),{href:t.tiktok,target:"_blank",rel:"noopener noreferrer",className:"text-text-secondary hover:text-rose-gold-dark transition-colors","aria-label":"Tiktok",onClick:()=>(0,p.Tr)("tiktok","footer"),children:(0,a.jsx)(g.H6Z,{className:"h-5 w-5"})})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-display text-lg font-semibold text-text-primary mb-4",children:"Quick Links"}),(0,a.jsx)("ul",{className:"space-y-2",children:f.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:e.href,className:"text-text-secondary hover:text-rose-gold-dark transition-colors text-sm",children:e.name})},e.name))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-display text-lg font-semibold text-text-primary mb-4",children:"Services"}),(0,a.jsx)("ul",{className:"space-y-2",children:u.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(n(),{href:e.href,className:"text-text-secondary hover:text-rose-gold-dark transition-colors text-sm",children:e.name})},e.name))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-display text-lg font-semibold text-text-primary mb-4",children:"Contact Info"}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(i.A,{className:"h-4 w-4 text-rose-gold-dark mt-1 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-text-secondary",children:[(0,a.jsx)("p",{children:e.contact.address.street}),(0,a.jsxs)("p",{children:[e.contact.address.city,", ",e.contact.address.state]}),(0,a.jsx)("p",{children:e.contact.address.country})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(c.A,{className:"h-4 w-4 text-rose-gold-dark flex-shrink-0"}),(0,a.jsx)(n(),{href:s,target:"_blank",rel:"noopener noreferrer",className:"text-sm text-text-secondary hover:text-rose-gold-dark transition-colors",onClick:()=>(0,p.uJ)("footer","contact_info"),children:e.contact.phone})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(d.A,{className:"h-4 w-4 text-rose-gold-dark flex-shrink-0"}),(0,a.jsx)(n(),{href:"mailto:".concat(e.contact.email),className:"text-sm text-text-secondary hover:text-rose-gold-dark transition-colors",onClick:()=>(0,p.cl)("footer"),children:e.contact.email})]}),(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(m.A,{className:"h-4 w-4 text-rose-gold-dark mt-1 flex-shrink-0"}),(0,a.jsxs)("div",{className:"text-sm text-text-secondary",children:[(0,a.jsx)("p",{children:"Sun-Fri: 9:00 AM - 6:00 PM"}),(0,a.jsx)("p",{children:"Sat: 10:00 AM - 4:00 PM"})]})]})]})]})]}),(0,a.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h4",{className:"font-display text-lg font-semibold text-text-primary mb-3",children:"Service Areas"}),(0,a.jsxs)("p",{className:"text-text-secondary text-sm",children:["Serving clients across"," ",e.serviceAreas.map((t,s)=>(0,a.jsxs)("span",{children:[t.name,s<e.serviceAreas.length-1&&", "]},t.name))]})]})}),(0,a.jsx)("div",{className:"mt-8 pt-8 border-t border-gray-200",children:(0,a.jsxs)("div",{className:"text-center space-y-2",children:[(0,a.jsxs)("p",{className:"text-text-muted text-sm",children:["\xa9 ",new Date().getFullYear()," ",e.site.name,". All rights reserved."]}),(0,a.jsxs)("p",{className:"text-text-muted text-xs",children:["Designed and developed with ❤️ by"," ",(0,a.jsx)(n(),{href:"https://ashishkamat.com.np/",target:"_blank",rel:"noopener noreferrer",className:"text-rose-gold-dark hover:text-rose-gold transition-colors underline underline-offset-2",children:"Ashish Kamat"})]})]})})]})})}}},e=>{e.O(0,[515,506,277,699,605,967,873,578,441,964,358],()=>e(e.s=4143)),_N_E=e.O()}]);