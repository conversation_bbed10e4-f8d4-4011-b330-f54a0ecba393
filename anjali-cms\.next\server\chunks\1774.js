"use strict";exports.id=1774,exports.ids=[1774],exports.modules={15079:(a,b,c)=>{c.d(b,{bq:()=>l,eb:()=>n,gC:()=>m,l6:()=>j,yv:()=>k});var d=c(60687);c(43210);var e=c(72951),f=c(78272),g=c(13964),h=c(3589),i=c(4780);function j({...a}){return(0,d.jsx)(e.bL,{"data-slot":"select",...a})}function k({...a}){return(0,d.jsx)(e.WT,{"data-slot":"select-value",...a})}function l({className:a,size:b="default",children:c,...g}){return(0,d.jsxs)(e.l9,{"data-slot":"select-trigger","data-size":b,className:(0,i.cn)("border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...g,children:[c,(0,d.jsx)(e.In,{asChild:!0,children:(0,d.jsx)(f.A,{className:"size-4 opacity-50"})})]})}function m({className:a,children:b,position:c="popper",...f}){return(0,d.jsx)(e.ZL,{children:(0,d.jsxs)(e.UC,{"data-slot":"select-content",className:(0,i.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md","popper"===c&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:c,...f,children:[(0,d.jsx)(o,{}),(0,d.jsx)(e.LM,{className:(0,i.cn)("p-1","popper"===c&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1"),children:b}),(0,d.jsx)(p,{})]})})}function n({className:a,children:b,...c}){return(0,d.jsxs)(e.q7,{"data-slot":"select-item",className:(0,i.cn)("focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",a),...c,children:[(0,d.jsx)("span",{className:"absolute right-2 flex size-3.5 items-center justify-center",children:(0,d.jsx)(e.VF,{children:(0,d.jsx)(g.A,{className:"size-4"})})}),(0,d.jsx)(e.p4,{children:b})]})}function o({className:a,...b}){return(0,d.jsx)(e.PP,{"data-slot":"select-scroll-up-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(h.A,{className:"size-4"})})}function p({className:a,...b}){return(0,d.jsx)(e.wn,{"data-slot":"select-scroll-down-button",className:(0,i.cn)("flex cursor-default items-center justify-center py-1",a),...b,children:(0,d.jsx)(f.A,{className:"size-4"})})}},19312:(a,b,c)=>{c.d(b,{B:()=>l});var d=c(60687),e=c(43210),f=c(29523),g=c(80013),h=c(11860),i=c(41862),j=c(16023),k=c(30474);function l({value:a,onChange:b,onRemove:c,disabled:l,folder:m="anjali-cms",label:n="Image"}){let[o,p]=(0,e.useState)(!1),[q,r]=(0,e.useState)(0),[s,t]=(0,e.useState)(null),u=(0,e.useRef)(null),v="groceease",w=async a=>{if(!v)throw Error("Cloudinary cloud name not configured");let b=await fetch("/api/cloudinary-signature",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({folder:m})});if(!b.ok)throw Error("Failed to get upload signature");let c=await b.json();console.log("Signature data received:",c);let d=new FormData;for(let[b,e]of(d.append("file",a),d.append("signature",c.signature),d.append("timestamp",c.timestamp.toString()),d.append("api_key",c.api_key),d.append("folder",m),console.log("Upload parameters:"),d.entries()))"file"!==b&&console.log(`${b}: ${e}`);let e=await fetch(`https://api.cloudinary.com/v1_1/${v}/image/upload`,{method:"POST",body:d});if(!e.ok){let a=await e.json();throw Error(a.error?.message||"Upload failed")}return e.json()},x=async a=>{let c=a.target.files?.[0];if(c){if(!["image/jpeg","image/jpg","image/png","image/gif","image/webp"].includes(c.type))return void t("Please select a valid image file (JPG, PNG, GIF, WebP)");if(c.size>5242880)return void t("File size must be less than 5MB");p(!0),t(null),r(0);try{let a=setInterval(()=>{r(a=>Math.min(a+10,90))},200),d=await w(c);clearInterval(a),r(100),setTimeout(()=>{b(d.secure_url),p(!1),r(0)},500)}catch(a){p(!1),r(0),t(a instanceof Error?a.message:"Upload failed"),console.error("Upload error:",a)}u.current&&(u.current.value="")}};return(0,d.jsxs)("div",{className:"space-y-4",children:[(0,d.jsx)(g.J,{children:n}),a&&(0,d.jsxs)("div",{className:"relative w-32 h-32 rounded-lg overflow-hidden border",children:[(0,d.jsx)(k.default,{src:a,alt:"Uploaded image",fill:!0,className:"object-cover"}),(0,d.jsx)("button",{type:"button",onClick:()=>{c?c():b(""),t(null)},className:"absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600 transition-colors",disabled:l||o,children:(0,d.jsx)(h.A,{className:"w-4 h-4"})})]}),(0,d.jsxs)("div",{className:"space-y-2",children:[(0,d.jsx)("input",{ref:u,type:"file",accept:"image/*",onChange:x,disabled:l||o,className:"hidden"}),(0,d.jsx)(f.$,{type:"button",variant:"outline",onClick:()=>u.current?.click(),disabled:l||o,className:"w-full",children:o?(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(i.A,{className:"w-4 h-4 mr-2 animate-spin"}),"Uploading... ",q,"%"]}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)(j.A,{className:"w-4 h-4 mr-2"}),a?"Change Image":"Upload Image"]})}),o&&(0,d.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,d.jsx)("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${q}%`}})}),s&&(0,d.jsx)("p",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:s})]}),(0,d.jsxs)("div",{className:"text-sm text-gray-500 space-y-1",children:[(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Supported formats:"})," JPG, PNG, GIF, WebP"]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Max file size:"})," 5MB"]}),(0,d.jsxs)("p",{children:[(0,d.jsx)("strong",{children:"Upload method:"})," Signed upload (no preset required)"]}),!v&&(0,d.jsx)("p",{className:"text-red-600",children:"⚠️ Cloudinary not configured. Please set up environment variables."})]})]})}},34729:(a,b,c)=>{c.d(b,{T:()=>f});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("textarea",{"data-slot":"textarea",className:(0,e.cn)("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),...b})}},44493:(a,b,c)=>{c.d(b,{BT:()=>i,Wu:()=>j,ZB:()=>h,Zp:()=>f,aR:()=>g});var d=c(60687);c(43210);var e=c(4780);function f({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card",className:(0,e.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",a),...b})}function g({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-header",className:(0,e.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",a),...b})}function h({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-title",className:(0,e.cn)("leading-none font-semibold",a),...b})}function i({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-description",className:(0,e.cn)("text-muted-foreground text-sm",a),...b})}function j({className:a,...b}){return(0,d.jsx)("div",{"data-slot":"card-content",className:(0,e.cn)("px-6",a),...b})}},71669:(a,b,c)=>{c.d(b,{C5:()=>s,MJ:()=>q,Rr:()=>r,eI:()=>o,lR:()=>p,lV:()=>j,zB:()=>l});var d=c(60687),e=c(43210),f=c(8730),g=c(27605),h=c(4780),i=c(80013);let j=g.Op,k=e.createContext({}),l=({...a})=>(0,d.jsx)(k.Provider,{value:{name:a.name},children:(0,d.jsx)(g.xI,{...a})}),m=()=>{let a=e.useContext(k),b=e.useContext(n),{getFieldState:c}=(0,g.xW)(),d=(0,g.lN)({name:a.name}),f=c(a.name,d);if(!a)throw Error("useFormField should be used within <FormField>");let{id:h}=b;return{id:h,name:a.name,formItemId:`${h}-form-item`,formDescriptionId:`${h}-form-item-description`,formMessageId:`${h}-form-item-message`,...f}},n=e.createContext({});function o({className:a,...b}){let c=e.useId();return(0,d.jsx)(n.Provider,{value:{id:c},children:(0,d.jsx)("div",{"data-slot":"form-item",className:(0,h.cn)("grid gap-2",a),...b})})}function p({className:a,...b}){let{error:c,formItemId:e}=m();return(0,d.jsx)(i.J,{"data-slot":"form-label","data-error":!!c,className:(0,h.cn)("data-[error=true]:text-destructive",a),htmlFor:e,...b})}function q({...a}){let{error:b,formItemId:c,formDescriptionId:e,formMessageId:g}=m();return(0,d.jsx)(f.DX,{"data-slot":"form-control",id:c,"aria-describedby":b?`${e} ${g}`:`${e}`,"aria-invalid":!!b,...a})}function r({className:a,...b}){let{formDescriptionId:c}=m();return(0,d.jsx)("p",{"data-slot":"form-description",id:c,className:(0,h.cn)("text-muted-foreground text-sm",a),...b})}function s({className:a,...b}){let{error:c,formMessageId:e}=m(),f=c?String(c?.message??""):b.children;return f?(0,d.jsx)("p",{"data-slot":"form-message",id:e,className:(0,h.cn)("text-destructive text-sm",a),...b,children:f}):null}},80013:(a,b,c)=>{c.d(b,{J:()=>g});var d=c(60687);c(43210);var e=c(78148),f=c(4780);function g({className:a,...b}){return(0,d.jsx)(e.b,{"data-slot":"label",className:(0,f.cn)("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",a),...b})}}};