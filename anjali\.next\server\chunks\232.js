"use strict";exports.id=232,exports.ids=[232],exports.modules={71134:(a,b,c)=>{c.r(b),c.d(b,{Section:()=>f,SectionHeader:()=>g});var d=c(60687),e=c(4780);function f({children:a,className:b,id:c,background:f="default"}){return(0,d.jsx)("section",{id:c,className:(0,e.cn)("py-16 md:py-24",{default:"bg-white",cream:"bg-cream","soft-gray":"bg-soft-gray",gradient:"bg-gradient-to-br from-cream to-soft-gray"}[f],b),children:(0,d.jsx)("div",{className:"mx-auto max-w-7xl px-4 sm:px-6 lg:px-8",children:a})})}function g({title:a,subtitle:b,description:c,centered:f=!0,className:g}){return(0,d.jsxs)("div",{className:(0,e.cn)("mb-12 md:mb-16",f&&"text-center",g),children:[b&&(0,d.jsx)("p",{className:"text-rose-gold-dark font-medium text-sm uppercase tracking-wide mb-2",children:b}),(0,d.jsx)("h2",{className:"font-display text-3xl md:text-4xl lg:text-5xl font-bold text-text-primary mb-4",children:a}),c&&(0,d.jsx)("p",{className:"text-text-secondary text-lg max-w-3xl mx-auto leading-relaxed",children:c})]})}},89554:(a,b,c)=>{c.d(b,{f0:()=>p,Kh:()=>n,LC:()=>r,Kz:()=>m,R0:()=>l,_K:()=>v,Uz:()=>t,iX:()=>s,XM:()=>u,Sq:()=>q,zZ:()=>o});var d=c(51423);class e extends Error{constructor(a,b,c){super(a),this.status=b,this.data=c,this.name="ApiError"}}async function f(a,b={}){let c=`http://localhost:3001/api${a}`,d={headers:{"Content-Type":"application/json",...b.headers},...b};try{let a=await fetch(c,d);if(!a.ok){let b;try{b=await a.json()}catch{b={message:a.statusText}}throw new e(b.error||b.message||"An error occurred",a.status,b)}if(204===a.status)return{};return await a.json()}catch(a){if(a instanceof e)throw a;throw new e("Network error or server unavailable",0,{originalError:a})}}let g={get:a=>f(a),post:(a,b)=>f(a,{method:"POST",body:b?JSON.stringify(b):void 0})};function h(a){let b=new URLSearchParams;Object.entries(a).forEach(([a,c])=>{null!=c&&""!==c&&b.append(a,String(c))});let c=b.toString();return c?`?${c}`:""}let i={getSettings:async()=>g.get("/settings"),getSetting:async a=>g.get(`/settings/${a}`),getSettingValue:async a=>(await g.get(`/settings/${a}`)).value,getSiteInfo:async()=>{let a=await i.getSettings(),b=a.grouped?.site||[];return{name:b.find(a=>"site.name"===a.key)?.value||"Anjali Makeup Artist",tagline:b.find(a=>"site.tagline"===a.key)?.value||"Enhancing Natural Beauty",description:b.find(a=>"site.description"===a.key)?.value||"",logo:b.find(a=>"site.logo"===a.key)?.value,favicon:b.find(a=>"site.favicon"===a.key)?.value}},getContactInfo:async()=>{let a=await i.getSettings(),b=a.grouped?.contact||[];return{email:b.find(a=>"contact.email"===a.key)?.value,phone:b.find(a=>"contact.phone"===a.key)?.value,address:b.find(a=>"contact.address"===a.key)?.value,city:b.find(a=>"contact.city"===a.key)?.value,state:b.find(a=>"contact.state"===a.key)?.value,zipcode:b.find(a=>"contact.zipcode"===a.key)?.value,country:b.find(a=>"contact.country"===a.key)?.value}},getSocialMedia:async()=>{let a=await i.getSettings(),b=a.grouped?.social||[];return{facebook:b.find(a=>"social.facebook"===a.key)?.value,instagram:b.find(a=>"social.instagram"===a.key)?.value,twitter:b.find(a=>"social.twitter"===a.key)?.value,youtube:b.find(a=>"social.youtube"===a.key)?.value,linkedin:b.find(a=>"social.linkedin"===a.key)?.value,tiktok:b.find(a=>"social.tiktok"===a.key)?.value}},getSEODefaults:async()=>{let a=await i.getSettings(),b=a.grouped?.seo||[];return{metaTitle:b.find(a=>"seo.meta_title"===a.key)?.value,metaDescription:b.find(a=>"seo.meta_description"===a.key)?.value,keywords:b.find(a=>"seo.keywords"===a.key)?.value,ogImage:b.find(a=>"seo.og_image"===a.key)?.value}},getBusinessInfo:async()=>{let a=await i.getSettings(),b=a.grouped?.business||[];return{hours:b.find(a=>"business.hours"===a.key)?.value,servicesIntro:b.find(a=>"business.services_intro"===a.key)?.value,aboutText:b.find(a=>"business.about_text"===a.key)?.value,bookingUrl:b.find(a=>"business.booking_url"===a.key)?.value}}},j={blogs:{getBlogs:async(a={})=>{let b=h(a);return g.get(`/blogs${b}`)},getBlog:async a=>g.get(`/blogs/${a}`),getBlogBySlug:async a=>g.get(`/blogs/slug/${a}`),getFeaturedBlogs:async(a=3)=>g.get(`/blogs?featured=true&limit=${a}&status=PUBLISHED`),getRecentBlogs:async(a=5)=>g.get(`/blogs?limit=${a}&status=PUBLISHED`)},services:{getServices:async(a={})=>{let b=h(a);return g.get(`/services${b}`)},getService:async a=>g.get(`/services/${a}`),getServiceBySlug:async a=>g.get(`/services/slug/${a}`),getActiveServices:async()=>g.get("/services?status=ACTIVE"),getPopularServices:async(a=6)=>g.get(`/services?popular=true&status=ACTIVE&limit=${a}`),getServicesByCategory:async a=>g.get(`/services?category=${encodeURIComponent(a)}&status=ACTIVE`)},packages:{getPackages:async(a={})=>{let b=h(a);return g.get(`/packages${b}`)},getPackage:async a=>g.get(`/packages/${a}`),getPackageBySlug:async a=>g.get(`/packages/slug/${a}`),getActivePackages:async()=>g.get("/packages?status=ACTIVE"),getPopularPackages:async(a=3)=>g.get(`/packages?popular=true&status=ACTIVE&limit=${a}`),getFeaturedPackages:async()=>g.get("/packages?popular=true&status=ACTIVE")},testimonials:{getTestimonials:async(a={})=>{let b=h(a);return g.get(`/testimonials${b}`)},getTestimonial:async a=>g.get(`/testimonials/${a}`),getApprovedTestimonials:async a=>{let b=h({status:"APPROVED",...a&&{limit:a}});return g.get(`/testimonials${b}`)},getTestimonialsByService:async a=>g.get(`/testimonials?service=${encodeURIComponent(a)}&status=APPROVED`),createTestimonial:async a=>g.post("/testimonials",a)},gallery:{getGallery:async(a={})=>{let b=h(a);return g.get(`/gallery${b}`)},getGalleryItem:async a=>g.get(`/gallery/${a}`),getActiveGallery:async a=>{let b=h({status:"ACTIVE",...a&&{limit:a}});return g.get(`/gallery${b}`)},getFeaturedGallery:async(a=12)=>g.get(`/gallery?featured=true&status=ACTIVE&limit=${a}`),getGalleryByCategory:async a=>g.get(`/gallery?category=${encodeURIComponent(a)}&status=ACTIVE`)},settings:i},k={blogs:{all:["blogs"],lists:()=>[...k.blogs.all,"list"],list:a=>[...k.blogs.lists(),a],details:()=>[...k.blogs.all,"detail"],detail:a=>[...k.blogs.details(),a],slug:a=>[...k.blogs.all,"slug",a],featured:a=>[...k.blogs.all,"featured",a],recent:a=>[...k.blogs.all,"recent",a]},services:{all:["services"],lists:()=>[...k.services.all,"list"],list:a=>[...k.services.lists(),a],details:()=>[...k.services.all,"detail"],detail:a=>[...k.services.details(),a],slug:a=>[...k.services.all,"slug",a],active:()=>[...k.services.all,"active"],popular:a=>[...k.services.all,"popular",a],category:a=>[...k.services.all,"category",a]},packages:{all:["packages"],lists:()=>[...k.packages.all,"list"],list:a=>[...k.packages.lists(),a],details:()=>[...k.packages.all,"detail"],detail:a=>[...k.packages.details(),a],slug:a=>[...k.packages.all,"slug",a],active:()=>[...k.packages.all,"active"],popular:a=>[...k.packages.all,"popular",a],featured:()=>[...k.packages.all,"featured"]},testimonials:{all:["testimonials"],lists:()=>[...k.testimonials.all,"list"],list:a=>[...k.testimonials.lists(),a],details:()=>[...k.testimonials.all,"detail"],detail:a=>[...k.testimonials.details(),a],approved:a=>[...k.testimonials.all,"approved",a],service:a=>[...k.testimonials.all,"service",a]},gallery:{all:["gallery"],lists:()=>[...k.gallery.all,"list"],list:a=>[...k.gallery.lists(),a],details:()=>[...k.gallery.all,"detail"],detail:a=>[...k.gallery.details(),a],active:a=>[...k.gallery.all,"active",a],featured:a=>[...k.gallery.all,"featured",a],category:a=>[...k.gallery.all,"category",a]},settings:{all:["settings"],detail:a=>[...k.settings.all,a],siteInfo:()=>[...k.settings.all,"siteInfo"],contactInfo:()=>[...k.settings.all,"contactInfo"],socialMedia:()=>[...k.settings.all,"socialMedia"],seoDefaults:()=>[...k.settings.all,"seoDefaults"],businessInfo:()=>[...k.settings.all,"businessInfo"]}},l=(a={})=>(0,d.I)({queryKey:k.blogs.list(a),queryFn:()=>j.blogs.getBlogs(a),staleTime:3e5}),m=a=>(0,d.I)({queryKey:k.blogs.slug(a),queryFn:()=>j.blogs.getBlogBySlug(a),enabled:!!a}),n=()=>(0,d.I)({queryKey:k.services.active(),queryFn:()=>j.services.getActiveServices(),staleTime:9e5}),o=(a=6)=>(0,d.I)({queryKey:k.services.popular(a),queryFn:()=>j.services.getPopularServices(a),staleTime:9e5}),p=()=>(0,d.I)({queryKey:k.packages.active(),queryFn:()=>j.packages.getActivePackages(),staleTime:9e5}),q=(a=3)=>(0,d.I)({queryKey:k.packages.popular(a),queryFn:()=>j.packages.getPopularPackages(a),staleTime:9e5}),r=a=>(0,d.I)({queryKey:k.testimonials.approved(a),queryFn:()=>j.testimonials.getApprovedTestimonials(a),staleTime:6e5}),s=(a={})=>(0,d.I)({queryKey:k.gallery.list(a),queryFn:()=>j.gallery.getGallery(a),staleTime:6e5}),t=(a=12)=>(0,d.I)({queryKey:k.gallery.featured(a),queryFn:()=>j.gallery.getFeaturedGallery(a),staleTime:9e5}),u=a=>(0,d.I)({queryKey:k.gallery.category(a),queryFn:()=>j.gallery.getGalleryByCategory(a),enabled:!!a,staleTime:6e5}),v=()=>(0,d.I)({queryKey:k.settings.contactInfo(),queryFn:()=>j.settings.getContactInfo(),staleTime:36e5})},96834:(a,b,c)=>{c.d(b,{Badge:()=>h});var d=c(60687);c(43210);var e=c(24224),f=c(4780);let g=(0,e.F)("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-rose-gold text-white shadow hover:bg-rose-gold-dark",secondary:"border-transparent bg-blush-pink text-text-primary hover:bg-blush-pink-dark",destructive:"border-transparent bg-red-500 text-white shadow hover:bg-red-600",outline:"text-text-primary border-gray-300",success:"border-transparent bg-green-500 text-white shadow hover:bg-green-600",warning:"border-transparent bg-yellow-500 text-white shadow hover:bg-yellow-600",lavender:"border-transparent bg-lavender text-text-primary hover:bg-lavender-dark"}},defaultVariants:{variant:"default"}});function h({className:a,variant:b,...c}){return(0,d.jsx)("div",{className:(0,f.cn)(g({variant:b}),a),...c})}}};