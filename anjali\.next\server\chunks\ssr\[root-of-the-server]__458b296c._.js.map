{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 30, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/about-hero.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/about-hero.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA0S,GACvU,wEACA", "debugId": null}}, {"offset": {"line": 42, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-hero.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/about-hero.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/about-hero.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAsR,GACnT,oDACA", "debugId": null}}, {"offset": {"line": 54, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/card.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Card = registerClientReference(\n    function() { throw new Error(\"Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx <module evaluation>\",\n    \"Card\",\n);\nexport const CardContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardContent() from the server but CardContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx <module evaluation>\",\n    \"CardContent\",\n);\nexport const CardDescription = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardDescription() from the server but CardDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx <module evaluation>\",\n    \"CardDescription\",\n);\nexport const CardFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardFooter() from the server but CardFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx <module evaluation>\",\n    \"CardFooter\",\n);\nexport const CardHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardHeader() from the server but CardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx <module evaluation>\",\n    \"CardHeader\",\n);\nexport const CardTitle = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardTitle() from the server but CardTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx <module evaluation>\",\n    \"CardTitle\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,4DACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,4DACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,4DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,4DACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,4DACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,4DACA", "debugId": null}}, {"offset": {"line": 94, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/card.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Card = registerClientReference(\n    function() { throw new Error(\"Attempted to call Card() from the server but Card is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx\",\n    \"Card\",\n);\nexport const CardContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardContent() from the server but CardContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx\",\n    \"CardContent\",\n);\nexport const CardDescription = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardDescription() from the server but CardDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx\",\n    \"CardDescription\",\n);\nexport const CardFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardFooter() from the server but CardFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx\",\n    \"CardFooter\",\n);\nexport const CardHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardHeader() from the server but CardHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx\",\n    \"CardHeader\",\n);\nexport const CardTitle = registerClientReference(\n    function() { throw new Error(\"Attempted to call CardTitle() from the server but CardTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/card.tsx\",\n    \"CardTitle\",\n);\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AACO,MAAM,OAAO,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACtC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,wCACA;AAEG,MAAM,cAAc,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,wCACA;AAEG,MAAM,kBAAkB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,wCACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,wCACA;AAEG,MAAM,aAAa,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,wCACA;AAEG,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,wCACA", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Section = registerClientReference(\n    function() { throw new Error(\"Attempted to call Section() from the server but Section is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/section.tsx <module evaluation>\",\n    \"Section\",\n);\nexport const SectionHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SectionHeader() from the server but SectionHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/section.tsx <module evaluation>\",\n    \"SectionHeader\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,+DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,+DACA", "debugId": null}}, {"offset": {"line": 150, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/section.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Section = registerClientReference(\n    function() { throw new Error(\"Attempted to call Section() from the server but Section is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/section.tsx\",\n    \"Section\",\n);\nexport const SectionHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SectionHeader() from the server but SectionHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/section.tsx\",\n    \"SectionHeader\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,UAAU,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACzC;IAAa,MAAM,IAAI,MAAM;AAA8N,GAC3P,2CACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,2CACA", "debugId": null}}, {"offset": {"line": 166, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-story.tsx"], "sourcesContent": ["import Image from 'next/image'\nimport { <PERSON>, <PERSON><PERSON><PERSON>, Target } from 'lucide-react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Section, SectionHeader } from '@/components/ui/section'\n\nconst values = [\n  {\n    icon: Heart,\n    title: 'Passion for Beauty',\n    description: 'Every makeup session is an opportunity to enhance natural beauty and boost confidence. I believe makeup is an art form that celebrates individuality.'\n  },\n  {\n    icon: Sparkles,\n    title: 'Attention to Detail',\n    description: 'From the perfect foundation match to the precise eyeliner application, every detail matters in creating a flawless, long-lasting look.'\n  },\n  {\n    icon: Target,\n    title: 'Client-Focused Approach',\n    description: 'Understanding your vision, preferences, and the occasion helps me create personalized looks that make you feel confident and beautiful.'\n  }\n]\n\nexport default function AboutStory() {\n  return (\n    <Section background=\"cream\">\n      <div className=\"grid lg:grid-cols-2 gap-16 items-center\">\n        {/* Story Content */}\n        <div className=\"space-y-8\">\n          <SectionHeader\n            subtitle=\"My Story\"\n            title=\"A Journey of Passion & Artistry\"\n            description=\"\"\n            centered={false}\n          />\n          \n          <div className=\"space-y-6 text-text-secondary leading-relaxed\">\n            <p>\n              My journey into the world of makeup artistry began over five years ago with a simple \n              fascination for colors, textures, and the transformative power of makeup. What started \n              as a hobby quickly evolved into a passionate career dedicated to helping people look \n              and feel their absolute best.\n            </p>\n            \n            <p>\n              Based in the beautiful city of Biratnagar, I&apos;ve had the privilege of working with \n              clients across Nepal, from intimate family gatherings to grand wedding celebrations. \n              Each client brings a unique story, and I consider myself fortunate to be part of \n              their special moments.\n            </p>\n            \n            <p>\n              My approach combines traditional techniques with modern trends, ensuring that every \n              look is both timeless and contemporary. Whether it&apos;s a bride&apos;s special day, a \n              professional photoshoot, or a festive celebration, I believe in creating makeup \n              that enhances natural beauty while reflecting personal style.\n            </p>\n            \n            <p>\n              Continuous learning is at the heart of my practice. I regularly update my skills \n              with the latest techniques and products, ensuring that my clients receive the best \n              possible service with current trends and high-quality products.\n            </p>\n          </div>\n        </div>\n\n        {/* Image Collage */}\n        <div className=\"relative\">\n          <div className=\"grid grid-cols-2 gap-4\">\n            <div className=\"space-y-4\">\n              <div className=\"relative aspect-[3/4] rounded-xl overflow-hidden shadow-lg\">\n                <Image\n                  src=\"https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=400&fit=crop&crop=face\"\n                  alt=\"Makeup artistry work\"\n                  fill\n                  className=\"object-cover\"\n                />\n              </div>\n              <div className=\"relative aspect-square rounded-xl overflow-hidden shadow-lg\">\n                <Image\n                  src=\"https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=300&fit=crop&crop=face\"\n                  alt=\"Professional makeup session\"\n                  fill\n                  className=\"object-cover\"\n                />\n              </div>\n            </div>\n            <div className=\"space-y-4 pt-8\">\n              <div className=\"relative aspect-square rounded-xl overflow-hidden shadow-lg\">\n                <Image\n                  src=\"https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=300&fit=crop&crop=face\"\n                  alt=\"Bridal makeup\"\n                  fill\n                  className=\"object-cover\"\n                />\n              </div>\n              <div className=\"relative aspect-[3/4] rounded-xl overflow-hidden shadow-lg\">\n                <Image\n                  src=\"https://images.unsplash.com/photo-1596462502278-27bfdc403348?w=300&h=400&fit=crop&crop=face\"\n                  alt=\"Traditional makeup\"\n                  fill\n                  className=\"object-cover\"\n                />\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Values Section */}\n      <div className=\"mt-20\">\n        <div>\n          <SectionHeader\n            subtitle=\"My Values\"\n            title=\"What Drives My Artistry\"\n            description=\"The principles that guide every makeup session and client interaction.\"\n          />\n        </div>\n\n        <div className=\"grid md:grid-cols-3 gap-8\">\n          {values.map((value) => (\n            <div key={value.title}>\n              <Card className=\"h-full bg-white/80 backdrop-blur-sm border-0 hover:shadow-lg transition-shadow duration-300\">\n                <CardContent className=\"p-6 text-center space-y-4\">\n                  <div className=\"w-16 h-16 bg-gradient-to-br from-rose-gold to-blush-pink rounded-full flex items-center justify-center mx-auto\">\n                    <value.icon className=\"w-8 h-8 text-white\" />\n                  </div>\n                  <h3 className=\"font-display text-xl font-semibold text-text-primary\">\n                    {value.title}\n                  </h3>\n                  <p className=\"text-text-secondary leading-relaxed\">\n                    {value.description}\n                  </p>\n                </CardContent>\n              </Card>\n            </div>\n          ))}\n        </div>\n      </div>\n    </Section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAAA;AACA;AACA;;;;;;AAEA,MAAM,SAAS;IACb;QACE,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;QACP,aAAa;IACf;IACA;QACE,MAAM,sMAAA,CAAA,SAAM;QACZ,OAAO;QACP,aAAa;IACf;CACD;AAEc,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;QAAC,YAAW;;0BAClB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mIAAA,CAAA,gBAAa;gCACZ,UAAS;gCACT,OAAM;gCACN,aAAY;gCACZ,UAAU;;;;;;0CAGZ,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;kDAAE;;;;;;kDAOH,8OAAC;kDAAE;;;;;;kDAOH,8OAAC;kDAAE;;;;;;kDAOH,8OAAC;kDAAE;;;;;;;;;;;;;;;;;;kCASP,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;;;;;;;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;gDACJ,KAAI;gDACJ,KAAI;gDACJ,IAAI;gDACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAStB,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;kCACC,cAAA,8OAAC,mIAAA,CAAA,gBAAa;4BACZ,UAAS;4BACT,OAAM;4BACN,aAAY;;;;;;;;;;;kCAIhB,8OAAC;wBAAI,WAAU;kCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;0CACC,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,MAAM,IAAI;oDAAC,WAAU;;;;;;;;;;;0DAExB,8OAAC;gDAAG,WAAU;0DACX,MAAM,KAAK;;;;;;0DAEd,8OAAC;gDAAE,WAAU;0DACV,MAAM,WAAW;;;;;;;;;;;;;;;;;+BAVhB,MAAM,KAAK;;;;;;;;;;;;;;;;;;;;;;AAoBjC", "debugId": null}}, {"offset": {"line": 471, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/badge.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Badge = registerClientReference(\n    function() { throw new Error(\"Attempted to call Badge() from the server but Badge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/badge.tsx <module evaluation>\",\n    \"Badge\",\n);\nexport const badgeVariants = registerClientReference(\n    function() { throw new Error(\"Attempted to call badgeVariants() from the server but badgeVariants is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/badge.tsx <module evaluation>\",\n    \"badgeVariants\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,6DACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6DACA", "debugId": null}}, {"offset": {"line": 487, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/ui/badge.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Badge = registerClientReference(\n    function() { throw new Error(\"Attempted to call Badge() from the server but Badge is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/badge.tsx\",\n    \"Badge\",\n);\nexport const badgeVariants = registerClientReference(\n    function() { throw new Error(\"Attempted to call badgeVariants() from the server but badgeVariants is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/badge.tsx\",\n    \"badgeVariants\",\n);\n"], "names": [], "mappings": ";;;;AAAA;;AACO,MAAM,QAAQ,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACvC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,yCACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,yCACA", "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 511, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-experience.tsx"], "sourcesContent": ["import { Calendar, Award, Users, Palette } from 'lucide-react'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Section, SectionHeader } from '@/components/ui/section'\n\nconst milestones = [\n  {\n    year: '2019',\n    title: 'Started Professional Journey',\n    description: 'Began my career as a professional makeup artist in Biratnagar, focusing on bridal and special occasion makeup.',\n    icon: Calendar,\n    color: 'from-rose-gold to-blush-pink'\n  },\n  {\n    year: '2020',\n    title: 'Expanded Service Areas',\n    description: 'Extended services to Itahari, Dharan, and surrounding areas, building a strong client base across the region.',\n    icon: Users,\n    color: 'from-blush-pink to-lavender'\n  },\n  {\n    year: '2021',\n    title: 'Advanced Training & Certification',\n    description: 'Completed advanced makeup courses and obtained professional certifications in modern makeup techniques.',\n    icon: Award,\n    color: 'from-lavender to-rose-gold'\n  },\n  {\n    year: '2022',\n    title: 'Specialized in Traditional Makeup',\n    description: 'Developed expertise in traditional Nepali makeup styles, becoming known for authentic cultural looks.',\n    icon: Palette,\n    color: 'from-rose-gold to-blush-pink'\n  },\n  {\n    year: '2023',\n    title: 'Reached 100+ Happy Clients',\n    description: 'Celebrated serving over 100 satisfied clients with consistently high ratings and positive reviews.',\n    icon: Users,\n    color: 'from-blush-pink to-lavender'\n  },\n  {\n    year: '2024',\n    title: 'Expanded to Kathmandu',\n    description: 'Extended services to Nepal\\'s capital, bringing professional makeup artistry to a wider audience.',\n    icon: Calendar,\n    color: 'from-lavender to-rose-gold'\n  }\n]\n\nconst skills = [\n  { name: 'Bridal Makeup', level: 95 },\n  { name: 'Traditional Makeup', level: 90 },\n  { name: 'Party & Event Makeup', level: 92 },\n  { name: 'Photoshoot Makeup', level: 88 },\n  { name: 'Color Matching', level: 94 },\n  { name: 'Contouring & Highlighting', level: 91 }\n]\n\nexport default function AboutExperience() {\n  return (\n    <Section>\n      <AnimatedElement animation=\"fadeIn\">\n        <SectionHeader\n          subtitle=\"Experience & Expertise\"\n          title=\"My Professional Journey\"\n          description=\"A timeline of growth, learning, and achievements in the makeup artistry field.\"\n        />\n      </AnimatedElement>\n\n      {/* Timeline */}\n      <div className=\"relative\">\n        {/* Timeline Line */}\n        <div className=\"absolute left-1/2 transform -translate-x-1/2 w-1 h-full bg-gradient-to-b from-rose-gold via-blush-pink to-lavender rounded-full hidden lg:block\"></div>\n\n        <StaggeredContainer className=\"space-y-12\">\n          {milestones.map((milestone, index) => (\n            <StaggeredItem key={milestone.year}>\n              <div className={`flex items-center ${index % 2 === 0 ? 'lg:flex-row' : 'lg:flex-row-reverse'} flex-col lg:gap-8 gap-4`}>\n                {/* Content Card */}\n                <div className=\"flex-1 lg:max-w-md\">\n                  <Card className=\"bg-white/80 backdrop-blur-sm border-0 hover:shadow-xl transition-all duration-300\">\n                    <CardContent className=\"p-6\">\n                      <div className=\"flex items-center gap-3 mb-3\">\n                        <Badge variant=\"secondary\" className=\"text-sm\">\n                          {milestone.year}\n                        </Badge>\n                        <div className={`w-8 h-8 bg-gradient-to-r ${milestone.color} rounded-full flex items-center justify-center`}>\n                          <milestone.icon className=\"w-4 h-4 text-white\" />\n                        </div>\n                      </div>\n                      <h3 className=\"font-display text-xl font-semibold text-text-primary mb-2\">\n                        {milestone.title}\n                      </h3>\n                      <p className=\"text-text-secondary leading-relaxed\">\n                        {milestone.description}\n                      </p>\n                    </CardContent>\n                  </Card>\n                </div>\n\n                {/* Timeline Node */}\n                <div className=\"hidden lg:block relative\">\n                  <div className={`w-4 h-4 bg-gradient-to-r ${milestone.color} rounded-full border-4 border-white shadow-lg`}></div>\n                </div>\n\n                {/* Spacer for alternating layout */}\n                <div className=\"flex-1 lg:max-w-md hidden lg:block\"></div>\n              </div>\n            </StaggeredItem>\n          ))}\n        </StaggeredContainer>\n      </div>\n\n      {/* Skills Section */}\n      <div className=\"mt-20\">\n        <AnimatedElement animation=\"fadeIn\">\n          <SectionHeader\n            subtitle=\"Skills & Expertise\"\n            title=\"Areas of Specialization\"\n            description=\"My core competencies and areas of expertise in makeup artistry.\"\n          />\n        </AnimatedElement>\n\n        <div className=\"grid md:grid-cols-2 gap-8\">\n          <AnimatedElement animation=\"slideRight\" delay={0.3}>\n            <Card className=\"bg-gradient-to-br from-rose-gold/10 to-blush-pink/10 border-0\">\n              <CardContent className=\"p-6\">\n                <h3 className=\"font-display text-xl font-semibold text-text-primary mb-6\">\n                  Technical Skills\n                </h3>\n                <div className=\"space-y-4\">\n                  {skills.map((skill) => (\n                    <div key={skill.name} className=\"space-y-2\">\n                      <div className=\"flex justify-between items-center\">\n                        <span className=\"text-text-primary font-medium\">{skill.name}</span>\n                        <span className=\"text-text-secondary text-sm\">{skill.level}%</span>\n                      </div>\n                      <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                        <div \n                          className=\"bg-gradient-to-r from-rose-gold to-blush-pink h-2 rounded-full transition-all duration-1000\"\n                          style={{ width: `${skill.level}%` }}\n                        ></div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </CardContent>\n            </Card>\n          </AnimatedElement>\n\n          <AnimatedElement animation=\"slideLeft\" delay={0.5}>\n            <Card className=\"bg-gradient-to-br from-lavender/10 to-blush-pink/10 border-0\">\n              <CardContent className=\"p-6\">\n                <h3 className=\"font-display text-xl font-semibold text-text-primary mb-6\">\n                  Professional Highlights\n                </h3>\n                <div className=\"space-y-4\">\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-2 h-2 bg-rose-gold rounded-full\"></div>\n                    <span className=\"text-text-secondary\">5+ Years Professional Experience</span>\n                  </div>\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-2 h-2 bg-blush-pink rounded-full\"></div>\n                    <span className=\"text-text-secondary\">100+ Successful Makeup Sessions</span>\n                  </div>\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-2 h-2 bg-lavender rounded-full\"></div>\n                    <span className=\"text-text-secondary\">5.0 Star Average Rating</span>\n                  </div>\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-2 h-2 bg-rose-gold rounded-full\"></div>\n                    <span className=\"text-text-secondary\">Certified in Advanced Techniques</span>\n                  </div>\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-2 h-2 bg-blush-pink rounded-full\"></div>\n                    <span className=\"text-text-secondary\">Specialized in Traditional & Modern Styles</span>\n                  </div>\n                  <div className=\"flex items-center gap-3\">\n                    <div className=\"w-2 h-2 bg-lavender rounded-full\"></div>\n                    <span className=\"text-text-secondary\">Serving 7 Cities Across Nepal</span>\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </AnimatedElement>\n        </div>\n      </div>\n    </Section>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;;;;;;AAEA,MAAM,aAAa;IACjB;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,wMAAA,CAAA,UAAO;QACb,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,oMAAA,CAAA,QAAK;QACX,OAAO;IACT;IACA;QACE,MAAM;QACN,OAAO;QACP,aAAa;QACb,MAAM,0MAAA,CAAA,WAAQ;QACd,OAAO;IACT;CACD;AAED,MAAM,SAAS;IACb;QAAE,MAAM;QAAiB,OAAO;IAAG;IACnC;QAAE,MAAM;QAAsB,OAAO;IAAG;IACxC;QAAE,MAAM;QAAwB,OAAO;IAAG;IAC1C;QAAE,MAAM;QAAqB,OAAO;IAAG;IACvC;QAAE,MAAM;QAAkB,OAAO;IAAG;IACpC;QAAE,MAAM;QAA6B,OAAO;IAAG;CAChD;AAEc,SAAS;IACtB,qBACE,8OAAC,mIAAA,CAAA,UAAO;;0BACN,8OAAC;gBAAgB,WAAU;0BACzB,cAAA,8OAAC,mIAAA,CAAA,gBAAa;oBACZ,UAAS;oBACT,OAAM;oBACN,aAAY;;;;;;;;;;;0BAKhB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;;;;;kCAEf,8OAAC;wBAAmB,WAAU;kCAC3B,WAAW,GAAG,CAAC,CAAC,WAAW,sBAC1B,8OAAC;0CACC,cAAA,8OAAC;oCAAI,WAAW,CAAC,kBAAkB,EAAE,QAAQ,MAAM,IAAI,gBAAgB,sBAAsB,wBAAwB,CAAC;;sDAEpH,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gDAAC,WAAU;0DACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;oDAAC,WAAU;;sEACrB,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEAAC,SAAQ;oEAAY,WAAU;8EAClC,UAAU,IAAI;;;;;;8EAEjB,8OAAC;oEAAI,WAAW,CAAC,yBAAyB,EAAE,UAAU,KAAK,CAAC,8CAA8C,CAAC;8EACzG,cAAA,8OAAC,UAAU,IAAI;wEAAC,WAAU;;;;;;;;;;;;;;;;;sEAG9B,8OAAC;4DAAG,WAAU;sEACX,UAAU,KAAK;;;;;;sEAElB,8OAAC;4DAAE,WAAU;sEACV,UAAU,WAAW;;;;;;;;;;;;;;;;;;;;;;sDAO9B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAI,WAAW,CAAC,yBAAyB,EAAE,UAAU,KAAK,CAAC,6CAA6C,CAAC;;;;;;;;;;;sDAI5G,8OAAC;4CAAI,WAAU;;;;;;;;;;;;+BA9BC,UAAU,IAAI;;;;;;;;;;;;;;;;0BAsCxC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAgB,WAAU;kCACzB,cAAA,8OAAC,mIAAA,CAAA,gBAAa;4BACZ,UAAS;4BACT,OAAM;4BACN,aAAY;;;;;;;;;;;kCAIhB,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAgB,WAAU;gCAAa,OAAO;0CAC7C,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAI,WAAU;0DACZ,OAAO,GAAG,CAAC,CAAC,sBACX,8OAAC;wDAAqB,WAAU;;0EAC9B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAK,WAAU;kFAAiC,MAAM,IAAI;;;;;;kFAC3D,8OAAC;wEAAK,WAAU;;4EAA+B,MAAM,KAAK;4EAAC;;;;;;;;;;;;;0EAE7D,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,MAAM,KAAK,CAAC,CAAC,CAAC;oEAAC;;;;;;;;;;;;uDAR9B,MAAM,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;0CAkB9B,8OAAC;gCAAgB,WAAU;gCAAY,OAAO;0CAC5C,cAAA,8OAAC,gIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,8OAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,8OAAC;gDAAG,WAAU;0DAA4D;;;;;;0DAG1E,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;kEAExC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;;;;;0EACf,8OAAC;gEAAK,WAAU;0EAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU1D", "debugId": null}}, {"offset": {"line": 1069, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-cities.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/about-cities.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/about-cities.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAA4S,GACzU,0EACA", "debugId": null}}, {"offset": {"line": 1081, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-cities.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/about-cities.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/about-cities.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAwR,GACrT,sDACA", "debugId": null}}, {"offset": {"line": 1093, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1101, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-cta.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/about-cta.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/about-cta.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyS,GACtU,uEACA", "debugId": null}}, {"offset": {"line": 1113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/components/sections/about-cta.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/src/components/sections/about-cta.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/sections/about-cta.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqR,GAClT,mDACA", "debugId": null}}, {"offset": {"line": 1125, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/ashish/anjali-portfolio/anjali/src/app/about/page.tsx"], "sourcesContent": ["import AboutHero from '@/components/sections/about-hero'\nimport AboutStory from '@/components/sections/about-story'\nimport AboutExperience from '@/components/sections/about-experience'\nimport AboutCities from '@/components/sections/about-cities'\nimport AboutCTA from '@/components/sections/about-cta'\n\nexport default function AboutPage() {\n  return (\n    <>\n      <AboutHero />\n      <AboutStory />\n      <AboutExperience />\n      <AboutCities />\n      <AboutCTA />\n    </>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;;;;;;;AAEe,SAAS;IACtB,qBACE;;0BACE,8OAAC,+IAAA,CAAA,UAAS;;;;;0BACV,8OAAC,gJAAA,CAAA,UAAU;;;;;0BACX,8OAAC,qJAAA,CAAA,UAAe;;;;;0BAChB,8OAAC,iJAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,8IAAA,CAAA,UAAQ;;;;;;;AAGf", "debugId": null}}]}