(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4133],{1216:(e,t,i)=>{Promise.resolve().then(i.bind(i,1491))},1491:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>r});var s=i(5155),a=i(2115),n=i(5695),l=i(1484),o=i(6671);function r(){let e=(0,n.useParams)(),[t,i]=(0,a.useState)(null),[r,d]=(0,a.useState)(!0);return((0,a.useEffect)(()=>{let t=async()=>{try{let t=await fetch("/api/testimonials/".concat(e.id));if(t.ok){let e=await t.json();i(e)}else o.oR.error("Testimonial not found")}catch(e){o.oR.error("Error fetching testimonial")}finally{d(!1)}};e.id&&t()},[e.id]),r)?(0,s.jsx)("div",{children:"Loading..."}):t?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold",children:"Edit Testimonial"}),(0,s.jsx)("p",{className:"text-muted-foreground",children:"Update the testimonial details and approval status"})]}),(0,s.jsx)(l.TestimonialForm,{initialData:t,isEditing:!0})]}):(0,s.jsx)("div",{children:"Testimonial not found"})}}},e=>{e.O(0,[5389,6671,651,7536,7764,8062,2804,9304,8496,8441,5964,7358],()=>e(e.s=1216)),_N_E=e.O()}]);