"use strict";exports.id=3001,exports.ids=[3001],exports.modules={2188:(a,b,c)=>{c.d(b,{N_:()=>aU,Ay:()=>aV});var d=c(81373);let e=(a,b)=>{for(let c in b)a[c]=b[c];return a},f="numeric",g="ascii",h="alpha",i="asciinumeric",j="alphanumeric",k="domain",l="emoji",m="whitespace";function n(a,b,c){for(let d in b[f]&&(b[i]=!0,b[j]=!0),b[g]&&(b[i]=!0,b[h]=!0),b[i]&&(b[j]=!0),b[h]&&(b[j]=!0),b[j]&&(b[k]=!0),b[l]&&(b[k]=!0),b){let b=(d in c||(c[d]=[]),c[d]);0>b.indexOf(a)&&b.push(a)}}function o(a=null){this.j={},this.jr=[],this.jd=null,this.t=a}o.groups={},o.prototype={accepts(){return!!this.t},go(a){let b=this.j[a];if(b)return b;for(let b=0;b<this.jr.length;b++){let c=this.jr[b][0],d=this.jr[b][1];if(d&&c.test(a))return d}return this.jd},has(a,b=!1){return b?a in this.j:!!this.go(a)},ta(a,b,c,d){for(let e=0;e<a.length;e++)this.tt(a[e],b,c,d)},tr(a,b,c,d){let e;return d=d||o.groups,b&&b.j?e=b:(e=new o(b),c&&d&&n(b,c,d)),this.jr.push([a,e]),e},ts(a,b,c,d){let e=this,f=a.length;if(!f)return e;for(let b=0;b<f-1;b++)e=e.tt(a[b]);return e.tt(a[f-1],b,c,d)},tt(a,b,c,d){if(d=d||o.groups,b&&b.j)return this.j[a]=b,b;let f,g=this.go(a);return g?(e((f=new o).j,g.j),f.jr.push.apply(f.jr,g.jr),f.jd=g.jd,f.t=g.t):f=new o,b&&(d&&(f.t&&"string"==typeof f.t?n(b,e(function(a,b){let c={};for(let d in b)b[d].indexOf(a)>=0&&(c[d]=!0);return c}(f.t,d),c),d):c&&n(b,c,d)),f.t=b),this.j[a]=f,f}};let p=(a,b,c,d,e)=>a.ta(b,c,d,e),q=(a,b,c,d,e)=>a.tr(b,c,d,e),r=(a,b,c,d,e)=>a.ts(b,c,d,e),s=(a,b,c,d,e)=>a.tt(b,c,d,e),t="WORD",u="UWORD",v="ASCIINUMERICAL",w="ALPHANUMERICAL",x="LOCALHOST",y="UTLD",z="SCHEME",A="SLASH_SCHEME",B="OPENBRACE",C="CLOSEBRACE",D="OPENBRACKET",E="CLOSEBRACKET",F="OPENPAREN",G="CLOSEPAREN",H="OPENANGLEBRACKET",I="CLOSEANGLEBRACKET",J="FULLWIDTHLEFTPAREN",K="FULLWIDTHRIGHTPAREN",L="LEFTCORNERBRACKET",M="RIGHTCORNERBRACKET",N="LEFTWHITECORNERBRACKET",O="RIGHTWHITECORNERBRACKET",P="FULLWIDTHLESSTHAN",Q="FULLWIDTHGREATERTHAN",R="AMPERSAND",S="APOSTROPHE",T="ASTERISK",U="BACKSLASH",V="BACKTICK",W="CARET",X="COLON",Y="COMMA",Z="DOLLAR",$="EQUALS",_="EXCLAMATION",aa="HYPHEN",ab="PERCENT",ac="PIPE",ad="PLUS",ae="POUND",af="QUERY",ag="QUOTE",ah="FULLWIDTHMIDDLEDOT",ai="SEMI",aj="SLASH",ak="TILDE",al="UNDERSCORE",am="EMOJI";var an=Object.freeze({__proto__:null,ALPHANUMERICAL:w,AMPERSAND:R,APOSTROPHE:S,ASCIINUMERICAL:v,ASTERISK:T,AT:"AT",BACKSLASH:U,BACKTICK:V,CARET:W,CLOSEANGLEBRACKET:I,CLOSEBRACE:C,CLOSEBRACKET:E,CLOSEPAREN:G,COLON:X,COMMA:Y,DOLLAR:Z,DOT:"DOT",EMOJI:am,EQUALS:$,EXCLAMATION:_,FULLWIDTHGREATERTHAN:Q,FULLWIDTHLEFTPAREN:J,FULLWIDTHLESSTHAN:P,FULLWIDTHMIDDLEDOT:ah,FULLWIDTHRIGHTPAREN:K,HYPHEN:aa,LEFTCORNERBRACKET:L,LEFTWHITECORNERBRACKET:N,LOCALHOST:x,NL:"NL",NUM:"NUM",OPENANGLEBRACKET:H,OPENBRACE:B,OPENBRACKET:D,OPENPAREN:F,PERCENT:ab,PIPE:ac,PLUS:ad,POUND:ae,QUERY:af,QUOTE:ag,RIGHTCORNERBRACKET:M,RIGHTWHITECORNERBRACKET:O,SCHEME:z,SEMI:ai,SLASH:aj,SLASH_SCHEME:A,SYM:"SYM",TILDE:ak,TLD:"TLD",UNDERSCORE:al,UTLD:y,UWORD:u,WORD:t,WS:"WS"});let ao=/[a-z]/,ap=/\p{L}/u,aq=/\p{Emoji}/u,ar=/\d/,as=/\s/,at=null,au=null;function av(a,b){let c=function(a){let b=[],c=a.length,d=0;for(;d<c;){let e,f=a.charCodeAt(d),g=f<55296||f>56319||d+1===c||(e=a.charCodeAt(d+1))<56320||e>57343?a[d]:a.slice(d,d+2);b.push(g),d+=g.length}return b}(b.replace(/[A-Z]/g,a=>a.toLowerCase())),d=c.length,e=[],f=0,g=0;for(;g<d;){let h=a,i=null,j=0,k=null,l=-1,m=-1;for(;g<d&&(i=h.go(c[g]));)(h=i).accepts()?(l=0,m=0,k=h):l>=0&&(l+=c[g].length,m++),j+=c[g].length,f+=c[g].length,g++;f-=l,g-=m,j-=l,e.push({t:k.t,v:b.slice(f-j,f),s:f-j,e:f})}return e}function aw(a,b,c,d,e){let f,g=b.length;for(let c=0;c<g-1;c++){let g=b[c];a.j[g]?f=a.j[g]:((f=new o(d)).jr=e.slice(),a.j[g]=f),a=f}return(f=new o(c)).jr=e.slice(),a.j[b[g-1]]=f,f}function ax(a){let b=[],c=[],d=0;for(;d<a.length;){let e=0;for(;"0123456789".indexOf(a[d+e])>=0;)e++;if(e>0){b.push(c.join(""));for(let b=parseInt(a.substring(d,d+e),10);b>0;b--)c.pop();d+=e}else c.push(a[d]),d++}return b}let ay={defaultProtocol:"http",events:null,format:aA,formatHref:aA,nl2br:!1,tagName:"a",target:null,rel:null,validate:!0,truncate:1/0,className:null,attributes:null,ignoreTags:[],render:null};function az(a,b=null){let c=e({},ay);a&&(c=e(c,a instanceof az?a.o:a));let d=c.ignoreTags,f=[];for(let a=0;a<d.length;a++)f.push(d[a].toUpperCase());this.o=c,b&&(this.defaultRender=b),this.ignoreTags=f}function aA(a){return a}function aB(a,b){this.t="token",this.v=a,this.tk=b}function aC(a,b){class c extends aB{constructor(b,c){super(b,c),this.t=a}}for(let a in b)c.prototype[a]=b[a];return c.t=a,c}az.prototype={o:ay,ignoreTags:[],defaultRender:a=>a,check(a){return this.get("validate",a.toString(),a)},get(a,b,c){let d=null!=b,e=this.o[a];return e&&("object"==typeof e?"function"==typeof(e=c.t in e?e[c.t]:ay[a])&&d&&(e=e(b,c)):"function"==typeof e&&d&&(e=e(b,c.t,c))),e},getObj(a,b,c){let d=this.o[a];return"function"==typeof d&&null!=b&&(d=d(b,c.t,c)),d},render(a){let b=a.render(this);return(this.get("render",null,a)||this.defaultRender)(b,a.t,a)}},aB.prototype={isLink:!1,toString(){return this.v},toHref(a){return this.toString()},toFormattedString(a){let b=this.toString(),c=a.get("truncate",b,this),d=a.get("format",b,this);return c&&d.length>c?d.substring(0,c)+"…":d},toFormattedHref(a){return a.get("formatHref",this.toHref(a.get("defaultProtocol")),this)},startIndex(){return this.tk[0].s},endIndex(){return this.tk[this.tk.length-1].e},toObject(a=ay.defaultProtocol){return{type:this.t,value:this.toString(),isLink:this.isLink,href:this.toHref(a),start:this.startIndex(),end:this.endIndex()}},toFormattedObject(a){return{type:this.t,value:this.toFormattedString(a),isLink:this.isLink,href:this.toFormattedHref(a),start:this.startIndex(),end:this.endIndex()}},validate(a){return a.get("validate",this.toString(),this)},render(a){let b=this.toHref(a.get("defaultProtocol")),c=a.get("formatHref",b,this),d=a.get("tagName",b,this),f=this.toFormattedString(a),g={},h=a.get("className",b,this),i=a.get("target",b,this),j=a.get("rel",b,this),k=a.getObj("attributes",b,this),l=a.getObj("events",b,this);return g.href=c,h&&(g.class=h),i&&(g.target=i),j&&(g.rel=j),k&&e(g,k),{tagName:d,attributes:g,content:f,eventListeners:l}}};let aD=aC("email",{isLink:!0,toHref(){return"mailto:"+this.toString()}}),aE=aC("text"),aF=aC("nl"),aG=aC("url",{isLink:!0,toHref(a=ay.defaultProtocol){return this.hasProtocol()?this.v:`${a}://${this.v}`},hasProtocol(){let a=this.tk;return a.length>=2&&a[0].t!==x&&a[1].t===X}}),aH=a=>new o(a);function aI(a,b,c){let d=c[0].s,e=c[c.length-1].e;return new a(b.slice(d,e),c)}let aJ="undefined"!=typeof console&&console&&console.warn||(()=>{}),aK={scanner:null,parser:null,tokenQueue:[],pluginQueue:[],customSchemes:[],initialized:!1};function aL(a,b=!1){if(aK.initialized&&aJ(`linkifyjs: already initialized - will not register custom scheme "${a}" until manual call of linkify.init(). Register all schemes and plugins before invoking linkify the first time.`),!/^[0-9a-z]+(-[0-9a-z]+)*$/.test(a))throw Error(`linkifyjs: incorrect scheme format.
1. Must only contain digits, lowercase ASCII letters or "-"
2. Cannot start or end with "-"
3. "-" cannot repeat`);aK.customSchemes.push([a,b])}function aM(a){return aK.initialized||function(){aK.scanner=function(a=[]){let b={};o.groups=b;let c=new o;null==at&&(at=ax("aaa1rp3bb0ott3vie4c1le2ogado5udhabi7c0ademy5centure6ountant0s9o1tor4d0s1ult4e0g1ro2tna4f0l1rica5g0akhan5ency5i0g1rbus3force5tel5kdn3l0ibaba4pay4lfinanz6state5y2sace3tom5m0azon4ericanexpress7family11x2fam3ica3sterdam8nalytics7droid5quan4z2o0l2partments8p0le4q0uarelle8r0ab1mco4chi3my2pa2t0e3s0da2ia2sociates9t0hleta5torney7u0ction5di0ble3o3spost5thor3o0s4w0s2x0a2z0ure5ba0by2idu3namex4d1k2r0celona5laycard4s5efoot5gains6seball5ketball8uhaus5yern5b0c1t1va3cg1n2d1e0ats2uty4er2rlin4st0buy5t2f1g1h0arti5i0ble3d1ke2ng0o3o1z2j1lack0friday9ockbuster8g1omberg7ue3m0s1w2n0pparibas9o0ats3ehringer8fa2m1nd2o0k0ing5sch2tik2on4t1utique6x2r0adesco6idgestone9oadway5ker3ther5ussels7s1t1uild0ers6siness6y1zz3v1w1y1z0h3ca0b1fe2l0l1vinklein9m0era3p2non3petown5ital0one8r0avan4ds2e0er0s4s2sa1e1h1ino4t0ering5holic7ba1n1re3c1d1enter4o1rn3f0a1d2g1h0anel2nel4rity4se2t2eap3intai5ristmas6ome4urch5i0priani6rcle4sco3tadel4i0c2y3k1l0aims4eaning6ick2nic1que6othing5ud3ub0med6m1n1o0ach3des3ffee4llege4ogne5m0mbank4unity6pany2re3uter5sec4ndos3struction8ulting7tact3ractors9oking4l1p2rsica5untry4pon0s4rses6pa2r0edit0card4union9icket5own3s1uise0s6u0isinella9v1w1x1y0mru3ou3z2dad1nce3ta1e1ing3sun4y2clk3ds2e0al0er2s3gree4livery5l1oitte5ta3mocrat6ntal2ist5si0gn4v2hl2iamonds6et2gital5rect0ory7scount3ver5h2y2j1k1m1np2o0cs1tor4g1mains5t1wnload7rive4tv2ubai3nlop4pont4rban5vag2r2z2earth3t2c0o2deka3u0cation8e1g1mail3erck5nergy4gineer0ing9terprises10pson4quipment8r0icsson6ni3s0q1tate5t1u0rovision8s2vents5xchange6pert3osed4ress5traspace10fage2il1rwinds6th3mily4n0s2rm0ers5shion4t3edex3edback6rrari3ero6i0delity5o2lm2nal1nce1ial7re0stone6mdale6sh0ing5t0ness6j1k1lickr3ghts4r2orist4wers5y2m1o0o0d1tball6rd1ex2sale4um3undation8x2r0ee1senius7l1ogans4ntier7tr2ujitsu5n0d2rniture7tbol5yi3ga0l0lery3o1up4me0s3p1rden4y2b0iz3d0n2e0a1nt0ing5orge5f1g0ee3h1i0ft0s3ves2ing5l0ass3e1obal2o4m0ail3bh2o1x2n1odaddy5ld0point6f2o0dyear5g0le4p1t1v2p1q1r0ainger5phics5tis4een3ipe3ocery4up4s1t1u0cci3ge2ide2tars5ru3w1y2hair2mburg5ngout5us3bo2dfc0bank7ealth0care8lp1sinki6re1mes5iphop4samitsu7tachi5v2k0t2m1n1ockey4ldings5iday5medepot5goods5s0ense7nda3rse3spital5t0ing5t0els3mail5use3w2r1sbc3t1u0ghes5yatt3undai7ibm2cbc2e1u2d1e0ee3fm2kano4l1m0amat4db2mo0bilien9n0c1dustries8finiti5o2g1k1stitute6urance4e4t0ernational10uit4vestments10o1piranga7q1r0ish4s0maili5t0anbul7t0au2v3jaguar4va3cb2e0ep2tzt3welry6io2ll2m0p2nj2o0bs1urg4t1y2p0morgan6rs3uegos4niper7kaufen5ddi3e0rryhotels6properties14fh2g1h1i0a1ds2m1ndle4tchen5wi3m1n1oeln3matsu5sher5p0mg2n2r0d1ed3uokgroup8w1y0oto4z2la0caixa5mborghini8er3nd0rover6xess5salle5t0ino3robe5w0yer5b1c1ds2ease3clerc5frak4gal2o2xus4gbt3i0dl2fe0insurance9style7ghting6ke2lly3mited4o2ncoln4k2ve1ing5k1lc1p2oan0s3cker3us3l1ndon4tte1o3ve3pl0financial11r1s1t0d0a3u0ndbeck6xe1ury5v1y2ma0drid4if1son4keup4n0agement7go3p1rket0ing3s4riott5shalls7ttel5ba2c0kinsey7d1e0d0ia3et2lbourne7me1orial6n0u2rckmsd7g1h1iami3crosoft7l1ni1t2t0subishi9k1l0b1s2m0a2n1o0bi0le4da2e1i1m1nash3ey2ster5rmon3tgage6scow4to0rcycles9v0ie4p1q1r1s0d2t0n1r2u0seum3ic4v1w1x1y1z2na0b1goya4me2vy3ba2c1e0c1t0bank4flix4work5ustar5w0s2xt0direct7us4f0l2g0o2hk2i0co2ke1on3nja3ssan1y5l1o0kia3rton4w0ruz3tv4p1r0a1w2tt2u1yc2z2obi1server7ffice5kinawa6layan0group9lo3m0ega4ne1g1l0ine5oo2pen3racle3nge4g0anic5igins6saka4tsuka4t2vh3pa0ge2nasonic7ris2s1tners4s1y3y2ccw3e0t2f0izer5g1h0armacy6d1ilips5one2to0graphy6s4ysio5ics1tet2ures6d1n0g1k2oneer5zza4k1l0ace2y0station9umbing5s3m1n0c2ohl2ker3litie5rn2st3r0america6xi3ess3ime3o0d0uctions8f1gressive8mo2perties3y5tection8u0dential9s1t1ub2w0c2y2qa1pon3uebec3st5racing4dio4e0ad1lestate6tor2y4cipes5d0stone5umbrella9hab3ise0n3t2liance6n0t0als5pair3ort3ublican8st0aurant8view0s5xroth6ich0ardli6oh3l1o1p2o0cks3deo3gers4om3s0vp3u0gby3hr2n2w0e2yukyu6sa0arland6fe0ty4kura4le1on3msclub4ung5ndvik0coromant12ofi4p1rl2s1ve2xo3b0i1s2c0b1haeffler7midt4olarships8ol3ule3warz5ience5ot3d1e0arch3t2cure1ity6ek2lect4ner3rvices6ven3w1x0y3fr2g1h0angrila6rp3ell3ia1ksha5oes2p0ping5uji3w3i0lk2na1gles5te3j1k0i0n2y0pe4l0ing4m0art3ile4n0cf3o0ccer3ial4ftbank4ware6hu2lar2utions7ng1y2y2pa0ce3ort2t3r0l2s1t0ada2ples4r1tebank4farm7c0group6ockholm6rage3e3ream4udio2y3yle4u0cks3pplies3y2ort5rf1gery5zuki5v1watch4iss4x1y0dney4stems6z2tab1ipei4lk2obao4rget4tamotors6r2too4x0i3c0i2d0k2eam2ch0nology8l1masek5nnis4va3f1g1h0d1eater2re6iaa2ckets5enda4ps2res2ol4j0maxx4x2k0maxx5l1m0all4n1o0day3kyo3ols3p1ray3shiba5tal3urs3wn2yota3s3r0ade1ing4ining5vel0ers0insurance16ust3v2t1ube2i1nes3shu4v0s2w1z2ua1bank3s2g1k1nicom3versity8o2ol2ps2s1y1z2va0cations7na1guard7c1e0gas3ntures6risign5m\xf6gensberater2ung14sicherung10t2g1i0ajes4deo3g1king4llas4n1p1rgin4sa1ion4va1o3laanderen9n1odka3lvo3te1ing3o2yage5u2wales2mart4ter4ng0gou5tch0es6eather0channel12bcam3er2site5d0ding5ibo2r3f1hoswho6ien2ki2lliamhill9n0dows4e1ners6me2olterskluwer11odside6rk0s2ld3w2s1tc1f3xbox3erox4ihuan4n2xx2yz3yachts4hoo3maxun5ndex5e1odobashi7ga2kohama6u0tube6t1un3za0ppos4ra3ero3ip2m1one3uerich6w2")),null==au&&(au=ax("ελ1υ2бг1ел3дети4ею2католик6ом3мкд2он1сква6онлайн5рг3рус2ф2сайт3рб3укр3қаз3հայ3ישראל5קום3ابوظبي5رامكو5لاردن4بحرين5جزائر5سعودية6عليان5مغرب5مارات5یران5بارت2زار4يتك3ھارت5تونس4سودان3رية5شبكة4عراق2ب2مان4فلسطين6قطر3كاثوليك6وم3مصر2ليسيا5وريتانيا7قع4همراه5پاکستان7ڀارت4कॉम3नेट3भारत0म्3ोत5संगठन5বাংলা5ভারত2ৰত4ਭਾਰਤ4ભારત4ଭାରତ4இந்தியா6லங்கை6சிங்கப்பூர்11భారత్5ಭಾರತ4ഭാരതം5ලංකා4คอม3ไทย3ລາວ3გე2みんな3アマゾン4クラウド4グーグル4コム2ストア3セール3ファッション6ポイント4世界2中信1国1國1文网3亚马逊3企业2佛山2信息2健康2八卦2公司1益2台湾1灣2商城1店1标2嘉里0大酒店5在线2大拿2天主教3娱乐2家電2广东2微博2慈善2我爱你3手机2招聘2政务1府2新加坡2闻2时尚2書籍2机构2淡马锡3游戏2澳門2点看2移动2组织机构4网址1店1站1络2联通2谷歌2购物2通販2集团2電訊盈科4飞利浦3食品2餐厅2香格里拉3港2닷넷1컴2삼성2한국2")),s(c,"'",S),s(c,"{",B),s(c,"}",C),s(c,"[",D),s(c,"]",E),s(c,"(",F),s(c,")",G),s(c,"<",H),s(c,">",I),s(c,"（",J),s(c,"）",K),s(c,"「",L),s(c,"」",M),s(c,"『",N),s(c,"』",O),s(c,"＜",P),s(c,"＞",Q),s(c,"&",R),s(c,"*",T),s(c,"@","AT"),s(c,"`",V),s(c,"^",W),s(c,":",X),s(c,",",Y),s(c,"$",Z),s(c,".","DOT"),s(c,"=",$),s(c,"!",_),s(c,"-",aa),s(c,"%",ab),s(c,"|",ac),s(c,"+",ad),s(c,"#",ae),s(c,"?",af),s(c,'"',ag),s(c,"/",aj),s(c,";",ai),s(c,"~",ak),s(c,"_",al),s(c,"\\",U),s(c,"・",ah);let d=q(c,ar,"NUM",{[f]:!0});q(d,ar,d);let p=q(d,ao,v,{[i]:!0}),av=q(d,ap,w,{[j]:!0}),ay=q(c,ao,t,{[g]:!0});q(ay,ar,p),q(ay,ao,ay),q(p,ar,p),q(p,ao,p);let az=q(c,ap,u,{[h]:!0});q(az,ao),q(az,ar,av),q(az,ap,az),q(av,ar,av),q(av,ao),q(av,ap,av);let aA=s(c,"\n","NL",{[m]:!0}),aB=s(c,"\r","WS",{[m]:!0}),aC=q(c,as,"WS",{[m]:!0});s(c,"￼",aC),s(aB,"\n",aA),s(aB,"￼",aC),q(aB,as,aC),s(aC,"\r"),s(aC,"\n"),q(aC,as,aC),s(aC,"￼",aC);let aD=q(c,aq,am,{[l]:!0});s(aD,"#"),q(aD,aq,aD),s(aD,"️",aD);let aE=s(aD,"‍");s(aE,"#"),q(aE,aq,aD);let aF=[[ao,ay],[ar,p]],aG=[[ao,null],[ap,az],[ar,av]];for(let a=0;a<at.length;a++)aw(c,at[a],"TLD",t,aF);for(let a=0;a<au.length;a++)aw(c,au[a],y,u,aG);n("TLD",{tld:!0,ascii:!0},b),n(y,{utld:!0,alpha:!0},b),aw(c,"file",z,t,aF),aw(c,"mailto",z,t,aF),aw(c,"http",A,t,aF),aw(c,"https",A,t,aF),aw(c,"ftp",A,t,aF),aw(c,"ftps",A,t,aF),n(z,{scheme:!0,ascii:!0},b),n(A,{slashscheme:!0,ascii:!0},b),a=a.sort((a,b)=>a[0]>b[0]?1:-1);for(let b=0;b<a.length;b++){let d=a[b][0],e=a[b][1]?{scheme:!0}:{slashscheme:!0};d.indexOf("-")>=0?e[k]=!0:ao.test(d)?ar.test(d)?e[i]=!0:e[g]=!0:e[f]=!0,r(c,d,d,e)}return r(c,"localhost",x,{ascii:!0}),c.jd=new o("SYM"),{start:c,tokens:e({groups:b},an)}}(aK.customSchemes);for(let a=0;a<aK.tokenQueue.length;a++)aK.tokenQueue[a][1]({scanner:aK.scanner});aK.parser=function({groups:a}){let b=a.domain.concat([R,T,"AT",U,V,W,Z,$,aa,"NUM",ab,ac,ad,ae,aj,"SYM",ak,al]),c=[S,X,Y,"DOT",_,ab,af,ag,ai,H,I,B,C,E,D,F,G,J,K,L,M,N,O,P,Q],d=[R,S,T,U,V,W,Z,$,aa,B,C,ab,ac,ad,ae,af,aj,"SYM",ak,al],e=aH(),f=s(e,ak);p(f,d,f),p(f,a.domain,f);let g=aH(),h=aH(),i=aH();p(e,a.domain,g),p(e,a.scheme,h),p(e,a.slashscheme,i),p(g,d,f),p(g,a.domain,g);let j=s(g,"AT");s(f,"AT",j),s(h,"AT",j),s(i,"AT",j);let k=s(f,"DOT");p(k,d,f),p(k,a.domain,f);let l=aH();p(j,a.domain,l),p(l,a.domain,l);let m=s(l,"DOT");p(m,a.domain,l);let n=aH(aD);p(m,a.tld,n),p(m,a.utld,n),s(j,x,n);let o=s(l,aa);s(o,aa,o),p(o,a.domain,l),p(n,a.domain,l),s(n,"DOT",m),s(n,aa,o),p(s(n,X),a.numeric,aD);let q=s(g,aa),r=s(g,"DOT");s(q,aa,q),p(q,a.domain,g),p(r,d,f),p(r,a.domain,g);let t=aH(aG);p(r,a.tld,t),p(r,a.utld,t),p(t,a.domain,g),p(t,d,f),s(t,"DOT",r),s(t,aa,q),s(t,"AT",j);let u=s(t,X),v=aH(aG);p(u,a.numeric,v);let w=aH(aG),y=aH();p(w,b,w),p(w,c,y),p(y,b,w),p(y,c,y),s(t,aj,w),s(v,aj,w);let z=s(h,X),A=s(i,X),ah=s(A,aj),am=s(ah,aj);p(h,a.domain,g),s(h,"DOT",r),s(h,aa,q),p(i,a.domain,g),s(i,"DOT",r),s(i,aa,q),p(z,a.domain,w),s(z,aj,w),s(z,af,w),p(am,a.domain,w),p(am,b,w),s(am,aj,w);let ao=[[B,C],[D,E],[F,G],[H,I],[J,K],[L,M],[N,O],[P,Q]];for(let a=0;a<ao.length;a++){let[d,e]=ao[a],f=s(w,d);s(y,d,f),s(f,e,w);let g=aH(aG);p(f,b,g);let h=aH();p(f,c),p(g,b,g),p(g,c,h),p(h,b,g),p(h,c,h),s(g,e,w),s(h,e,w)}return s(e,x,t),s(e,"NL",aF),{start:e,tokens:an}}(aK.scanner.tokens);for(let a=0;a<aK.pluginQueue.length;a++)aK.pluginQueue[a][1]({scanner:aK.scanner,parser:aK.parser});aK.initialized=!0}(),function(a,b,c){let d=c.length,e=0,f=[],g=[];for(;e<d;){let h=a,i=null,j=null,k=0,l=null,m=-1;for(;e<d&&!(i=h.go(c[e].t));)g.push(c[e++]);for(;e<d&&(j=i||h.go(c[e].t));)i=null,(h=j).accepts()?(m=0,l=h):m>=0&&m++,e++,k++;if(m<0)(e-=k)<d&&(g.push(c[e]),e++);else{g.length>0&&(f.push(aI(aE,b,g)),g=[]),e-=m,k-=m;let a=l.t,d=c.slice(e-k,e);f.push(aI(a,b,d))}}return g.length>0&&f.push(aI(aE,b,g)),f}(aK.parser.start,a,av(aK.scanner.start,a))}function aN(a,b=null,c=null){if(b&&"object"==typeof b){if(c)throw Error(`linkifyjs: Invalid link type ${b}; must be a string`);c=b,b=null}let d=new az(c),e=aM(a),f=[];for(let a=0;a<e.length;a++){let c=e[a];c.isLink&&(!b||c.t===b)&&d.check(c)&&f.push(c.toFormattedObject(d))}return f}aM.scan=av;var aO=c(35857),aP="[\0- \xa0 ᠎ -\u2029 　]",aQ=new RegExp(aP),aR=RegExp(`${aP}$`),aS=RegExp(aP,"g");function aT(a,b){let c=["http","https","ftp","ftps","mailto","tel","callto","sms","cid","xmpp"];return b&&b.forEach(a=>{let b="string"==typeof a?a:a.scheme;b&&c.push(b)}),!a||a.replace(aS,"").match(RegExp(`^(?:(?:${c.join("|")}):|[^a-z]|[a-z0-9+.-]+(?:[^a-z+.-:]|$))`,"i"))}var aU=d.CU.create({name:"link",priority:1e3,keepOnSplit:!1,exitable:!0,onCreate(){this.options.validate&&!this.options.shouldAutoLink&&(this.options.shouldAutoLink=this.options.validate,console.warn("The `validate` option is deprecated. Rename to the `shouldAutoLink` option instead.")),this.options.protocols.forEach(a=>{if("string"==typeof a)return void aL(a);aL(a.scheme,a.optionalSlashes)})},onDestroy(){o.groups={},aK.scanner=null,aK.parser=null,aK.tokenQueue=[],aK.pluginQueue=[],aK.customSchemes=[],aK.initialized=!1},inclusive(){return this.options.autolink},addOptions:()=>({openOnClick:!0,enableClickSelection:!1,linkOnPaste:!0,autolink:!0,protocols:[],defaultProtocol:"http",HTMLAttributes:{target:"_blank",rel:"noopener noreferrer nofollow",class:null},isAllowedUri:(a,b)=>!!aT(a,b.protocols),validate:a=>!!a,shouldAutoLink:a=>!!a}),addAttributes(){return{href:{default:null,parseHTML:a=>a.getAttribute("href")},target:{default:this.options.HTMLAttributes.target},rel:{default:this.options.HTMLAttributes.rel},class:{default:this.options.HTMLAttributes.class}}},parseHTML(){return[{tag:"a[href]",getAttrs:a=>{let b=a.getAttribute("href");return!!b&&!!this.options.isAllowedUri(b,{defaultValidate:a=>!!aT(a,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&null}}]},renderHTML({HTMLAttributes:a}){return this.options.isAllowedUri(a.href,{defaultValidate:a=>!!aT(a,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})?["a",(0,d.KV)(this.options.HTMLAttributes,a),0]:["a",(0,d.KV)(this.options.HTMLAttributes,{...a,href:""}),0]},addCommands(){return{setLink:a=>({chain:b})=>{let{href:c}=a;return!!this.options.isAllowedUri(c,{defaultValidate:a=>!!aT(a,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol})&&b().setMark(this.name,a).setMeta("preventAutolink",!0).run()},toggleLink:a=>({chain:b})=>{let{href:c}=a||{};return(!c||!!this.options.isAllowedUri(c,{defaultValidate:a=>!!aT(a,this.options.protocols),protocols:this.options.protocols,defaultProtocol:this.options.defaultProtocol}))&&b().toggleMark(this.name,a,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()},unsetLink:()=>({chain:a})=>a().unsetMark(this.name,{extendEmptyMarkRange:!0}).setMeta("preventAutolink",!0).run()}},addPasteRules(){return[(0,d.Zc)({find:a=>{let b=[];if(a){let{protocols:c,defaultProtocol:d}=this.options,e=aN(a).filter(a=>a.isLink&&this.options.isAllowedUri(a.value,{defaultValidate:a=>!!aT(a,c),protocols:c,defaultProtocol:d}));e.length&&e.forEach(a=>b.push({text:a.value,data:{href:a.href},index:a.start}))}return b},type:this.type,getAttributes:a=>{var b;return{href:null==(b=a.data)?void 0:b.href}}})]},addProseMirrorPlugins(){var a,b,c;let e=[],{protocols:f,defaultProtocol:g}=this.options;return this.options.autolink&&e.push((a={type:this.type,defaultProtocol:this.options.defaultProtocol,validate:a=>this.options.isAllowedUri(a,{defaultValidate:a=>!!aT(a,f),protocols:f,defaultProtocol:g}),shouldAutoLink:this.options.shouldAutoLink},new aO.k_({key:new aO.hs("autolink"),appendTransaction:(b,c,e)=>{let f=b.some(a=>a.docChanged)&&!c.doc.eq(e.doc),g=b.some(a=>a.getMeta("preventAutolink"));if(!f||g)return;let{tr:h}=e,i=(0,d.T7)(c.doc,[...b]);if((0,d.FF)(i).forEach(({newRange:b})=>{let c,f,g=(0,d.Nx)(e.doc,b,a=>a.isTextblock);if(g.length>1)c=g[0],f=e.doc.textBetween(c.pos,c.pos+c.node.nodeSize,void 0," ");else if(g.length){let a=e.doc.textBetween(b.from,b.to," "," ");if(!aR.test(a))return;c=g[0],f=e.doc.textBetween(c.pos,b.to,void 0," ")}if(c&&f){let b=f.split(aQ).filter(Boolean);if(b.length<=0)return!1;let g=b[b.length-1],i=c.pos+f.lastIndexOf(g);if(!g)return!1;let j=aM(g).map(b=>b.toObject(a.defaultProtocol));if(!(1===j.length?j[0].isLink:3===j.length&&!!j[1].isLink&&["()","[]"].includes(j[0].value+j[2].value)))return!1;j.filter(a=>a.isLink).map(a=>({...a,from:i+a.start+1,to:i+a.end+1})).filter(a=>!e.schema.marks.code||!e.doc.rangeHasMark(a.from,a.to,e.schema.marks.code)).filter(b=>a.validate(b.value)).filter(b=>a.shouldAutoLink(b.value)).forEach(b=>{(0,d.hO)(b.from,b.to,e.doc).some(b=>b.mark.type===a.type)||h.addMark(b.from,b.to,a.type.create({href:b.href}))})}}),h.steps.length)return h}}))),!0===this.options.openOnClick&&e.push((b={type:this.type,editor:this.editor,enableClickSelection:this.options.enableClickSelection},new aO.k_({key:new aO.hs("handleClickLink"),props:{handleClick:(a,c,e)=>{var f,g;if(0!==e.button||!a.editable)return!1;let h=null;if(e.target instanceof HTMLAnchorElement)h=e.target;else{let a=e.target,b=[];for(;"DIV"!==a.nodeName;)b.push(a),a=a.parentNode;h=b.find(a=>"A"===a.nodeName)}if(!h)return!1;let i=(0,d.gu)(a.state,b.type.name),j=null!=(f=null==h?void 0:h.href)?f:i.href,k=null!=(g=null==h?void 0:h.target)?g:i.target;return b.enableClickSelection&&b.editor.commands.extendMarkRange(b.type.name),!!h&&!!j&&(window.open(j,k),!0)}}}))),this.options.linkOnPaste&&e.push((c={editor:this.editor,defaultProtocol:this.options.defaultProtocol,type:this.type},new aO.k_({key:new aO.hs("handlePasteLink"),props:{handlePaste:(a,b,d)=>{let{state:e}=a,{selection:f}=e,{empty:g}=f;if(g)return!1;let h="";d.content.forEach(a=>{h+=a.textContent});let i=aN(h,{defaultProtocol:c.defaultProtocol}).find(a=>a.isLink&&a.value===h);return!!h&&!!i&&c.editor.commands.setMark(c.type,{href:i.href})}}}))),e}}),aV=aU},7203:(a,b,c)=>{c.d(b,{$Z:()=>n,hG:()=>t});var d=c(43210),e=c(51215),f=c(57379),g=c(60687),h=c(81373),i=c(10274),j=c(39733),k=({contentComponent:a})=>{let b=(0,f.useSyncExternalStore)(a.subscribe,a.getSnapshot,a.getServerSnapshot);return(0,g.jsx)(g.Fragment,{children:Object.values(b)})},l=class extends d.Component{constructor(a){var b;super(a),this.editorContentRef=d.createRef(),this.initialized=!1,this.state={hasContentComponentInitialized:!!(null==(b=a.editor)?void 0:b.contentComponent)}}componentDidMount(){this.init()}componentDidUpdate(){this.init()}init(){let a=this.props.editor;if(a&&!a.isDestroyed&&a.options.element){if(a.contentComponent)return;let b=this.editorContentRef.current;b.append(...a.options.element.childNodes),a.setOptions({element:b}),a.contentComponent=function(){let a=new Set,b={};return{subscribe:b=>(a.add(b),()=>{a.delete(b)}),getSnapshot:()=>b,getServerSnapshot:()=>b,setRenderer(c,d){b={...b,[c]:e.createPortal(d.reactElement,d.element,c)},a.forEach(a=>a())},removeRenderer(c){let d={...b};delete d[c],b=d,a.forEach(a=>a())}}}(),this.state.hasContentComponentInitialized||(this.unsubscribeToContentComponent=a.contentComponent.subscribe(()=>{this.setState(a=>a.hasContentComponentInitialized?a:{hasContentComponentInitialized:!0}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent()})),a.createNodeViews(),this.initialized=!0}}componentWillUnmount(){var a;let b=this.props.editor;if(!b||(this.initialized=!1,b.isDestroyed||b.view.setProps({nodeViews:{}}),this.unsubscribeToContentComponent&&this.unsubscribeToContentComponent(),b.contentComponent=null,!(null==(a=b.options.element)?void 0:a.firstChild)))return;let c=document.createElement("div");c.append(...b.options.element.childNodes),b.setOptions({element:c})}render(){let{editor:a,innerRef:b,...c}=this.props;return(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)("div",{ref:((...a)=>b=>{a.forEach(a=>{"function"==typeof a?a(b):a&&(a.current=b)})})(b,this.editorContentRef),...c}),(null==a?void 0:a.contentComponent)&&(0,g.jsx)(k,{contentComponent:a.contentComponent})]})}},m=(0,d.forwardRef)((a,b)=>{let c=d.useMemo(()=>Math.floor(0xffffffff*Math.random()).toString(),[a.editor]);return d.createElement(l,{key:c,innerRef:b,...a})}),n=d.memo(m),o="undefined"!=typeof window?d.useLayoutEffect:d.useEffect,p=class{constructor(a){this.transactionNumber=0,this.lastTransactionNumber=0,this.subscribers=new Set,this.editor=a,this.lastSnapshot={editor:a,transactionNumber:0},this.getSnapshot=this.getSnapshot.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.watch=this.watch.bind(this),this.subscribe=this.subscribe.bind(this)}getSnapshot(){return this.transactionNumber===this.lastTransactionNumber||(this.lastTransactionNumber=this.transactionNumber,this.lastSnapshot={editor:this.editor,transactionNumber:this.transactionNumber}),this.lastSnapshot}getServerSnapshot(){return{editor:null,transactionNumber:0}}subscribe(a){return this.subscribers.add(a),()=>{this.subscribers.delete(a)}}watch(a){if(this.editor=a,this.editor){let a=()=>{this.transactionNumber+=1,this.subscribers.forEach(a=>a())},b=this.editor;return b.on("transaction",a),()=>{b.off("transaction",a)}}}},q="undefined"==typeof window,r=q||!!("undefined"!=typeof window&&window.next),s=class a{constructor(a){this.editor=null,this.subscriptions=new Set,this.isComponentMounted=!1,this.previousDeps=null,this.instanceId="",this.options=a,this.subscriptions=new Set,this.setEditor(this.getInitialEditor()),this.scheduleDestroy(),this.getEditor=this.getEditor.bind(this),this.getServerSnapshot=this.getServerSnapshot.bind(this),this.subscribe=this.subscribe.bind(this),this.refreshEditorInstance=this.refreshEditorInstance.bind(this),this.scheduleDestroy=this.scheduleDestroy.bind(this),this.onRender=this.onRender.bind(this),this.createEditor=this.createEditor.bind(this)}setEditor(a){this.editor=a,this.instanceId=Math.random().toString(36).slice(2,9),this.subscriptions.forEach(a=>a())}getInitialEditor(){if(void 0===this.options.current.immediatelyRender)return q||r?null:this.createEditor();return this.options.current.immediatelyRender,this.options.current.immediatelyRender?this.createEditor():null}createEditor(){let a={...this.options.current,onBeforeCreate:(...a)=>{var b,c;return null==(c=(b=this.options.current).onBeforeCreate)?void 0:c.call(b,...a)},onBlur:(...a)=>{var b,c;return null==(c=(b=this.options.current).onBlur)?void 0:c.call(b,...a)},onCreate:(...a)=>{var b,c;return null==(c=(b=this.options.current).onCreate)?void 0:c.call(b,...a)},onDestroy:(...a)=>{var b,c;return null==(c=(b=this.options.current).onDestroy)?void 0:c.call(b,...a)},onFocus:(...a)=>{var b,c;return null==(c=(b=this.options.current).onFocus)?void 0:c.call(b,...a)},onSelectionUpdate:(...a)=>{var b,c;return null==(c=(b=this.options.current).onSelectionUpdate)?void 0:c.call(b,...a)},onTransaction:(...a)=>{var b,c;return null==(c=(b=this.options.current).onTransaction)?void 0:c.call(b,...a)},onUpdate:(...a)=>{var b,c;return null==(c=(b=this.options.current).onUpdate)?void 0:c.call(b,...a)},onContentError:(...a)=>{var b,c;return null==(c=(b=this.options.current).onContentError)?void 0:c.call(b,...a)},onDrop:(...a)=>{var b,c;return null==(c=(b=this.options.current).onDrop)?void 0:c.call(b,...a)},onPaste:(...a)=>{var b,c;return null==(c=(b=this.options.current).onPaste)?void 0:c.call(b,...a)},onDelete:(...a)=>{var b,c;return null==(c=(b=this.options.current).onDelete)?void 0:c.call(b,...a)}};return new h.KE(a)}getEditor(){return this.editor}getServerSnapshot(){return null}subscribe(a){return this.subscriptions.add(a),()=>{this.subscriptions.delete(a)}}static compareOptions(a,b){return Object.keys(a).every(c=>!!["onCreate","onBeforeCreate","onDestroy","onUpdate","onTransaction","onFocus","onBlur","onSelectionUpdate","onContentError","onDrop","onPaste"].includes(c)||("extensions"===c&&a.extensions&&b.extensions?a.extensions.length===b.extensions.length&&a.extensions.every((a,c)=>{var d;return a===(null==(d=b.extensions)?void 0:d[c])}):a[c]===b[c]))}onRender(b){return()=>(this.isComponentMounted=!0,clearTimeout(this.scheduledDestructionTimeout),this.editor&&!this.editor.isDestroyed&&0===b.length?a.compareOptions(this.options.current,this.editor.options)||this.editor.setOptions({...this.options.current,editable:this.editor.isEditable}):this.refreshEditorInstance(b),()=>{this.isComponentMounted=!1,this.scheduleDestroy()})}refreshEditorInstance(a){if(this.editor&&!this.editor.isDestroyed){if(null===this.previousDeps){this.previousDeps=a;return}if(this.previousDeps.length===a.length&&this.previousDeps.every((b,c)=>b===a[c]))return}this.editor&&!this.editor.isDestroyed&&this.editor.destroy(),this.setEditor(this.createEditor()),this.previousDeps=a}scheduleDestroy(){let a=this.instanceId,b=this.editor;this.scheduledDestructionTimeout=setTimeout(()=>{if(this.isComponentMounted&&this.instanceId===a){b&&b.setOptions(this.options.current);return}b&&!b.isDestroyed&&(b.destroy(),this.instanceId===a&&this.setEditor(null))},1)}};function t(a={},b=[]){let c=(0,d.useRef)(a);c.current=a;let[e]=(0,d.useState)(()=>new s(c)),g=(0,f.useSyncExternalStore)(e.subscribe,e.getEditor,e.getServerSnapshot);return(0,d.useDebugValue)(g),(0,d.useEffect)(e.onRender(b)),!function(a){var b;let[c]=(0,d.useState)(()=>new p(a.editor)),e=(0,j.useSyncExternalStoreWithSelector)(c.subscribe,c.getSnapshot,c.getServerSnapshot,a.selector,null!=(b=a.equalityFn)?b:i);o(()=>c.watch(a.editor),[a.editor,c]),(0,d.useDebugValue)(e)}({editor:g,selector:({transactionNumber:b})=>!1===a.shouldRerenderOnTransaction||void 0===a.shouldRerenderOnTransaction?null:a.immediatelyRender&&0===b?0:b+1}),g}(0,d.createContext)({editor:null}).Consumer;var u=(0,d.createContext)({onDragStart:()=>{},nodeViewContentChildren:void 0,nodeViewContentRef:()=>{}});d.forwardRef((a,b)=>{let{onDragStart:c}=(0,d.useContext)(u),e=a.as||"div";return(0,g.jsx)(e,{...a,ref:b,"data-node-view-wrapper":"",onDragStart:c,style:{whiteSpace:"normal",...a.style}})}),d.createContext({markViewContentRef:()=>{}}),h.Db,h.Yv},9005:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]])},10274:a=>{a.exports=function a(b,c){if(b===c)return!0;if(b&&c&&"object"==typeof b&&"object"==typeof c){if(b.constructor!==c.constructor)return!1;if(Array.isArray(b)){if((d=b.length)!=c.length)return!1;for(e=d;0!=e--;)if(!a(b[e],c[e]))return!1;return!0}if(b instanceof Map&&c instanceof Map){if(b.size!==c.size)return!1;for(e of b.entries())if(!c.has(e[0]))return!1;for(e of b.entries())if(!a(e[1],c.get(e[0])))return!1;return!0}if(b instanceof Set&&c instanceof Set){if(b.size!==c.size)return!1;for(e of b.entries())if(!c.has(e[0]))return!1;return!0}if(ArrayBuffer.isView(b)&&ArrayBuffer.isView(c)){if((d=b.length)!=c.length)return!1;for(e=d;0!=e--;)if(b[e]!==c[e])return!1;return!0}if(b.constructor===RegExp)return b.source===c.source&&b.flags===c.flags;if(b.valueOf!==Object.prototype.valueOf)return b.valueOf()===c.valueOf();if(b.toString!==Object.prototype.toString)return b.toString()===c.toString();if((d=(f=Object.keys(b)).length)!==Object.keys(c).length)return!1;for(e=d;0!=e--;)if(!Object.prototype.hasOwnProperty.call(c,f[e]))return!1;for(e=d;0!=e--;){var d,e,f,g=f[e];if(("_owner"!==g||!b.$$typeof)&&!a(b[g],c[g]))return!1}return!0}return b!=b&&c!=c}},10907:(a,b,c)=>{var d=c(43210),e=c(57379),f="function"==typeof Object.is?Object.is:function(a,b){return a===b&&(0!==a||1/a==1/b)||a!=a&&b!=b},g=e.useSyncExternalStore,h=d.useRef,i=d.useEffect,j=d.useMemo,k=d.useDebugValue;b.useSyncExternalStoreWithSelector=function(a,b,c,d,e){var l=h(null);if(null===l.current){var m={hasValue:!1,value:null};l.current=m}else m=l.current;var n=g(a,(l=j(function(){function a(a){if(!i){if(i=!0,g=a,a=d(a),void 0!==e&&m.hasValue){var b=m.value;if(e(b,a))return h=b}return h=a}if(b=h,f(g,a))return b;var c=d(a);return void 0!==e&&e(b,c)?(g=a,b):(g=a,h=c)}var g,h,i=!1,j=void 0===c?null:c;return[function(){return a(b())},null===j?void 0:function(){return a(j())}]},[b,c,d,e]))[0],l[1]);return i(function(){m.hasValue=!0,m.value=n},[n]),k(n),n}},11922:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("italic",[["line",{x1:"19",x2:"10",y1:"4",y2:"4",key:"15jd3p"}],["line",{x1:"14",x2:"5",y1:"20",y2:"20",key:"bu0au3"}],["line",{x1:"15",x2:"9",y1:"4",y2:"20",key:"uljnxc"}]])},14290:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("list-ordered",[["path",{d:"M10 12h11",key:"6m4ad9"}],["path",{d:"M10 18h11",key:"11hvi2"}],["path",{d:"M10 6h11",key:"c7qv1k"}],["path",{d:"M4 10h2",key:"16xx2s"}],["path",{d:"M4 6h1v4",key:"cnovpq"}],["path",{d:"M6 18H4c0-1 2-2 2-3s-1-1.5-2-1",key:"m9a95d"}]])},16023:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]])},21782:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("heading-3",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M17.5 10.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2",key:"68ncm8"}],["path",{d:"M17 17.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2",key:"1ejuhz"}]])},25366:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},27777:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("bold",[["path",{d:"M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8",key:"mg9rjx"}]])},31110:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("redo",[["path",{d:"M21 7v6h-6",key:"3ptur4"}],["path",{d:"M3 17a9 9 0 0 1 9-9 9 9 0 0 1 6 2.3l3 2.7",key:"1kgawr"}]])},31224:(a,b,c)=>{c.d(b,{K:()=>o,w:()=>n});for(var d={8:"Backspace",9:"Tab",10:"Enter",12:"NumLock",13:"Enter",16:"Shift",17:"Control",18:"Alt",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",44:"PrintScreen",45:"Insert",46:"Delete",59:";",61:"=",91:"Meta",92:"Meta",106:"*",107:"+",108:",",109:"-",110:".",111:"/",144:"NumLock",145:"ScrollLock",160:"Shift",161:"Shift",162:"Control",163:"Control",164:"Alt",165:"Alt",173:"-",186:";",187:"=",188:",",189:"-",190:".",191:"/",192:"`",219:"[",220:"\\",221:"]",222:"'"},e={48:")",49:"!",50:"@",51:"#",52:"$",53:"%",54:"^",55:"&",56:"*",57:"(",59:":",61:"+",173:"_",186:":",187:"+",188:"<",189:"_",190:">",191:"?",192:"~",219:"{",220:"|",221:"}",222:'"'},f="undefined"!=typeof navigator&&/Mac/.test(navigator.platform),g="undefined"!=typeof navigator&&/MSIE \d|Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(navigator.userAgent),h=0;h<10;h++)d[48+h]=d[96+h]=String(h);for(var h=1;h<=24;h++)d[h+111]="F"+h;for(var h=65;h<=90;h++)d[h]=String.fromCharCode(h+32),e[h]=String.fromCharCode(h);for(var i in d)e.hasOwnProperty(i)||(e[i]=d[i]);var j=c(35857);let k="undefined"!=typeof navigator&&/Mac|iP(hone|[oa]d)/.test(navigator.platform),l="undefined"!=typeof navigator&&/Win/.test(navigator.platform);function m(a,b,c=!0){return b.altKey&&(a="Alt-"+a),b.ctrlKey&&(a="Ctrl-"+a),b.metaKey&&(a="Meta-"+a),c&&b.shiftKey&&(a="Shift-"+a),a}function n(a){return new j.k_({props:{handleKeyDown:o(a)}})}function o(a){let b=function(a){let b=Object.create(null);for(let c in a)b[function(a){let b,c,d,e,f=a.split(/-(?!$)/),g=f[f.length-1];"Space"==g&&(g=" ");for(let a=0;a<f.length-1;a++){let g=f[a];if(/^(cmd|meta|m)$/i.test(g))e=!0;else if(/^a(lt)?$/i.test(g))b=!0;else if(/^(c|ctrl|control)$/i.test(g))c=!0;else if(/^s(hift)?$/i.test(g))d=!0;else if(/^mod$/i.test(g))k?e=!0:c=!0;else throw Error("Unrecognized modifier name: "+g)}return b&&(g="Alt-"+g),c&&(g="Ctrl-"+g),e&&(g="Meta-"+g),d&&(g="Shift-"+g),g}(c)]=a[c];return b}(a);return function(a,c){var h;let i=("Esc"==(h=!(f&&c.metaKey&&c.shiftKey&&!c.ctrlKey&&!c.altKey||g&&c.shiftKey&&c.key&&1==c.key.length||"Unidentified"==c.key)&&c.key||(c.shiftKey?e:d)[c.keyCode]||c.key||"Unidentified")&&(h="Escape"),"Del"==h&&(h="Delete"),"Left"==h&&(h="ArrowLeft"),"Up"==h&&(h="ArrowUp"),"Right"==h&&(h="ArrowRight"),"Down"==h&&(h="ArrowDown"),h),j,k=b[m(i,c)];if(k&&k(a.state,a.dispatch,a))return!0;if(1==i.length&&" "!=i){if(c.shiftKey){let d=b[m(i,c,!1)];if(d&&d(a.state,a.dispatch,a))return!0}if((c.altKey||c.metaKey||c.ctrlKey)&&!(l&&c.ctrlKey&&c.altKey)&&(j=d[c.keyCode])&&j!=i){let d=b[m(j,c)];if(d&&d(a.state,a.dispatch,a))return!0}}return!1}}},31425:(a,b,c)=>{c.d(b,{A:()=>aq});var d=c(81373),e=(a,b)=>{if("slot"===a)return 0;if(a instanceof Function)return a(b);let{children:c,...d}=null!=b?b:{};if("svg"===a)throw Error("SVG elements are not supported in the JSX syntax, use the array syntax instead");return[a,d,c]},f=/^\s*>\s$/,g=d.bP.create({name:"blockquote",addOptions:()=>({HTMLAttributes:{}}),content:"block+",group:"block",defining:!0,parseHTML:()=>[{tag:"blockquote"}],renderHTML({HTMLAttributes:a}){return e("blockquote",{...(0,d.KV)(this.options.HTMLAttributes,a),children:e("slot",{})})},addCommands(){return{setBlockquote:()=>({commands:a})=>a.wrapIn(this.name),toggleBlockquote:()=>({commands:a})=>a.toggleWrap(this.name),unsetBlockquote:()=>({commands:a})=>a.lift(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-b":()=>this.editor.commands.toggleBlockquote()}},addInputRules(){return[(0,d.tG)({find:f,type:this.type})]}}),h=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))$/,i=/(?:^|\s)(\*\*(?!\s+\*\*)((?:[^*]+))\*\*(?!\s+\*\*))/g,j=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))$/,k=/(?:^|\s)(__(?!\s+__)((?:[^_]+))__(?!\s+__))/g,l=d.CU.create({name:"bold",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"strong"},{tag:"b",getAttrs:a=>"normal"!==a.style.fontWeight&&null},{style:"font-weight=400",clearMark:a=>a.type.name===this.name},{style:"font-weight",getAttrs:a=>/^(bold(er)?|[5-9]\d{2,})$/.test(a)&&null}]},renderHTML({HTMLAttributes:a}){return e("strong",{...(0,d.KV)(this.options.HTMLAttributes,a),children:e("slot",{})})},addCommands(){return{setBold:()=>({commands:a})=>a.setMark(this.name),toggleBold:()=>({commands:a})=>a.toggleMark(this.name),unsetBold:()=>({commands:a})=>a.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-b":()=>this.editor.commands.toggleBold(),"Mod-B":()=>this.editor.commands.toggleBold()}},addInputRules(){return[(0,d.OX)({find:h,type:this.type}),(0,d.OX)({find:j,type:this.type})]},addPasteRules(){return[(0,d.Zc)({find:i,type:this.type}),(0,d.Zc)({find:k,type:this.type})]}}),m=/(^|[^`])`([^`]+)`(?!`)/,n=/(^|[^`])`([^`]+)`(?!`)/g,o=d.CU.create({name:"code",addOptions:()=>({HTMLAttributes:{}}),excludes:"_",code:!0,exitable:!0,parseHTML:()=>[{tag:"code"}],renderHTML({HTMLAttributes:a}){return["code",(0,d.KV)(this.options.HTMLAttributes,a),0]},addCommands(){return{setCode:()=>({commands:a})=>a.setMark(this.name),toggleCode:()=>({commands:a})=>a.toggleMark(this.name),unsetCode:()=>({commands:a})=>a.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-e":()=>this.editor.commands.toggleCode()}},addInputRules(){return[(0,d.OX)({find:m,type:this.type})]},addPasteRules(){return[(0,d.Zc)({find:n,type:this.type})]}}),p=c(35857),q=/^```([a-z]+)?[\s\n]$/,r=/^~~~([a-z]+)?[\s\n]$/,s=d.bP.create({name:"codeBlock",addOptions:()=>({languageClassPrefix:"language-",exitOnTripleEnter:!0,exitOnArrowDown:!0,defaultLanguage:null,HTMLAttributes:{}}),content:"text*",marks:"",group:"block",code:!0,defining:!0,addAttributes(){return{language:{default:this.options.defaultLanguage,parseHTML:a=>{var b;let{languageClassPrefix:c}=this.options,d=[...(null==(b=a.firstElementChild)?void 0:b.classList)||[]].filter(a=>a.startsWith(c)).map(a=>a.replace(c,""))[0];return d||null},rendered:!1}}},parseHTML:()=>[{tag:"pre",preserveWhitespace:"full"}],renderHTML({node:a,HTMLAttributes:b}){return["pre",(0,d.KV)(this.options.HTMLAttributes,b),["code",{class:a.attrs.language?this.options.languageClassPrefix+a.attrs.language:null},0]]},addCommands(){return{setCodeBlock:a=>({commands:b})=>b.setNode(this.name,a),toggleCodeBlock:a=>({commands:b})=>b.toggleNode(this.name,"paragraph",a)}},addKeyboardShortcuts(){return{"Mod-Alt-c":()=>this.editor.commands.toggleCodeBlock(),Backspace:()=>{let{empty:a,$anchor:b}=this.editor.state.selection,c=1===b.pos;return!!a&&b.parent.type.name===this.name&&(!!c||!b.parent.textContent.length)&&this.editor.commands.clearNodes()},Enter:({editor:a})=>{if(!this.options.exitOnTripleEnter)return!1;let{state:b}=a,{selection:c}=b,{$from:d,empty:e}=c;if(!e||d.parent.type!==this.type)return!1;let f=d.parentOffset===d.parent.nodeSize-2,g=d.parent.textContent.endsWith("\n\n");return!!f&&!!g&&a.chain().command(({tr:a})=>(a.delete(d.pos-2,d.pos),!0)).exitCode().run()},ArrowDown:({editor:a})=>{if(!this.options.exitOnArrowDown)return!1;let{state:b}=a,{selection:c,doc:d}=b,{$from:e,empty:f}=c;if(!f||e.parent.type!==this.type||e.parentOffset!==e.parent.nodeSize-2)return!1;let g=e.after();return void 0!==g&&(d.nodeAt(g)?a.commands.command(({tr:a})=>(a.setSelection(p.LN.near(d.resolve(g))),!0)):a.commands.exitCode())}}},addInputRules(){return[(0,d.JJ)({find:q,type:this.type,getAttributes:a=>({language:a[1]})}),(0,d.JJ)({find:r,type:this.type,getAttributes:a=>({language:a[1]})})]},addProseMirrorPlugins(){return[new p.k_({key:new p.hs("codeBlockVSCodeHandler"),props:{handlePaste:(a,b)=>{if(!b.clipboardData||this.editor.isActive(this.type.name))return!1;let c=b.clipboardData.getData("text/plain"),d=b.clipboardData.getData("vscode-editor-data"),e=d?JSON.parse(d):void 0,f=null==e?void 0:e.mode;if(!c||!f)return!1;let{tr:g,schema:h}=a.state,i=h.text(c.replace(/\r\n?/g,"\n"));return g.replaceSelectionWith(this.type.create({language:f},i)),g.selection.$from.parent.type!==this.type&&g.setSelection(p.U3.near(g.doc.resolve(Math.max(0,g.selection.from-2)))),g.setMeta("paste",!0),a.dispatch(g),!0}}})]}}),t=d.bP.create({name:"doc",topNode:!0,content:"block+"}),u=d.bP.create({name:"hardBreak",addOptions:()=>({keepMarks:!0,HTMLAttributes:{}}),inline:!0,group:"inline",selectable:!1,linebreakReplacement:!0,parseHTML:()=>[{tag:"br"}],renderHTML({HTMLAttributes:a}){return["br",(0,d.KV)(this.options.HTMLAttributes,a)]},renderText:()=>"\n",addCommands(){return{setHardBreak:()=>({commands:a,chain:b,state:c,editor:d})=>a.first([()=>a.exitCode(),()=>a.command(()=>{let{selection:a,storedMarks:e}=c;if(a.$from.parent.type.spec.isolating)return!1;let{keepMarks:f}=this.options,{splittableMarks:g}=d.extensionManager,h=e||a.$to.parentOffset&&a.$from.marks();return b().insertContent({type:this.name}).command(({tr:a,dispatch:b})=>{if(b&&h&&f){let b=h.filter(a=>g.includes(a.type.name));a.ensureMarks(b)}return!0}).run()})])}},addKeyboardShortcuts(){return{"Mod-Enter":()=>this.editor.commands.setHardBreak(),"Shift-Enter":()=>this.editor.commands.setHardBreak()}}}),v=d.bP.create({name:"heading",addOptions:()=>({levels:[1,2,3,4,5,6],HTMLAttributes:{}}),content:"inline*",group:"block",defining:!0,addAttributes:()=>({level:{default:1,rendered:!1}}),parseHTML(){return this.options.levels.map(a=>({tag:`h${a}`,attrs:{level:a}}))},renderHTML({node:a,HTMLAttributes:b}){let c=this.options.levels.includes(a.attrs.level)?a.attrs.level:this.options.levels[0];return[`h${c}`,(0,d.KV)(this.options.HTMLAttributes,b),0]},addCommands(){return{setHeading:a=>({commands:b})=>!!this.options.levels.includes(a.level)&&b.setNode(this.name,a),toggleHeading:a=>({commands:b})=>!!this.options.levels.includes(a.level)&&b.toggleNode(this.name,"paragraph",a)}},addKeyboardShortcuts(){return this.options.levels.reduce((a,b)=>({...a,...{[`Mod-Alt-${b}`]:()=>this.editor.commands.toggleHeading({level:b})}}),{})},addInputRules(){return this.options.levels.map(a=>(0,d.JJ)({find:RegExp(`^(#{${Math.min(...this.options.levels)},${a}})\\s$`),type:this.type,getAttributes:{level:a}}))}}),w=d.bP.create({name:"horizontalRule",addOptions:()=>({HTMLAttributes:{}}),group:"block",parseHTML:()=>[{tag:"hr"}],renderHTML({HTMLAttributes:a}){return["hr",(0,d.KV)(this.options.HTMLAttributes,a)]},addCommands(){return{setHorizontalRule:()=>({chain:a,state:b})=>{if(!(0,d.AB)(b,b.schema.nodes[this.name]))return!1;let{selection:c}=b,{$to:e}=c,f=a();return(0,d.BQ)(c)?f.insertContentAt(e.pos,{type:this.name}):f.insertContent({type:this.name}),f.command(({tr:a,dispatch:b})=>{var c;if(b){let{$to:b}=a.selection,d=b.end();if(b.nodeAfter)b.nodeAfter.isTextblock?a.setSelection(p.U3.create(a.doc,b.pos+1)):b.nodeAfter.isBlock?a.setSelection(p.nh.create(a.doc,b.pos)):a.setSelection(p.U3.create(a.doc,b.pos));else{let e=null==(c=b.parent.type.contentMatch.defaultType)?void 0:c.create();e&&(a.insert(d,e),a.setSelection(p.U3.create(a.doc,d+1)))}a.scrollIntoView()}return!0}).run()}}},addInputRules(){return[(0,d.jT)({find:/^(?:---|—-|___\s|\*\*\*\s)$/,type:this.type})]}}),x=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))$/,y=/(?:^|\s)(\*(?!\s+\*)((?:[^*]+))\*(?!\s+\*))/g,z=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))$/,A=/(?:^|\s)(_(?!\s+_)((?:[^_]+))_(?!\s+_))/g,B=d.CU.create({name:"italic",addOptions:()=>({HTMLAttributes:{}}),parseHTML(){return[{tag:"em"},{tag:"i",getAttrs:a=>"normal"!==a.style.fontStyle&&null},{style:"font-style=normal",clearMark:a=>a.type.name===this.name},{style:"font-style=italic"}]},renderHTML({HTMLAttributes:a}){return["em",(0,d.KV)(this.options.HTMLAttributes,a),0]},addCommands(){return{setItalic:()=>({commands:a})=>a.setMark(this.name),toggleItalic:()=>({commands:a})=>a.toggleMark(this.name),unsetItalic:()=>({commands:a})=>a.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-i":()=>this.editor.commands.toggleItalic(),"Mod-I":()=>this.editor.commands.toggleItalic()}},addInputRules(){return[(0,d.OX)({find:x,type:this.type}),(0,d.OX)({find:z,type:this.type})]},addPasteRules(){return[(0,d.Zc)({find:y,type:this.type}),(0,d.Zc)({find:A,type:this.type})]}}),C=c(2188),D=c(65636),E=d.bP.create({name:"paragraph",priority:1e3,addOptions:()=>({HTMLAttributes:{}}),group:"block",content:"inline*",parseHTML:()=>[{tag:"p"}],renderHTML({HTMLAttributes:a}){return["p",(0,d.KV)(this.options.HTMLAttributes,a),0]},addCommands(){return{setParagraph:()=>({commands:a})=>a.setNode(this.name)}},addKeyboardShortcuts(){return{"Mod-Alt-0":()=>this.editor.commands.setParagraph()}}}),F=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))$/,G=/(?:^|\s)(~~(?!\s+~~)((?:[^~]+))~~(?!\s+~~))/g,H=d.CU.create({name:"strike",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"s"},{tag:"del"},{tag:"strike"},{style:"text-decoration",consuming:!1,getAttrs:a=>!!a.includes("line-through")&&{}}],renderHTML({HTMLAttributes:a}){return["s",(0,d.KV)(this.options.HTMLAttributes,a),0]},addCommands(){return{setStrike:()=>({commands:a})=>a.setMark(this.name),toggleStrike:()=>({commands:a})=>a.toggleMark(this.name),unsetStrike:()=>({commands:a})=>a.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-Shift-s":()=>this.editor.commands.toggleStrike()}},addInputRules(){return[(0,d.OX)({find:F,type:this.type})]},addPasteRules(){return[(0,d.Zc)({find:G,type:this.type})]}}),I=d.bP.create({name:"text",group:"inline"}),J=d.CU.create({name:"underline",addOptions:()=>({HTMLAttributes:{}}),parseHTML:()=>[{tag:"u"},{style:"text-decoration",consuming:!1,getAttrs:a=>!!a.includes("underline")&&{}}],renderHTML({HTMLAttributes:a}){return["u",(0,d.KV)(this.options.HTMLAttributes,a),0]},addCommands(){return{setUnderline:()=>({commands:a})=>a.setMark(this.name),toggleUnderline:()=>({commands:a})=>a.toggleMark(this.name),unsetUnderline:()=>({commands:a})=>a.unsetMark(this.name)}},addKeyboardShortcuts(){return{"Mod-u":()=>this.editor.commands.toggleUnderline(),"Mod-U":()=>this.editor.commands.toggleUnderline()}}}),K=c(41662);class L{constructor(a,b){var c;this.editorView=a,this.cursorPos=null,this.element=null,this.timeout=-1,this.width=null!=(c=b.width)?c:1,this.color=!1===b.color?void 0:b.color||"black",this.class=b.class,this.handlers=["dragover","dragend","drop","dragleave"].map(b=>{let c=a=>{this[b](a)};return a.dom.addEventListener(b,c),{name:b,handler:c}})}destroy(){this.handlers.forEach(({name:a,handler:b})=>this.editorView.dom.removeEventListener(a,b))}update(a,b){null!=this.cursorPos&&b.doc!=a.state.doc&&(this.cursorPos>a.state.doc.content.size?this.setCursor(null):this.updateOverlay())}setCursor(a){a!=this.cursorPos&&(this.cursorPos=a,null==a?(this.element.parentNode.removeChild(this.element),this.element=null):this.updateOverlay())}updateOverlay(){let a,b,c=this.editorView.state.doc.resolve(this.cursorPos),d=!c.parent.inlineContent,e,f=this.editorView.dom,g=f.getBoundingClientRect(),h=g.width/f.offsetWidth,i=g.height/f.offsetHeight;if(d){let a=c.nodeBefore,b=c.nodeAfter;if(a||b){let c=this.editorView.nodeDOM(this.cursorPos-(a?a.nodeSize:0));if(c){let d=c.getBoundingClientRect(),f=a?d.bottom:d.top;a&&b&&(f=(f+this.editorView.nodeDOM(this.cursorPos).getBoundingClientRect().top)/2);let g=this.width/2*i;e={left:d.left,right:d.right,top:f-g,bottom:f+g}}}}if(!e){let a=this.editorView.coordsAtPos(this.cursorPos),b=this.width/2*h;e={left:a.left-b,right:a.left+b,top:a.top,bottom:a.bottom}}let j=this.editorView.dom.offsetParent;if(!this.element&&(this.element=j.appendChild(document.createElement("div")),this.class&&(this.element.className=this.class),this.element.style.cssText="position: absolute; z-index: 50; pointer-events: none;",this.color&&(this.element.style.backgroundColor=this.color)),this.element.classList.toggle("prosemirror-dropcursor-block",d),this.element.classList.toggle("prosemirror-dropcursor-inline",!d),j&&(j!=document.body||"static"!=getComputedStyle(j).position)){let c=j.getBoundingClientRect(),d=c.width/j.offsetWidth,e=c.height/j.offsetHeight;a=c.left-j.scrollLeft*d,b=c.top-j.scrollTop*e}else a=-pageXOffset,b=-pageYOffset;this.element.style.left=(e.left-a)/h+"px",this.element.style.top=(e.top-b)/i+"px",this.element.style.width=(e.right-e.left)/h+"px",this.element.style.height=(e.bottom-e.top)/i+"px"}scheduleRemoval(a){clearTimeout(this.timeout),this.timeout=setTimeout(()=>this.setCursor(null),a)}dragover(a){if(!this.editorView.editable)return;let b=this.editorView.posAtCoords({left:a.clientX,top:a.clientY}),c=b&&b.inside>=0&&this.editorView.state.doc.nodeAt(b.inside),d=c&&c.type.spec.disableDropCursor,e="function"==typeof d?d(this.editorView,b,a):d;if(b&&!e){let a=b.pos;if(this.editorView.dragging&&this.editorView.dragging.slice){let b=(0,K.Um)(this.editorView.state.doc,a,this.editorView.dragging.slice);null!=b&&(a=b)}this.setCursor(a),this.scheduleRemoval(5e3)}}dragend(){this.scheduleRemoval(20)}drop(){this.scheduleRemoval(20)}dragleave(a){this.editorView.dom.contains(a.relatedTarget)||this.setCursor(null)}}var M=c(98031),N=c(31224),O=c(81553);class P extends p.LN{constructor(a){super(a,a)}map(a,b){let c=a.resolve(b.map(this.head));return P.valid(c)?new P(c):p.LN.near(c)}content(){return O.Ji.empty}eq(a){return a instanceof P&&a.head==this.head}toJSON(){return{type:"gapcursor",pos:this.head}}static fromJSON(a,b){if("number"!=typeof b.pos)throw RangeError("Invalid input for GapCursor.fromJSON");return new P(a.resolve(b.pos))}getBookmark(){return new Q(this.anchor)}static valid(a){let b=a.parent;if(b.isTextblock||!function(a){for(let b=a.depth;b>=0;b--){let c=a.index(b),d=a.node(b);if(0==c){if(d.type.spec.isolating)return!0;continue}for(let a=d.child(c-1);;a=a.lastChild){if(0==a.childCount&&!a.inlineContent||a.isAtom||a.type.spec.isolating)return!0;if(a.inlineContent)return!1}}return!0}(a)||!function(a){for(let b=a.depth;b>=0;b--){let c=a.indexAfter(b),d=a.node(b);if(c==d.childCount){if(d.type.spec.isolating)return!0;continue}for(let a=d.child(c);;a=a.firstChild){if(0==a.childCount&&!a.inlineContent||a.isAtom||a.type.spec.isolating)return!0;if(a.inlineContent)return!1}}return!0}(a))return!1;let c=b.type.spec.allowGapCursor;if(null!=c)return c;let d=b.contentMatchAt(a.index()).defaultType;return d&&d.isTextblock}static findGapCursorFrom(a,b,c=!1){a:for(;;){if(!c&&P.valid(a))return a;let d=a.pos,e=null;for(let c=a.depth;;c--){let f=a.node(c);if(b>0?a.indexAfter(c)<f.childCount:a.index(c)>0){e=f.child(b>0?a.indexAfter(c):a.index(c)-1);break}if(0==c)return null;d+=b;let g=a.doc.resolve(d);if(P.valid(g))return g}for(;;){let f=b>0?e.firstChild:e.lastChild;if(!f){if(e.isAtom&&!e.isText&&!p.nh.isSelectable(e)){a=a.doc.resolve(d+e.nodeSize*b),c=!1;continue a}break}e=f,d+=b;let g=a.doc.resolve(d);if(P.valid(g))return g}return null}}}P.prototype.visible=!1,P.findFrom=P.findGapCursorFrom,p.LN.jsonID("gapcursor",P);class Q{constructor(a){this.pos=a}map(a){return new Q(a.map(this.pos))}resolve(a){let b=a.resolve(this.pos);return P.valid(b)?new P(b):p.LN.near(b)}}let R=(0,N.K)({ArrowLeft:S("horiz",-1),ArrowRight:S("horiz",1),ArrowUp:S("vert",-1),ArrowDown:S("vert",1)});function S(a,b){let c="vert"==a?b>0?"down":"up":b>0?"right":"left";return function(a,d,e){let f=a.selection,g=b>0?f.$to:f.$from,h=f.empty;if(f instanceof p.U3){if(!e.endOfTextblock(c)||0==g.depth)return!1;h=!1,g=a.doc.resolve(b>0?g.after():g.before())}let i=P.findGapCursorFrom(g,b,h);return!!i&&(d&&d(a.tr.setSelection(new P(i))),!0)}}function T(a,b,c){if(!a||!a.editable)return!1;let d=a.state.doc.resolve(b);if(!P.valid(d))return!1;let e=a.posAtCoords({left:c.clientX,top:c.clientY});return!(e&&e.inside>-1&&p.nh.isSelectable(a.state.doc.nodeAt(e.inside)))&&(a.dispatch(a.state.tr.setSelection(new P(d))),!0)}function U(a,b){if("insertCompositionText"!=b.inputType||!(a.state.selection instanceof P))return!1;let{$from:c}=a.state.selection,d=c.parent.contentMatchAt(c.index()).findWrapping(a.state.schema.nodes.text);if(!d)return!1;let e=O.FK.empty;for(let a=d.length-1;a>=0;a--)e=O.FK.from(d[a].createAndFill(null,e));let f=a.state.tr.replace(c.pos,c.pos,new O.Ji(e,0,0));return f.setSelection(p.U3.near(f.doc.resolve(c.pos+1))),a.dispatch(f),!1}function V(a){if(!(a.selection instanceof P))return null;let b=document.createElement("div");return b.className="ProseMirror-gapcursor",M.zF.create(a.doc,[M.NZ.widget(a.selection.head,b,{key:"gapcursor"})])}var W=function(){};W.prototype.append=function(a){return a.length?(a=W.from(a),!this.length&&a||a.length<200&&this.leafAppend(a)||this.length<200&&a.leafPrepend(this)||this.appendInner(a)):this},W.prototype.prepend=function(a){return a.length?W.from(a).append(this):this},W.prototype.appendInner=function(a){return new Y(this,a)},W.prototype.slice=function(a,b){return(void 0===a&&(a=0),void 0===b&&(b=this.length),a>=b)?W.empty:this.sliceInner(Math.max(0,a),Math.min(this.length,b))},W.prototype.get=function(a){if(!(a<0)&&!(a>=this.length))return this.getInner(a)},W.prototype.forEach=function(a,b,c){void 0===b&&(b=0),void 0===c&&(c=this.length),b<=c?this.forEachInner(a,b,c,0):this.forEachInvertedInner(a,b,c,0)},W.prototype.map=function(a,b,c){void 0===b&&(b=0),void 0===c&&(c=this.length);var d=[];return this.forEach(function(b,c){return d.push(a(b,c))},b,c),d},W.from=function(a){return a instanceof W?a:a&&a.length?new X(a):W.empty};var X=function(a){function b(b){a.call(this),this.values=b}a&&(b.__proto__=a),b.prototype=Object.create(a&&a.prototype),b.prototype.constructor=b;var c={length:{configurable:!0},depth:{configurable:!0}};return b.prototype.flatten=function(){return this.values},b.prototype.sliceInner=function(a,c){return 0==a&&c==this.length?this:new b(this.values.slice(a,c))},b.prototype.getInner=function(a){return this.values[a]},b.prototype.forEachInner=function(a,b,c,d){for(var e=b;e<c;e++)if(!1===a(this.values[e],d+e))return!1},b.prototype.forEachInvertedInner=function(a,b,c,d){for(var e=b-1;e>=c;e--)if(!1===a(this.values[e],d+e))return!1},b.prototype.leafAppend=function(a){if(this.length+a.length<=200)return new b(this.values.concat(a.flatten()))},b.prototype.leafPrepend=function(a){if(this.length+a.length<=200)return new b(a.flatten().concat(this.values))},c.length.get=function(){return this.values.length},c.depth.get=function(){return 0},Object.defineProperties(b.prototype,c),b}(W);W.empty=new X([]);var Y=function(a){function b(b,c){a.call(this),this.left=b,this.right=c,this.length=b.length+c.length,this.depth=Math.max(b.depth,c.depth)+1}return a&&(b.__proto__=a),b.prototype=Object.create(a&&a.prototype),b.prototype.constructor=b,b.prototype.flatten=function(){return this.left.flatten().concat(this.right.flatten())},b.prototype.getInner=function(a){return a<this.left.length?this.left.get(a):this.right.get(a-this.left.length)},b.prototype.forEachInner=function(a,b,c,d){var e=this.left.length;if(b<e&&!1===this.left.forEachInner(a,b,Math.min(c,e),d)||c>e&&!1===this.right.forEachInner(a,Math.max(b-e,0),Math.min(this.length,c)-e,d+e))return!1},b.prototype.forEachInvertedInner=function(a,b,c,d){var e=this.left.length;if(b>e&&!1===this.right.forEachInvertedInner(a,b-e,Math.max(c,e)-e,d+e)||c<e&&!1===this.left.forEachInvertedInner(a,Math.min(b,e),c,d))return!1},b.prototype.sliceInner=function(a,b){if(0==a&&b==this.length)return this;var c=this.left.length;return b<=c?this.left.slice(a,b):a>=c?this.right.slice(a-c,b-c):this.left.slice(a,c).append(this.right.slice(0,b-c))},b.prototype.leafAppend=function(a){var c=this.right.leafAppend(a);if(c)return new b(this.left,c)},b.prototype.leafPrepend=function(a){var c=this.left.leafPrepend(a);if(c)return new b(c,this.right)},b.prototype.appendInner=function(a){return this.left.depth>=Math.max(this.right.depth,a.depth)+1?new b(this.left,new b(this.right,a)):new b(this,a)},b}(W);class Z{constructor(a,b){this.items=a,this.eventCount=b}popEvent(a,b){let c,d,e,f;if(0==this.eventCount)return null;let g=this.items.length;for(;;g--)if(this.items.get(g-1).selection){--g;break}b&&(d=(c=this.remapping(g,this.items.length)).maps.length);let h=a.tr,i=[],j=[];return this.items.forEach((a,b)=>{if(!a.step){c||(d=(c=this.remapping(g,b+1)).maps.length),d--,j.push(a);return}if(c){j.push(new $(a.map));let b=a.step.map(c.slice(d)),e;b&&h.maybeStep(b).doc&&(e=h.mapping.maps[h.mapping.maps.length-1],i.push(new $(e,void 0,void 0,i.length+j.length))),d--,e&&c.appendMap(e,d)}else h.maybeStep(a.step);if(a.selection)return e=c?a.selection.map(c.slice(d)):a.selection,f=new Z(this.items.slice(0,g).append(j.reverse().concat(i)),this.eventCount-1),!1},this.items.length,0),{remaining:f,transform:h,selection:e}}addTransform(a,b,c,d){var e,f;let g,h=[],i=this.eventCount,j=this.items,k=!d&&j.length?j.get(j.length-1):null;for(let c=0;c<a.steps.length;c++){let e=a.steps[c].invert(a.docs[c]),f=new $(a.mapping.maps[c],e,b),g;(g=k&&k.merge(f))&&(f=g,c?h.pop():j=j.slice(0,j.length-1)),h.push(f),b&&(i++,b=void 0),d||(k=f)}let l=i-c.depth;return l>aa&&(e=j,f=l,e.forEach((a,b)=>{if(a.selection&&0==f--)return g=b,!1}),j=e.slice(g),i-=l),new Z(j.append(h),i)}remapping(a,b){let c=new K.X9;return this.items.forEach((b,d)=>{let e=null!=b.mirrorOffset&&d-b.mirrorOffset>=a?c.maps.length-b.mirrorOffset:void 0;c.appendMap(b.map,e)},a,b),c}addMaps(a){return 0==this.eventCount?this:new Z(this.items.append(a.map(a=>new $(a))),this.eventCount)}rebased(a,b){if(!this.eventCount)return this;let c=[],d=Math.max(0,this.items.length-b),e=a.mapping,f=a.steps.length,g=this.eventCount;this.items.forEach(a=>{a.selection&&g--},d);let h=b;this.items.forEach(b=>{let d=e.getMirror(--h);if(null==d)return;f=Math.min(f,d);let i=e.maps[d];if(b.step){let f=a.steps[d].invert(a.docs[d]),j=b.selection&&b.selection.map(e.slice(h+1,d));j&&g++,c.push(new $(i,f,j))}else c.push(new $(i))},d);let i=[];for(let a=b;a<f;a++)i.push(new $(e.maps[a]));let j=new Z(this.items.slice(0,d).append(i).append(c),g);return j.emptyItemCount()>500&&(j=j.compress(this.items.length-c.length)),j}emptyItemCount(){let a=0;return this.items.forEach(b=>{!b.step&&a++}),a}compress(a=this.items.length){let b=this.remapping(0,a),c=b.maps.length,d=[],e=0;return this.items.forEach((f,g)=>{if(g>=a)d.push(f),f.selection&&e++;else if(f.step){let a=f.step.map(b.slice(c)),g=a&&a.getMap();if(c--,g&&b.appendMap(g,c),a){let h=f.selection&&f.selection.map(b.slice(c));h&&e++;let i=new $(g.invert(),a,h),j,k=d.length-1;(j=d.length&&d[k].merge(i))?d[k]=j:d.push(i)}}else f.map&&c--},this.items.length,0),new Z(W.from(d.reverse()),e)}}Z.empty=new Z(W.empty,0);class ${constructor(a,b,c,d){this.map=a,this.step=b,this.selection=c,this.mirrorOffset=d}merge(a){if(this.step&&a.step&&!a.selection){let b=a.step.merge(this.step);if(b)return new $(b.getMap().invert(),b,this.selection)}}}class _{constructor(a,b,c,d,e){this.done=a,this.undone=b,this.prevRanges=c,this.prevTime=d,this.prevComposition=e}}let aa=20;function ab(a){let b=[];for(let c=a.length-1;c>=0&&0==b.length;c--)a[c].forEach((a,c,d,e)=>b.push(d,e));return b}function ac(a,b){if(!a)return null;let c=[];for(let d=0;d<a.length;d+=2){let e=b.map(a[d],1),f=b.map(a[d+1],-1);e<=f&&c.push(e,f)}return c}let ad=!1,ae=null;function af(a){let b=a.plugins;if(ae!=b){ad=!1,ae=b;for(let a=0;a<b.length;a++)if(b[a].spec.historyPreserveItems){ad=!0;break}}return ad}let ag=new p.hs("history"),ah=new p.hs("closeHistory");function ai(a,b){return(c,d)=>{let e=ag.getState(c);if(!e||0==(a?e.undone:e.done).eventCount)return!1;if(d){let f=function(a,b,c){let d=af(b),e=ag.get(b).spec.config,f=(c?a.undone:a.done).popEvent(b,d);if(!f)return null;let g=f.selection.resolve(f.transform.doc),h=(c?a.done:a.undone).addTransform(f.transform,b.selection.getBookmark(),e,d),i=new _(c?h:f.remaining,c?f.remaining:h,null,0,-1);return f.transform.setSelection(g).setMeta(ag,{redo:c,historyState:i})}(e,c,a);f&&d(b?f.scrollIntoView():f)}return!0}}let aj=ai(!1,!0),ak=ai(!0,!0);ai(!1,!1),ai(!0,!1),d.YY.create({name:"characterCount",addOptions:()=>({limit:null,mode:"textSize",textCounter:a=>a.length,wordCounter:a=>a.split(" ").filter(a=>""!==a).length}),addStorage:()=>({characters:()=>0,words:()=>0}),onBeforeCreate(){this.storage.characters=a=>{let b=(null==a?void 0:a.node)||this.editor.state.doc;if("textSize"===((null==a?void 0:a.mode)||this.options.mode)){let a=b.textBetween(0,b.content.size,void 0," ");return this.options.textCounter(a)}return b.nodeSize},this.storage.words=a=>{let b=(null==a?void 0:a.node)||this.editor.state.doc,c=b.textBetween(0,b.content.size," "," ");return this.options.wordCounter(c)}},addProseMirrorPlugins(){let a=!1;return[new p.k_({key:new p.hs("characterCount"),appendTransaction:(b,c,d)=>{if(a)return;let e=this.options.limit;if(null==e||0===e){a=!0;return}let f=this.storage.characters({node:d.doc});if(f>e){console.warn(`[CharacterCount] Initial content exceeded limit of ${e} characters. Content was automatically trimmed.`);let b=d.tr.deleteRange(0,f-e);return a=!0,b}a=!0},filterTransaction:(a,b)=>{let c=this.options.limit;if(!a.docChanged||0===c||null==c)return!0;let d=this.storage.characters({node:b.doc}),e=this.storage.characters({node:a.doc});if(e<=c||d>c&&e>c&&e<=d)return!0;if(d>c&&e>c&&e>d||!a.getMeta("paste"))return!1;let f=a.selection.$head.pos;return a.deleteRange(f-(e-c),f),!(this.storage.characters({node:a.doc})>c)}})]}});var al=d.YY.create({name:"dropCursor",addOptions:()=>({color:"currentColor",width:1,class:void 0}),addProseMirrorPlugins(){return[function(a={}){return new p.k_({view:b=>new L(b,a)})}(this.options)]}});d.YY.create({name:"focus",addOptions:()=>({className:"has-focus",mode:"all"}),addProseMirrorPlugins(){return[new p.k_({key:new p.hs("focus"),props:{decorations:({doc:a,selection:b})=>{let{isEditable:c,isFocused:d}=this.editor,{anchor:e}=b,f=[];if(!c||!d)return M.zF.create(a,[]);let g=0;"deepest"===this.options.mode&&a.descendants((a,b)=>{if(!a.isText){if(!(e>=b&&e<=b+a.nodeSize-1))return!1;g+=1}});let h=0;return a.descendants((a,b)=>!a.isText&&!!(e>=b&&e<=b+a.nodeSize-1)&&((h+=1,"deepest"===this.options.mode&&g-h>0||"shallowest"===this.options.mode&&h>1)?"deepest"===this.options.mode:void f.push(M.NZ.node(b,b+a.nodeSize,{class:this.options.className})))),M.zF.create(a,f)}}})]}});var am=d.YY.create({name:"gapCursor",addProseMirrorPlugins:()=>[new p.k_({props:{decorations:V,createSelectionBetween:(a,b,c)=>b.pos==c.pos&&P.valid(c)?new P(c):null,handleClick:T,handleKeyDown:R,handleDOMEvents:{beforeinput:U}}})],extendNodeSchema(a){var b;let c={name:a.name,options:a.options,storage:a.storage};return{allowGapCursor:null!=(b=(0,d.gk)((0,d.iI)(a,"allowGapCursor",c)))?b:null}}});function an({types:a,node:b}){return b&&Array.isArray(a)&&a.includes(b.type)||(null==b?void 0:b.type)===a}d.YY.create({name:"placeholder",addOptions:()=>({emptyEditorClass:"is-editor-empty",emptyNodeClass:"is-empty",placeholder:"Write something …",showOnlyWhenEditable:!0,showOnlyCurrent:!0,includeChildren:!1}),addProseMirrorPlugins(){return[new p.k_({key:new p.hs("placeholder"),props:{decorations:({doc:a,selection:b})=>{let c=this.editor.isEditable||!this.options.showOnlyWhenEditable,{anchor:e}=b,f=[];if(!c)return null;let g=this.editor.isEmpty;return a.descendants((a,b)=>{let c=e>=b&&e<=b+a.nodeSize,h=!a.isLeaf&&(0,d.Op)(a);if((c||!this.options.showOnlyCurrent)&&h){let d=[this.options.emptyNodeClass];g&&d.push(this.options.emptyEditorClass);let e=M.NZ.node(b,b+a.nodeSize,{class:d.join(" "),"data-placeholder":"function"==typeof this.options.placeholder?this.options.placeholder({editor:this.editor,node:a,pos:b,hasAnchor:c}):this.options.placeholder});f.push(e)}return this.options.includeChildren}),M.zF.create(a,f)}}})]}}),d.YY.create({name:"selection",addOptions:()=>({className:"selection"}),addProseMirrorPlugins(){let{editor:a,options:b}=this;return[new p.k_({key:new p.hs("selection"),props:{decorations:c=>c.selection.empty||a.isFocused||!a.isEditable||(0,d.BQ)(c.selection)||a.view.dragging?null:M.zF.create(c.doc,[M.NZ.inline(c.selection.from,c.selection.to,{class:b.className})])}})]}});var ao=d.YY.create({name:"trailingNode",addOptions:()=>({node:"paragraph",notAfter:[]}),addProseMirrorPlugins(){let a=new p.hs(this.name),b=Object.entries(this.editor.schema.nodes).map(([,a])=>a).filter(a=>(this.options.notAfter||[]).concat(this.options.node).includes(a.name));return[new p.k_({key:a,appendTransaction:(b,c,d)=>{let{doc:e,tr:f,schema:g}=d,h=a.getState(d),i=e.content.size,j=g.nodes[this.options.node];if(h)return f.insert(i,j.create())},state:{init:(a,c)=>!an({node:c.tr.doc.lastChild,types:b}),apply:(a,c)=>a.docChanged?!an({node:a.doc.lastChild,types:b}):c}})]}}),ap=d.YY.create({name:"undoRedo",addOptions:()=>({depth:100,newGroupDelay:500}),addCommands:()=>({undo:()=>({state:a,dispatch:b})=>aj(a,b),redo:()=>({state:a,dispatch:b})=>ak(a,b)}),addProseMirrorPlugins(){return[function(a={}){return a={depth:a.depth||100,newGroupDelay:a.newGroupDelay||500},new p.k_({key:ag,state:{init:()=>new _(Z.empty,Z.empty,null,0,-1),apply:(b,c,d)=>(function(a,b,c,d){let e=c.getMeta(ag),f;if(e)return e.historyState;c.getMeta(ah)&&(a=new _(a.done,a.undone,null,0,-1));let g=c.getMeta("appendedTransaction");if(0==c.steps.length)return a;if(g&&g.getMeta(ag))if(g.getMeta(ag).redo)return new _(a.done.addTransform(c,void 0,d,af(b)),a.undone,ab(c.mapping.maps),a.prevTime,a.prevComposition);else return new _(a.done,a.undone.addTransform(c,void 0,d,af(b)),null,a.prevTime,a.prevComposition);if(!1===c.getMeta("addToHistory")||g&&!1===g.getMeta("addToHistory"))if(f=c.getMeta("rebased"))return new _(a.done.rebased(c,f),a.undone.rebased(c,f),ac(a.prevRanges,c.mapping),a.prevTime,a.prevComposition);else return new _(a.done.addMaps(c.mapping.maps),a.undone.addMaps(c.mapping.maps),ac(a.prevRanges,c.mapping),a.prevTime,a.prevComposition);{let e=c.getMeta("composition"),f=0==a.prevTime||!g&&a.prevComposition!=e&&(a.prevTime<(c.time||0)-d.newGroupDelay||!function(a,b){if(!b)return!1;if(!a.docChanged)return!0;let c=!1;return a.mapping.maps[0].forEach((a,d)=>{for(let e=0;e<b.length;e+=2)a<=b[e+1]&&d>=b[e]&&(c=!0)}),c}(c,a.prevRanges)),h=g?ac(a.prevRanges,c.mapping):ab(c.mapping.maps);return new _(a.done.addTransform(c,f?b.selection.getBookmark():void 0,d,af(b)),Z.empty,h,c.time,null==e?a.prevComposition:e)}})(c,d,b,a)},config:a,props:{handleDOMEvents:{beforeinput(a,b){let c=b.inputType,d="historyUndo"==c?aj:"historyRedo"==c?ak:null;return!!d&&(b.preventDefault(),d(a.state,a.dispatch))}}}})}(this.options)]},addKeyboardShortcuts(){return{"Mod-z":()=>this.editor.commands.undo(),"Shift-Mod-z":()=>this.editor.commands.redo(),"Mod-y":()=>this.editor.commands.redo(),"Mod-я":()=>this.editor.commands.undo(),"Shift-Mod-я":()=>this.editor.commands.redo()}}}),aq=d.YY.create({name:"starterKit",addExtensions(){var a,b,c,d;let e=[];return!1!==this.options.bold&&e.push(l.configure(this.options.bold)),!1!==this.options.blockquote&&e.push(g.configure(this.options.blockquote)),!1!==this.options.bulletList&&e.push(D.Rg.configure(this.options.bulletList)),!1!==this.options.code&&e.push(o.configure(this.options.code)),!1!==this.options.codeBlock&&e.push(s.configure(this.options.codeBlock)),!1!==this.options.document&&e.push(t.configure(this.options.document)),!1!==this.options.dropcursor&&e.push(al.configure(this.options.dropcursor)),!1!==this.options.gapcursor&&e.push(am.configure(this.options.gapcursor)),!1!==this.options.hardBreak&&e.push(u.configure(this.options.hardBreak)),!1!==this.options.heading&&e.push(v.configure(this.options.heading)),!1!==this.options.undoRedo&&e.push(ap.configure(this.options.undoRedo)),!1!==this.options.horizontalRule&&e.push(w.configure(this.options.horizontalRule)),!1!==this.options.italic&&e.push(B.configure(this.options.italic)),!1!==this.options.listItem&&e.push(D.ck.configure(this.options.listItem)),!1!==this.options.listKeymap&&e.push(D.it.configure(null==(a=this.options)?void 0:a.listKeymap)),!1!==this.options.link&&e.push(C.N_.configure(null==(b=this.options)?void 0:b.link)),!1!==this.options.orderedList&&e.push(D._J.configure(this.options.orderedList)),!1!==this.options.paragraph&&e.push(E.configure(this.options.paragraph)),!1!==this.options.strike&&e.push(H.configure(this.options.strike)),!1!==this.options.text&&e.push(I.configure(this.options.text)),!1!==this.options.underline&&e.push(J.configure(null==(c=this.options)?void 0:c.underline)),!1!==this.options.trailingNode&&e.push(ao.configure(null==(d=this.options)?void 0:d.trailingNode)),e}})},35857:(a,b,c)=>{c.d(b,{$t:()=>x,LN:()=>g,U3:()=>k,hs:()=>B,i5:()=>o,k_:()=>y,nh:()=>m});var d=c(81553),e=c(41662);let f=Object.create(null);class g{constructor(a,b,c){this.$anchor=a,this.$head=b,this.ranges=c||[new h(a.min(b),a.max(b))]}get anchor(){return this.$anchor.pos}get head(){return this.$head.pos}get from(){return this.$from.pos}get to(){return this.$to.pos}get $from(){return this.ranges[0].$from}get $to(){return this.ranges[0].$to}get empty(){let a=this.ranges;for(let b=0;b<a.length;b++)if(a[b].$from.pos!=a[b].$to.pos)return!1;return!0}content(){return this.$from.doc.slice(this.from,this.to,!0)}replace(a,b=d.Ji.empty){let c=b.content.lastChild,e=null;for(let a=0;a<b.openEnd;a++)e=c,c=c.lastChild;let f=a.steps.length,g=this.ranges;for(let h=0;h<g.length;h++){let{$from:i,$to:j}=g[h],k=a.mapping.slice(f);a.replaceRange(k.map(i.pos),k.map(j.pos),h?d.Ji.empty:b),0==h&&r(a,f,(c?c.isInline:e&&e.isTextblock)?-1:1)}}replaceWith(a,b){let c=a.steps.length,d=this.ranges;for(let e=0;e<d.length;e++){let{$from:f,$to:g}=d[e],h=a.mapping.slice(c),i=h.map(f.pos),j=h.map(g.pos);e?a.deleteRange(i,j):(a.replaceRangeWith(i,j,b),r(a,c,b.isInline?-1:1))}}static findFrom(a,b,c=!1){let d=a.parent.inlineContent?new k(a):q(a.node(0),a.parent,a.pos,a.index(),b,c);if(d)return d;for(let d=a.depth-1;d>=0;d--){let e=b<0?q(a.node(0),a.node(d),a.before(d+1),a.index(d),b,c):q(a.node(0),a.node(d),a.after(d+1),a.index(d)+1,b,c);if(e)return e}return null}static near(a,b=1){return this.findFrom(a,b)||this.findFrom(a,-b)||new o(a.node(0))}static atStart(a){return q(a,a,0,0,1)||new o(a)}static atEnd(a){return q(a,a,a.content.size,a.childCount,-1)||new o(a)}static fromJSON(a,b){if(!b||!b.type)throw RangeError("Invalid input for Selection.fromJSON");let c=f[b.type];if(!c)throw RangeError(`No selection type ${b.type} defined`);return c.fromJSON(a,b)}static jsonID(a,b){if(a in f)throw RangeError("Duplicate use of selection JSON ID "+a);return f[a]=b,b.prototype.jsonID=a,b}getBookmark(){return k.between(this.$anchor,this.$head).getBookmark()}}g.prototype.visible=!0;class h{constructor(a,b){this.$from=a,this.$to=b}}let i=!1;function j(a){i||a.parent.inlineContent||(i=!0,console.warn("TextSelection endpoint not pointing into a node with inline content ("+a.parent.type.name+")"))}class k extends g{constructor(a,b=a){j(a),j(b),super(a,b)}get $cursor(){return this.$anchor.pos==this.$head.pos?this.$head:null}map(a,b){let c=a.resolve(b.map(this.head));if(!c.parent.inlineContent)return g.near(c);let d=a.resolve(b.map(this.anchor));return new k(d.parent.inlineContent?d:c,c)}replace(a,b=d.Ji.empty){if(super.replace(a,b),b==d.Ji.empty){let b=this.$from.marksAcross(this.$to);b&&a.ensureMarks(b)}}eq(a){return a instanceof k&&a.anchor==this.anchor&&a.head==this.head}getBookmark(){return new l(this.anchor,this.head)}toJSON(){return{type:"text",anchor:this.anchor,head:this.head}}static fromJSON(a,b){if("number"!=typeof b.anchor||"number"!=typeof b.head)throw RangeError("Invalid input for TextSelection.fromJSON");return new k(a.resolve(b.anchor),a.resolve(b.head))}static create(a,b,c=b){let d=a.resolve(b);return new this(d,c==b?d:a.resolve(c))}static between(a,b,c){let d=a.pos-b.pos;if((!c||d)&&(c=d>=0?1:-1),!b.parent.inlineContent){let a=g.findFrom(b,c,!0)||g.findFrom(b,-c,!0);if(!a)return g.near(b,c);b=a.$head}return a.parent.inlineContent||(0==d?a=b:(a=(g.findFrom(a,-c,!0)||g.findFrom(a,c,!0)).$anchor).pos<b.pos!=d<0&&(a=b)),new k(a,b)}}g.jsonID("text",k);class l{constructor(a,b){this.anchor=a,this.head=b}map(a){return new l(a.map(this.anchor),a.map(this.head))}resolve(a){return k.between(a.resolve(this.anchor),a.resolve(this.head))}}class m extends g{constructor(a){let b=a.nodeAfter;super(a,a.node(0).resolve(a.pos+b.nodeSize)),this.node=b}map(a,b){let{deleted:c,pos:d}=b.mapResult(this.anchor),e=a.resolve(d);return c?g.near(e):new m(e)}content(){return new d.Ji(d.FK.from(this.node),0,0)}eq(a){return a instanceof m&&a.anchor==this.anchor}toJSON(){return{type:"node",anchor:this.anchor}}getBookmark(){return new n(this.anchor)}static fromJSON(a,b){if("number"!=typeof b.anchor)throw RangeError("Invalid input for NodeSelection.fromJSON");return new m(a.resolve(b.anchor))}static create(a,b){return new m(a.resolve(b))}static isSelectable(a){return!a.isText&&!1!==a.type.spec.selectable}}m.prototype.visible=!1,g.jsonID("node",m);class n{constructor(a){this.anchor=a}map(a){let{deleted:b,pos:c}=a.mapResult(this.anchor);return b?new l(c,c):new n(c)}resolve(a){let b=a.resolve(this.anchor),c=b.nodeAfter;return c&&m.isSelectable(c)?new m(b):g.near(b)}}class o extends g{constructor(a){super(a.resolve(0),a.resolve(a.content.size))}replace(a,b=d.Ji.empty){if(b==d.Ji.empty){a.delete(0,a.doc.content.size);let b=g.atStart(a.doc);b.eq(a.selection)||a.setSelection(b)}else super.replace(a,b)}toJSON(){return{type:"all"}}static fromJSON(a){return new o(a)}map(a){return new o(a)}eq(a){return a instanceof o}getBookmark(){return p}}g.jsonID("all",o);let p={map(){return this},resolve:a=>new o(a)};function q(a,b,c,d,e,f=!1){if(b.inlineContent)return k.create(a,c);for(let g=d-(e>0?0:1);e>0?g<b.childCount:g>=0;g+=e){let d=b.child(g);if(d.isAtom){if(!f&&m.isSelectable(d))return m.create(a,c-(e<0?d.nodeSize:0))}else{let b=q(a,d,c+e,e<0?d.childCount:0,e,f);if(b)return b}c+=d.nodeSize*e}return null}function r(a,b,c){let d,f=a.steps.length-1;if(f<b)return;let h=a.steps[f];(h instanceof e.Ln||h instanceof e.Wg)&&(a.mapping.maps[f].forEach((a,b,c,e)=>{null==d&&(d=e)}),a.setSelection(g.near(a.doc.resolve(d),c)))}class s extends e.dL{constructor(a){super(a.doc),this.curSelectionFor=0,this.updated=0,this.meta=Object.create(null),this.time=Date.now(),this.curSelection=a.selection,this.storedMarks=a.storedMarks}get selection(){return this.curSelectionFor<this.steps.length&&(this.curSelection=this.curSelection.map(this.doc,this.mapping.slice(this.curSelectionFor)),this.curSelectionFor=this.steps.length),this.curSelection}setSelection(a){if(a.$from.doc!=this.doc)throw RangeError("Selection passed to setSelection must point at the current document");return this.curSelection=a,this.curSelectionFor=this.steps.length,this.updated=(1|this.updated)&-3,this.storedMarks=null,this}get selectionSet(){return(1&this.updated)>0}setStoredMarks(a){return this.storedMarks=a,this.updated|=2,this}ensureMarks(a){return d.CU.sameSet(this.storedMarks||this.selection.$from.marks(),a)||this.setStoredMarks(a),this}addStoredMark(a){return this.ensureMarks(a.addToSet(this.storedMarks||this.selection.$head.marks()))}removeStoredMark(a){return this.ensureMarks(a.removeFromSet(this.storedMarks||this.selection.$head.marks()))}get storedMarksSet(){return(2&this.updated)>0}addStep(a,b){super.addStep(a,b),this.updated=-3&this.updated,this.storedMarks=null}setTime(a){return this.time=a,this}replaceSelection(a){return this.selection.replace(this,a),this}replaceSelectionWith(a,b=!0){let c=this.selection;return b&&(a=a.mark(this.storedMarks||(c.empty?c.$from.marks():c.$from.marksAcross(c.$to)||d.CU.none))),c.replaceWith(this,a),this}deleteSelection(){return this.selection.replace(this),this}insertText(a,b,c){let d=this.doc.type.schema;if(null==b)return a?this.replaceSelectionWith(d.text(a),!0):this.deleteSelection();{if(null==c&&(c=b),c=null==c?b:c,!a)return this.deleteRange(b,c);let e=this.storedMarks;if(!e){let a=this.doc.resolve(b);e=c==b?a.marks():a.marksAcross(this.doc.resolve(c))}return this.replaceRangeWith(b,c,d.text(a,e)),this.selection.empty||this.setSelection(g.near(this.selection.$to)),this}}setMeta(a,b){return this.meta["string"==typeof a?a:a.key]=b,this}getMeta(a){return this.meta["string"==typeof a?a:a.key]}get isGeneric(){for(let a in this.meta)return!1;return!0}scrollIntoView(){return this.updated|=4,this}get scrolledIntoView(){return(4&this.updated)>0}}function t(a,b){return b&&a?a.bind(b):a}class u{constructor(a,b,c){this.name=a,this.init=t(b.init,c),this.apply=t(b.apply,c)}}let v=[new u("doc",{init:a=>a.doc||a.schema.topNodeType.createAndFill(),apply:a=>a.doc}),new u("selection",{init:(a,b)=>a.selection||g.atStart(b.doc),apply:a=>a.selection}),new u("storedMarks",{init:a=>a.storedMarks||null,apply:(a,b,c,d)=>d.selection.$cursor?a.storedMarks:null}),new u("scrollToSelection",{init:()=>0,apply:(a,b)=>a.scrolledIntoView?b+1:b})];class w{constructor(a,b){this.schema=a,this.plugins=[],this.pluginsByKey=Object.create(null),this.fields=v.slice(),b&&b.forEach(a=>{if(this.pluginsByKey[a.key])throw RangeError("Adding different instances of a keyed plugin ("+a.key+")");this.plugins.push(a),this.pluginsByKey[a.key]=a,a.spec.state&&this.fields.push(new u(a.key,a.spec.state,a))})}}class x{constructor(a){this.config=a}get schema(){return this.config.schema}get plugins(){return this.config.plugins}apply(a){return this.applyTransaction(a).state}filterTransaction(a,b=-1){for(let c=0;c<this.config.plugins.length;c++)if(c!=b){let b=this.config.plugins[c];if(b.spec.filterTransaction&&!b.spec.filterTransaction.call(b,a,this))return!1}return!0}applyTransaction(a){if(!this.filterTransaction(a))return{state:this,transactions:[]};let b=[a],c=this.applyInner(a),d=null;for(;;){let e=!1;for(let f=0;f<this.config.plugins.length;f++){let g=this.config.plugins[f];if(g.spec.appendTransaction){let h=d?d[f].n:0,i=d?d[f].state:this,j=h<b.length&&g.spec.appendTransaction.call(g,h?b.slice(h):b,i,c);if(j&&c.filterTransaction(j,f)){if(j.setMeta("appendedTransaction",a),!d){d=[];for(let a=0;a<this.config.plugins.length;a++)d.push(a<f?{state:c,n:b.length}:{state:this,n:0})}b.push(j),c=c.applyInner(j),e=!0}d&&(d[f]={state:c,n:b.length})}}if(!e)return{state:c,transactions:b}}}applyInner(a){if(!a.before.eq(this.doc))throw RangeError("Applying a mismatched transaction");let b=new x(this.config),c=this.config.fields;for(let d=0;d<c.length;d++){let e=c[d];b[e.name]=e.apply(a,this[e.name],this,b)}return b}get tr(){return new s(this)}static create(a){let b=new w(a.doc?a.doc.type.schema:a.schema,a.plugins),c=new x(b);for(let d=0;d<b.fields.length;d++)c[b.fields[d].name]=b.fields[d].init(a,c);return c}reconfigure(a){let b=new w(this.schema,a.plugins),c=b.fields,d=new x(b);for(let b=0;b<c.length;b++){let e=c[b].name;d[e]=this.hasOwnProperty(e)?this[e]:c[b].init(a,d)}return d}toJSON(a){let b={doc:this.doc.toJSON(),selection:this.selection.toJSON()};if(this.storedMarks&&(b.storedMarks=this.storedMarks.map(a=>a.toJSON())),a&&"object"==typeof a)for(let c in a){if("doc"==c||"selection"==c)throw RangeError("The JSON fields `doc` and `selection` are reserved");let d=a[c],e=d.spec.state;e&&e.toJSON&&(b[c]=e.toJSON.call(d,this[d.key]))}return b}static fromJSON(a,b,c){if(!b)throw RangeError("Invalid input for EditorState.fromJSON");if(!a.schema)throw RangeError("Required config field 'schema' missing");let e=new w(a.schema,a.plugins),f=new x(e);return e.fields.forEach(e=>{if("doc"==e.name)f.doc=d.bP.fromJSON(a.schema,b.doc);else if("selection"==e.name)f.selection=g.fromJSON(f.doc,b.selection);else if("storedMarks"==e.name)b.storedMarks&&(f.storedMarks=b.storedMarks.map(a.schema.markFromJSON));else{if(c)for(let d in c){let g=c[d],h=g.spec.state;if(g.key==e.name&&h&&h.fromJSON&&Object.prototype.hasOwnProperty.call(b,d)){f[e.name]=h.fromJSON.call(g,a,b[d],f);return}}f[e.name]=e.init(a,f)}}),f}}class y{constructor(a){this.spec=a,this.props={},a.props&&function a(b,c,d){for(let e in b){let f=b[e];f instanceof Function?f=f.bind(c):"handleDOMEvents"==e&&(f=a(f,c,{})),d[e]=f}return d}(a.props,this,this.props),this.key=a.key?a.key.key:A("plugin")}getState(a){return a[this.key]}}let z=Object.create(null);function A(a){return a in z?a+"$"+ ++z[a]:(z[a]=0,a+"$")}class B{constructor(a="key"){this.key=A(a)}get(a){return a.config.pluginsByKey[this.key]}getState(a){return a[this.key]}}},36293:(a,b,c)=>{c.d(b,{Q1:()=>g,xJ:()=>e});var d=c(81373),e=d.CU.create({name:"textStyle",priority:101,addOptions:()=>({HTMLAttributes:{},mergeNestedSpanStyles:!0}),parseHTML(){return[{tag:"span",consuming:!1,getAttrs:a=>!!a.hasAttribute("style")&&(this.options.mergeNestedSpanStyles&&(a=>{if(!a.children.length)return;let b=a.querySelectorAll("span");b&&b.forEach(a=>{var b,c;let d=a.getAttribute("style"),e=null==(c=null==(b=a.parentElement)?void 0:b.closest("span"))?void 0:c.getAttribute("style");a.setAttribute("style",`${e};${d}`)})})(a),{})}]},renderHTML({HTMLAttributes:a}){return["span",(0,d.KV)(this.options.HTMLAttributes,a),0]},addCommands(){return{toggleTextStyle:a=>({commands:b})=>b.toggleMark(this.name,a),removeEmptyTextStyle:()=>({tr:a})=>{let{selection:b}=a;return a.doc.nodesBetween(b.from,b.to,(b,c)=>{if(b.isTextblock)return!0;b.marks.filter(a=>a.type===this.type).some(a=>Object.values(a.attrs).some(a=>!!a))||a.removeMark(c,c+b.nodeSize,this.type)}),!0}}}}),f=d.YY.create({name:"backgroundColor",addOptions:()=>({types:["textStyle"]}),addGlobalAttributes(){return[{types:this.options.types,attributes:{backgroundColor:{default:null,parseHTML:a=>{var b;return null==(b=a.style.backgroundColor)?void 0:b.replace(/['"]+/g,"")},renderHTML:a=>a.backgroundColor?{style:`background-color: ${a.backgroundColor}`}:{}}}}]},addCommands:()=>({setBackgroundColor:a=>({chain:b})=>b().setMark("textStyle",{backgroundColor:a}).run(),unsetBackgroundColor:()=>({chain:a})=>a().setMark("textStyle",{backgroundColor:null}).removeEmptyTextStyle().run()})}),g=d.YY.create({name:"color",addOptions:()=>({types:["textStyle"]}),addGlobalAttributes(){return[{types:this.options.types,attributes:{color:{default:null,parseHTML:a=>{var b;return null==(b=a.style.color)?void 0:b.replace(/['"]+/g,"")},renderHTML:a=>a.color?{style:`color: ${a.color}`}:{}}}}]},addCommands:()=>({setColor:a=>({chain:b})=>b().setMark("textStyle",{color:a}).run(),unsetColor:()=>({chain:a})=>a().setMark("textStyle",{color:null}).removeEmptyTextStyle().run()})}),h=d.YY.create({name:"fontFamily",addOptions:()=>({types:["textStyle"]}),addGlobalAttributes(){return[{types:this.options.types,attributes:{fontFamily:{default:null,parseHTML:a=>a.style.fontFamily,renderHTML:a=>a.fontFamily?{style:`font-family: ${a.fontFamily}`}:{}}}}]},addCommands:()=>({setFontFamily:a=>({chain:b})=>b().setMark("textStyle",{fontFamily:a}).run(),unsetFontFamily:()=>({chain:a})=>a().setMark("textStyle",{fontFamily:null}).removeEmptyTextStyle().run()})}),i=d.YY.create({name:"fontSize",addOptions:()=>({types:["textStyle"]}),addGlobalAttributes(){return[{types:this.options.types,attributes:{fontSize:{default:null,parseHTML:a=>a.style.fontSize,renderHTML:a=>a.fontSize?{style:`font-size: ${a.fontSize}`}:{}}}}]},addCommands:()=>({setFontSize:a=>({chain:b})=>b().setMark("textStyle",{fontSize:a}).run(),unsetFontSize:()=>({chain:a})=>a().setMark("textStyle",{fontSize:null}).removeEmptyTextStyle().run()})}),j=d.YY.create({name:"lineHeight",addOptions:()=>({types:["textStyle"]}),addGlobalAttributes(){return[{types:this.options.types,attributes:{lineHeight:{default:null,parseHTML:a=>a.style.lineHeight,renderHTML:a=>a.lineHeight?{style:`line-height: ${a.lineHeight}`}:{}}}}]},addCommands:()=>({setLineHeight:a=>({chain:b})=>b().setMark("textStyle",{lineHeight:a}).run(),unsetLineHeight:()=>({chain:a})=>a().setMark("textStyle",{lineHeight:null}).removeEmptyTextStyle().run()})});d.YY.create({name:"textStyleKit",addExtensions(){let a=[];return!1!==this.options.backgroundColor&&a.push(f.configure(this.options.backgroundColor)),!1!==this.options.color&&a.push(g.configure(this.options.color)),!1!==this.options.fontFamily&&a.push(h.configure(this.options.fontFamily)),!1!==this.options.fontSize&&a.push(i.configure(this.options.fontSize)),!1!==this.options.lineHeight&&a.push(j.configure(this.options.lineHeight)),!1!==this.options.textStyle&&a.push(e.configure(this.options.textStyle)),a}})},39733:(a,b,c)=>{a.exports=c(10907)},40211:(a,b,c)=>{c.d(b,{C1:()=>x,bL:()=>v});var d=c(43210),e=c(98599),f=c(11273),g=c(70569),h=c(65551),i=c(83721),j=c(18853),k=c(46059),l=c(14163),m=c(60687),n="Checkbox",[o,p]=(0,f.A)(n),[q,r]=o(n);function s(a){let{__scopeCheckbox:b,checked:c,children:e,defaultChecked:f,disabled:g,form:i,name:j,onCheckedChange:k,required:l,value:o="on",internal_do_not_use_render:p}=a,[r,s]=(0,h.i)({prop:c,defaultProp:f??!1,onChange:k,caller:n}),[t,u]=d.useState(null),[v,w]=d.useState(null),x=d.useRef(!1),y=!t||!!i||!!t.closest("form"),z={checked:r,disabled:g,setChecked:s,control:t,setControl:u,name:j,form:i,value:o,hasConsumerStoppedPropagationRef:x,required:l,defaultChecked:!A(f)&&f,isFormControl:y,bubbleInput:v,setBubbleInput:w};return(0,m.jsx)(q,{scope:b,...z,children:"function"==typeof p?p(z):e})}var t="CheckboxTrigger",u=d.forwardRef(({__scopeCheckbox:a,onKeyDown:b,onClick:c,...f},h)=>{let{control:i,value:j,disabled:k,checked:n,required:o,setControl:p,setChecked:q,hasConsumerStoppedPropagationRef:s,isFormControl:u,bubbleInput:v}=r(t,a),w=(0,e.s)(h,p),x=d.useRef(n);return d.useEffect(()=>{let a=i?.form;if(a){let b=()=>q(x.current);return a.addEventListener("reset",b),()=>a.removeEventListener("reset",b)}},[i,q]),(0,m.jsx)(l.sG.button,{type:"button",role:"checkbox","aria-checked":A(n)?"mixed":n,"aria-required":o,"data-state":B(n),"data-disabled":k?"":void 0,disabled:k,value:j,...f,ref:w,onKeyDown:(0,g.m)(b,a=>{"Enter"===a.key&&a.preventDefault()}),onClick:(0,g.m)(c,a=>{q(a=>!!A(a)||!a),v&&u&&(s.current=a.isPropagationStopped(),s.current||a.stopPropagation())})})});u.displayName=t;var v=d.forwardRef((a,b)=>{let{__scopeCheckbox:c,name:d,checked:e,defaultChecked:f,required:g,disabled:h,value:i,onCheckedChange:j,form:k,...l}=a;return(0,m.jsx)(s,{__scopeCheckbox:c,checked:e,defaultChecked:f,disabled:h,required:g,onCheckedChange:j,name:d,form:k,value:i,internal_do_not_use_render:({isFormControl:a})=>(0,m.jsxs)(m.Fragment,{children:[(0,m.jsx)(u,{...l,ref:b,__scopeCheckbox:c}),a&&(0,m.jsx)(z,{__scopeCheckbox:c})]})})});v.displayName=n;var w="CheckboxIndicator",x=d.forwardRef((a,b)=>{let{__scopeCheckbox:c,forceMount:d,...e}=a,f=r(w,c);return(0,m.jsx)(k.C,{present:d||A(f.checked)||!0===f.checked,children:(0,m.jsx)(l.sG.span,{"data-state":B(f.checked),"data-disabled":f.disabled?"":void 0,...e,ref:b,style:{pointerEvents:"none",...a.style}})})});x.displayName=w;var y="CheckboxBubbleInput",z=d.forwardRef(({__scopeCheckbox:a,...b},c)=>{let{control:f,hasConsumerStoppedPropagationRef:g,checked:h,defaultChecked:k,required:n,disabled:o,name:p,value:q,form:s,bubbleInput:t,setBubbleInput:u}=r(y,a),v=(0,e.s)(c,u),w=(0,i.Z)(h),x=(0,j.X)(f);d.useEffect(()=>{if(!t)return;let a=Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype,"checked").set,b=!g.current;if(w!==h&&a){let c=new Event("click",{bubbles:b});t.indeterminate=A(h),a.call(t,!A(h)&&h),t.dispatchEvent(c)}},[t,w,h,g]);let z=d.useRef(!A(h)&&h);return(0,m.jsx)(l.sG.input,{type:"checkbox","aria-hidden":!0,defaultChecked:k??z.current,required:n,disabled:o,name:p,value:q,form:s,...b,tabIndex:-1,ref:v,style:{...b.style,...x,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});function A(a){return"indeterminate"===a}function B(a){return A(a)?"indeterminate":a?"checked":"unchecked"}z.displayName=y},41662:(a,b,c)=>{c.d(b,{$L:()=>D,Ln:()=>p,N0:()=>B,Um:()=>C,Wg:()=>q,X9:()=>g,Ys:()=>m,dL:()=>O,jP:()=>t,n9:()=>z,oM:()=>u,zy:()=>y});var d=c(81553);class e{constructor(a,b,c){this.pos=a,this.delInfo=b,this.recover=c}get deleted(){return(8&this.delInfo)>0}get deletedBefore(){return(5&this.delInfo)>0}get deletedAfter(){return(6&this.delInfo)>0}get deletedAcross(){return(4&this.delInfo)>0}}class f{constructor(a,b=!1){if(this.ranges=a,this.inverted=b,!a.length&&f.empty)return f.empty}recover(a){let b=0,c=65535&a;if(!this.inverted)for(let a=0;a<c;a++)b+=this.ranges[3*a+2]-this.ranges[3*a+1];return this.ranges[3*c]+b+(a-(65535&a))/65536}mapResult(a,b=1){return this._map(a,b,!1)}map(a,b=1){return this._map(a,b,!0)}_map(a,b,c){let d=0,f=this.inverted?2:1,g=this.inverted?1:2;for(let h=0;h<this.ranges.length;h+=3){let i=this.ranges[h]-(this.inverted?d:0);if(i>a)break;let j=this.ranges[h+f],k=this.ranges[h+g],l=i+j;if(a<=l){let f=j?a==i?-1:a==l?1:b:b,g=i+d+(f<0?0:k);if(c)return g;let m=a==(b<0?i:l)?null:h/3+(a-i)*65536,n=a==i?2:a==l?1:4;return(b<0?a!=i:a!=l)&&(n|=8),new e(g,n,m)}d+=k-j}return c?a+d:new e(a+d,0,null)}touches(a,b){let c=0,d=65535&b,e=this.inverted?2:1,f=this.inverted?1:2;for(let b=0;b<this.ranges.length;b+=3){let g=this.ranges[b]-(this.inverted?c:0);if(g>a)break;let h=this.ranges[b+e];if(a<=g+h&&b==3*d)return!0;c+=this.ranges[b+f]-h}return!1}forEach(a){let b=this.inverted?2:1,c=this.inverted?1:2;for(let d=0,e=0;d<this.ranges.length;d+=3){let f=this.ranges[d],g=f-(this.inverted?e:0),h=f+(this.inverted?0:e),i=this.ranges[d+b],j=this.ranges[d+c];a(g,g+i,h,h+j),e+=j-i}}invert(){return new f(this.ranges,!this.inverted)}toString(){return(this.inverted?"-":"")+JSON.stringify(this.ranges)}static offset(a){return 0==a?f.empty:new f(a<0?[0,-a,0]:[0,0,a])}}f.empty=new f([]);class g{constructor(a,b,c=0,d=a?a.length:0){this.mirror=b,this.from=c,this.to=d,this._maps=a||[],this.ownData=!(a||b)}get maps(){return this._maps}slice(a=0,b=this.maps.length){return new g(this._maps,this.mirror,a,b)}appendMap(a,b){this.ownData||(this._maps=this._maps.slice(),this.mirror=this.mirror&&this.mirror.slice(),this.ownData=!0),this.to=this._maps.push(a),null!=b&&this.setMirror(this._maps.length-1,b)}appendMapping(a){for(let b=0,c=this._maps.length;b<a._maps.length;b++){let d=a.getMirror(b);this.appendMap(a._maps[b],null!=d&&d<b?c+d:void 0)}}getMirror(a){if(this.mirror){for(let b=0;b<this.mirror.length;b++)if(this.mirror[b]==a)return this.mirror[b+(b%2?-1:1)]}}setMirror(a,b){this.mirror||(this.mirror=[]),this.mirror.push(a,b)}appendMappingInverted(a){for(let b=a.maps.length-1,c=this._maps.length+a._maps.length;b>=0;b--){let d=a.getMirror(b);this.appendMap(a._maps[b].invert(),null!=d&&d>b?c-d-1:void 0)}}invert(){let a=new g;return a.appendMappingInverted(this),a}map(a,b=1){if(this.mirror)return this._map(a,b,!0);for(let c=this.from;c<this.to;c++)a=this._maps[c].map(a,b);return a}mapResult(a,b=1){return this._map(a,b,!1)}_map(a,b,c){let d=0;for(let c=this.from;c<this.to;c++){let e=this._maps[c].mapResult(a,b);if(null!=e.recover){let b=this.getMirror(c);if(null!=b&&b>c&&b<this.to){c=b,a=this._maps[b].recover(e.recover);continue}}d|=e.delInfo,a=e.pos}return c?a:new e(a,d,null)}}let h=Object.create(null);class i{getMap(){return f.empty}merge(a){return null}static fromJSON(a,b){if(!b||!b.stepType)throw RangeError("Invalid input for Step.fromJSON");let c=h[b.stepType];if(!c)throw RangeError(`No step type ${b.stepType} defined`);return c.fromJSON(a,b)}static jsonID(a,b){if(a in h)throw RangeError("Duplicate use of step JSON ID "+a);return h[a]=b,b.prototype.jsonID=a,b}}class j{constructor(a,b){this.doc=a,this.failed=b}static ok(a){return new j(a,null)}static fail(a){return new j(null,a)}static fromReplace(a,b,c,e){try{return j.ok(a.replace(b,c,e))}catch(a){if(a instanceof d.vI)return j.fail(a.message);throw a}}}function k(a,b,c){let e=[];for(let d=0;d<a.childCount;d++){let f=a.child(d);f.content.size&&(f=f.copy(k(f.content,b,f))),f.isInline&&(f=b(f,c,d)),e.push(f)}return d.FK.fromArray(e)}class l extends i{constructor(a,b,c){super(),this.from=a,this.to=b,this.mark=c}apply(a){let b=a.slice(this.from,this.to),c=a.resolve(this.from),e=c.node(c.sharedDepth(this.to)),f=new d.Ji(k(b.content,(a,b)=>a.isAtom&&b.type.allowsMarkType(this.mark.type)?a.mark(this.mark.addToSet(a.marks)):a,e),b.openStart,b.openEnd);return j.fromReplace(a,this.from,this.to,f)}invert(){return new m(this.from,this.to,this.mark)}map(a){let b=a.mapResult(this.from,1),c=a.mapResult(this.to,-1);return b.deleted&&c.deleted||b.pos>=c.pos?null:new l(b.pos,c.pos,this.mark)}merge(a){return a instanceof l&&a.mark.eq(this.mark)&&this.from<=a.to&&this.to>=a.from?new l(Math.min(this.from,a.from),Math.max(this.to,a.to),this.mark):null}toJSON(){return{stepType:"addMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(a,b){if("number"!=typeof b.from||"number"!=typeof b.to)throw RangeError("Invalid input for AddMarkStep.fromJSON");return new l(b.from,b.to,a.markFromJSON(b.mark))}}i.jsonID("addMark",l);class m extends i{constructor(a,b,c){super(),this.from=a,this.to=b,this.mark=c}apply(a){let b=a.slice(this.from,this.to),c=new d.Ji(k(b.content,a=>a.mark(this.mark.removeFromSet(a.marks)),a),b.openStart,b.openEnd);return j.fromReplace(a,this.from,this.to,c)}invert(){return new l(this.from,this.to,this.mark)}map(a){let b=a.mapResult(this.from,1),c=a.mapResult(this.to,-1);return b.deleted&&c.deleted||b.pos>=c.pos?null:new m(b.pos,c.pos,this.mark)}merge(a){return a instanceof m&&a.mark.eq(this.mark)&&this.from<=a.to&&this.to>=a.from?new m(Math.min(this.from,a.from),Math.max(this.to,a.to),this.mark):null}toJSON(){return{stepType:"removeMark",mark:this.mark.toJSON(),from:this.from,to:this.to}}static fromJSON(a,b){if("number"!=typeof b.from||"number"!=typeof b.to)throw RangeError("Invalid input for RemoveMarkStep.fromJSON");return new m(b.from,b.to,a.markFromJSON(b.mark))}}i.jsonID("removeMark",m);class n extends i{constructor(a,b){super(),this.pos=a,this.mark=b}apply(a){let b=a.nodeAt(this.pos);if(!b)return j.fail("No node at mark step's position");let c=b.type.create(b.attrs,null,this.mark.addToSet(b.marks));return j.fromReplace(a,this.pos,this.pos+1,new d.Ji(d.FK.from(c),0,+!b.isLeaf))}invert(a){let b=a.nodeAt(this.pos);if(b){let a=this.mark.addToSet(b.marks);if(a.length==b.marks.length){for(let c=0;c<b.marks.length;c++)if(!b.marks[c].isInSet(a))return new n(this.pos,b.marks[c]);return new n(this.pos,this.mark)}}return new o(this.pos,this.mark)}map(a){let b=a.mapResult(this.pos,1);return b.deletedAfter?null:new n(b.pos,this.mark)}toJSON(){return{stepType:"addNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(a,b){if("number"!=typeof b.pos)throw RangeError("Invalid input for AddNodeMarkStep.fromJSON");return new n(b.pos,a.markFromJSON(b.mark))}}i.jsonID("addNodeMark",n);class o extends i{constructor(a,b){super(),this.pos=a,this.mark=b}apply(a){let b=a.nodeAt(this.pos);if(!b)return j.fail("No node at mark step's position");let c=b.type.create(b.attrs,null,this.mark.removeFromSet(b.marks));return j.fromReplace(a,this.pos,this.pos+1,new d.Ji(d.FK.from(c),0,+!b.isLeaf))}invert(a){let b=a.nodeAt(this.pos);return b&&this.mark.isInSet(b.marks)?new n(this.pos,this.mark):this}map(a){let b=a.mapResult(this.pos,1);return b.deletedAfter?null:new o(b.pos,this.mark)}toJSON(){return{stepType:"removeNodeMark",pos:this.pos,mark:this.mark.toJSON()}}static fromJSON(a,b){if("number"!=typeof b.pos)throw RangeError("Invalid input for RemoveNodeMarkStep.fromJSON");return new o(b.pos,a.markFromJSON(b.mark))}}i.jsonID("removeNodeMark",o);class p extends i{constructor(a,b,c,d=!1){super(),this.from=a,this.to=b,this.slice=c,this.structure=d}apply(a){return this.structure&&r(a,this.from,this.to)?j.fail("Structure replace would overwrite content"):j.fromReplace(a,this.from,this.to,this.slice)}getMap(){return new f([this.from,this.to-this.from,this.slice.size])}invert(a){return new p(this.from,this.from+this.slice.size,a.slice(this.from,this.to))}map(a){let b=a.mapResult(this.from,1),c=a.mapResult(this.to,-1);return b.deletedAcross&&c.deletedAcross?null:new p(b.pos,Math.max(b.pos,c.pos),this.slice,this.structure)}merge(a){if(!(a instanceof p)||a.structure||this.structure)return null;if(this.from+this.slice.size!=a.from||this.slice.openEnd||a.slice.openStart)if(a.to!=this.from||this.slice.openStart||a.slice.openEnd)return null;else{let b=this.slice.size+a.slice.size==0?d.Ji.empty:new d.Ji(a.slice.content.append(this.slice.content),a.slice.openStart,this.slice.openEnd);return new p(a.from,this.to,b,this.structure)}{let b=this.slice.size+a.slice.size==0?d.Ji.empty:new d.Ji(this.slice.content.append(a.slice.content),this.slice.openStart,a.slice.openEnd);return new p(this.from,this.to+(a.to-a.from),b,this.structure)}}toJSON(){let a={stepType:"replace",from:this.from,to:this.to};return this.slice.size&&(a.slice=this.slice.toJSON()),this.structure&&(a.structure=!0),a}static fromJSON(a,b){if("number"!=typeof b.from||"number"!=typeof b.to)throw RangeError("Invalid input for ReplaceStep.fromJSON");return new p(b.from,b.to,d.Ji.fromJSON(a,b.slice),!!b.structure)}}i.jsonID("replace",p);class q extends i{constructor(a,b,c,d,e,f,g=!1){super(),this.from=a,this.to=b,this.gapFrom=c,this.gapTo=d,this.slice=e,this.insert=f,this.structure=g}apply(a){if(this.structure&&(r(a,this.from,this.gapFrom)||r(a,this.gapTo,this.to)))return j.fail("Structure gap-replace would overwrite content");let b=a.slice(this.gapFrom,this.gapTo);if(b.openStart||b.openEnd)return j.fail("Gap is not a flat range");let c=this.slice.insertAt(this.insert,b.content);return c?j.fromReplace(a,this.from,this.to,c):j.fail("Content does not fit in gap")}getMap(){return new f([this.from,this.gapFrom-this.from,this.insert,this.gapTo,this.to-this.gapTo,this.slice.size-this.insert])}invert(a){let b=this.gapTo-this.gapFrom;return new q(this.from,this.from+this.slice.size+b,this.from+this.insert,this.from+this.insert+b,a.slice(this.from,this.to).removeBetween(this.gapFrom-this.from,this.gapTo-this.from),this.gapFrom-this.from,this.structure)}map(a){let b=a.mapResult(this.from,1),c=a.mapResult(this.to,-1),d=this.from==this.gapFrom?b.pos:a.map(this.gapFrom,-1),e=this.to==this.gapTo?c.pos:a.map(this.gapTo,1);return b.deletedAcross&&c.deletedAcross||d<b.pos||e>c.pos?null:new q(b.pos,c.pos,d,e,this.slice,this.insert,this.structure)}toJSON(){let a={stepType:"replaceAround",from:this.from,to:this.to,gapFrom:this.gapFrom,gapTo:this.gapTo,insert:this.insert};return this.slice.size&&(a.slice=this.slice.toJSON()),this.structure&&(a.structure=!0),a}static fromJSON(a,b){if("number"!=typeof b.from||"number"!=typeof b.to||"number"!=typeof b.gapFrom||"number"!=typeof b.gapTo||"number"!=typeof b.insert)throw RangeError("Invalid input for ReplaceAroundStep.fromJSON");return new q(b.from,b.to,b.gapFrom,b.gapTo,d.Ji.fromJSON(a,b.slice),b.insert,!!b.structure)}}function r(a,b,c){let d=a.resolve(b),e=c-b,f=d.depth;for(;e>0&&f>0&&d.indexAfter(f)==d.node(f).childCount;)f--,e--;if(e>0){let a=d.node(f).maybeChild(d.indexAfter(f));for(;e>0;){if(!a||a.isLeaf)return!0;a=a.firstChild,e--}}return!1}function s(a,b,c,e=c.contentMatch,f=!0){let g=a.doc.nodeAt(b),h=[],i=b+1;for(let b=0;b<g.childCount;b++){let j=g.child(b),k=i+j.nodeSize,l=e.matchType(j.type);if(l){e=l;for(let b=0;b<j.marks.length;b++)c.allowsMarkType(j.marks[b].type)||a.step(new m(i,k,j.marks[b]));if(f&&j.isText&&"pre"!=c.whitespace){let a,b=/\r?\n|\r/g,e;for(;a=b.exec(j.text);)e||(e=new d.Ji(d.FK.from(c.schema.text(" ",c.allowedMarks(j.marks))),0,0)),h.push(new p(i+a.index,i+a.index+a[0].length,e))}}else h.push(new p(i,k,d.Ji.empty));i=k}if(!e.validEnd){let b=e.fillBefore(d.FK.empty,!0);a.replace(i,i,new d.Ji(b,0,0))}for(let b=h.length-1;b>=0;b--)a.step(h[b])}function t(a){let b=a.parent.content.cutByIndex(a.startIndex,a.endIndex);for(let c=a.depth;;--c){let d=a.$from.node(c),e=a.$from.index(c),f=a.$to.indexAfter(c);if(c<a.depth&&d.canReplace(e,f,b))return c;if(0==c||d.type.spec.isolating||!((0==e||d.canReplace(e,d.childCount))&&(f==d.childCount||d.canReplace(0,f))))break}return null}function u(a,b,c=null,d=a){let e=function(a,b){let{parent:c,startIndex:d,endIndex:e}=a,f=c.contentMatchAt(d).findWrapping(b);if(!f)return null;let g=f.length?f[0]:b;return c.canReplaceWith(d,e,g)?f:null}(a,b),f=e&&function(a,b){let{parent:c,startIndex:d,endIndex:e}=a,f=c.child(d),g=b.contentMatch.findWrapping(f.type);if(!g)return null;let h=(g.length?g[g.length-1]:b).contentMatch;for(let a=d;h&&a<e;a++)h=h.matchType(c.child(a).type);return h&&h.validEnd?g:null}(d,b);return f?e.map(v).concat({type:b,attrs:c}).concat(f.map(v)):null}function v(a){return{type:a,attrs:null}}function w(a,b,c,d){b.forEach((e,f)=>{if(e.isText){let g,h=/\r?\n|\r/g;for(;g=h.exec(e.text);){let e=a.mapping.slice(d).map(c+1+f+g.index);a.replaceWith(e,e+1,b.type.schema.linebreakReplacement.create())}}})}function x(a,b,c,d){b.forEach((e,f)=>{if(e.type==e.type.schema.linebreakReplacement){let e=a.mapping.slice(d).map(c+1+f);a.replaceWith(e,e+1,b.type.schema.text("\n"))}})}function y(a,b,c=1,d){let e=a.resolve(b),f=e.depth-c,g=d&&d[d.length-1]||e.parent;if(f<0||e.parent.type.spec.isolating||!e.parent.canReplace(e.index(),e.parent.childCount)||!g.type.validContent(e.parent.content.cutByIndex(e.index(),e.parent.childCount)))return!1;for(let a=e.depth-1,b=c-2;a>f;a--,b--){let c=e.node(a),f=e.index(a);if(c.type.spec.isolating)return!1;let g=c.content.cutByIndex(f,c.childCount),h=d&&d[b+1];h&&(g=g.replaceChild(0,h.type.create(h.attrs)));let i=d&&d[b]||c;if(!c.canReplace(f+1,c.childCount)||!i.type.validContent(g))return!1}let h=e.indexAfter(f),i=d&&d[0];return e.node(f).canReplaceWith(h,h,i?i.type:e.node(f+1).type)}function z(a,b){let c=a.resolve(b),d=c.index();return A(c.nodeBefore,c.nodeAfter)&&c.parent.canReplace(d,d+1)}function A(a,b){return!!(a&&b&&!a.isLeaf&&function(a,b){b.content.size||a.type.compatibleContent(b.type);let c=a.contentMatchAt(a.childCount),{linebreakReplacement:d}=a.type.schema;for(let e=0;e<b.childCount;e++){let f=b.child(e),g=f.type==d?a.type.schema.nodes.text:f.type;if(!(c=c.matchType(g))||!a.type.allowsMarks(f.marks))return!1}return c.validEnd}(a,b))}function B(a,b,c=-1){let d=a.resolve(b);for(let a=d.depth;;a--){let e,f,g=d.index(a);if(a==d.depth?(e=d.nodeBefore,f=d.nodeAfter):c>0?(e=d.node(a+1),g++,f=d.node(a).maybeChild(g)):(e=d.node(a).maybeChild(g-1),f=d.node(a+1)),e&&!e.isTextblock&&A(e,f)&&d.node(a).canReplace(g,g+1))return b;if(0==a)break;b=c<0?d.before(a):d.after(a)}}function C(a,b,c){let d=a.resolve(b);if(!c.content.size)return b;let e=c.content;for(let a=0;a<c.openStart;a++)e=e.firstChild.content;for(let a=1;a<=(0==c.openStart&&c.size?2:1);a++)for(let b=d.depth;b>=0;b--){let c=b==d.depth?0:d.pos<=(d.start(b+1)+d.end(b+1))/2?-1:1,f=d.index(b)+ +(c>0),g=d.node(b),h=!1;if(1==a)h=g.canReplace(f,f,e);else{let a=g.contentMatchAt(f).findWrapping(e.firstChild.type);h=a&&g.canReplaceWith(f,f,a[0])}if(h)return 0==c?d.pos:c<0?d.before(b+1):d.after(b+1)}return null}function D(a,b,c=b,e=d.Ji.empty){if(b==c&&!e.size)return null;let f=a.resolve(b),g=a.resolve(c);return E(f,g,e)?new p(b,c,e):new F(f,g,e).fit()}function E(a,b,c){return!c.openStart&&!c.openEnd&&a.start()==b.start()&&a.parent.canReplace(a.index(),b.index(),c.content)}i.jsonID("replaceAround",q);class F{constructor(a,b,c){this.$from=a,this.$to=b,this.unplaced=c,this.frontier=[],this.placed=d.FK.empty;for(let b=0;b<=a.depth;b++){let c=a.node(b);this.frontier.push({type:c.type,match:c.contentMatchAt(a.indexAfter(b))})}for(let b=a.depth;b>0;b--)this.placed=d.FK.from(a.node(b).copy(this.placed))}get depth(){return this.frontier.length-1}fit(){for(;this.unplaced.size;){let a=this.findFittable();a?this.placeNodes(a):this.openMore()||this.dropNode()}let a=this.mustMoveInline(),b=this.placed.size-this.depth-this.$from.depth,c=this.$from,e=this.close(a<0?this.$to:c.doc.resolve(a));if(!e)return null;let f=this.placed,g=c.depth,h=e.depth;for(;g&&h&&1==f.childCount;)f=f.firstChild.content,g--,h--;let i=new d.Ji(f,g,h);return a>-1?new q(c.pos,a,this.$to.pos,this.$to.end(),i,b):i.size||c.pos!=this.$to.pos?new p(c.pos,e.pos,i):null}findFittable(){let a=this.unplaced.openStart;for(let b=this.unplaced.content,c=0,d=this.unplaced.openEnd;c<a;c++){let e=b.firstChild;if(b.childCount>1&&(d=0),e.type.spec.isolating&&d<=c){a=c;break}b=e.content}for(let b=1;b<=2;b++)for(let c=1==b?a:this.unplaced.openStart;c>=0;c--){let a=null,e=(c?(a=I(this.unplaced.content,c-1).firstChild).content:this.unplaced.content).firstChild;for(let f=this.depth;f>=0;f--){let{type:g,match:h}=this.frontier[f],i,j=null;if(1==b&&(e?h.matchType(e.type)||(j=h.fillBefore(d.FK.from(e),!1)):a&&g.compatibleContent(a.type)))return{sliceDepth:c,frontierDepth:f,parent:a,inject:j};if(2==b&&e&&(i=h.findWrapping(e.type)))return{sliceDepth:c,frontierDepth:f,parent:a,wrap:i};if(a&&h.matchType(a.type))break}}}openMore(){let{content:a,openStart:b,openEnd:c}=this.unplaced,e=I(a,b);return!!e.childCount&&!e.firstChild.isLeaf&&(this.unplaced=new d.Ji(a,b+1,Math.max(c,e.size+b>=a.size-c?b+1:0)),!0)}dropNode(){let{content:a,openStart:b,openEnd:c}=this.unplaced,e=I(a,b);if(e.childCount<=1&&b>0){let f=a.size-b<=b+e.size;this.unplaced=new d.Ji(G(a,b-1,1),b-1,f?b-1:c)}else this.unplaced=new d.Ji(G(a,b,1),b,c)}placeNodes({sliceDepth:a,frontierDepth:b,parent:c,inject:e,wrap:f}){for(;this.depth>b;)this.closeFrontierNode();if(f)for(let a=0;a<f.length;a++)this.openFrontierNode(f[a]);let g=this.unplaced,h=c?c.content:g.content,i=g.openStart-a,j=0,k=[],{match:l,type:m}=this.frontier[b];if(e){for(let a=0;a<e.childCount;a++)k.push(e.child(a));l=l.matchFragment(e)}let n=h.size+a-(g.content.size-g.openEnd);for(;j<h.childCount;){let a=h.child(j),b=l.matchType(a.type);if(!b)break;(++j>1||0==i||a.content.size)&&(l=b,k.push(function a(b,c,e){if(c<=0)return b;let f=b.content;return c>1&&(f=f.replaceChild(0,a(f.firstChild,c-1,1==f.childCount?e-1:0))),c>0&&(f=b.type.contentMatch.fillBefore(f).append(f),e<=0&&(f=f.append(b.type.contentMatch.matchFragment(f).fillBefore(d.FK.empty,!0)))),b.copy(f)}(a.mark(m.allowedMarks(a.marks)),1==j?i:0,j==h.childCount?n:-1)))}let o=j==h.childCount;o||(n=-1),this.placed=H(this.placed,b,d.FK.from(k)),this.frontier[b].match=l,o&&n<0&&c&&c.type==this.frontier[this.depth].type&&this.frontier.length>1&&this.closeFrontierNode();for(let a=0,b=h;a<n;a++){let a=b.lastChild;this.frontier.push({type:a.type,match:a.contentMatchAt(a.childCount)}),b=a.content}this.unplaced=o?0==a?d.Ji.empty:new d.Ji(G(g.content,a-1,1),a-1,n<0?g.openEnd:a-1):new d.Ji(G(g.content,a,j),g.openStart,g.openEnd)}mustMoveInline(){if(!this.$to.parent.isTextblock)return -1;let a=this.frontier[this.depth],b;if(!a.type.isTextblock||!J(this.$to,this.$to.depth,a.type,a.match,!1)||this.$to.depth==this.depth&&(b=this.findCloseLevel(this.$to))&&b.depth==this.depth)return -1;let{depth:c}=this.$to,d=this.$to.after(c);for(;c>1&&d==this.$to.end(--c);)++d;return d}findCloseLevel(a){b:for(let b=Math.min(this.depth,a.depth);b>=0;b--){let{match:c,type:d}=this.frontier[b],e=b<a.depth&&a.end(b+1)==a.pos+(a.depth-(b+1)),f=J(a,b,d,c,e);if(f){for(let c=b-1;c>=0;c--){let{match:b,type:d}=this.frontier[c],e=J(a,c,d,b,!0);if(!e||e.childCount)continue b}return{depth:b,fit:f,move:e?a.doc.resolve(a.after(b+1)):a}}}}close(a){let b=this.findCloseLevel(a);if(!b)return null;for(;this.depth>b.depth;)this.closeFrontierNode();b.fit.childCount&&(this.placed=H(this.placed,b.depth,b.fit)),a=b.move;for(let c=b.depth+1;c<=a.depth;c++){let b=a.node(c),d=b.type.contentMatch.fillBefore(b.content,!0,a.index(c));this.openFrontierNode(b.type,b.attrs,d)}return a}openFrontierNode(a,b=null,c){let e=this.frontier[this.depth];e.match=e.match.matchType(a),this.placed=H(this.placed,this.depth,d.FK.from(a.create(b,c))),this.frontier.push({type:a,match:a.contentMatch})}closeFrontierNode(){let a=this.frontier.pop().match.fillBefore(d.FK.empty,!0);a.childCount&&(this.placed=H(this.placed,this.frontier.length,a))}}function G(a,b,c){return 0==b?a.cutByIndex(c,a.childCount):a.replaceChild(0,a.firstChild.copy(G(a.firstChild.content,b-1,c)))}function H(a,b,c){return 0==b?a.append(c):a.replaceChild(a.childCount-1,a.lastChild.copy(H(a.lastChild.content,b-1,c)))}function I(a,b){for(let c=0;c<b;c++)a=a.firstChild.content;return a}function J(a,b,c,d,e){let f=a.node(b),g=e?a.indexAfter(b):a.index(b);if(g==f.childCount&&!c.compatibleContent(f.type))return null;let h=d.fillBefore(f.content,!0,g);return h&&!function(a,b,c){for(let d=c;d<b.childCount;d++)if(!a.allowsMarks(b.child(d).marks))return!0;return!1}(c,f.content,g)?h:null}function K(a,b){let c=[],d=Math.min(a.depth,b.depth);for(let e=d;e>=0;e--){let d=a.start(e);if(d<a.pos-(a.depth-e)||b.end(e)>b.pos+(b.depth-e)||a.node(e).type.spec.isolating||b.node(e).type.spec.isolating)break;(d==b.start(e)||e==a.depth&&e==b.depth&&a.parent.inlineContent&&b.parent.inlineContent&&e&&b.start(e-1)==d-1)&&c.push(e)}return c}class L extends i{constructor(a,b,c){super(),this.pos=a,this.attr=b,this.value=c}apply(a){let b=a.nodeAt(this.pos);if(!b)return j.fail("No node at attribute step's position");let c=Object.create(null);for(let a in b.attrs)c[a]=b.attrs[a];c[this.attr]=this.value;let e=b.type.create(c,null,b.marks);return j.fromReplace(a,this.pos,this.pos+1,new d.Ji(d.FK.from(e),0,+!b.isLeaf))}getMap(){return f.empty}invert(a){return new L(this.pos,this.attr,a.nodeAt(this.pos).attrs[this.attr])}map(a){let b=a.mapResult(this.pos,1);return b.deletedAfter?null:new L(b.pos,this.attr,this.value)}toJSON(){return{stepType:"attr",pos:this.pos,attr:this.attr,value:this.value}}static fromJSON(a,b){if("number"!=typeof b.pos||"string"!=typeof b.attr)throw RangeError("Invalid input for AttrStep.fromJSON");return new L(b.pos,b.attr,b.value)}}i.jsonID("attr",L);class M extends i{constructor(a,b){super(),this.attr=a,this.value=b}apply(a){let b=Object.create(null);for(let c in a.attrs)b[c]=a.attrs[c];b[this.attr]=this.value;let c=a.type.create(b,a.content,a.marks);return j.ok(c)}getMap(){return f.empty}invert(a){return new M(this.attr,a.attrs[this.attr])}map(a){return this}toJSON(){return{stepType:"docAttr",attr:this.attr,value:this.value}}static fromJSON(a,b){if("string"!=typeof b.attr)throw RangeError("Invalid input for DocAttrStep.fromJSON");return new M(b.attr,b.value)}}i.jsonID("docAttr",M);let N=class extends Error{};(N=function a(b){let c=Error.call(this,b);return c.__proto__=a.prototype,c}).prototype=Object.create(Error.prototype),N.prototype.constructor=N,N.prototype.name="TransformError";class O{constructor(a){this.doc=a,this.steps=[],this.docs=[],this.mapping=new g}get before(){return this.docs.length?this.docs[0]:this.doc}step(a){let b=this.maybeStep(a);if(b.failed)throw new N(b.failed);return this}maybeStep(a){let b=a.apply(this.doc);return b.failed||this.addStep(a,b.doc),b}get docChanged(){return this.steps.length>0}addStep(a,b){this.docs.push(this.doc),this.steps.push(a),this.mapping.appendMap(a.getMap()),this.doc=b}replace(a,b=a,c=d.Ji.empty){let e=D(this.doc,a,b,c);return e&&this.step(e),this}replaceWith(a,b,c){return this.replace(a,b,new d.Ji(d.FK.from(c),0,0))}delete(a,b){return this.replace(a,b,d.Ji.empty)}insert(a,b){return this.replaceWith(a,a,b)}replaceRange(a,b,c){return!function(a,b,c,e){if(!e.size)return a.deleteRange(b,c);let f=a.doc.resolve(b),g=a.doc.resolve(c);if(E(f,g,e))return a.step(new p(b,c,e));let h=K(f,a.doc.resolve(c));0==h[h.length-1]&&h.pop();let i=-(f.depth+1);h.unshift(i);for(let a=f.depth,b=f.pos-1;a>0;a--,b--){let c=f.node(a).type.spec;if(c.defining||c.definingAsContext||c.isolating)break;h.indexOf(a)>-1?i=a:f.before(a)==b&&h.splice(1,0,-a)}let j=h.indexOf(i),k=[],l=e.openStart;for(let a=e.content,b=0;;b++){let c=a.firstChild;if(k.push(c),b==e.openStart)break;a=c.content}for(let a=l-1;a>=0;a--){var m;let b=k[a],c=(m=b.type).spec.defining||m.spec.definingForContent;if(c&&!b.sameMarkup(f.node(Math.abs(i)-1)))l=a;else if(c||!b.type.isTextblock)break}for(let b=e.openStart;b>=0;b--){let i=(b+l+1)%(e.openStart+1),m=k[i];if(m)for(let b=0;b<h.length;b++){let k=h[(b+j)%h.length],l=!0;k<0&&(l=!1,k=-k);let n=f.node(k-1),o=f.index(k-1);if(n.canReplaceWith(o,o,m.type,m.marks))return a.replace(f.before(k),l?g.after(k):c,new d.Ji(function a(b,c,e,f,g){if(c<e){let d=b.firstChild;b=b.replaceChild(0,d.copy(a(d.content,c+1,e,f,d)))}if(c>f){let a=g.contentMatchAt(0),c=a.fillBefore(b).append(b);b=c.append(a.matchFragment(c).fillBefore(d.FK.empty,!0))}return b}(e.content,0,e.openStart,i),i,e.openEnd))}}let n=a.steps.length;for(let d=h.length-1;d>=0&&(a.replace(b,c,e),!(a.steps.length>n));d--){let a=h[d];a<0||(b=f.before(a),c=g.after(a))}}(this,a,b,c),this}replaceRangeWith(a,b,c){var e=a,f=b;if(!c.isInline&&e==f&&this.doc.resolve(e).parent.content.size){let a=function(a,b,c){let d=a.resolve(b);if(d.parent.canReplaceWith(d.index(),d.index(),c))return b;if(0==d.parentOffset)for(let a=d.depth-1;a>=0;a--){let b=d.index(a);if(d.node(a).canReplaceWith(b,b,c))return d.before(a+1);if(b>0)return null}if(d.parentOffset==d.parent.content.size)for(let a=d.depth-1;a>=0;a--){let b=d.indexAfter(a);if(d.node(a).canReplaceWith(b,b,c))return d.after(a+1);if(b<d.node(a).childCount)break}return null}(this.doc,e,c.type);null!=a&&(e=f=a)}return this.replaceRange(e,f,new d.Ji(d.FK.from(c),0,0)),this}deleteRange(a,b){return!function(a,b,c){let d=a.doc.resolve(b),e=a.doc.resolve(c),f=K(d,e);for(let b=0;b<f.length;b++){let c=f[b],g=b==f.length-1;if(g&&0==c||d.node(c).type.contentMatch.validEnd)return a.delete(d.start(c),e.end(c));if(c>0&&(g||d.node(c-1).canReplace(d.index(c-1),e.indexAfter(c-1))))return a.delete(d.before(c),e.after(c))}for(let f=1;f<=d.depth&&f<=e.depth;f++)if(b-d.start(f)==d.depth-f&&c>d.end(f)&&e.end(f)-c!=e.depth-f&&d.start(f-1)==e.start(f-1)&&d.node(f-1).canReplace(d.index(f-1),e.index(f-1)))return a.delete(d.before(f),c);a.delete(b,c)}(this,a,b),this}lift(a,b){return!function(a,b,c){let{$from:e,$to:f,depth:g}=b,h=e.before(g+1),i=f.after(g+1),j=h,k=i,l=d.FK.empty,m=0;for(let a=g,b=!1;a>c;a--)b||e.index(a)>0?(b=!0,l=d.FK.from(e.node(a).copy(l)),m++):j--;let n=d.FK.empty,o=0;for(let a=g,b=!1;a>c;a--)b||f.after(a+1)<f.end(a)?(b=!0,n=d.FK.from(f.node(a).copy(n)),o++):k++;a.step(new q(j,k,h,i,new d.Ji(l.append(n),m,o),l.size-m,!0))}(this,a,b),this}join(a,b=1){return!function(a,b,c){let e=null,{linebreakReplacement:f}=a.doc.type.schema,g=a.doc.resolve(b-c),h=g.node().type;if(f&&h.inlineContent){let a="pre"==h.whitespace,b=!!h.contentMatch.matchType(f);a&&!b?e=!1:!a&&b&&(e=!0)}let i=a.steps.length;if(!1===e){let d=a.doc.resolve(b+c);x(a,d.node(),d.before(),i)}h.inlineContent&&s(a,b+c-1,h,g.node().contentMatchAt(g.index()),null==e);let j=a.mapping.slice(i),k=j.map(b-c);if(a.step(new p(k,j.map(b+c,-1),d.Ji.empty,!0)),!0===e){let b=a.doc.resolve(k);w(a,b.node(),b.before(),a.steps.length)}}(this,a,b),this}wrap(a,b){return!function(a,b,c){let e=d.FK.empty;for(let a=c.length-1;a>=0;a--){if(e.size){let b=c[a].type.contentMatch.matchFragment(e);if(!b||!b.validEnd)throw RangeError("Wrapper type given to Transform.wrap does not form valid content of its parent wrapper")}e=d.FK.from(c[a].type.create(c[a].attrs,e))}let f=b.start,g=b.end;a.step(new q(f,g,f,g,new d.Ji(e,0,0),c.length,!0))}(this,a,b),this}setBlockType(a,b=a,c,e=null){var f=this;if(!c.isTextblock)throw RangeError("Type given to setBlockType should be a textblock");let g=f.steps.length;return f.doc.nodesBetween(a,b,(a,b)=>{var h,i,j;let k,l,m="function"==typeof e?e(a):e;if(a.isTextblock&&!a.hasMarkup(c,m)&&(h=f.doc,i=f.mapping.slice(g).map(b),j=c,l=(k=h.resolve(i)).index(),k.parent.canReplaceWith(l,l+1,j))){let e=null;if(c.schema.linebreakReplacement){let a="pre"==c.whitespace,b=!!c.contentMatch.matchType(c.schema.linebreakReplacement);a&&!b?e=!1:!a&&b&&(e=!0)}!1===e&&x(f,a,b,g),s(f,f.mapping.slice(g).map(b,1),c,void 0,null===e);let h=f.mapping.slice(g),i=h.map(b,1),j=h.map(b+a.nodeSize,1);return f.step(new q(i,j,i+1,j-1,new d.Ji(d.FK.from(c.create(m,null,a.marks)),0,0),1,!0)),!0===e&&w(f,a,b,g),!1}}),this}setNodeMarkup(a,b,c=null,e){return!function(a,b,c,e,f){let g=a.doc.nodeAt(b);if(!g)throw RangeError("No node at given position");c||(c=g.type);let h=c.create(e,null,f||g.marks);if(g.isLeaf)return a.replaceWith(b,b+g.nodeSize,h);if(!c.validContent(g.content))throw RangeError("Invalid content for node type "+c.name);a.step(new q(b,b+g.nodeSize,b+1,b+g.nodeSize-1,new d.Ji(d.FK.from(h),0,0),1,!0))}(this,a,b,c,e),this}setNodeAttribute(a,b,c){return this.step(new L(a,b,c)),this}setDocAttribute(a,b){return this.step(new M(a,b)),this}addNodeMark(a,b){return this.step(new n(a,b)),this}removeNodeMark(a,b){let c=this.doc.nodeAt(a);if(!c)throw RangeError("No node at position "+a);if(b instanceof d.CU)b.isInSet(c.marks)&&this.step(new o(a,b));else{let d=c.marks,e,f=[];for(;e=b.isInSet(d);)f.push(new o(a,e)),d=e.removeFromSet(d);for(let a=f.length-1;a>=0;a--)this.step(f[a])}return this}split(a,b=1,c){return!function(a,b,c=1,e){let f=a.doc.resolve(b),g=d.FK.empty,h=d.FK.empty;for(let a=f.depth,b=f.depth-c,i=c-1;a>b;a--,i--){g=d.FK.from(f.node(a).copy(g));let b=e&&e[i];h=d.FK.from(b?b.type.create(b.attrs,h):f.node(a).copy(h))}a.step(new p(b,b,new d.Ji(g.append(h),c,c),!0))}(this,a,b,c),this}addMark(a,b,c){var d;let e,f,g,h;return d=this,g=[],h=[],d.doc.nodesBetween(a,b,(d,i,j)=>{if(!d.isInline)return;let k=d.marks;if(!c.isInSet(k)&&j.type.allowsMarkType(c.type)){let j=Math.max(i,a),n=Math.min(i+d.nodeSize,b),o=c.addToSet(k);for(let a=0;a<k.length;a++)k[a].isInSet(o)||(e&&e.to==j&&e.mark.eq(k[a])?e.to=n:g.push(e=new m(j,n,k[a])));f&&f.to==j?f.to=n:h.push(f=new l(j,n,c))}}),g.forEach(a=>d.step(a)),h.forEach(a=>d.step(a)),this}removeMark(a,b,c){var e;let f,g;return e=this,f=[],g=0,e.doc.nodesBetween(a,b,(e,h)=>{if(!e.isInline)return;g++;let i=null;if(c instanceof d.sX){let a=e.marks,b;for(;b=c.isInSet(a);)(i||(i=[])).push(b),a=b.removeFromSet(a)}else c?c.isInSet(e.marks)&&(i=[c]):i=e.marks;if(i&&i.length){let c=Math.min(h+e.nodeSize,b);for(let b=0;b<i.length;b++){let d=i[b],e;for(let a=0;a<f.length;a++){let b=f[a];b.step==g-1&&d.eq(f[a].style)&&(e=b)}e?(e.to=c,e.step=g):f.push({style:d,from:Math.max(h,a),to:c,step:g})}}}),f.forEach(a=>e.step(new m(a.from,a.to,a.style))),this}clearIncompatible(a,b,c){return s(this,a,b,c),this}}},41862:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},44731:(a,b,c)=>{c.d(b,{A:()=>d});var d=c(65636).Rg},45984:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("heading-1",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"m17 12 3-2v8",key:"1hhhft"}]])},47342:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("link",[["path",{d:"M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71",key:"1cjeqo"}],["path",{d:"M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71",key:"19qd67"}]])},54388:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("undo",[["path",{d:"M3 7v6h6",key:"1v2h90"}],["path",{d:"M21 17a9 9 0 0 0-9-9 9 9 0 0 0-6 2.3L3 13",key:"1r6uu6"}]])},65636:(a,b,c)=>{c.d(b,{Rg:()=>h,_J:()=>w,ck:()=>i,it:()=>t});var d=c(81373),e=Object.defineProperty,f="textStyle",g=/^\s*([-+*])\s$/,h=d.bP.create({name:"bulletList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML:()=>[{tag:"ul"}],renderHTML({HTMLAttributes:a}){return["ul",(0,d.KV)(this.options.HTMLAttributes,a),0]},addCommands(){return{toggleBulletList:()=>({commands:a,chain:b})=>this.options.keepAttributes?b().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(f)).run():a.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-8":()=>this.editor.commands.toggleBulletList()}},addInputRules(){let a=(0,d.tG)({find:g,type:this.type});return(this.options.keepMarks||this.options.keepAttributes)&&(a=(0,d.tG)({find:g,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:()=>this.editor.getAttributes(f),editor:this.editor})),[a]}}),i=d.bP.create({name:"listItem",addOptions:()=>({HTMLAttributes:{},bulletListTypeName:"bulletList",orderedListTypeName:"orderedList"}),content:"paragraph block*",defining:!0,parseHTML:()=>[{tag:"li"}],renderHTML({HTMLAttributes:a}){return["li",(0,d.KV)(this.options.HTMLAttributes,a),0]},addKeyboardShortcuts(){return{Enter:()=>this.editor.commands.splitListItem(this.name),Tab:()=>this.editor.commands.sinkListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)}}});((a,b)=>{for(var c in b)e(a,c,{get:b[c],enumerable:!0})})({},{findListItemPos:()=>j,getNextListDepth:()=>k,handleBackspace:()=>o,handleDelete:()=>r,hasListBefore:()=>l,hasListItemAfter:()=>s,hasListItemBefore:()=>m,listItemHasSubList:()=>n,nextListIsDeeper:()=>p,nextListIsHigher:()=>q});var j=(a,b)=>{let{$from:c}=b.selection,e=(0,d.Pg)(a,b.schema),f=c.depth,g=c.pos,h=null;for(;f>0&&null===h;)c.node(f).type===e?h=f:(f-=1,g-=1);return null===h?null:{$pos:b.doc.resolve(g),depth:h}},k=(a,b)=>{let c=j(a,b);if(!c)return!1;let[,e]=(0,d.fl)(b,a,c.$pos.pos+4);return e},l=(a,b,c)=>{let{$anchor:d}=a.selection,e=Math.max(0,d.pos-2),f=a.doc.resolve(e).node();return!!f&&!!c.includes(f.type.name)},m=(a,b)=>{var c;let{$anchor:d}=b.selection,e=b.doc.resolve(d.pos-2);return 0!==e.index()&&(null==(c=e.nodeBefore)?void 0:c.type.name)===a},n=(a,b,c)=>{if(!c)return!1;let e=(0,d.Pg)(a,b.schema),f=!1;return c.descendants(a=>{a.type===e&&(f=!0)}),f},o=(a,b,c)=>{if(a.commands.undoInputRule())return!0;if(a.state.selection.from!==a.state.selection.to)return!1;if(!(0,d.rU)(a.state,b)&&l(a.state,b,c)){let{$anchor:c}=a.state.selection,d=a.state.doc.resolve(c.before()-1),e=[];d.node().descendants((a,c)=>{a.type.name===b&&e.push({node:a,pos:c})});let f=e.at(-1);if(!f)return!1;let g=a.state.doc.resolve(d.start()+f.pos+1);return a.chain().cut({from:c.start()-1,to:c.end()+1},g.end()).joinForward().run()}if(!(0,d.rU)(a.state,b)||!(0,d.J_)(a.state))return!1;let e=j(b,a.state);if(!e)return!1;let f=a.state.doc.resolve(e.$pos.pos-2).node(e.depth),g=n(b,a.state,f);return m(b,a.state)&&!g?a.commands.joinItemBackward():a.chain().liftListItem(b).run()},p=(a,b)=>{let c=k(a,b),d=j(a,b);return!!d&&!!c&&c>d.depth},q=(a,b)=>{let c=k(a,b),d=j(a,b);return!!d&&!!c&&c<d.depth},r=(a,b)=>{if(!(0,d.rU)(a.state,b)||!(0,d.QN)(a.state,b))return!1;let{selection:c}=a.state,{$from:e,$to:f}=c;return!(!c.empty&&e.sameParent(f))&&(p(b,a.state)?a.chain().focus(a.state.selection.from+4).lift(b).joinBackward().run():q(b,a.state)?a.chain().joinForward().joinBackward().run():a.commands.joinItemForward())},s=(a,b)=>{var c;let{$anchor:d}=b.selection,e=b.doc.resolve(d.pos-d.parentOffset-2);return e.index()!==e.parent.childCount-1&&(null==(c=e.nodeAfter)?void 0:c.type.name)===a},t=d.YY.create({name:"listKeymap",addOptions:()=>({listTypes:[{itemName:"listItem",wrapperNames:["bulletList","orderedList"]},{itemName:"taskItem",wrapperNames:["taskList"]}]}),addKeyboardShortcuts(){return{Delete:({editor:a})=>{let b=!1;return this.options.listTypes.forEach(({itemName:c})=>{void 0!==a.state.schema.nodes[c]&&r(a,c)&&(b=!0)}),b},"Mod-Delete":({editor:a})=>{let b=!1;return this.options.listTypes.forEach(({itemName:c})=>{void 0!==a.state.schema.nodes[c]&&r(a,c)&&(b=!0)}),b},Backspace:({editor:a})=>{let b=!1;return this.options.listTypes.forEach(({itemName:c,wrapperNames:d})=>{void 0!==a.state.schema.nodes[c]&&o(a,c,d)&&(b=!0)}),b},"Mod-Backspace":({editor:a})=>{let b=!1;return this.options.listTypes.forEach(({itemName:c,wrapperNames:d})=>{void 0!==a.state.schema.nodes[c]&&o(a,c,d)&&(b=!0)}),b}}}}),u="textStyle",v=/^(\d+)\.\s$/,w=d.bP.create({name:"orderedList",addOptions:()=>({itemTypeName:"listItem",HTMLAttributes:{},keepMarks:!1,keepAttributes:!1}),group:"block list",content(){return`${this.options.itemTypeName}+`},addAttributes:()=>({start:{default:1,parseHTML:a=>a.hasAttribute("start")?parseInt(a.getAttribute("start")||"",10):1},type:{default:null,parseHTML:a=>a.getAttribute("type")}}),parseHTML:()=>[{tag:"ol"}],renderHTML({HTMLAttributes:a}){let{start:b,...c}=a;return 1===b?["ol",(0,d.KV)(this.options.HTMLAttributes,c),0]:["ol",(0,d.KV)(this.options.HTMLAttributes,a),0]},addCommands(){return{toggleOrderedList:()=>({commands:a,chain:b})=>this.options.keepAttributes?b().toggleList(this.name,this.options.itemTypeName,this.options.keepMarks).updateAttributes("listItem",this.editor.getAttributes(u)).run():a.toggleList(this.name,this.options.itemTypeName,this.options.keepMarks)}},addKeyboardShortcuts(){return{"Mod-Shift-7":()=>this.editor.commands.toggleOrderedList()}},addInputRules(){let a=(0,d.tG)({find:v,type:this.type,getAttributes:a=>({start:+a[1]}),joinPredicate:(a,b)=>b.childCount+b.attrs.start===+a[1]});return(this.options.keepMarks||this.options.keepAttributes)&&(a=(0,d.tG)({find:v,type:this.type,keepMarks:this.options.keepMarks,keepAttributes:this.options.keepAttributes,getAttributes:a=>({start:+a[1],...this.editor.getAttributes(u)}),joinPredicate:(a,b)=>b.childCount+b.attrs.start===+a[1],editor:this.editor})),[a]}}),x=/^\s*(\[([( |x])?\])\s$/,y=d.bP.create({name:"taskItem",addOptions:()=>({nested:!1,HTMLAttributes:{},taskListTypeName:"taskList",a11y:void 0}),content(){return this.options.nested?"paragraph block*":"paragraph+"},defining:!0,addAttributes:()=>({checked:{default:!1,keepOnSplit:!1,parseHTML:a=>{let b=a.getAttribute("data-checked");return""===b||"true"===b},renderHTML:a=>({"data-checked":a.checked})}}),parseHTML(){return[{tag:`li[data-type="${this.name}"]`,priority:51}]},renderHTML({node:a,HTMLAttributes:b}){return["li",(0,d.KV)(this.options.HTMLAttributes,b,{"data-type":this.name}),["label",["input",{type:"checkbox",checked:a.attrs.checked?"checked":null}],["span"]],["div",0]]},addKeyboardShortcuts(){let a={Enter:()=>this.editor.commands.splitListItem(this.name),"Shift-Tab":()=>this.editor.commands.liftListItem(this.name)};return this.options.nested?{...a,Tab:()=>this.editor.commands.sinkListItem(this.name)}:a},addNodeView(){return({node:a,HTMLAttributes:b,getPos:c,editor:d})=>{let e=document.createElement("li"),f=document.createElement("label"),g=document.createElement("span"),h=document.createElement("input"),i=document.createElement("div"),j=()=>{var b,c;h.ariaLabel=(null==(c=null==(b=this.options.a11y)?void 0:b.checkboxLabel)?void 0:c.call(b,a,h.checked))||`Task item checkbox for ${a.textContent||"empty task item"}`};return j(),f.contentEditable="false",h.type="checkbox",h.addEventListener("mousedown",a=>a.preventDefault()),h.addEventListener("change",b=>{if(!d.isEditable&&!this.options.onReadOnlyChecked){h.checked=!h.checked;return}let{checked:e}=b.target;d.isEditable&&"function"==typeof c&&d.chain().focus(void 0,{scrollIntoView:!1}).command(({tr:a})=>{let b=c();if("number"!=typeof b)return!1;let d=a.doc.nodeAt(b);return a.setNodeMarkup(b,void 0,{...null==d?void 0:d.attrs,checked:e}),!0}).run(),d.isEditable||!this.options.onReadOnlyChecked||this.options.onReadOnlyChecked(a,e)||(h.checked=!h.checked)}),Object.entries(this.options.HTMLAttributes).forEach(([a,b])=>{e.setAttribute(a,b)}),e.dataset.checked=a.attrs.checked,h.checked=a.attrs.checked,f.append(h,g),e.append(f,i),Object.entries(b).forEach(([a,b])=>{e.setAttribute(a,b)}),{dom:e,contentDOM:i,update:a=>a.type===this.type&&(e.dataset.checked=a.attrs.checked,h.checked=a.attrs.checked,j(),!0)}}},addInputRules(){return[(0,d.tG)({find:x,type:this.type,getAttributes:a=>({checked:"x"===a[a.length-1]})})]}}),z=d.bP.create({name:"taskList",addOptions:()=>({itemTypeName:"taskItem",HTMLAttributes:{}}),group:"block list",content(){return`${this.options.itemTypeName}+`},parseHTML(){return[{tag:`ul[data-type="${this.name}"]`,priority:51}]},renderHTML({HTMLAttributes:a}){return["ul",(0,d.KV)(this.options.HTMLAttributes,a,{"data-type":this.name}),0]},addCommands(){return{toggleTaskList:()=>({commands:a})=>a.toggleList(this.name,this.options.itemTypeName)}},addKeyboardShortcuts(){return{"Mod-Shift-9":()=>this.editor.commands.toggleTaskList()}}});d.YY.create({name:"listKit",addExtensions(){let a=[];return!1!==this.options.bulletList&&a.push(h.configure(this.options.bulletList)),!1!==this.options.listItem&&a.push(i.configure(this.options.listItem)),!1!==this.options.listKeymap&&a.push(t.configure(this.options.listKeymap)),!1!==this.options.orderedList&&a.push(w.configure(this.options.orderedList)),!1!==this.options.taskItem&&a.push(y.configure(this.options.taskItem)),!1!==this.options.taskList&&a.push(z.configure(this.options.taskList)),a}})},68174:(a,b,c)=>{c.d(b,{A:()=>d});var d=c(65636).ck},68324:(a,b,c)=>{c.d(b,{A:()=>d});var d=c(65636)._J},69169:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("heading-2",[["path",{d:"M4 12h8",key:"17cfdx"}],["path",{d:"M4 18V6",key:"1rz3zl"}],["path",{d:"M12 18V6",key:"zqpxq5"}],["path",{d:"M21 18h-4c0-4 4-3 4-6 0-1.5-2-2.5-4-1",key:"9jr5yi"}]])},75687:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("strikethrough",[["path",{d:"M16 4H9a3 3 0 0 0-2.83 4",key:"43sutm"}],["path",{d:"M14 12a4 4 0 0 1 0 8H6",key:"nlfj13"}],["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}]])},79801:(a,b,c)=>{c.d(b,{Ay:()=>f});var d=c(81373),e=/(?:^|\s)(!\[(.+|:?)]\((\S+)(?:(?:\s+)["'](\S+)["'])?\))$/,f=d.bP.create({name:"image",addOptions:()=>({inline:!1,allowBase64:!1,HTMLAttributes:{}}),inline(){return this.options.inline},group(){return this.options.inline?"inline":"block"},draggable:!0,addAttributes:()=>({src:{default:null},alt:{default:null},title:{default:null},width:{default:null},height:{default:null}}),parseHTML(){return[{tag:this.options.allowBase64?"img[src]":'img[src]:not([src^="data:"])'}]},renderHTML({HTMLAttributes:a}){return["img",(0,d.KV)(this.options.HTMLAttributes,a)]},addCommands(){return{setImage:a=>({commands:b})=>b.insertContent({type:this.name,attrs:a})}},addInputRules(){return[(0,d.jT)({find:e,type:this.type,getAttributes:a=>{let[,,b,c,d]=a;return{src:c,alt:b,title:d}}})]}})},80375:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("code",[["path",{d:"m16 18 6-6-6-6",key:"eg8j8"}],["path",{d:"m8 6-6 6 6 6",key:"ppft3o"}]])},81373:(a,b,c)=>{c.d(b,{KE:()=>b2,YY:()=>aP,CU:()=>aL,Db:()=>b9,bP:()=>ca,Yv:()=>cb,gk:()=>Y,AB:()=>b7,T7:()=>N,Nx:()=>S,gu:()=>al,FF:()=>am,iI:()=>U,hO:()=>as,fl:()=>at,Pg:()=>aj,zU:()=>aa,QN:()=>ay,J_:()=>az,rU:()=>ax,Op:()=>aC,BQ:()=>aD,OX:()=>b3,Zc:()=>cc,KV:()=>_,jT:()=>b4,JJ:()=>b5,tG:()=>b6});var d=c(35857),e=c(98031),f=c(31224),g=c(41662),h=c(81553);let i=(a,b)=>!a.selection.empty&&(b&&b(a.tr.deleteSelection().scrollIntoView()),!0);function j(a,b){let{$cursor:c}=a.selection;return c&&(b?b.endOfTextblock("backward",a):!(c.parentOffset>0))?c:null}let k=(a,b,c)=>{let e=j(a,c);if(!e)return!1;let f=o(e);if(!f){let c=e.blockRange(),d=c&&(0,g.jP)(c);return null!=d&&(b&&b(a.tr.lift(c,d).scrollIntoView()),!0)}let i=f.nodeBefore;if(y(a,f,b,-1))return!0;if(0==e.parent.content.size&&(m(i,"end")||d.nh.isSelectable(i)))for(let c=e.depth;;c--){let j=(0,g.$L)(a.doc,e.before(c),e.after(c),h.Ji.empty);if(j&&j.slice.size<j.to-j.from){if(b){let c=a.tr.step(j);c.setSelection(m(i,"end")?d.LN.findFrom(c.doc.resolve(c.mapping.map(f.pos,-1)),-1):d.nh.create(c.doc,f.pos-i.nodeSize)),b(c.scrollIntoView())}return!0}if(1==c||e.node(c-1).childCount>1)break}return!!i.isAtom&&f.depth==e.depth-1&&(b&&b(a.tr.delete(f.pos-i.nodeSize,f.pos).scrollIntoView()),!0)};function l(a,b,c){let e=b.nodeBefore,f=b.pos-1;for(;!e.isTextblock;f--){if(e.type.spec.isolating)return!1;let a=e.lastChild;if(!a)return!1;e=a}let i=b.nodeAfter,j=b.pos+1;for(;!i.isTextblock;j++){if(i.type.spec.isolating)return!1;let a=i.firstChild;if(!a)return!1;i=a}let k=(0,g.$L)(a.doc,f,j,h.Ji.empty);if(!k||k.from!=f||k instanceof g.Ln&&k.slice.size>=j-f)return!1;if(c){let b=a.tr.step(k);b.setSelection(d.U3.create(b.doc,f)),c(b.scrollIntoView())}return!0}function m(a,b,c=!1){for(let d=a;d;d="start"==b?d.firstChild:d.lastChild){if(d.isTextblock)return!0;if(c&&1!=d.childCount)break}return!1}let n=(a,b,c)=>{let{$head:e,empty:f}=a.selection,g=e;if(!f)return!1;if(e.parent.isTextblock){if(c?!c.endOfTextblock("backward",a):e.parentOffset>0)return!1;g=o(e)}let h=g&&g.nodeBefore;return!!h&&!!d.nh.isSelectable(h)&&(b&&b(a.tr.setSelection(d.nh.create(a.doc,g.pos-h.nodeSize)).scrollIntoView()),!0)};function o(a){if(!a.parent.type.spec.isolating)for(let b=a.depth-1;b>=0;b--){if(a.index(b)>0)return a.doc.resolve(a.before(b+1));if(a.node(b).type.spec.isolating)break}return null}function p(a,b){let{$cursor:c}=a.selection;return c&&(b?b.endOfTextblock("forward",a):!(c.parentOffset<c.parent.content.size))?c:null}let q=(a,b,c)=>{let e=p(a,c);if(!e)return!1;let f=s(e);if(!f)return!1;let i=f.nodeAfter;if(y(a,f,b,1))return!0;if(0==e.parent.content.size&&(m(i,"start")||d.nh.isSelectable(i))){let c=(0,g.$L)(a.doc,e.before(),e.after(),h.Ji.empty);if(c&&c.slice.size<c.to-c.from){if(b){let e=a.tr.step(c);e.setSelection(m(i,"start")?d.LN.findFrom(e.doc.resolve(e.mapping.map(f.pos)),1):d.nh.create(e.doc,e.mapping.map(f.pos))),b(e.scrollIntoView())}return!0}}return!!i.isAtom&&f.depth==e.depth-1&&(b&&b(a.tr.delete(f.pos,f.pos+i.nodeSize).scrollIntoView()),!0)},r=(a,b,c)=>{let{$head:e,empty:f}=a.selection,g=e;if(!f)return!1;if(e.parent.isTextblock){if(c?!c.endOfTextblock("forward",a):e.parentOffset<e.parent.content.size)return!1;g=s(e)}let h=g&&g.nodeAfter;return!!h&&!!d.nh.isSelectable(h)&&(b&&b(a.tr.setSelection(d.nh.create(a.doc,g.pos)).scrollIntoView()),!0)};function s(a){if(!a.parent.type.spec.isolating)for(let b=a.depth-1;b>=0;b--){let c=a.node(b);if(a.index(b)+1<c.childCount)return a.doc.resolve(a.after(b+1));if(c.type.spec.isolating)break}return null}let t=(a,b)=>{let{$head:c,$anchor:d}=a.selection;return!!c.parent.type.spec.code&&!!c.sameParent(d)&&(b&&b(a.tr.insertText("\n").scrollIntoView()),!0)};function u(a){for(let b=0;b<a.edgeCount;b++){let{type:c}=a.edge(b);if(c.isTextblock&&!c.hasRequiredAttrs())return c}return null}let v=(a,b)=>{let{$head:c,$anchor:e}=a.selection;if(!c.parent.type.spec.code||!c.sameParent(e))return!1;let f=c.node(-1),g=c.indexAfter(-1),h=u(f.contentMatchAt(g));if(!h||!f.canReplaceWith(g,g,h))return!1;if(b){let e=c.after(),f=a.tr.replaceWith(e,e,h.createAndFill());f.setSelection(d.LN.near(f.doc.resolve(e),1)),b(f.scrollIntoView())}return!0},w=(a,b)=>{let c=a.selection,{$from:e,$to:f}=c;if(c instanceof d.i5||e.parent.inlineContent||f.parent.inlineContent)return!1;let g=u(f.parent.contentMatchAt(f.indexAfter()));if(!g||!g.isTextblock)return!1;if(b){let c=(!e.parentOffset&&f.index()<f.parent.childCount?e:f).pos,h=a.tr.insert(c,g.createAndFill());h.setSelection(d.U3.create(h.doc,c+1)),b(h.scrollIntoView())}return!0},x=(a,b)=>{let{$cursor:c}=a.selection;if(!c||c.parent.content.size)return!1;if(c.depth>1&&c.after()!=c.end(-1)){let d=c.before();if((0,g.zy)(a.doc,d))return b&&b(a.tr.split(d).scrollIntoView()),!0}let d=c.blockRange(),e=d&&(0,g.jP)(d);return null!=e&&(b&&b(a.tr.lift(d,e).scrollIntoView()),!0)};function y(a,b,c,e){let f,i,j,k=b.nodeBefore,l=b.nodeAfter,n,o,p=k.type.spec.isolating||l.type.spec.isolating;if(!p&&(f=b.nodeBefore,i=b.nodeAfter,j=b.index(),f&&i&&f.type.compatibleContent(i.type)&&(!f.content.size&&b.parent.canReplace(j-1,j)?(c&&c(a.tr.delete(b.pos-f.nodeSize,b.pos).scrollIntoView()),!0):!!b.parent.canReplace(j,j+1)&&!!(i.isTextblock||(0,g.n9)(a.doc,b.pos))&&(c&&c(a.tr.join(b.pos).scrollIntoView()),!0))))return!0;let q=!p&&b.parent.canReplace(b.index(),b.index()+1);if(q&&(n=(o=k.contentMatchAt(k.childCount)).findWrapping(l.type))&&o.matchType(n[0]||l.type).validEnd){if(c){let d=b.pos+l.nodeSize,e=h.FK.empty;for(let a=n.length-1;a>=0;a--)e=h.FK.from(n[a].create(null,e));e=h.FK.from(k.copy(e));let f=a.tr.step(new g.Wg(b.pos-1,d,b.pos,d,new h.Ji(e,1,0),n.length,!0)),i=f.doc.resolve(d+2*n.length);i.nodeAfter&&i.nodeAfter.type==k.type&&(0,g.n9)(f.doc,i.pos)&&f.join(i.pos),c(f.scrollIntoView())}return!0}let r=l.type.spec.isolating||e>0&&p?null:d.LN.findFrom(b,1),s=r&&r.$from.blockRange(r.$to),t=s&&(0,g.jP)(s);if(null!=t&&t>=b.depth)return c&&c(a.tr.lift(s,t).scrollIntoView()),!0;if(q&&m(l,"start",!0)&&m(k,"end")){let d=k,e=[];for(;e.push(d),!d.isTextblock;)d=d.lastChild;let f=l,i=1;for(;!f.isTextblock;f=f.firstChild)i++;if(d.canReplace(d.childCount,d.childCount,f.content)){if(c){let d=h.FK.empty;for(let a=e.length-1;a>=0;a--)d=h.FK.from(e[a].copy(d));c(a.tr.step(new g.Wg(b.pos-e.length,b.pos+l.nodeSize,b.pos+i,b.pos+l.nodeSize-i,new h.Ji(d,e.length,0),0,!0)).scrollIntoView())}return!0}}return!1}function z(a){return function(b,c){let e=b.selection,f=a<0?e.$from:e.$to,g=f.depth;for(;f.node(g).isInline;){if(!g)return!1;g--}return!!f.node(g).isTextblock&&(c&&c(b.tr.setSelection(d.U3.create(b.doc,a<0?f.start(g):f.end(g)))),!0)}}let A=z(-1),B=z(1);function C(a,b=null){return function(c,d){let e=!1;for(let d=0;d<c.selection.ranges.length&&!e;d++){let{$from:{pos:f},$to:{pos:g}}=c.selection.ranges[d];c.doc.nodesBetween(f,g,(d,f)=>{if(e)return!1;if(!(!d.isTextblock||d.hasMarkup(a,b)))if(d.type==a)e=!0;else{let b=c.doc.resolve(f),d=b.index();e=b.parent.canReplaceWith(d,d+1,a)}})}if(!e)return!1;if(d){let e=c.tr;for(let d=0;d<c.selection.ranges.length;d++){let{$from:{pos:f},$to:{pos:g}}=c.selection.ranges[d];e.setBlockType(f,g,a,b)}d(e.scrollIntoView())}return!0}}function D(...a){return function(b,c,d){for(let e=0;e<a.length;e++)if(a[e](b,c,d))return!0;return!1}}let E=D(i,k,n),F=D(i,q,r),G={Enter:D(t,w,x,(a,b)=>{let{$from:c,$to:e}=a.selection;if(a.selection instanceof d.nh&&a.selection.node.isBlock)return!!c.parentOffset&&!!(0,g.zy)(a.doc,c.pos)&&(b&&b(a.tr.split(c.pos).scrollIntoView()),!0);if(!c.depth)return!1;let f=[],h,i,j=!1,k=!1;for(let a=c.depth;;a--){if(c.node(a).isBlock){let b;j=c.end(a)==c.pos+(c.depth-a),k=c.start(a)==c.pos-(c.depth-a),i=u(c.node(a-1).contentMatchAt(c.indexAfter(a-1)));f.unshift(b||(j&&i?{type:i}:null)),h=a;break}if(1==a)return!1;f.unshift(null)}let l=a.tr;(a.selection instanceof d.U3||a.selection instanceof d.i5)&&l.deleteSelection();let m=l.mapping.map(c.pos),n=(0,g.zy)(l.doc,m,f.length,f);if(n||(f[0]=i?{type:i}:null,n=(0,g.zy)(l.doc,m,f.length,f)),!n)return!1;if(l.split(m,f.length,f),!j&&k&&c.node(h).type!=i){let a=l.mapping.map(c.before(h)),b=l.doc.resolve(a);i&&c.node(h-1).canReplaceWith(b.index(),b.index()+1,i)&&l.setNodeMarkup(l.mapping.map(c.before(h)),i)}return b&&b(l.scrollIntoView()),!0}),"Mod-Enter":v,Backspace:E,"Mod-Backspace":E,"Shift-Backspace":E,Delete:F,"Mod-Delete":F,"Mod-a":(a,b)=>(b&&b(a.tr.setSelection(new d.i5(a.doc))),!0)},H={"Ctrl-h":G.Backspace,"Alt-Backspace":G["Mod-Backspace"],"Ctrl-d":G.Delete,"Ctrl-Alt-Backspace":G["Mod-Delete"],"Alt-Delete":G["Mod-Delete"],"Alt-d":G["Mod-Delete"],"Ctrl-a":A,"Ctrl-e":B};for(let a in G)H[a]=G[a];"undefined"!=typeof navigator?/Mac|iP(hone|[oa]d)/.test(navigator.platform):"undefined"!=typeof os&&os.platform&&os.platform();var I=Object.defineProperty,J=(a,b)=>{for(var c in b)I(a,c,{get:b[c],enumerable:!0})};function K(a){let{state:b,transaction:c}=a,{selection:d}=c,{doc:e}=c,{storedMarks:f}=c;return{...b,apply:b.apply.bind(b),applyTransaction:b.applyTransaction.bind(b),plugins:b.plugins,schema:b.schema,reconfigure:b.reconfigure.bind(b),toJSON:b.toJSON.bind(b),get storedMarks(){return f},get selection(){return d},get doc(){return e},get tr(){return d=c.selection,e=c.doc,f=c.storedMarks,c}}}var L=class{constructor(a){this.editor=a.editor,this.rawCommands=this.editor.extensionManager.commands,this.customState=a.state}get hasCustomState(){return!!this.customState}get state(){return this.customState||this.editor.state}get commands(){let{rawCommands:a,editor:b,state:c}=this,{view:d}=b,{tr:e}=c,f=this.buildProps(e);return Object.fromEntries(Object.entries(a).map(([a,b])=>[a,(...a)=>{let c=b(...a)(f);return e.getMeta("preventDispatch")||this.hasCustomState||d.dispatch(e),c}]))}get chain(){return()=>this.createChain()}get can(){return()=>this.createCan()}createChain(a,b=!0){let{rawCommands:c,editor:d,state:e}=this,{view:f}=d,g=[],h=!!a,i=a||e.tr,j={...Object.fromEntries(Object.entries(c).map(([a,c])=>[a,(...a)=>{let d=this.buildProps(i,b),e=c(...a)(d);return g.push(e),j}])),run:()=>(h||!b||i.getMeta("preventDispatch")||this.hasCustomState||f.dispatch(i),g.every(a=>!0===a))};return j}createCan(a){let{rawCommands:b,state:c}=this,d=a||c.tr,e=this.buildProps(d,!1);return{...Object.fromEntries(Object.entries(b).map(([a,b])=>[a,(...a)=>b(...a)({...e,dispatch:void 0})])),chain:()=>this.createChain(d,!1)}}buildProps(a,b=!0){let{rawCommands:c,editor:d,state:e}=this,{view:f}=d,g={tr:a,editor:d,view:f,state:K({state:e,transaction:a}),dispatch:b?()=>void 0:void 0,chain:()=>this.createChain(a,b),can:()=>this.createCan(a),get commands(){return Object.fromEntries(Object.entries(c).map(([a,b])=>[a,(...a)=>b(...a)(g)]))}};return g}},M=class{constructor(){this.callbacks={}}on(a,b){return this.callbacks[a]||(this.callbacks[a]=[]),this.callbacks[a].push(b),this}emit(a,...b){let c=this.callbacks[a];return c&&c.forEach(a=>a.apply(this,b)),this}off(a,b){let c=this.callbacks[a];return c&&(b?this.callbacks[a]=c.filter(a=>a!==b):delete this.callbacks[a]),this}once(a,b){let c=(...d)=>{this.off(a,c),b.apply(this,d)};return this.on(a,c)}removeAllListeners(){this.callbacks={}}};function N(a,b){let c=new g.dL(a);return b.forEach(a=>{a.steps.forEach(a=>{c.step(a)})}),c}var O=a=>{let b=a.childNodes;for(let c=b.length-1;c>=0;c-=1){let d=b[c];3===d.nodeType&&d.nodeValue&&/^(\n\s\s|\n)$/.test(d.nodeValue)?a.removeChild(d):1===d.nodeType&&O(d)}return a};function P(a){if("undefined"==typeof window)throw Error("[tiptap error]: there is no window object available, so this function cannot be used");let b=`<body>${a}</body>`;return O(new window.DOMParser().parseFromString(b,"text/html").body)}function Q(a,b,c){if(a instanceof h.bP||a instanceof h.FK)return a;c={slice:!0,parseOptions:{},...c};let d="object"==typeof a&&null!==a,e="string"==typeof a;if(d)try{if(Array.isArray(a)&&a.length>0)return h.FK.fromArray(a.map(a=>b.nodeFromJSON(a)));let d=b.nodeFromJSON(a);return c.errorOnInvalidContent&&d.check(),d}catch(d){if(c.errorOnInvalidContent)throw Error("[tiptap error]: Invalid JSON content",{cause:d});return console.warn("[tiptap warn]: Invalid content.","Passed value:",a,"Error:",d),Q("",b,c)}if(e){if(c.errorOnInvalidContent){let d=!1,e="",f=new h.Sj({topNode:b.spec.topNode,marks:b.spec.marks,nodes:b.spec.nodes.append({__tiptap__private__unknown__catch__all__node:{content:"inline*",group:"block",parseDOM:[{tag:"*",getAttrs:a=>(d=!0,e="string"==typeof a?a:a.outerHTML,null)}]}})});if(c.slice?h.S4.fromSchema(f).parseSlice(P(a),c.parseOptions):h.S4.fromSchema(f).parse(P(a),c.parseOptions),c.errorOnInvalidContent&&d)throw Error("[tiptap error]: Invalid HTML content",{cause:Error(`Invalid element found: ${e}`)})}let d=h.S4.fromSchema(b);return c.slice?d.parseSlice(P(a),c.parseOptions).content:d.parse(P(a),c.parseOptions)}return Q("",b,c)}function R(a,b,c={},d={}){return Q(a,b,{slice:!1,parseOptions:c,errorOnInvalidContent:d.errorOnInvalidContent})}function S(a,b,c){let d=[];return a.nodesBetween(b.from,b.to,(a,b)=>{c(a)&&d.push({node:a,pos:b})}),d}function T(a){return b=>(function(a,b){for(let c=a.depth;c>0;c-=1){let d=a.node(c);if(b(d))return{pos:c>0?a.before(c):0,start:a.start(c),depth:c,node:d}}})(b.$from,a)}function U(a,b,c){return void 0===a.config[b]&&a.parent?U(a.parent,b,c):"function"==typeof a.config[b]?a.config[b].bind({...c,parent:a.parent?U(a.parent,b,c):null}):a.config[b]}function V(a){return a.map(a=>{let b={name:a.name,options:a.options,storage:a.storage},c=U(a,"addExtensions",b);return c?[a,...V(c())]:a}).flat(10)}function W(a,b){let c=h.ZF.fromSchema(b).serializeFragment(a),d=document.implementation.createHTMLDocument().createElement("div");return d.appendChild(c),d.innerHTML}function X(a){return"function"==typeof a}function Y(a,b,...c){return X(a)?b?a.bind(b)(...c):a(...c):a}function Z(a){let b=a.filter(a=>"extension"===a.type);return{baseExtensions:b,nodeExtensions:a.filter(a=>"node"===a.type),markExtensions:a.filter(a=>"mark"===a.type)}}function $(a){let b=[],{nodeExtensions:c,markExtensions:d}=Z(a),e=[...c,...d],f={default:null,validate:void 0,rendered:!0,renderHTML:null,parseHTML:null,keepOnSplit:!0,isRequired:!1};return a.forEach(a=>{let c={name:a.name,options:a.options,storage:a.storage,extensions:e},d=U(a,"addGlobalAttributes",c);d&&d().forEach(a=>{a.types.forEach(c=>{Object.entries(a.attributes).forEach(([a,d])=>{b.push({type:c,name:a,attribute:{...f,...d}})})})})}),e.forEach(a=>{let c={name:a.name,options:a.options,storage:a.storage},d=U(a,"addAttributes",c);d&&Object.entries(d()).forEach(([c,d])=>{let e={...f,...d};"function"==typeof(null==e?void 0:e.default)&&(e.default=e.default()),(null==e?void 0:e.isRequired)&&(null==e?void 0:e.default)===void 0&&delete e.default,b.push({type:a.name,name:c,attribute:e})})}),b}function _(...a){return a.filter(a=>!!a).reduce((a,b)=>{let c={...a};return Object.entries(b).forEach(([a,b])=>{if(!c[a]){c[a]=b;return}if("class"===a){let d=b?String(b).split(" "):[],e=c[a]?c[a].split(" "):[],f=d.filter(a=>!e.includes(a));c[a]=[...e,...f].join(" ")}else if("style"===a){let d=b?b.split(";").map(a=>a.trim()).filter(Boolean):[],e=c[a]?c[a].split(";").map(a=>a.trim()).filter(Boolean):[],f=new Map;e.forEach(a=>{let[b,c]=a.split(":").map(a=>a.trim());f.set(b,c)}),d.forEach(a=>{let[b,c]=a.split(":").map(a=>a.trim());f.set(b,c)}),c[a]=Array.from(f.entries()).map(([a,b])=>`${a}: ${b}`).join("; ")}else c[a]=b}),c},{})}function aa(a,b){return b.filter(b=>b.type===a.type.name).filter(a=>a.attribute.rendered).map(b=>b.attribute.renderHTML?b.attribute.renderHTML(a.attrs)||{}:{[b.name]:a.attrs[b.name]}).reduce((a,b)=>_(a,b),{})}function ab(a,b){return"style"in a?a:{...a,getAttrs:c=>{let d=a.getAttrs?a.getAttrs(c):a.attrs;if(!1===d)return!1;let e=b.reduce((a,b)=>{var d;let e=b.attribute.parseHTML?b.attribute.parseHTML(c):"string"!=typeof(d=c.getAttribute(b.name))?d:d.match(/^[+-]?(?:\d*\.)?\d+$/)?Number(d):"true"===d||"false"!==d&&d;return null==e?a:{...a,[b.name]:e}},{});return{...d,...e}}}}function ac(a){return Object.fromEntries(Object.entries(a).filter(([a,b])=>!("attrs"===a&&function(a={}){return 0===Object.keys(a).length&&a.constructor===Object}(b))&&null!=b))}function ad(a){return a.sort((a,b)=>{let c=U(a,"priority")||100,d=U(b,"priority")||100;return c>d?-1:+(c<d)})}function ae(a){var b;let c=ad(V(a)),d=Array.from(new Set((b=c.map(a=>a.name)).filter((a,c)=>b.indexOf(a)!==c)));return d.length&&console.warn(`[tiptap warn]: Duplicate extension names found: [${d.map(a=>`'${a}'`).join(", ")}]. This can lead to issues.`),c}function af(a,b,c){let{from:d,to:e}=b,{blockSeparator:f="\n\n",textSerializers:g={}}=c||{},h="";return a.nodesBetween(d,e,(a,c,i,j)=>{var k;a.isBlock&&c>d&&(h+=f);let l=null==g?void 0:g[a.type.name];if(l)return i&&(h+=l({node:a,pos:c,parent:i,index:j,range:b})),!1;a.isText&&(h+=null==(k=null==a?void 0:a.text)?void 0:k.slice(Math.max(d,c)-c,e-c))}),h}function ag(a){return Object.fromEntries(Object.entries(a.nodes).filter(([,a])=>a.spec.toText).map(([a,b])=>[a,b.spec.toText]))}function ah(a,b){if("string"==typeof a){if(!b.marks[a])throw Error(`There is no mark type named '${a}'. Maybe you forgot to add the extension?`);return b.marks[a]}return a}function ai(a,b){let c=ah(b,a.schema),{from:d,to:e,empty:f}=a.selection,g=[];f?(a.storedMarks&&g.push(...a.storedMarks),g.push(...a.selection.$head.marks())):a.doc.nodesBetween(d,e,a=>{g.push(...a.marks)});let h=g.find(a=>a.type.name===c.name);return h?{...h.attrs}:{}}function aj(a,b){if("string"==typeof a){if(!b.nodes[a])throw Error(`There is no node type named '${a}'. Maybe you forgot to add the extension?`);return b.nodes[a]}return a}function ak(a,b){return b.nodes[a]?"node":b.marks[a]?"mark":null}function al(a,b){let c=ak("string"==typeof b?b:b.name,a.schema);if("node"===c){let c=aj(b,a.schema),{from:d,to:e}=a.selection,f=[];a.doc.nodesBetween(d,e,a=>{f.push(a)});let g=f.reverse().find(a=>a.type.name===c.name);return g?{...g.attrs}:{}}return"mark"===c?ai(a,b):{}}function am(a){let{mapping:b,steps:c}=a,d=[];b.maps.forEach((a,e)=>{let f=[];if(a.ranges.length)a.forEach((a,b)=>{f.push({from:a,to:b})});else{let{from:a,to:b}=c[e];if(void 0===a||void 0===b)return;f.push({from:a,to:b})}f.forEach(({from:a,to:c})=>{let f=b.slice(e).map(a,-1),g=b.slice(e).map(c),h=b.invert().map(f,-1),i=b.invert().map(g);d.push({oldRange:{from:h,to:i},newRange:{from:f,to:g}})})});let e=function(a,b=JSON.stringify){let c={};return a.filter(a=>{let d=b(a);return!Object.prototype.hasOwnProperty.call(c,d)&&(c[d]=!0)})}(d);return 1===e.length?e:e.filter((a,b)=>!e.filter((a,c)=>c!==b).some(b=>a.oldRange.from>=b.oldRange.from&&a.oldRange.to<=b.oldRange.to&&a.newRange.from>=b.newRange.from&&a.newRange.to<=b.newRange.to))}function an(a){return"[object RegExp]"===Object.prototype.toString.call(a)}function ao(a,b,c={strict:!0}){let d=Object.keys(b);return!d.length||d.every(d=>c.strict?b[d]===a[d]:an(b[d])?b[d].test(a[d]):b[d]===a[d])}function ap(a,b,c={}){return a.find(a=>a.type===b&&ao(Object.fromEntries(Object.keys(c).map(b=>[b,a.attrs[b]])),c))}function aq(a,b,c={}){return!!ap(a,b,c)}function ar(a,b,c){var d;if(!a||!b)return;let e=a.parent.childAfter(a.parentOffset);if(e.node&&e.node.marks.some(a=>a.type===b)||(e=a.parent.childBefore(a.parentOffset)),!e.node||!e.node.marks.some(a=>a.type===b)||(c=c||(null==(d=e.node.marks[0])?void 0:d.attrs),!ap([...e.node.marks],b,c)))return;let f=e.index,g=a.start()+e.offset,h=f+1,i=g+e.node.nodeSize;for(;f>0&&aq([...a.parent.child(f-1).marks],b,c);)f-=1,g-=a.parent.child(f).nodeSize;for(;h<a.parent.childCount&&aq([...a.parent.child(h).marks],b,c);)i+=a.parent.child(h).nodeSize,h+=1;return{from:g,to:i}}function as(a,b,c){let d=[];return a===b?c.resolve(a).marks().forEach(b=>{let e=ar(c.resolve(a),b.type);e&&d.push({mark:b,...e})}):c.nodesBetween(a,b,(a,b)=>{a&&(null==a?void 0:a.nodeSize)!==void 0&&d.push(...a.marks.map(c=>({from:b,to:b+a.nodeSize,mark:c})))}),d}var at=(a,b,c,d=20)=>{let e=a.doc.resolve(c),f=d,g=null;for(;f>0&&null===g;){let a=e.node(f);(null==a?void 0:a.type.name)===b?g=a:f-=1}return[g,f]};function au(a,b){return b.nodes[a]||b.marks[a]||null}function av(a,b,c){return Object.fromEntries(Object.entries(c).filter(([c])=>{let d=a.find(a=>a.type===b&&a.name===c);return!!d&&d.attribute.keepOnSplit}))}function aw(a,b,c={}){let{empty:d,ranges:e}=a.selection,f=b?ah(b,a.schema):null;if(d)return!!(a.storedMarks||a.selection.$from.marks()).filter(a=>!f||f.name===a.type.name).find(a=>ao(a.attrs,c,{strict:!1}));let g=0,h=[];if(e.forEach(({$from:b,$to:c})=>{let d=b.pos,e=c.pos;a.doc.nodesBetween(d,e,(a,b)=>{if(!a.isText&&!a.marks.length)return;let c=Math.max(d,b),f=Math.min(e,b+a.nodeSize);g+=f-c,h.push(...a.marks.map(a=>({mark:a,from:c,to:f})))})}),0===g)return!1;let i=h.filter(a=>!f||f.name===a.mark.type.name).filter(a=>ao(a.mark.attrs,c,{strict:!1})).reduce((a,b)=>a+b.to-b.from,0),j=h.filter(a=>!f||a.mark.type!==f&&a.mark.type.excludes(f)).reduce((a,b)=>a+b.to-b.from,0);return(i>0?i+j:i)>=g}function ax(a,b,c={}){let{from:d,to:e,empty:f}=a.selection,g=b?aj(b,a.schema):null,h=[];a.doc.nodesBetween(d,e,(a,b)=>{if(a.isText)return;let c=Math.max(d,b),f=Math.min(e,b+a.nodeSize);h.push({node:a,from:c,to:f})});let i=e-d,j=h.filter(a=>!g||g.name===a.node.type.name).filter(a=>ao(a.node.attrs,c,{strict:!1}));return f?!!j.length:j.reduce((a,b)=>a+b.to-b.from,0)>=i}var ay=(a,b)=>{let{$from:c,$to:d,$anchor:e}=a.selection;if(b){let c=T(a=>a.type.name===b)(a.selection);if(!c)return!1;let d=a.doc.resolve(c.pos+1);return e.pos+1===d.end()}return!(d.parentOffset<d.parent.nodeSize-2)&&c.pos===d.pos},az=a=>{let{$from:b,$to:c}=a.selection;return!(b.parentOffset>0)&&b.pos===c.pos};function aA(a,b){return Array.isArray(b)?b.some(b=>("string"==typeof b?b:b.name)===a.name):b}function aB(a,b){let{nodeExtensions:c}=Z(b),d=c.find(b=>b.name===a);if(!d)return!1;let e={name:d.name,options:d.options,storage:d.storage},f=Y(U(d,"group",e));return"string"==typeof f&&f.split(" ").includes("list")}function aC(a,{checkChildren:b=!0,ignoreWhitespace:c=!1}={}){var d;if(c){if("hardBreak"===a.type.name)return!0;if(a.isText)return/^\s*$/m.test(null!=(d=a.text)?d:"")}if(a.isText)return!a.text;if(a.isAtom||a.isLeaf)return!1;if(0===a.content.childCount)return!0;if(b){let d=!0;return a.content.forEach(a=>{!1!==d&&(aC(a,{ignoreWhitespace:c,checkChildren:b})||(d=!1))}),d}return!1}function aD(a){return a instanceof d.nh}function aE(a){return a instanceof d.U3}function aF(a=0,b=0,c=0){return Math.min(Math.max(a,b),c)}function aG(a,b=null){if(!b)return null;let c=d.LN.atStart(a),e=d.LN.atEnd(a);if("start"===b||!0===b)return c;if("end"===b)return e;let f=c.from,g=e.to;return"all"===b?d.U3.create(a,aF(0,f,g),aF(a.content.size,f,g)):d.U3.create(a,aF(b,f,g),aF(b,f,g))}var aH=class{constructor(a){this.find=a.find,this.handler=a.handler}};function aI(a){var b;let{editor:c,from:d,to:e,text:f,rules:g,plugin:h}=a,{view:i}=c;if(i.composing)return!1;let j=i.state.doc.resolve(d);if(j.parent.type.spec.code||(null==(b=j.nodeBefore||j.nodeAfter)?void 0:b.marks.find(a=>a.type.spec.code)))return!1;let k=!1,l=((a,b=500)=>{let c="",d=a.parentOffset;return a.parent.nodesBetween(Math.max(0,d-b),d,(a,b,e,f)=>{var g,h;let i=(null==(h=(g=a.type.spec).toText)?void 0:h.call(g,{node:a,pos:b,parent:e,index:f}))||a.textContent||"%leaf%";c+=a.isAtom&&!a.isText?i:i.slice(0,Math.max(0,d-b))}),c})(j)+f;return g.forEach(a=>{if(k)return;let b=((a,b)=>{if(an(b))return b.exec(a);let c=b(a);if(!c)return null;let d=[c.text];return d.index=c.index,d.input=a,d.data=c.data,c.replaceWith&&(c.text.includes(c.replaceWith)||console.warn('[tiptap warn]: "inputRuleMatch.replaceWith" must be part of "inputRuleMatch.text".'),d.push(c.replaceWith)),d})(l,a.find);if(!b)return;let g=i.state.tr,j=K({state:i.state,transaction:g}),m={from:d-(b[0].length-f.length),to:e},{commands:n,chain:o,can:p}=new L({editor:c,state:j});null!==a.handler({state:j,range:m,match:b,commands:n,chain:o,can:p})&&g.steps.length&&(g.setMeta(h,{transform:g,from:d,to:e,text:f}),i.dispatch(g),k=!0)}),k}function aJ(a){return"Object"===Object.prototype.toString.call(a).slice(8,-1)&&a.constructor===Object&&Object.getPrototypeOf(a)===Object.prototype}var aK=class{constructor(a={}){this.type="extendable",this.parent=null,this.child=null,this.name="",this.config={name:this.name},this.config={...this.config,...a},this.name=this.config.name}get options(){return{...Y(U(this,"addOptions",{name:this.name}))||{}}}get storage(){return{...Y(U(this,"addStorage",{name:this.name,options:this.options}))||{}}}configure(a={}){let b=this.extend({...this.config,addOptions:()=>(function a(b,c){let d={...b};return aJ(b)&&aJ(c)&&Object.keys(c).forEach(e=>{aJ(c[e])&&aJ(b[e])?d[e]=a(b[e],c[e]):d[e]=c[e]}),d})(this.options,a)});return b.name=this.name,b.parent=this.parent,b}extend(a={}){let b=new this.constructor({...this.config,...a});return b.parent=this,this.child=b,b.name="name"in a?a.name:b.parent.name,b}},aL=class a extends aK{constructor(){super(...arguments),this.type="mark"}static create(b={}){return new a("function"==typeof b?b():b)}static handleExit({editor:a,mark:b}){let{tr:c}=a.state,d=a.state.selection.$from;if(d.pos===d.end()){let e=d.marks();if(!e.find(a=>(null==a?void 0:a.type.name)===b.name))return!1;let f=e.find(a=>(null==a?void 0:a.type.name)===b.name);return f&&c.removeStoredMark(f),c.insertText(" ",d.pos),a.view.dispatch(c),!0}return!1}configure(a){return super.configure(a)}extend(a){let b="function"==typeof a?a():a;return super.extend(b)}},aM=class{constructor(a){this.find=a.find,this.handler=a.handler}},aN=null,aO=class{constructor(a,b){this.splittableMarks=[],this.editor=b,this.extensions=ae(a),this.schema=function(a,b){var c;let d=$(a),{nodeExtensions:e,markExtensions:f}=Z(a),g=null==(c=e.find(a=>U(a,"topNode")))?void 0:c.name,i=Object.fromEntries(e.map(c=>{let e=d.filter(a=>a.type===c.name),f={name:c.name,options:c.options,storage:c.storage,editor:b},g=ac({...a.reduce((a,b)=>{let d=U(b,"extendNodeSchema",f);return{...a,...d?d(c):{}}},{}),content:Y(U(c,"content",f)),marks:Y(U(c,"marks",f)),group:Y(U(c,"group",f)),inline:Y(U(c,"inline",f)),atom:Y(U(c,"atom",f)),selectable:Y(U(c,"selectable",f)),draggable:Y(U(c,"draggable",f)),code:Y(U(c,"code",f)),whitespace:Y(U(c,"whitespace",f)),linebreakReplacement:Y(U(c,"linebreakReplacement",f)),defining:Y(U(c,"defining",f)),isolating:Y(U(c,"isolating",f)),attrs:Object.fromEntries(e.map(a=>{var b,c;return[a.name,{default:null==(b=null==a?void 0:a.attribute)?void 0:b.default,validate:null==(c=null==a?void 0:a.attribute)?void 0:c.validate}]}))}),h=Y(U(c,"parseHTML",f));h&&(g.parseDOM=h.map(a=>ab(a,e)));let i=U(c,"renderHTML",f);i&&(g.toDOM=a=>i({node:a,HTMLAttributes:aa(a,e)}));let j=U(c,"renderText",f);return j&&(g.toText=j),[c.name,g]})),j=Object.fromEntries(f.map(c=>{let e=d.filter(a=>a.type===c.name),f={name:c.name,options:c.options,storage:c.storage,editor:b},g=ac({...a.reduce((a,b)=>{let d=U(b,"extendMarkSchema",f);return{...a,...d?d(c):{}}},{}),inclusive:Y(U(c,"inclusive",f)),excludes:Y(U(c,"excludes",f)),group:Y(U(c,"group",f)),spanning:Y(U(c,"spanning",f)),code:Y(U(c,"code",f)),attrs:Object.fromEntries(e.map(a=>{var b,c;return[a.name,{default:null==(b=null==a?void 0:a.attribute)?void 0:b.default,validate:null==(c=null==a?void 0:a.attribute)?void 0:c.validate}]}))}),h=Y(U(c,"parseHTML",f));h&&(g.parseDOM=h.map(a=>ab(a,e)));let i=U(c,"renderHTML",f);return i&&(g.toDOM=a=>i({mark:a,HTMLAttributes:aa(a,e)})),[c.name,g]}));return new h.Sj({topNode:g,nodes:i,marks:j})}(this.extensions,b),this.setupExtensions()}get commands(){return this.extensions.reduce((a,b)=>{let c={name:b.name,options:b.options,storage:this.editor.extensionStorage[b.name],editor:this.editor,type:au(b.name,this.schema)},d=U(b,"addCommands",c);return d?{...a,...d()}:a},{})}get plugins(){let{editor:a}=this,b=ad([...this.extensions].reverse()),c=[],e=[],g=b.map(b=>{let d={name:b.name,options:b.options,storage:this.editor.extensionStorage[b.name],editor:a,type:au(b.name,this.schema)},g=[],h=U(b,"addKeyboardShortcuts",d),i={};if("mark"===b.type&&U(b,"exitable",d)&&(i.ArrowRight=()=>aL.handleExit({editor:a,mark:b})),h){let b=Object.fromEntries(Object.entries(h()).map(([b,c])=>[b,()=>c({editor:a})]));i={...i,...b}}let j=(0,f.w)(i);g.push(j);let k=U(b,"addInputRules",d);aA(b,a.options.enableInputRules)&&k&&c.push(...k());let l=U(b,"addPasteRules",d);aA(b,a.options.enablePasteRules)&&l&&e.push(...l());let m=U(b,"addProseMirrorPlugins",d);if(m){let a=m();g.push(...a)}return g}).flat();return[function(a){let{editor:b,rules:c}=a,e=new d.k_({state:{init:()=>null,apply(a,d,f){let g=a.getMeta(e);if(g)return g;let i=a.getMeta("applyInputRules");return i&&setTimeout(()=>{let{text:a}=i;"string"==typeof a||(a=W(h.FK.from(a),f.schema));let{from:d}=i,g=d+a.length;aI({editor:b,from:d,to:g,text:a,rules:c,plugin:e})}),a.selectionSet||a.docChanged?null:d}},props:{handleTextInput:(a,d,f,g)=>aI({editor:b,from:d,to:f,text:g,rules:c,plugin:e}),handleDOMEvents:{compositionend:a=>(setTimeout(()=>{let{$cursor:d}=a.state.selection;d&&aI({editor:b,from:d.pos,to:d.pos,text:"",rules:c,plugin:e})}),!1)},handleKeyDown(a,d){if("Enter"!==d.key)return!1;let{$cursor:f}=a.state.selection;return!!f&&aI({editor:b,from:f.pos,to:f.pos,text:"\n",rules:c,plugin:e})}},isInputRules:!0});return e}({editor:a,rules:c}),...function(a){let b,{editor:c,rules:e}=a,f=null,g=!1,i=!1,j="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null;try{b="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{b=null}let k=({state:a,from:d,to:e,rule:f,pasteEvt:g})=>{let h=a.tr;if(function(a){let{editor:b,state:c,from:d,to:e,rule:f,pasteEvent:g,dropEvent:h}=a,{commands:i,chain:j,can:k}=new L({editor:b,state:c}),l=[];return c.doc.nodesBetween(d,e,(a,b)=>{if(!a.isTextblock||a.type.spec.code)return;let m=Math.max(d,b),n=Math.min(e,b+a.content.size);((a,b,c)=>{if(an(b))return[...a.matchAll(b)];let d=b(a,c);return d?d.map(b=>{let c=[b.text];return c.index=b.index,c.input=a,c.data=b.data,b.replaceWith&&(b.text.includes(b.replaceWith)||console.warn('[tiptap warn]: "pasteRuleMatch.replaceWith" must be part of "pasteRuleMatch.text".'),c.push(b.replaceWith)),c}):[]})(a.textBetween(m-b,n-b,void 0,"￼"),f.find,g).forEach(a=>{if(void 0===a.index)return;let b=m+a.index+1,d=b+a[0].length,e={from:c.tr.mapping.map(b),to:c.tr.mapping.map(d)},n=f.handler({state:c,range:e,match:a,commands:i,chain:j,can:k,pasteEvent:g,dropEvent:h});l.push(n)})}),l.every(a=>null!==a)}({editor:c,state:K({state:a,transaction:h}),from:Math.max(d-1,0),to:e.b-1,rule:f,pasteEvent:g,dropEvent:b})&&h.steps.length){try{b="undefined"!=typeof DragEvent?new DragEvent("drop"):null}catch{b=null}return j="undefined"!=typeof ClipboardEvent?new ClipboardEvent("paste"):null,h}};return e.map(a=>new d.k_({view(a){let b=b=>{var d;(f=(null==(d=a.dom.parentElement)?void 0:d.contains(b.target))?a.dom.parentElement:null)&&(aN=c)},d=()=>{aN&&(aN=null)};return window.addEventListener("dragstart",b),window.addEventListener("dragend",d),{destroy(){window.removeEventListener("dragstart",b),window.removeEventListener("dragend",d)}}},props:{handleDOMEvents:{drop:(a,c)=>{if(i=f===a.dom.parentElement,b=c,!i){let a=aN;(null==a?void 0:a.isEditable)&&setTimeout(()=>{let b=a.state.selection;b&&a.commands.deleteRange({from:b.from,to:b.to})},10)}return!1},paste:(a,b)=>{var c;let d=null==(c=b.clipboardData)?void 0:c.getData("text/html");return j=b,g=!!(null==d?void 0:d.includes("data-pm-slice")),!1}}},appendTransaction:(b,c,d)=>{let e=b[0],f="paste"===e.getMeta("uiEvent")&&!g,l="drop"===e.getMeta("uiEvent")&&!i,m=e.getMeta("applyPasteRules"),n=!!m;if(!f&&!l&&!n)return;if(n){let{text:b}=m;"string"==typeof b||(b=W(h.FK.from(b),d.schema));let{from:c}=m,e=c+b.length;return k({rule:a,state:d,from:c,to:{b:e},pasteEvt:(a=>{var b;let c=new ClipboardEvent("paste",{clipboardData:new DataTransfer});return null==(b=c.clipboardData)||b.setData("text/html",a),c})(b)})}let o=c.doc.content.findDiffStart(d.doc.content),p=c.doc.content.findDiffEnd(d.doc.content);if("number"==typeof o&&p&&o!==p.b)return k({rule:a,state:d,from:o,to:p,pasteEvt:j})}}))}({editor:a,rules:e}),...g]}get attributes(){return $(this.extensions)}get nodeViews(){let{editor:a}=this,{nodeExtensions:b}=Z(this.extensions);return Object.fromEntries(b.filter(a=>!!U(a,"addNodeView")).map(b=>{let c=this.attributes.filter(a=>a.type===b.name),d={name:b.name,options:b.options,storage:this.editor.extensionStorage[b.name],editor:a,type:aj(b.name,this.schema)},e=U(b,"addNodeView",d);return e?[b.name,(d,f,g,h,i)=>{let j=aa(d,c);return e()({node:d,view:f,getPos:g,decorations:h,innerDecorations:i,editor:a,extension:b,HTMLAttributes:j})}]:[]}))}get markViews(){let{editor:a}=this,{markExtensions:b}=Z(this.extensions);return Object.fromEntries(b.filter(a=>!!U(a,"addMarkView")).map(b=>{let c=this.attributes.filter(a=>a.type===b.name),d={name:b.name,options:b.options,storage:this.editor.extensionStorage[b.name],editor:a,type:ah(b.name,this.schema)},e=U(b,"addMarkView",d);return e?[b.name,(d,f,g)=>{let h=aa(d,c);return e()({mark:d,view:f,inline:g,editor:a,extension:b,HTMLAttributes:h,updateAttributes:b=>{b8(d,a,b)}})}]:[]}))}setupExtensions(){let a=this.extensions;this.editor.extensionStorage=Object.fromEntries(a.map(a=>[a.name,a.storage])),a.forEach(a=>{var b;let c={name:a.name,options:a.options,storage:this.editor.extensionStorage[a.name],editor:this.editor,type:au(a.name,this.schema)};"mark"===a.type&&(null==(b=Y(U(a,"keepOnSplit",c)))||b)&&this.splittableMarks.push(a.name);let d=U(a,"onBeforeCreate",c),e=U(a,"onCreate",c),f=U(a,"onUpdate",c),g=U(a,"onSelectionUpdate",c),h=U(a,"onTransaction",c),i=U(a,"onFocus",c),j=U(a,"onBlur",c),k=U(a,"onDestroy",c);d&&this.editor.on("beforeCreate",d),e&&this.editor.on("create",e),f&&this.editor.on("update",f),g&&this.editor.on("selectionUpdate",g),h&&this.editor.on("transaction",h),i&&this.editor.on("focus",i),j&&this.editor.on("blur",j),k&&this.editor.on("destroy",k)})}};aO.resolve=ae,aO.sort=ad,aO.flatten=V,J({},{ClipboardTextSerializer:()=>aQ,Commands:()=>bT,Delete:()=>bU,Drop:()=>bV,Editable:()=>bW,FocusEvents:()=>bY,Keymap:()=>bZ,Paste:()=>b$,Tabindex:()=>b_,focusEventsPluginKey:()=>bX});var aP=class a extends aK{constructor(){super(...arguments),this.type="extension"}static create(b={}){return new a("function"==typeof b?b():b)}configure(a){return super.configure(a)}extend(a){let b="function"==typeof a?a():a;return super.extend(b)}},aQ=aP.create({name:"clipboardTextSerializer",addOptions:()=>({blockSeparator:void 0}),addProseMirrorPlugins(){return[new d.k_({key:new d.hs("clipboardTextSerializer"),props:{clipboardTextSerializer:()=>{let{editor:a}=this,{state:b,schema:c}=a,{doc:d,selection:e}=b,{ranges:f}=e,g=Math.min(...f.map(a=>a.$from.pos)),h=Math.max(...f.map(a=>a.$to.pos)),i=ag(c);return af(d,{from:g,to:h},{...void 0!==this.options.blockSeparator?{blockSeparator:this.options.blockSeparator}:{},textSerializers:i})}}})]}}),aR={};J(aR,{blur:()=>aS,clearContent:()=>aT,clearNodes:()=>aU,command:()=>aV,createParagraphNear:()=>aW,cut:()=>aX,deleteCurrentNode:()=>aY,deleteNode:()=>aZ,deleteRange:()=>a$,deleteSelection:()=>a_,enter:()=>a0,exitCode:()=>a1,extendMarkRange:()=>a2,first:()=>a3,focus:()=>a6,forEach:()=>a7,insertContent:()=>a8,insertContentAt:()=>a9,joinBackward:()=>bc,joinDown:()=>bb,joinForward:()=>bd,joinItemBackward:()=>be,joinItemForward:()=>bf,joinTextblockBackward:()=>bg,joinTextblockForward:()=>bh,joinUp:()=>ba,keyboardShortcut:()=>bj,lift:()=>bk,liftEmptyBlock:()=>bl,liftListItem:()=>bm,newlineInCode:()=>bn,resetAttributes:()=>bp,scrollIntoView:()=>bq,selectAll:()=>br,selectNodeBackward:()=>bs,selectNodeForward:()=>bt,selectParentNode:()=>bu,selectTextblockEnd:()=>bv,selectTextblockStart:()=>bw,setContent:()=>bx,setMark:()=>by,setMeta:()=>bz,setNode:()=>bA,setNodeSelection:()=>bB,setTextSelection:()=>bC,sinkListItem:()=>bD,splitBlock:()=>bF,splitListItem:()=>bG,toggleList:()=>bJ,toggleMark:()=>bK,toggleNode:()=>bL,toggleWrap:()=>bM,undoInputRule:()=>bN,unsetAllMarks:()=>bO,unsetMark:()=>bP,updateAttributes:()=>bQ,wrapIn:()=>bR,wrapInList:()=>bS});var aS=()=>({editor:a,view:b})=>(requestAnimationFrame(()=>{var c;a.isDestroyed||(b.dom.blur(),null==(c=null==window?void 0:window.getSelection())||c.removeAllRanges())}),!0),aT=(a=!0)=>({commands:b})=>b.setContent("",{emitUpdate:a}),aU=()=>({state:a,tr:b,dispatch:c})=>{let{selection:d}=b,{ranges:e}=d;return!c||(e.forEach(({$from:c,$to:d})=>{a.doc.nodesBetween(c.pos,d.pos,(a,c)=>{if(a.type.isText)return;let{doc:d,mapping:e}=b,f=d.resolve(e.map(c)),h=d.resolve(e.map(c+a.nodeSize)),i=f.blockRange(h);if(!i)return;let j=(0,g.jP)(i);if(a.type.isTextblock){let{defaultType:a}=f.parent.contentMatchAt(f.index());b.setNodeMarkup(i.start,a)}(j||0===j)&&b.lift(i,j)})}),!0)},aV=a=>b=>a(b),aW=()=>({state:a,dispatch:b})=>w(a,b),aX=(a,b)=>({editor:c,tr:e})=>{let{state:f}=c,g=f.doc.slice(a.from,a.to);e.deleteRange(a.from,a.to);let h=e.mapping.map(b);return e.insert(h,g.content),e.setSelection(new d.U3(e.doc.resolve(Math.max(h-1,0)))),!0},aY=()=>({tr:a,dispatch:b})=>{let{selection:c}=a,d=c.$anchor.node();if(d.content.size>0)return!1;let e=a.selection.$anchor;for(let c=e.depth;c>0;c-=1)if(e.node(c).type===d.type){if(b){let b=e.before(c),d=e.after(c);a.delete(b,d).scrollIntoView()}return!0}return!1},aZ=a=>({tr:b,state:c,dispatch:d})=>{let e=aj(a,c.schema),f=b.selection.$anchor;for(let a=f.depth;a>0;a-=1)if(f.node(a).type===e){if(d){let c=f.before(a),d=f.after(a);b.delete(c,d).scrollIntoView()}return!0}return!1},a$=a=>({tr:b,dispatch:c})=>{let{from:d,to:e}=a;return c&&b.delete(d,e),!0},a_=()=>({state:a,dispatch:b})=>i(a,b),a0=()=>({commands:a})=>a.keyboardShortcut("Enter"),a1=()=>({state:a,dispatch:b})=>v(a,b),a2=(a,b={})=>({tr:c,state:e,dispatch:f})=>{let g=ah(a,e.schema),{doc:h,selection:i}=c,{$from:j,from:k,to:l}=i;if(f){let a=ar(j,g,b);if(a&&a.from<=k&&a.to>=l){let b=d.U3.create(h,a.from,a.to);c.setSelection(b)}}return!0},a3=a=>b=>{let c="function"==typeof a?a(b):a;for(let a=0;a<c.length;a+=1)if(c[a](b))return!0;return!1};function a4(){return"Android"===navigator.platform||/android/i.test(navigator.userAgent)}function a5(){return["iPad Simulator","iPhone Simulator","iPod Simulator","iPad","iPhone","iPod"].includes(navigator.platform)||navigator.userAgent.includes("Mac")&&"ontouchend"in document}var a6=(a=null,b={})=>({editor:c,view:d,tr:e,dispatch:f})=>{b={scrollIntoView:!0,...b};let g=()=>{(a5()||a4())&&d.dom.focus(),requestAnimationFrame(()=>{!c.isDestroyed&&(d.focus(),(null==b?void 0:b.scrollIntoView)&&c.commands.scrollIntoView())})};if(d.hasFocus()&&null===a||!1===a)return!0;if(f&&null===a&&!aE(c.state.selection))return g(),!0;let h=aG(e.doc,a)||c.state.selection,i=c.state.selection.eq(h);return f&&(i||e.setSelection(h),i&&e.storedMarks&&e.setStoredMarks(e.storedMarks),g()),!0},a7=(a,b)=>c=>a.every((a,d)=>b(a,{...c,index:d})),a8=(a,b)=>({tr:c,commands:d})=>d.insertContentAt({from:c.selection.from,to:c.selection.to},a,b),a9=(a,b,c)=>({tr:e,dispatch:f,editor:i})=>{var j;if(f){let f,k;c={parseOptions:i.options.parseOptions,updateSelection:!0,applyInputRules:!1,applyPasteRules:!1,...c};let{selection:l}=i.state,m=a=>{i.emit("contentError",{editor:i,error:a,disableCollaboration:()=>{"collaboration"in i.storage&&"object"==typeof i.storage.collaboration&&i.storage.collaboration&&(i.storage.collaboration.isDisabled=!0)}})},n={preserveWhitespace:"full",...c.parseOptions};if(!c.errorOnInvalidContent&&!i.options.enableContentCheck&&i.options.emitContentError)try{Q(b,i.schema,{parseOptions:n,errorOnInvalidContent:!0})}catch(a){m(a)}try{f=Q(b,i.schema,{parseOptions:n,errorOnInvalidContent:null!=(j=c.errorOnInvalidContent)?j:i.options.enableContentCheck})}catch(a){return m(a),!1}let{from:o,to:p}="number"==typeof a?{from:a,to:a}:{from:a.from,to:a.to},q=!0,r=!0;if((!("type"in f)?f:[f]).forEach(a=>{a.check(),q=!!q&&a.isText&&0===a.marks.length,r=!!r&&a.isBlock}),o===p&&r){let{parent:a}=e.doc.resolve(o);!a.isTextblock||a.type.spec.code||a.childCount||(o-=1,p+=1)}if(q){if(Array.isArray(b))k=b.map(a=>a.text||"").join("");else if(b instanceof h.FK){let a="";b.forEach(b=>{b.text&&(a+=b.text)}),k=a}else k="object"==typeof b&&b&&b.text?b.text:b;e.insertText(k,o,p)}else{k=f;let a=0===l.$from.parentOffset,b=l.$from.node().isText||l.$from.node().isTextblock,c=l.$from.node().content.size>0;a&&b&&c&&(o=Math.max(0,o-1)),e.replaceWith(o,p,k)}c.updateSelection&&function(a,b,c){let e=a.steps.length-1;if(e<b)return;let f=a.steps[e];if(!(f instanceof g.Ln||f instanceof g.Wg))return;let h=a.mapping.maps[e],i=0;h.forEach((a,b,c,d)=>{0===i&&(i=d)}),a.setSelection(d.LN.near(a.doc.resolve(i),-1))}(e,e.steps.length-1,0),c.applyInputRules&&e.setMeta("applyInputRules",{from:o,text:k}),c.applyPasteRules&&e.setMeta("applyPasteRules",{from:o,text:k})}return!0},ba=()=>({state:a,dispatch:b})=>((a,b)=>{let c=a.selection,e=c instanceof d.nh,f;if(e){if(c.node.isTextblock||!(0,g.n9)(a.doc,c.from))return!1;f=c.from}else if(null==(f=(0,g.N0)(a.doc,c.from,-1)))return!1;if(b){let c=a.tr.join(f);e&&c.setSelection(d.nh.create(c.doc,f-a.doc.resolve(f).nodeBefore.nodeSize)),b(c.scrollIntoView())}return!0})(a,b),bb=()=>({state:a,dispatch:b})=>((a,b)=>{let c=a.selection,e;if(c instanceof d.nh){if(c.node.isTextblock||!(0,g.n9)(a.doc,c.to))return!1;e=c.to}else if(null==(e=(0,g.N0)(a.doc,c.to,1)))return!1;return b&&b(a.tr.join(e).scrollIntoView()),!0})(a,b),bc=()=>({state:a,dispatch:b})=>k(a,b),bd=()=>({state:a,dispatch:b})=>q(a,b),be=()=>({state:a,dispatch:b,tr:c})=>{try{let d=(0,g.N0)(a.doc,a.selection.$from.pos,-1);if(null==d)return!1;return c.join(d,2),b&&b(c),!0}catch{return!1}},bf=()=>({state:a,dispatch:b,tr:c})=>{try{let d=(0,g.N0)(a.doc,a.selection.$from.pos,1);if(null==d)return!1;return c.join(d,2),b&&b(c),!0}catch{return!1}},bg=()=>({state:a,dispatch:b})=>((a,b,c)=>{let d=j(a,c);if(!d)return!1;let e=o(d);return!!e&&l(a,e,b)})(a,b),bh=()=>({state:a,dispatch:b})=>((a,b,c)=>{let d=p(a,c);if(!d)return!1;let e=s(d);return!!e&&l(a,e,b)})(a,b);function bi(){return"undefined"!=typeof navigator&&/Mac/.test(navigator.platform)}var bj=a=>({editor:b,view:c,tr:d,dispatch:e})=>{let f=(function(a){let b,c,d,e,f=a.split(/-(?!$)/),g=f[f.length-1];"Space"===g&&(g=" ");for(let a=0;a<f.length-1;a+=1){let g=f[a];if(/^(cmd|meta|m)$/i.test(g))e=!0;else if(/^a(lt)?$/i.test(g))b=!0;else if(/^(c|ctrl|control)$/i.test(g))c=!0;else if(/^s(hift)?$/i.test(g))d=!0;else if(/^mod$/i.test(g))a5()||bi()?e=!0:c=!0;else throw Error(`Unrecognized modifier name: ${g}`)}return b&&(g=`Alt-${g}`),c&&(g=`Ctrl-${g}`),e&&(g=`Meta-${g}`),d&&(g=`Shift-${g}`),g})(a).split(/-(?!$)/),g=f.find(a=>!["Alt","Ctrl","Meta","Shift"].includes(a)),h=new KeyboardEvent("keydown",{key:"Space"===g?" ":g,altKey:f.includes("Alt"),ctrlKey:f.includes("Ctrl"),metaKey:f.includes("Meta"),shiftKey:f.includes("Shift"),bubbles:!0,cancelable:!0}),i=b.captureTransaction(()=>{c.someProp("handleKeyDown",a=>a(c,h))});return null==i||i.steps.forEach(a=>{let b=a.map(d.mapping);b&&e&&d.maybeStep(b)}),!0},bk=(a,b={})=>({state:c,dispatch:d})=>{let e=aj(a,c.schema);return!!ax(c,e,b)&&((a,b)=>{let{$from:c,$to:d}=a.selection,e=c.blockRange(d),f=e&&(0,g.jP)(e);return null!=f&&(b&&b(a.tr.lift(e,f).scrollIntoView()),!0)})(c,d)},bl=()=>({state:a,dispatch:b})=>x(a,b),bm=a=>({state:b,dispatch:c})=>(function(a){return function(b,c){let{$from:d,$to:e}=b.selection,f=d.blockRange(e,b=>b.childCount>0&&b.firstChild.type==a);return!!f&&(!c||(d.node(f.depth-1).type==a?function(a,b,c,d){let e=a.tr,f=d.end,i=d.$to.end(d.depth);f<i&&(e.step(new g.Wg(f-1,i,f,i,new h.Ji(h.FK.from(c.create(null,d.parent.copy())),1,0),1,!0)),d=new h.u$(e.doc.resolve(d.$from.pos),e.doc.resolve(i),d.depth));let j=(0,g.jP)(d);if(null==j)return!1;e.lift(d,j);let k=e.doc.resolve(e.mapping.map(f,-1)-1);return(0,g.n9)(e.doc,k.pos)&&k.nodeBefore.type==k.nodeAfter.type&&e.join(k.pos),b(e.scrollIntoView()),!0}(b,c,a,f):function(a,b,c){let d=a.tr,e=c.parent;for(let a=c.end,b=c.endIndex-1,f=c.startIndex;b>f;b--)a-=e.child(b).nodeSize,d.delete(a-1,a+1);let f=d.doc.resolve(c.start),i=f.nodeAfter;if(d.mapping.map(c.end)!=c.start+f.nodeAfter.nodeSize)return!1;let j=0==c.startIndex,k=c.endIndex==e.childCount,l=f.node(-1),m=f.index(-1);if(!l.canReplace(m+ +!j,m+1,i.content.append(k?h.FK.empty:h.FK.from(e))))return!1;let n=f.pos,o=n+i.nodeSize;return d.step(new g.Wg(n-!!j,o+ +!!k,n+1,o-1,new h.Ji((j?h.FK.empty:h.FK.from(e.copy(h.FK.empty))).append(k?h.FK.empty:h.FK.from(e.copy(h.FK.empty))),+!j,+!k),+!j)),b(d.scrollIntoView()),!0}(b,c,f)))}})(aj(a,b.schema))(b,c),bn=()=>({state:a,dispatch:b})=>t(a,b);function bo(a,b){let c="string"==typeof b?[b]:b;return Object.keys(a).reduce((b,d)=>(c.includes(d)||(b[d]=a[d]),b),{})}var bp=(a,b)=>({tr:c,state:d,dispatch:e})=>{let f=null,g=null,h=ak("string"==typeof a?a:a.name,d.schema);return!!h&&("node"===h&&(f=aj(a,d.schema)),"mark"===h&&(g=ah(a,d.schema)),e&&c.selection.ranges.forEach(a=>{d.doc.nodesBetween(a.$from.pos,a.$to.pos,(a,d)=>{f&&f===a.type&&c.setNodeMarkup(d,void 0,bo(a.attrs,b)),g&&a.marks.length&&a.marks.forEach(e=>{g===e.type&&c.addMark(d,d+a.nodeSize,g.create(bo(e.attrs,b)))})})}),!0)},bq=()=>({tr:a,dispatch:b})=>(b&&a.scrollIntoView(),!0),br=()=>({tr:a,dispatch:b})=>{if(b){let b=new d.i5(a.doc);a.setSelection(b)}return!0},bs=()=>({state:a,dispatch:b})=>n(a,b),bt=()=>({state:a,dispatch:b})=>r(a,b),bu=()=>({state:a,dispatch:b})=>((a,b)=>{let{$from:c,to:e}=a.selection,f,g=c.sharedDepth(e);return 0!=g&&(f=c.before(g),b&&b(a.tr.setSelection(d.nh.create(a.doc,f))),!0)})(a,b),bv=()=>({state:a,dispatch:b})=>B(a,b),bw=()=>({state:a,dispatch:b})=>A(a,b),bx=(a,{errorOnInvalidContent:b,emitUpdate:c=!0,parseOptions:d={}}={})=>({editor:e,tr:f,dispatch:g,commands:h})=>{let{doc:i}=f;if("full"!==d.preserveWhitespace){let h=R(a,e.schema,d,{errorOnInvalidContent:null!=b?b:e.options.enableContentCheck});return g&&f.replaceWith(0,i.content.size,h).setMeta("preventUpdate",!c),!0}return g&&f.setMeta("preventUpdate",!c),h.insertContentAt({from:0,to:i.content.size},a,{parseOptions:d,errorOnInvalidContent:null!=b?b:e.options.enableContentCheck})},by=(a,b={})=>({tr:c,state:d,dispatch:e})=>{let{selection:f}=c,{empty:g,ranges:h}=f,i=ah(a,d.schema);if(e)if(g){let a=ai(d,i);c.addStoredMark(i.create({...a,...b}))}else h.forEach(a=>{let e=a.$from.pos,f=a.$to.pos;d.doc.nodesBetween(e,f,(a,d)=>{let g=Math.max(d,e),h=Math.min(d+a.nodeSize,f);a.marks.find(a=>a.type===i)?a.marks.forEach(a=>{i===a.type&&c.addMark(g,h,i.create({...a.attrs,...b}))}):c.addMark(g,h,i.create(b))})});return function(a,b,c){var d;let{selection:e}=b,f=null;if(aE(e)&&(f=e.$cursor),f){let b=null!=(d=a.storedMarks)?d:f.marks();return!!c.isInSet(b)||!b.some(a=>a.type.excludes(c))}let{ranges:g}=e;return g.some(({$from:b,$to:d})=>{let e=0===b.depth&&a.doc.inlineContent&&a.doc.type.allowsMarkType(c);return a.doc.nodesBetween(b.pos,d.pos,(a,b,d)=>{if(e)return!1;if(a.isInline){let b=!d||d.type.allowsMarkType(c),f=!!c.isInSet(a.marks)||!a.marks.some(a=>a.type.excludes(c));e=b&&f}return!e}),e})}(d,c,i)},bz=(a,b)=>({tr:c})=>(c.setMeta(a,b),!0),bA=(a,b={})=>({state:c,dispatch:d,chain:e})=>{let f,g=aj(a,c.schema);return(c.selection.$anchor.sameParent(c.selection.$head)&&(f=c.selection.$anchor.parent.attrs),g.isTextblock)?e().command(({commands:a})=>!!C(g,{...f,...b})(c)||a.clearNodes()).command(({state:a})=>C(g,{...f,...b})(a,d)).run():(console.warn('[tiptap warn]: Currently "setNode()" only supports text block nodes.'),!1)},bB=a=>({tr:b,dispatch:c})=>{if(c){let{doc:c}=b,e=aF(a,0,c.content.size),f=d.nh.create(c,e);b.setSelection(f)}return!0},bC=a=>({tr:b,dispatch:c})=>{if(c){let{doc:c}=b,{from:e,to:f}="number"==typeof a?{from:a,to:a}:a,g=d.U3.atStart(c).from,h=d.U3.atEnd(c).to,i=aF(e,g,h),j=aF(f,g,h),k=d.U3.create(c,i,j);b.setSelection(k)}return!0},bD=a=>({state:b,dispatch:c})=>(function(a){return function(b,c){let{$from:d,$to:e}=b.selection,f=d.blockRange(e,b=>b.childCount>0&&b.firstChild.type==a);if(!f)return!1;let i=f.startIndex;if(0==i)return!1;let j=f.parent,k=j.child(i-1);if(k.type!=a)return!1;if(c){let d=k.lastChild&&k.lastChild.type==j.type,e=h.FK.from(d?a.create():null),i=new h.Ji(h.FK.from(a.create(null,h.FK.from(j.type.create(null,e)))),d?3:1,0),l=f.start,m=f.end;c(b.tr.step(new g.Wg(l-(d?3:1),m,l,m,i,1,!0)).scrollIntoView())}return!0}})(aj(a,b.schema))(b,c);function bE(a,b){let c=a.storedMarks||a.selection.$to.parentOffset&&a.selection.$from.marks();if(c){let d=c.filter(a=>null==b?void 0:b.includes(a.type.name));a.tr.ensureMarks(d)}}var bF=({keepMarks:a=!0}={})=>({tr:b,state:c,dispatch:e,editor:f})=>{let{selection:h,doc:i}=b,{$from:j,$to:k}=h,l=av(f.extensionManager.attributes,j.node().type.name,j.node().attrs);if(h instanceof d.nh&&h.node.isBlock)return!!j.parentOffset&&!!(0,g.zy)(i,j.pos)&&(e&&(a&&bE(c,f.extensionManager.splittableMarks),b.split(j.pos).scrollIntoView()),!0);if(!j.parent.isBlock)return!1;let m=k.parentOffset===k.parent.content.size,n=0===j.depth?void 0:function(a){for(let b=0;b<a.edgeCount;b+=1){let{type:c}=a.edge(b);if(c.isTextblock&&!c.hasRequiredAttrs())return c}return null}(j.node(-1).contentMatchAt(j.indexAfter(-1))),o=m&&n?[{type:n,attrs:l}]:void 0,p=(0,g.zy)(b.doc,b.mapping.map(j.pos),1,o);if(!o&&!p&&(0,g.zy)(b.doc,b.mapping.map(j.pos),1,n?[{type:n}]:void 0)&&(p=!0,o=n?[{type:n,attrs:l}]:void 0),e){if(p&&(h instanceof d.U3&&b.deleteSelection(),b.split(b.mapping.map(j.pos),1,o),n&&!m&&!j.parentOffset&&j.parent.type!==n)){let a=b.mapping.map(j.before()),c=b.doc.resolve(a);j.node(-1).canReplaceWith(c.index(),c.index()+1,n)&&b.setNodeMarkup(b.mapping.map(j.before()),n)}a&&bE(c,f.extensionManager.splittableMarks),b.scrollIntoView()}return p},bG=(a,b={})=>({tr:c,state:e,dispatch:f,editor:i})=>{var j;let k=aj(a,e.schema),{$from:l,$to:m}=e.selection,n=e.selection.node;if(n&&n.isBlock||l.depth<2||!l.sameParent(m))return!1;let o=l.node(-1);if(o.type!==k)return!1;let p=i.extensionManager.attributes;if(0===l.parent.content.size&&l.node(-1).childCount===l.indexAfter(-1)){if(2===l.depth||l.node(-3).type!==k||l.index(-2)!==l.node(-2).childCount-1)return!1;if(f){let a=h.FK.empty,e=l.index(-1)?1:l.index(-2)?2:3;for(let b=l.depth-e;b>=l.depth-3;b-=1)a=h.FK.from(l.node(b).copy(a));let f=l.indexAfter(-1)<l.node(-2).childCount?1:l.indexAfter(-2)<l.node(-3).childCount?2:3,g={...av(p,l.node().type.name,l.node().attrs),...b},i=(null==(j=k.contentMatch.defaultType)?void 0:j.createAndFill(g))||void 0;a=a.append(h.FK.from(k.createAndFill(null,i)||void 0));let m=l.before(l.depth-(e-1));c.replace(m,l.after(-f),new h.Ji(a,4-e,0));let n=-1;c.doc.nodesBetween(m,c.doc.content.size,(a,b)=>{if(n>-1)return!1;a.isTextblock&&0===a.content.size&&(n=b+1)}),n>-1&&c.setSelection(d.U3.near(c.doc.resolve(n))),c.scrollIntoView()}return!0}let q=m.pos===l.end()?o.contentMatchAt(0).defaultType:null,r={...av(p,o.type.name,o.attrs),...b},s={...av(p,l.node().type.name,l.node().attrs),...b};c.delete(l.pos,m.pos);let t=q?[{type:k,attrs:r},{type:q,attrs:s}]:[{type:k,attrs:r}];if(!(0,g.zy)(c.doc,l.pos,2))return!1;if(f){let{selection:a,storedMarks:b}=e,{splittableMarks:d}=i.extensionManager,g=b||a.$to.parentOffset&&a.$from.marks();if(c.split(l.pos,2,t).scrollIntoView(),!g||!f)return!0;let h=g.filter(a=>d.includes(a.type.name));c.ensureMarks(h)}return!0},bH=(a,b)=>{let c=T(a=>a.type===b)(a.selection);if(!c)return!0;let d=a.doc.resolve(Math.max(0,c.pos-1)).before(c.depth);if(void 0===d)return!0;let e=a.doc.nodeAt(d);return!(c.node.type===(null==e?void 0:e.type)&&(0,g.n9)(a.doc,c.pos))||(a.join(c.pos),!0)},bI=(a,b)=>{let c=T(a=>a.type===b)(a.selection);if(!c)return!0;let d=a.doc.resolve(c.start).after(c.depth);if(void 0===d)return!0;let e=a.doc.nodeAt(d);return!(c.node.type===(null==e?void 0:e.type)&&(0,g.n9)(a.doc,d))||(a.join(d),!0)},bJ=(a,b,c,d={})=>({editor:e,tr:f,state:g,dispatch:h,chain:i,commands:j,can:k})=>{let{extensions:l,splittableMarks:m}=e.extensionManager,n=aj(a,g.schema),o=aj(b,g.schema),{selection:p,storedMarks:q}=g,{$from:r,$to:s}=p,t=r.blockRange(s),u=q||p.$to.parentOffset&&p.$from.marks();if(!t)return!1;let v=T(a=>aB(a.type.name,l))(p);if(t.depth>=1&&v&&t.depth-v.depth<=1){if(v.node.type===n)return j.liftListItem(o);if(aB(v.node.type.name,l)&&n.validContent(v.node.content)&&h)return i().command(()=>(f.setNodeMarkup(v.pos,n),!0)).command(()=>bH(f,n)).command(()=>bI(f,n)).run()}return c&&u&&h?i().command(()=>{let a=k().wrapInList(n,d),b=u.filter(a=>m.includes(a.type.name));return f.ensureMarks(b),!!a||j.clearNodes()}).wrapInList(n,d).command(()=>bH(f,n)).command(()=>bI(f,n)).run():i().command(()=>!!k().wrapInList(n,d)||j.clearNodes()).wrapInList(n,d).command(()=>bH(f,n)).command(()=>bI(f,n)).run()},bK=(a,b={},c={})=>({state:d,commands:e})=>{let{extendEmptyMarkRange:f=!1}=c,g=ah(a,d.schema);return aw(d,g,b)?e.unsetMark(g,{extendEmptyMarkRange:f}):e.setMark(g,b)},bL=(a,b,c={})=>({state:d,commands:e})=>{let f,g=aj(a,d.schema),h=aj(b,d.schema),i=ax(d,g,c);return(d.selection.$anchor.sameParent(d.selection.$head)&&(f=d.selection.$anchor.parent.attrs),i)?e.setNode(h,f):e.setNode(g,{...f,...c})},bM=(a,b={})=>({state:c,commands:d})=>{let e=aj(a,c.schema);return ax(c,e,b)?d.lift(e):d.wrapIn(e,b)},bN=()=>({state:a,dispatch:b})=>{let c=a.plugins;for(let d=0;d<c.length;d+=1){let e,f=c[d];if(f.spec.isInputRules&&(e=f.getState(a))){if(b){let b=a.tr,c=e.transform;for(let a=c.steps.length-1;a>=0;a-=1)b.step(c.steps[a].invert(c.docs[a]));if(e.text){let c=b.doc.resolve(e.from).marks();b.replaceWith(e.from,e.to,a.schema.text(e.text,c))}else b.delete(e.from,e.to)}return!0}}return!1},bO=()=>({tr:a,dispatch:b})=>{let{selection:c}=a,{empty:d,ranges:e}=c;return!!d||(b&&e.forEach(b=>{a.removeMark(b.$from.pos,b.$to.pos)}),!0)},bP=(a,b={})=>({tr:c,state:d,dispatch:e})=>{var f;let{extendEmptyMarkRange:g=!1}=b,{selection:h}=c,i=ah(a,d.schema),{$from:j,empty:k,ranges:l}=h;if(!e)return!0;if(k&&g){let{from:a,to:b}=h,d=null==(f=j.marks().find(a=>a.type===i))?void 0:f.attrs,e=ar(j,i,d);e&&(a=e.from,b=e.to),c.removeMark(a,b,i)}else l.forEach(a=>{c.removeMark(a.$from.pos,a.$to.pos,i)});return c.removeStoredMark(i),!0},bQ=(a,b={})=>({tr:c,state:d,dispatch:e})=>{let f=null,g=null,h=ak("string"==typeof a?a:a.name,d.schema);return!!h&&("node"===h&&(f=aj(a,d.schema)),"mark"===h&&(g=ah(a,d.schema)),e&&c.selection.ranges.forEach(a=>{let e,h,i,j,k=a.$from.pos,l=a.$to.pos;c.selection.empty?d.doc.nodesBetween(k,l,(a,b)=>{f&&f===a.type&&(i=Math.max(b,k),j=Math.min(b+a.nodeSize,l),e=b,h=a)}):d.doc.nodesBetween(k,l,(a,d)=>{d<k&&f&&f===a.type&&(i=Math.max(d,k),j=Math.min(d+a.nodeSize,l),e=d,h=a),d>=k&&d<=l&&(f&&f===a.type&&c.setNodeMarkup(d,void 0,{...a.attrs,...b}),g&&a.marks.length&&a.marks.forEach(e=>{if(g===e.type){let f=Math.max(d,k),h=Math.min(d+a.nodeSize,l);c.addMark(f,h,g.create({...e.attrs,...b}))}}))}),h&&(void 0!==e&&c.setNodeMarkup(e,void 0,{...h.attrs,...b}),g&&h.marks.length&&h.marks.forEach(a=>{g===a.type&&c.addMark(i,j,g.create({...a.attrs,...b}))}))}),!0)},bR=(a,b={})=>({state:c,dispatch:d})=>(function(a,b=null){return function(c,d){let{$from:e,$to:f}=c.selection,h=e.blockRange(f),i=h&&(0,g.oM)(h,a,b);return!!i&&(d&&d(c.tr.wrap(h,i).scrollIntoView()),!0)}})(aj(a,c.schema),b)(c,d),bS=(a,b={})=>({state:c,dispatch:d})=>(function(a,b=null){return function(c,d){let{$from:e,$to:f}=c.selection,i=e.blockRange(f);if(!i)return!1;let j=d?c.tr:null;return!!function(a,b,c,d=null){let e=!1,f=b,i=b.$from.doc;if(b.depth>=2&&b.$from.node(b.depth-1).type.compatibleContent(c)&&0==b.startIndex){if(0==b.$from.index(b.depth-1))return!1;let a=i.resolve(b.start-2);f=new h.u$(a,a,b.depth),b.endIndex<b.parent.childCount&&(b=new h.u$(b.$from,i.resolve(b.$to.end(b.depth)),b.depth)),e=!0}let j=(0,g.oM)(f,c,d,b);return!!j&&(a&&function(a,b,c,d,e){let f=h.FK.empty;for(let a=c.length-1;a>=0;a--)f=h.FK.from(c[a].type.create(c[a].attrs,f));a.step(new g.Wg(b.start-2*!!d,b.end,b.start,b.end,new h.Ji(f,0,0),c.length,!0));let i=0;for(let a=0;a<c.length;a++)c[a].type==e&&(i=a+1);let j=c.length-i,k=b.start+c.length-2*!!d,l=b.parent;for(let c=b.startIndex,d=b.endIndex,e=!0;c<d;c++,e=!1)!e&&(0,g.zy)(a.doc,k,j)&&(a.split(k,j),k+=2*j),k+=l.child(c).nodeSize}(a,b,j,e,c),!0)}(j,i,a,b)&&(d&&d(j.scrollIntoView()),!0)}})(aj(a,c.schema),b)(c,d),bT=aP.create({name:"commands",addCommands:()=>({...aR})}),bU=aP.create({name:"delete",onUpdate({transaction:a,appendedTransactions:b}){var c,d,e;let f=()=>{var c,d,e,f;if(null!=(f=null==(e=null==(d=null==(c=this.editor.options.coreExtensionOptions)?void 0:c.delete)?void 0:d.filterTransaction)?void 0:e.call(d,a))?f:a.getMeta("y-sync$"))return;let h=N(a.before,[a,...b]);am(h).forEach(b=>{h.mapping.mapResult(b.oldRange.from).deletedAfter&&h.mapping.mapResult(b.oldRange.to).deletedBefore&&h.before.nodesBetween(b.oldRange.from,b.oldRange.to,(c,d)=>{let e=d+c.nodeSize-2,f=b.oldRange.from<=d&&e<=b.oldRange.to;this.editor.emit("delete",{type:"node",node:c,from:d,to:e,newFrom:h.mapping.map(d),newTo:h.mapping.map(e),deletedRange:b.oldRange,newRange:b.newRange,partial:!f,editor:this.editor,transaction:a,combinedTransform:h})})});let i=h.mapping;h.steps.forEach((b,c)=>{var d,e;if(b instanceof g.Ys){let f=i.slice(c).map(b.from,-1),g=i.slice(c).map(b.to),j=i.invert().map(f,-1),k=i.invert().map(g),l=null==(d=h.doc.nodeAt(f-1))?void 0:d.marks.some(a=>a.eq(b.mark)),m=null==(e=h.doc.nodeAt(g))?void 0:e.marks.some(a=>a.eq(b.mark));this.editor.emit("delete",{type:"mark",mark:b.mark,from:b.from,to:b.to,deletedRange:{from:j,to:k},newRange:{from:f,to:g},partial:!!(m||l),editor:this.editor,transaction:a,combinedTransform:h})}})};null==(e=null==(d=null==(c=this.editor.options.coreExtensionOptions)?void 0:c.delete)?void 0:d.async)||e?setTimeout(f,0):f()}}),bV=aP.create({name:"drop",addProseMirrorPlugins(){return[new d.k_({key:new d.hs("tiptapDrop"),props:{handleDrop:(a,b,c,d)=>{this.editor.emit("drop",{editor:this.editor,event:b,slice:c,moved:d})}}})]}}),bW=aP.create({name:"editable",addProseMirrorPlugins(){return[new d.k_({key:new d.hs("editable"),props:{editable:()=>this.editor.options.editable}})]}}),bX=new d.hs("focusEvents"),bY=aP.create({name:"focusEvents",addProseMirrorPlugins(){let{editor:a}=this;return[new d.k_({key:bX,props:{handleDOMEvents:{focus:(b,c)=>{a.isFocused=!0;let d=a.state.tr.setMeta("focus",{event:c}).setMeta("addToHistory",!1);return b.dispatch(d),!1},blur:(b,c)=>{a.isFocused=!1;let d=a.state.tr.setMeta("blur",{event:c}).setMeta("addToHistory",!1);return b.dispatch(d),!1}}}})]}}),bZ=aP.create({name:"keymap",addKeyboardShortcuts(){let a=()=>this.editor.commands.first(({commands:a})=>[()=>a.undoInputRule(),()=>a.command(({tr:b})=>{let{selection:c,doc:e}=b,{empty:f,$anchor:g}=c,{pos:h,parent:i}=g,j=g.parent.isTextblock&&h>0?b.doc.resolve(h-1):g,k=j.parent.type.spec.isolating,l=g.pos-g.parentOffset,m=k&&1===j.parent.childCount?l===g.pos:d.LN.atStart(e).from===h;return!!f&&!!i.type.isTextblock&&!i.textContent.length&&!!m&&(!m||"paragraph"!==g.parent.type.name)&&a.clearNodes()}),()=>a.deleteSelection(),()=>a.joinBackward(),()=>a.selectNodeBackward()]),b=()=>this.editor.commands.first(({commands:a})=>[()=>a.deleteSelection(),()=>a.deleteCurrentNode(),()=>a.joinForward(),()=>a.selectNodeForward()]),c={Enter:()=>this.editor.commands.first(({commands:a})=>[()=>a.newlineInCode(),()=>a.createParagraphNear(),()=>a.liftEmptyBlock(),()=>a.splitBlock()]),"Mod-Enter":()=>this.editor.commands.exitCode(),Backspace:a,"Mod-Backspace":a,"Shift-Backspace":a,Delete:b,"Mod-Delete":b,"Mod-a":()=>this.editor.commands.selectAll()},e={...c},f={...c,"Ctrl-h":a,"Alt-Backspace":a,"Ctrl-d":b,"Ctrl-Alt-Backspace":b,"Alt-Delete":b,"Alt-d":b,"Ctrl-a":()=>this.editor.commands.selectTextblockStart(),"Ctrl-e":()=>this.editor.commands.selectTextblockEnd()};return a5()||bi()?f:e},addProseMirrorPlugins(){return[new d.k_({key:new d.hs("clearDocument"),appendTransaction:(a,b,c)=>{if(a.some(a=>a.getMeta("composition")))return;let e=a.some(a=>a.docChanged)&&!b.doc.eq(c.doc),f=a.some(a=>a.getMeta("preventClearDocument"));if(!e||f)return;let{empty:g,from:h,to:i}=b.selection,j=d.LN.atStart(b.doc).from,k=d.LN.atEnd(b.doc).to;if(g||h!==j||i!==k||!aC(c.doc))return;let l=c.tr,m=K({state:c,transaction:l}),{commands:n}=new L({editor:this.editor,state:m});if(n.clearNodes(),l.steps.length)return l}})]}}),b$=aP.create({name:"paste",addProseMirrorPlugins(){return[new d.k_({key:new d.hs("tiptapPaste"),props:{handlePaste:(a,b,c)=>{this.editor.emit("paste",{editor:this.editor,event:b,slice:c})}}})]}}),b_=aP.create({name:"tabindex",addProseMirrorPlugins(){return[new d.k_({key:new d.hs("tabindex"),props:{attributes:()=>this.editor.isEditable?{tabindex:"0"}:{}}})]}}),b0=class a{constructor(a,b,c=!1,d=null){this.currentNode=null,this.actualDepth=null,this.isBlock=c,this.resolvedPos=a,this.editor=b,this.currentNode=d}get name(){return this.node.type.name}get node(){return this.currentNode||this.resolvedPos.node()}get element(){return this.editor.view.domAtPos(this.pos).node}get depth(){var a;return null!=(a=this.actualDepth)?a:this.resolvedPos.depth}get pos(){return this.resolvedPos.pos}get content(){return this.node.content}set content(a){let b=this.from,c=this.to;if(this.isBlock){if(0===this.content.size)return void console.error(`You can\u2019t set content on a block node. Tried to set content on ${this.name} at ${this.pos}`);b=this.from+1,c=this.to-1}this.editor.commands.insertContentAt({from:b,to:c},a)}get attributes(){return this.node.attrs}get textContent(){return this.node.textContent}get size(){return this.node.nodeSize}get from(){return this.isBlock?this.pos:this.resolvedPos.start(this.resolvedPos.depth)}get range(){return{from:this.from,to:this.to}}get to(){return this.isBlock?this.pos+this.size:this.resolvedPos.end(this.resolvedPos.depth)+ +!this.node.isText}get parent(){if(0===this.depth)return null;let b=this.resolvedPos.start(this.resolvedPos.depth-1);return new a(this.resolvedPos.doc.resolve(b),this.editor)}get before(){let b=this.resolvedPos.doc.resolve(this.from-(this.isBlock?1:2));return b.depth!==this.depth&&(b=this.resolvedPos.doc.resolve(this.from-3)),new a(b,this.editor)}get after(){let b=this.resolvedPos.doc.resolve(this.to+(this.isBlock?2:1));return b.depth!==this.depth&&(b=this.resolvedPos.doc.resolve(this.to+3)),new a(b,this.editor)}get children(){let b=[];return this.node.content.forEach((c,d)=>{let e=c.isBlock&&!c.isTextblock,f=c.isAtom&&!c.isText,g=this.pos+d+ +!f;if(g<0||g>this.resolvedPos.doc.nodeSize-2)return;let h=this.resolvedPos.doc.resolve(g);if(!e&&h.depth<=this.depth)return;let i=new a(h,this.editor,e,e?c:null);e&&(i.actualDepth=this.depth+1),b.push(new a(h,this.editor,e,e?c:null))}),b}get firstChild(){return this.children[0]||null}get lastChild(){let a=this.children;return a[a.length-1]||null}closest(a,b={}){let c=null,d=this.parent;for(;d&&!c;){if(d.node.type.name===a)if(Object.keys(b).length>0){let a=d.node.attrs,c=Object.keys(b);for(let d=0;d<c.length;d+=1){let e=c[d];if(a[e]!==b[e])break}}else c=d;d=d.parent}return c}querySelector(a,b={}){return this.querySelectorAll(a,b,!0)[0]||null}querySelectorAll(a,b={},c=!1){let d=[];if(!this.children||0===this.children.length)return d;let e=Object.keys(b);return this.children.forEach(f=>{(!c||!(d.length>0))&&(f.node.type.name===a&&e.every(a=>b[a]===f.node.attrs[a])&&d.push(f),c&&d.length>0||(d=d.concat(f.querySelectorAll(a,b,c))))}),d}setAttribute(a){let{tr:b}=this.editor.state;b.setNodeMarkup(this.from,void 0,{...this.node.attrs,...a}),this.editor.view.dispatch(b)}},b1=`.ProseMirror {
  position: relative;
}

.ProseMirror {
  word-wrap: break-word;
  white-space: pre-wrap;
  white-space: break-spaces;
  -webkit-font-variant-ligatures: none;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0; /* the above doesn't seem to work in Edge */
}

.ProseMirror [contenteditable="false"] {
  white-space: normal;
}

.ProseMirror [contenteditable="false"] [contenteditable="true"] {
  white-space: pre-wrap;
}

.ProseMirror pre {
  white-space: pre-wrap;
}

img.ProseMirror-separator {
  display: inline !important;
  border: none !important;
  margin: 0 !important;
  width: 0 !important;
  height: 0 !important;
}

.ProseMirror-gapcursor {
  display: none;
  pointer-events: none;
  position: absolute;
  margin: 0;
}

.ProseMirror-gapcursor:after {
  content: "";
  display: block;
  position: absolute;
  top: -2px;
  width: 20px;
  border-top: 1px solid black;
  animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
}

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

.ProseMirror-hideselection *::selection {
  background: transparent;
}

.ProseMirror-hideselection *::-moz-selection {
  background: transparent;
}

.ProseMirror-hideselection * {
  caret-color: transparent;
}

.ProseMirror-focused .ProseMirror-gapcursor {
  display: block;
}`,b2=class extends M{constructor(a={}){super(),this.css=null,this.editorView=null,this.isFocused=!1,this.isInitialized=!1,this.extensionStorage={},this.instanceId=Math.random().toString(36).slice(2,9),this.options={element:"undefined"!=typeof document?document.createElement("div"):null,content:"",injectCSS:!0,injectNonce:void 0,extensions:[],autofocus:!1,editable:!0,editorProps:{},parseOptions:{},coreExtensionOptions:{},enableInputRules:!0,enablePasteRules:!0,enableCoreExtensions:!0,enableContentCheck:!1,emitContentError:!1,onBeforeCreate:()=>null,onCreate:()=>null,onUpdate:()=>null,onSelectionUpdate:()=>null,onTransaction:()=>null,onFocus:()=>null,onBlur:()=>null,onDestroy:()=>null,onContentError:({error:a})=>{throw a},onPaste:()=>null,onDrop:()=>null,onDelete:()=>null},this.isCapturingTransaction=!1,this.capturedTransaction=null,this.setOptions(a),this.createExtensionManager(),this.createCommandManager(),this.createSchema(),this.on("beforeCreate",this.options.onBeforeCreate),this.emit("beforeCreate",{editor:this}),this.on("contentError",this.options.onContentError),this.on("create",this.options.onCreate),this.on("update",this.options.onUpdate),this.on("selectionUpdate",this.options.onSelectionUpdate),this.on("transaction",this.options.onTransaction),this.on("focus",this.options.onFocus),this.on("blur",this.options.onBlur),this.on("destroy",this.options.onDestroy),this.on("drop",({event:a,slice:b,moved:c})=>this.options.onDrop(a,b,c)),this.on("paste",({event:a,slice:b})=>this.options.onPaste(a,b)),this.on("delete",this.options.onDelete);let b=this.createDoc(),c=aG(b,this.options.autofocus);this.editorState=d.$t.create({doc:b,schema:this.schema,selection:c||void 0}),this.options.element&&this.mount(this.options.element)}mount(a){if("undefined"==typeof document)throw Error("[tiptap error]: The editor cannot be mounted because there is no 'document' defined in this environment.");this.createView(a),window.setTimeout(()=>{this.isDestroyed||(this.commands.focus(this.options.autofocus),this.emit("create",{editor:this}),this.isInitialized=!0)},0)}unmount(){var a;if(this.editorView){let a=this.editorView.dom;(null==a?void 0:a.editor)&&delete a.editor,this.editorView.destroy()}this.editorView=null,this.isInitialized=!1,null==(a=this.css)||a.remove(),this.css=null}get storage(){return this.extensionStorage}get commands(){return this.commandManager.commands}chain(){return this.commandManager.chain()}can(){return this.commandManager.can()}injectCSS(){this.options.injectCSS&&"undefined"!=typeof document&&(this.css=function(a,b,c){let d=document.querySelector("style[data-tiptap-style]");if(null!==d)return d;let e=document.createElement("style");return b&&e.setAttribute("nonce",b),e.setAttribute("data-tiptap-style",""),e.innerHTML=a,document.getElementsByTagName("head")[0].appendChild(e),e}(b1,this.options.injectNonce))}setOptions(a={}){this.options={...this.options,...a},this.editorView&&this.state&&!this.isDestroyed&&(this.options.editorProps&&this.view.setProps(this.options.editorProps),this.view.updateState(this.state))}setEditable(a,b=!0){this.setOptions({editable:a}),b&&this.emit("update",{editor:this,transaction:this.state.tr,appendedTransactions:[]})}get isEditable(){return this.options.editable&&this.view&&this.view.editable}get view(){return this.editorView?this.editorView:new Proxy({state:this.editorState,updateState:a=>{this.editorState=a},dispatch:a=>{this.editorState=this.state.apply(a)},composing:!1,dragging:null,editable:!0,isDestroyed:!1},{get:(a,b)=>{if("state"===b)return this.editorState;if(b in a)return Reflect.get(a,b);throw Error(`[tiptap error]: The editor view is not available. Cannot access view['${b}']. The editor may not be mounted yet.`)}})}get state(){return this.editorView&&(this.editorState=this.view.state),this.editorState}registerPlugin(a,b){let c=X(b)?b(a,[...this.state.plugins]):[...this.state.plugins,a],d=this.state.reconfigure({plugins:c});return this.view.updateState(d),d}unregisterPlugin(a){if(this.isDestroyed)return;let b=this.state.plugins,c=b;if([].concat(a).forEach(a=>{let b="string"==typeof a?`${a}$`:a.key;c=c.filter(a=>!a.key.startsWith(b))}),b.length===c.length)return;let d=this.state.reconfigure({plugins:c});return this.view.updateState(d),d}createExtensionManager(){var a,b;let c=[...this.options.enableCoreExtensions?[bW,aQ.configure({blockSeparator:null==(b=null==(a=this.options.coreExtensionOptions)?void 0:a.clipboardTextSerializer)?void 0:b.blockSeparator}),bT,bY,bZ,b_,bV,b$,bU].filter(a=>"object"!=typeof this.options.enableCoreExtensions||!1!==this.options.enableCoreExtensions[a.name]):[],...this.options.extensions].filter(a=>["extension","node","mark"].includes(null==a?void 0:a.type));this.extensionManager=new aO(c,this)}createCommandManager(){this.commandManager=new L({editor:this})}createSchema(){this.schema=this.extensionManager.schema}createDoc(){let a;try{a=R(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:this.options.enableContentCheck})}catch(b){if(!(b instanceof Error)||!["[tiptap error]: Invalid JSON content","[tiptap error]: Invalid HTML content"].includes(b.message))throw b;this.emit("contentError",{editor:this,error:b,disableCollaboration:()=>{"collaboration"in this.storage&&"object"==typeof this.storage.collaboration&&this.storage.collaboration&&(this.storage.collaboration.isDisabled=!0),this.options.extensions=this.options.extensions.filter(a=>"collaboration"!==a.name),this.createExtensionManager()}}),a=R(this.options.content,this.schema,this.options.parseOptions,{errorOnInvalidContent:!1})}return a}createView(a){var b;this.editorView=new e.Lz(a,{...this.options.editorProps,attributes:{role:"textbox",...null==(b=this.options.editorProps)?void 0:b.attributes},dispatchTransaction:this.dispatchTransaction.bind(this),state:this.editorState});let c=this.state.reconfigure({plugins:this.extensionManager.plugins});this.view.updateState(c),this.createNodeViews(),this.prependClass(),this.injectCSS(),this.view.dom.editor=this}createNodeViews(){this.view.isDestroyed||this.view.setProps({markViews:this.extensionManager.markViews,nodeViews:this.extensionManager.nodeViews})}prependClass(){this.view.dom.className=`tiptap ${this.view.dom.className}`}captureTransaction(a){this.isCapturingTransaction=!0,a(),this.isCapturingTransaction=!1;let b=this.capturedTransaction;return this.capturedTransaction=null,b}dispatchTransaction(a){if(this.view.isDestroyed)return;if(this.isCapturingTransaction){if(!this.capturedTransaction){this.capturedTransaction=a;return}a.steps.forEach(a=>{var b;return null==(b=this.capturedTransaction)?void 0:b.step(a)});return}let{state:b,transactions:c}=this.state.applyTransaction(a),d=!this.state.selection.eq(b.selection),e=c.includes(a),f=this.state;if(this.emit("beforeTransaction",{editor:this,transaction:a,nextState:b}),!e)return;this.view.updateState(b),this.emit("transaction",{editor:this,transaction:a,appendedTransactions:c.slice(1)}),d&&this.emit("selectionUpdate",{editor:this,transaction:a});let g=c.findLast(a=>a.getMeta("focus")||a.getMeta("blur")),h=null==g?void 0:g.getMeta("focus"),i=null==g?void 0:g.getMeta("blur");h&&this.emit("focus",{editor:this,event:h.event,transaction:g}),i&&this.emit("blur",{editor:this,event:i.event,transaction:g}),a.getMeta("preventUpdate")||!c.some(a=>a.docChanged)||f.doc.eq(b.doc)||this.emit("update",{editor:this,transaction:a,appendedTransactions:c.slice(1)})}getAttributes(a){return al(this.state,a)}isActive(a,b){let c="string"==typeof a?a:null,d="string"==typeof a?b:a;return function(a,b,c={}){if(!b)return ax(a,null,c)||aw(a,null,c);let d=ak(b,a.schema);return"node"===d?ax(a,b,c):"mark"===d&&aw(a,b,c)}(this.state,c,d)}getJSON(){return this.state.doc.toJSON()}getHTML(){return W(this.state.doc.content,this.schema)}getText(a){let{blockSeparator:b="\n\n",textSerializers:c={}}=a||{};return function(a,b){let c={from:0,to:a.content.size};return af(a,c,b)}(this.state.doc,{blockSeparator:b,textSerializers:{...ag(this.schema),...c}})}get isEmpty(){return aC(this.state.doc)}destroy(){this.emit("destroy"),this.unmount(),this.removeAllListeners()}get isDestroyed(){var a,b;return null==(b=null==(a=this.editorView)?void 0:a.isDestroyed)||b}$node(a,b){var c;return(null==(c=this.$doc)?void 0:c.querySelector(a,b))||null}$nodes(a,b){var c;return(null==(c=this.$doc)?void 0:c.querySelectorAll(a,b))||null}$pos(a){return new b0(this.state.doc.resolve(a),this)}get $doc(){return this.$pos(0)}};function b3(a){return new aH({find:a.find,handler:({state:b,range:c,match:d})=>{let e=Y(a.getAttributes,void 0,d);if(!1===e||null===e)return null;let{tr:f}=b,g=d[d.length-1],h=d[0];if(g){let d=h.search(/\S/),i=c.from+h.indexOf(g),j=i+g.length;if(as(c.from,c.to,b.doc).filter(b=>b.mark.type.excluded.find(c=>c===a.type&&c!==b.mark.type)).filter(a=>a.to>i).length)return null;j<c.to&&f.delete(j,c.to),i>c.from&&f.delete(c.from+d,i);let k=c.from+d+g.length;f.addMark(c.from+d,k,a.type.create(e||{})),f.removeStoredMark(a.type)}}})}function b4(a){return new aH({find:a.find,handler:({state:b,range:c,match:d})=>{let e=Y(a.getAttributes,void 0,d)||{},{tr:f}=b,g=c.from,h=c.to,i=a.type.create(e);if(d[1]){let a=g+d[0].lastIndexOf(d[1]);a>h?a=h:h=a+d[1].length;let b=d[0][d[0].length-1];f.insertText(b,g+d[0].length-1),f.replaceWith(a,h,i)}else if(d[0]){let b=a.type.isInline?g:g-1;f.insert(b,a.type.create(e)).delete(f.mapping.map(g),f.mapping.map(h))}f.scrollIntoView()}})}function b5(a){return new aH({find:a.find,handler:({state:b,range:c,match:d})=>{let e=b.doc.resolve(c.from),f=Y(a.getAttributes,void 0,d)||{};if(!e.node(-1).canReplaceWith(e.index(-1),e.indexAfter(-1),a.type))return null;b.tr.delete(c.from,c.to).setBlockType(c.from,c.from,a.type,f)}})}function b6(a){return new aH({find:a.find,handler:({state:b,range:c,match:d,chain:e})=>{let f=Y(a.getAttributes,void 0,d)||{},h=b.tr.delete(c.from,c.to),i=h.doc.resolve(c.from).blockRange(),j=i&&(0,g.oM)(i,a.type,f);if(!j)return null;if(h.wrap(i,j),a.keepMarks&&a.editor){let{selection:c,storedMarks:d}=b,{splittableMarks:e}=a.editor.extensionManager,f=d||c.$to.parentOffset&&c.$from.marks();if(f){let a=f.filter(a=>e.includes(a.type.name));h.ensureMarks(a)}}if(a.keepAttributes){let b="bulletList"===a.type.name||"orderedList"===a.type.name?"listItem":"taskList";e().updateAttributes(b,f).run()}let k=h.doc.resolve(c.from-1).nodeBefore;k&&k.type===a.type&&(0,g.n9)(h.doc,c.from-1)&&(!a.joinPredicate||a.joinPredicate(d,k))&&h.join(c.from-1)}})}function b7(a,b){let{selection:c}=a,{$from:e}=c;if(c instanceof d.nh){let a=e.index();return e.parent.canReplaceWith(a,a+1,b)}let f=e.depth;for(;f>=0;){let a=e.index(f);if(e.node(f).contentMatchAt(a).matchType(b))return!0;f-=1}return!1}function b8(a,b,c={}){let{state:d}=b,{doc:e,tr:f}=d;e.descendants((b,d)=>{let e=f.mapping.map(d),g=f.mapping.map(d)+b.nodeSize,h=null;if(b.marks.forEach(b=>{if(b!==a)return!1;h=b}),!h)return;let i=!1;if(Object.keys(c).forEach(a=>{c[a]!==h.attrs[a]&&(i=!0)}),i){let b=a.type.create({...a.attrs,...c});f.removeMark(e,g,a.type),f.addMark(e,g,b)}}),f.docChanged&&b.view.dispatch(f)}var b9=class{constructor(a,b,c){this.component=a,this.editor=b.editor,this.options={...c},this.mark=b.mark,this.HTMLAttributes=b.HTMLAttributes}get dom(){return this.editor.view.dom}get contentDOM(){return null}updateAttributes(a,b){b8(b||this.mark,this.editor,a)}ignoreMutation(a){return!this.dom||!this.contentDOM||("function"==typeof this.options.ignoreMutation?this.options.ignoreMutation({mutation:a}):!("selection"===a.type||this.dom.contains(a.target)&&"childList"===a.type&&(a5()||a4())&&this.editor.isFocused&&[...Array.from(a.addedNodes),...Array.from(a.removedNodes)].every(a=>a.isContentEditable))&&(this.contentDOM===a.target&&"attributes"===a.type||!this.contentDOM.contains(a.target)))}},ca=class a extends aK{constructor(){super(...arguments),this.type="node"}static create(b={}){return new a("function"==typeof b?b():b)}configure(a){return super.configure(a)}extend(a){let b="function"==typeof a?a():a;return super.extend(b)}},cb=class{constructor(a,b,c){this.isDragging=!1,this.component=a,this.editor=b.editor,this.options={stopEvent:null,ignoreMutation:null,...c},this.extension=b.extension,this.node=b.node,this.decorations=b.decorations,this.innerDecorations=b.innerDecorations,this.view=b.view,this.HTMLAttributes=b.HTMLAttributes,this.getPos=b.getPos,this.mount()}mount(){}get dom(){return this.editor.view.dom}get contentDOM(){return null}onDragStart(a){var b,c,e,f,g,h,i;let{view:j}=this.editor,k=a.target,l=3===k.nodeType?null==(b=k.parentElement)?void 0:b.closest("[data-drag-handle]"):k.closest("[data-drag-handle]");if(!this.dom||(null==(c=this.contentDOM)?void 0:c.contains(k))||!l)return;let m=0,n=0;if(this.dom!==l){let b=this.dom.getBoundingClientRect(),c=l.getBoundingClientRect(),d=null!=(f=a.offsetX)?f:null==(e=a.nativeEvent)?void 0:e.offsetX,i=null!=(h=a.offsetY)?h:null==(g=a.nativeEvent)?void 0:g.offsetY;m=c.x-b.x+d,n=c.y-b.y+i}let o=this.dom.cloneNode(!0);null==(i=a.dataTransfer)||i.setDragImage(o,m,n);let p=this.getPos();if("number"!=typeof p)return;let q=d.nh.create(j.state.doc,p),r=j.state.tr.setSelection(q);j.dispatch(r)}stopEvent(a){var b;if(!this.dom)return!1;if("function"==typeof this.options.stopEvent)return this.options.stopEvent({event:a});let c=a.target;if(!(this.dom.contains(c)&&!(null==(b=this.contentDOM)?void 0:b.contains(c))))return!1;let e=a.type.startsWith("drag"),f="drop"===a.type;if((["INPUT","BUTTON","SELECT","TEXTAREA"].includes(c.tagName)||c.isContentEditable)&&!f&&!e)return!0;let{isEditable:g}=this.editor,{isDragging:h}=this,i=!!this.node.type.spec.draggable,j=d.nh.isSelectable(this.node),k="copy"===a.type,l="paste"===a.type,m="cut"===a.type,n="mousedown"===a.type;if(!i&&j&&e&&a.target===this.dom&&a.preventDefault(),i&&e&&!h&&a.target===this.dom)return a.preventDefault(),!1;if(i&&g&&!h&&n){let a=c.closest("[data-drag-handle]");a&&(this.dom===a||this.dom.contains(a))&&(this.isDragging=!0,document.addEventListener("dragend",()=>{this.isDragging=!1},{once:!0}),document.addEventListener("drop",()=>{this.isDragging=!1},{once:!0}),document.addEventListener("mouseup",()=>{this.isDragging=!1},{once:!0}))}return!h&&!f&&!k&&!l&&!m&&(!n||!j)}ignoreMutation(a){return!this.dom||!this.contentDOM||("function"==typeof this.options.ignoreMutation?this.options.ignoreMutation({mutation:a}):!!this.node.isLeaf||!!this.node.isAtom||!("selection"===a.type||this.dom.contains(a.target)&&"childList"===a.type&&(a5()||a4())&&this.editor.isFocused&&[...Array.from(a.addedNodes),...Array.from(a.removedNodes)].every(a=>a.isContentEditable))&&(this.contentDOM===a.target&&"attributes"===a.type||!this.contentDOM.contains(a.target)))}updateAttributes(a){this.editor.commands.command(({tr:b})=>{let c=this.getPos();return"number"==typeof c&&(b.setNodeMarkup(c,void 0,{...this.node.attrs,...a}),!0)})}deleteNode(){let a=this.getPos();if("number"!=typeof a)return;let b=a+this.node.nodeSize;this.editor.commands.deleteRange({from:a,to:b})}};function cc(a){return new aM({find:a.find,handler:({state:b,range:c,match:d,pasteEvent:e})=>{let f=Y(a.getAttributes,void 0,d,e);if(!1===f||null===f)return null;let{tr:g}=b,h=d[d.length-1],i=d[0],j=c.to;if(h){let d=i.search(/\S/),e=c.from+i.indexOf(h),k=e+h.length;if(as(c.from,c.to,b.doc).filter(b=>b.mark.type.excluded.find(c=>c===a.type&&c!==b.mark.type)).filter(a=>a.to>e).length)return null;k<c.to&&g.delete(k,c.to),e>c.from&&g.delete(c.from+d,e),j=c.from+d+h.length,g.addMark(c.from+d,j,a.type.create(f||{})),g.removeStoredMark(a.type)}}})}},81553:(a,b,c)=>{function d(a){this.content=a}c.d(b,{S4:()=>O,ZF:()=>X,FK:()=>e,CU:()=>i,sX:()=>L,bP:()=>x,u$:()=>v,vI:()=>j,Sj:()=>M,Ji:()=>k}),d.prototype={constructor:d,find:function(a){for(var b=0;b<this.content.length;b+=2)if(this.content[b]===a)return b;return -1},get:function(a){var b=this.find(a);return -1==b?void 0:this.content[b+1]},update:function(a,b,c){var e=c&&c!=a?this.remove(c):this,f=e.find(a),g=e.content.slice();return -1==f?g.push(c||a,b):(g[f+1]=b,c&&(g[f]=c)),new d(g)},remove:function(a){var b=this.find(a);if(-1==b)return this;var c=this.content.slice();return c.splice(b,2),new d(c)},addToStart:function(a,b){return new d([a,b].concat(this.remove(a).content))},addToEnd:function(a,b){var c=this.remove(a).content.slice();return c.push(a,b),new d(c)},addBefore:function(a,b,c){var e=this.remove(b),f=e.content.slice(),g=e.find(a);return f.splice(-1==g?f.length:g,0,b,c),new d(f)},forEach:function(a){for(var b=0;b<this.content.length;b+=2)a(this.content[b],this.content[b+1])},prepend:function(a){return(a=d.from(a)).size?new d(a.content.concat(this.subtract(a).content)):this},append:function(a){return(a=d.from(a)).size?new d(this.subtract(a).content.concat(a.content)):this},subtract:function(a){var b=this;a=d.from(a);for(var c=0;c<a.content.length;c+=2)b=b.remove(a.content[c]);return b},toObject:function(){var a={};return this.forEach(function(b,c){a[b]=c}),a},get size(){return this.content.length>>1}},d.from=function(a){if(a instanceof d)return a;var b=[];if(a)for(var c in a)b.push(c,a[c]);return new d(b)};class e{constructor(a,b){if(this.content=a,this.size=b||0,null==b)for(let b=0;b<a.length;b++)this.size+=a[b].nodeSize}nodesBetween(a,b,c,d=0,e){for(let f=0,g=0;g<b;f++){let h=this.content[f],i=g+h.nodeSize;if(i>a&&!1!==c(h,d+g,e||null,f)&&h.content.size){let e=g+1;h.nodesBetween(Math.max(0,a-e),Math.min(h.content.size,b-e),c,d+e)}g=i}}descendants(a){this.nodesBetween(0,this.size,a)}textBetween(a,b,c,d){let e="",f=!0;return this.nodesBetween(a,b,(g,h)=>{let i=g.isText?g.text.slice(Math.max(a,h)-h,b-h):g.isLeaf?d?"function"==typeof d?d(g):d:g.type.spec.leafText?g.type.spec.leafText(g):"":"";g.isBlock&&(g.isLeaf&&i||g.isTextblock)&&c&&(f?f=!1:e+=c),e+=i},0),e}append(a){if(!a.size)return this;if(!this.size)return a;let b=this.lastChild,c=a.firstChild,d=this.content.slice(),f=0;for(b.isText&&b.sameMarkup(c)&&(d[d.length-1]=b.withText(b.text+c.text),f=1);f<a.content.length;f++)d.push(a.content[f]);return new e(d,this.size+a.size)}cut(a,b=this.size){if(0==a&&b==this.size)return this;let c=[],d=0;if(b>a)for(let e=0,f=0;f<b;e++){let g=this.content[e],h=f+g.nodeSize;h>a&&((f<a||h>b)&&(g=g.isText?g.cut(Math.max(0,a-f),Math.min(g.text.length,b-f)):g.cut(Math.max(0,a-f-1),Math.min(g.content.size,b-f-1))),c.push(g),d+=g.nodeSize),f=h}return new e(c,d)}cutByIndex(a,b){return a==b?e.empty:0==a&&b==this.content.length?this:new e(this.content.slice(a,b))}replaceChild(a,b){let c=this.content[a];if(c==b)return this;let d=this.content.slice(),f=this.size+b.nodeSize-c.nodeSize;return d[a]=b,new e(d,f)}addToStart(a){return new e([a].concat(this.content),this.size+a.nodeSize)}addToEnd(a){return new e(this.content.concat(a),this.size+a.nodeSize)}eq(a){if(this.content.length!=a.content.length)return!1;for(let b=0;b<this.content.length;b++)if(!this.content[b].eq(a.content[b]))return!1;return!0}get firstChild(){return this.content.length?this.content[0]:null}get lastChild(){return this.content.length?this.content[this.content.length-1]:null}get childCount(){return this.content.length}child(a){let b=this.content[a];if(!b)throw RangeError("Index "+a+" out of range for "+this);return b}maybeChild(a){return this.content[a]||null}forEach(a){for(let b=0,c=0;b<this.content.length;b++){let d=this.content[b];a(d,c,b),c+=d.nodeSize}}findDiffStart(a,b=0){return function a(b,c,d){for(let e=0;;e++){if(e==b.childCount||e==c.childCount)return b.childCount==c.childCount?null:d;let f=b.child(e),g=c.child(e);if(f==g){d+=f.nodeSize;continue}if(!f.sameMarkup(g))return d;if(f.isText&&f.text!=g.text){for(let a=0;f.text[a]==g.text[a];a++)d++;return d}if(f.content.size||g.content.size){let b=a(f.content,g.content,d+1);if(null!=b)return b}d+=f.nodeSize}}(this,a,b)}findDiffEnd(a,b=this.size,c=a.size){return function a(b,c,d,e){for(let f=b.childCount,g=c.childCount;;){if(0==f||0==g)return f==g?null:{a:d,b:e};let h=b.child(--f),i=c.child(--g),j=h.nodeSize;if(h==i){d-=j,e-=j;continue}if(!h.sameMarkup(i))return{a:d,b:e};if(h.isText&&h.text!=i.text){let a=0,b=Math.min(h.text.length,i.text.length);for(;a<b&&h.text[h.text.length-a-1]==i.text[i.text.length-a-1];)a++,d--,e--;return{a:d,b:e}}if(h.content.size||i.content.size){let b=a(h.content,i.content,d-1,e-1);if(b)return b}d-=j,e-=j}}(this,a,b,c)}findIndex(a){if(0==a)return g(0,a);if(a==this.size)return g(this.content.length,a);if(a>this.size||a<0)throw RangeError(`Position ${a} outside of fragment (${this})`);for(let b=0,c=0;;b++){let d=c+this.child(b).nodeSize;if(d>=a){if(d==a)return g(b+1,d);return g(b,c)}c=d}}toString(){return"<"+this.toStringInner()+">"}toStringInner(){return this.content.join(", ")}toJSON(){return this.content.length?this.content.map(a=>a.toJSON()):null}static fromJSON(a,b){if(!b)return e.empty;if(!Array.isArray(b))throw RangeError("Invalid input for Fragment.fromJSON");return new e(b.map(a.nodeFromJSON))}static fromArray(a){if(!a.length)return e.empty;let b,c=0;for(let d=0;d<a.length;d++){let e=a[d];c+=e.nodeSize,d&&e.isText&&a[d-1].sameMarkup(e)?(b||(b=a.slice(0,d)),b[b.length-1]=e.withText(b[b.length-1].text+e.text)):b&&b.push(e)}return new e(b||a,c)}static from(a){if(!a)return e.empty;if(a instanceof e)return a;if(Array.isArray(a))return this.fromArray(a);if(a.attrs)return new e([a],a.nodeSize);throw RangeError("Can not convert "+a+" to a Fragment"+(a.nodesBetween?" (looks like multiple versions of prosemirror-model were loaded)":""))}}e.empty=new e([],0);let f={index:0,offset:0};function g(a,b){return f.index=a,f.offset=b,f}function h(a,b){if(a===b)return!0;if(!(a&&"object"==typeof a)||!(b&&"object"==typeof b))return!1;let c=Array.isArray(a);if(Array.isArray(b)!=c)return!1;if(c){if(a.length!=b.length)return!1;for(let c=0;c<a.length;c++)if(!h(a[c],b[c]))return!1}else{for(let c in a)if(!(c in b)||!h(a[c],b[c]))return!1;for(let c in b)if(!(c in a))return!1}return!0}class i{constructor(a,b){this.type=a,this.attrs=b}addToSet(a){let b,c=!1;for(let d=0;d<a.length;d++){let e=a[d];if(this.eq(e))return a;if(this.type.excludes(e.type))b||(b=a.slice(0,d));else{if(e.type.excludes(this.type))return a;!c&&e.type.rank>this.type.rank&&(b||(b=a.slice(0,d)),b.push(this),c=!0),b&&b.push(e)}}return b||(b=a.slice()),c||b.push(this),b}removeFromSet(a){for(let b=0;b<a.length;b++)if(this.eq(a[b]))return a.slice(0,b).concat(a.slice(b+1));return a}isInSet(a){for(let b=0;b<a.length;b++)if(this.eq(a[b]))return!0;return!1}eq(a){return this==a||this.type==a.type&&h(this.attrs,a.attrs)}toJSON(){let a={type:this.type.name};for(let b in this.attrs){a.attrs=this.attrs;break}return a}static fromJSON(a,b){if(!b)throw RangeError("Invalid input for Mark.fromJSON");let c=a.marks[b.type];if(!c)throw RangeError(`There is no mark type ${b.type} in this schema`);let d=c.create(b.attrs);return c.checkAttrs(d.attrs),d}static sameSet(a,b){if(a==b)return!0;if(a.length!=b.length)return!1;for(let c=0;c<a.length;c++)if(!a[c].eq(b[c]))return!1;return!0}static setFrom(a){if(!a||Array.isArray(a)&&0==a.length)return i.none;if(a instanceof i)return[a];let b=a.slice();return b.sort((a,b)=>a.type.rank-b.type.rank),b}}i.none=[];class j extends Error{}class k{constructor(a,b,c){this.content=a,this.openStart=b,this.openEnd=c}get size(){return this.content.size-this.openStart-this.openEnd}insertAt(a,b){let c=function a(b,c,d,e){let{index:f,offset:g}=b.findIndex(c),h=b.maybeChild(f);if(g==c||h.isText)return e&&!e.canReplace(f,f,d)?null:b.cut(0,c).append(d).append(b.cut(c));let i=a(h.content,c-g-1,d);return i&&b.replaceChild(f,h.copy(i))}(this.content,a+this.openStart,b);return c&&new k(c,this.openStart,this.openEnd)}removeBetween(a,b){return new k(function a(b,c,d){let{index:e,offset:f}=b.findIndex(c),g=b.maybeChild(e),{index:h,offset:i}=b.findIndex(d);if(f==c||g.isText){if(i!=d&&!b.child(h).isText)throw RangeError("Removing non-flat range");return b.cut(0,c).append(b.cut(d))}if(e!=h)throw RangeError("Removing non-flat range");return b.replaceChild(e,g.copy(a(g.content,c-f-1,d-f-1)))}(this.content,a+this.openStart,b+this.openStart),this.openStart,this.openEnd)}eq(a){return this.content.eq(a.content)&&this.openStart==a.openStart&&this.openEnd==a.openEnd}toString(){return this.content+"("+this.openStart+","+this.openEnd+")"}toJSON(){if(!this.content.size)return null;let a={content:this.content.toJSON()};return this.openStart>0&&(a.openStart=this.openStart),this.openEnd>0&&(a.openEnd=this.openEnd),a}static fromJSON(a,b){if(!b)return k.empty;let c=b.openStart||0,d=b.openEnd||0;if("number"!=typeof c||"number"!=typeof d)throw RangeError("Invalid input for Slice.fromJSON");return new k(e.fromJSON(a,b.content),c,d)}static maxOpen(a,b=!0){let c=0,d=0;for(let d=a.firstChild;d&&!d.isLeaf&&(b||!d.type.spec.isolating);d=d.firstChild)c++;for(let c=a.lastChild;c&&!c.isLeaf&&(b||!c.type.spec.isolating);c=c.lastChild)d++;return new k(a,c,d)}}function l(a,b){if(!b.type.compatibleContent(a.type))throw new j("Cannot join "+b.type.name+" onto "+a.type.name)}function m(a,b,c){let d=a.node(c);return l(d,b.node(c)),d}function n(a,b){let c=b.length-1;c>=0&&a.isText&&a.sameMarkup(b[c])?b[c]=a.withText(b[c].text+a.text):b.push(a)}function o(a,b,c,d){let e=(b||a).node(c),f=0,g=b?b.index(c):e.childCount;a&&(f=a.index(c),a.depth>c?f++:a.textOffset&&(n(a.nodeAfter,d),f++));for(let a=f;a<g;a++)n(e.child(a),d);b&&b.depth==c&&b.textOffset&&n(b.nodeBefore,d)}function p(a,b){return a.type.checkContent(b),a.copy(b)}function q(a,b,c){let d=[];return o(null,a,c,d),a.depth>c&&n(p(m(a,b,c+1),q(a,b,c+1)),d),o(b,null,c,d),new e(d)}k.empty=new k(e.empty,0,0);class r{constructor(a,b,c){this.pos=a,this.path=b,this.parentOffset=c,this.depth=b.length/3-1}resolveDepth(a){return null==a?this.depth:a<0?this.depth+a:a}get parent(){return this.node(this.depth)}get doc(){return this.node(0)}node(a){return this.path[3*this.resolveDepth(a)]}index(a){return this.path[3*this.resolveDepth(a)+1]}indexAfter(a){return a=this.resolveDepth(a),this.index(a)+(a!=this.depth||this.textOffset?1:0)}start(a){return 0==(a=this.resolveDepth(a))?0:this.path[3*a-1]+1}end(a){return a=this.resolveDepth(a),this.start(a)+this.node(a).content.size}before(a){if(!(a=this.resolveDepth(a)))throw RangeError("There is no position before the top-level node");return a==this.depth+1?this.pos:this.path[3*a-1]}after(a){if(!(a=this.resolveDepth(a)))throw RangeError("There is no position after the top-level node");return a==this.depth+1?this.pos:this.path[3*a-1]+this.path[3*a].nodeSize}get textOffset(){return this.pos-this.path[this.path.length-1]}get nodeAfter(){let a=this.parent,b=this.index(this.depth);if(b==a.childCount)return null;let c=this.pos-this.path[this.path.length-1],d=a.child(b);return c?a.child(b).cut(c):d}get nodeBefore(){let a=this.index(this.depth),b=this.pos-this.path[this.path.length-1];return b?this.parent.child(a).cut(0,b):0==a?null:this.parent.child(a-1)}posAtIndex(a,b){b=this.resolveDepth(b);let c=this.path[3*b],d=0==b?0:this.path[3*b-1]+1;for(let b=0;b<a;b++)d+=c.child(b).nodeSize;return d}marks(){let a=this.parent,b=this.index();if(0==a.content.size)return i.none;if(this.textOffset)return a.child(b).marks;let c=a.maybeChild(b-1),d=a.maybeChild(b);if(!c){let a=c;c=d,d=a}let e=c.marks;for(var f=0;f<e.length;f++)!1!==e[f].type.spec.inclusive||d&&e[f].isInSet(d.marks)||(e=e[f--].removeFromSet(e));return e}marksAcross(a){let b=this.parent.maybeChild(this.index());if(!b||!b.isInline)return null;let c=b.marks,d=a.parent.maybeChild(a.index());for(var e=0;e<c.length;e++)!1!==c[e].type.spec.inclusive||d&&c[e].isInSet(d.marks)||(c=c[e--].removeFromSet(c));return c}sharedDepth(a){for(let b=this.depth;b>0;b--)if(this.start(b)<=a&&this.end(b)>=a)return b;return 0}blockRange(a=this,b){if(a.pos<this.pos)return a.blockRange(this);for(let c=this.depth-(this.parent.inlineContent||this.pos==a.pos?1:0);c>=0;c--)if(a.pos<=this.end(c)&&(!b||b(this.node(c))))return new v(this,a,c);return null}sameParent(a){return this.pos-this.parentOffset==a.pos-a.parentOffset}max(a){return a.pos>this.pos?a:this}min(a){return a.pos<this.pos?a:this}toString(){let a="";for(let b=1;b<=this.depth;b++)a+=(a?"/":"")+this.node(b).type.name+"_"+this.index(b-1);return a+":"+this.parentOffset}static resolve(a,b){if(!(b>=0&&b<=a.content.size))throw RangeError("Position "+b+" out of range");let c=[],d=0,e=b;for(let b=a;;){let{index:a,offset:f}=b.content.findIndex(e),g=e-f;if(c.push(b,a,d+f),!g||(b=b.child(a)).isText)break;e=g-1,d+=f+1}return new r(b,c,e)}static resolveCached(a,b){let c=u.get(a);if(c)for(let a=0;a<c.elts.length;a++){let d=c.elts[a];if(d.pos==b)return d}else u.set(a,c=new s);let d=c.elts[c.i]=r.resolve(a,b);return c.i=(c.i+1)%t,d}}class s{constructor(){this.elts=[],this.i=0}}let t=12,u=new WeakMap;class v{constructor(a,b,c){this.$from=a,this.$to=b,this.depth=c}get start(){return this.$from.before(this.depth+1)}get end(){return this.$to.after(this.depth+1)}get parent(){return this.$from.node(this.depth)}get startIndex(){return this.$from.index(this.depth)}get endIndex(){return this.$to.indexAfter(this.depth)}}let w=Object.create(null);class x{constructor(a,b,c,d=i.none){this.type=a,this.attrs=b,this.marks=d,this.content=c||e.empty}get children(){return this.content.content}get nodeSize(){return this.isLeaf?1:2+this.content.size}get childCount(){return this.content.childCount}child(a){return this.content.child(a)}maybeChild(a){return this.content.maybeChild(a)}forEach(a){this.content.forEach(a)}nodesBetween(a,b,c,d=0){this.content.nodesBetween(a,b,c,d,this)}descendants(a){this.nodesBetween(0,this.content.size,a)}get textContent(){return this.isLeaf&&this.type.spec.leafText?this.type.spec.leafText(this):this.textBetween(0,this.content.size,"")}textBetween(a,b,c,d){return this.content.textBetween(a,b,c,d)}get firstChild(){return this.content.firstChild}get lastChild(){return this.content.lastChild}eq(a){return this==a||this.sameMarkup(a)&&this.content.eq(a.content)}sameMarkup(a){return this.hasMarkup(a.type,a.attrs,a.marks)}hasMarkup(a,b,c){return this.type==a&&h(this.attrs,b||a.defaultAttrs||w)&&i.sameSet(this.marks,c||i.none)}copy(a=null){return a==this.content?this:new x(this.type,this.attrs,a,this.marks)}mark(a){return a==this.marks?this:new x(this.type,this.attrs,this.content,a)}cut(a,b=this.content.size){return 0==a&&b==this.content.size?this:this.copy(this.content.cut(a,b))}slice(a,b=this.content.size,c=!1){if(a==b)return k.empty;let d=this.resolve(a),e=this.resolve(b),f=c?0:d.sharedDepth(b),g=d.start(f);return new k(d.node(f).content.cut(d.pos-g,e.pos-g),d.depth-f,e.depth-f)}replace(a,b,c){var d=this.resolve(a),f=this.resolve(b);if(c.openStart>d.depth)throw new j("Inserted content deeper than insertion position");if(d.depth-c.openStart!=f.depth-c.openEnd)throw new j("Inconsistent open depths");return function a(b,c,d,f){let g=b.index(f),h=b.node(f);if(g==c.index(f)&&f<b.depth-d.openStart){let e=a(b,c,d,f+1);return h.copy(h.content.replaceChild(g,e))}if(!d.content.size)return p(h,q(b,c,f));if(d.openStart||d.openEnd||b.depth!=f||c.depth!=f){let{start:a,end:g}=function(a,b){let c=b.depth-a.openStart,d=b.node(c).copy(a.content);for(let a=c-1;a>=0;a--)d=b.node(a).copy(e.from(d));return{start:d.resolveNoCache(a.openStart+c),end:d.resolveNoCache(d.content.size-a.openEnd-c)}}(d,b);return p(h,function a(b,c,d,f,g){let h=b.depth>g&&m(b,c,g+1),i=f.depth>g&&m(d,f,g+1),j=[];return o(null,b,g,j),h&&i&&c.index(g)==d.index(g)?(l(h,i),n(p(h,a(b,c,d,f,g+1)),j)):(h&&n(p(h,q(b,c,g+1)),j),o(c,d,g,j),i&&n(p(i,q(d,f,g+1)),j)),o(f,null,g,j),new e(j)}(b,a,g,c,f))}{let a=b.parent,e=a.content;return p(a,e.cut(0,b.parentOffset).append(d.content).append(e.cut(c.parentOffset)))}}(d,f,c,0)}nodeAt(a){for(let b=this;;){let{index:c,offset:d}=b.content.findIndex(a);if(!(b=b.maybeChild(c)))return null;if(d==a||b.isText)return b;a-=d+1}}childAfter(a){let{index:b,offset:c}=this.content.findIndex(a);return{node:this.content.maybeChild(b),index:b,offset:c}}childBefore(a){if(0==a)return{node:null,index:0,offset:0};let{index:b,offset:c}=this.content.findIndex(a);if(c<a)return{node:this.content.child(b),index:b,offset:c};let d=this.content.child(b-1);return{node:d,index:b-1,offset:c-d.nodeSize}}resolve(a){return r.resolveCached(this,a)}resolveNoCache(a){return r.resolve(this,a)}rangeHasMark(a,b,c){let d=!1;return b>a&&this.nodesBetween(a,b,a=>(c.isInSet(a.marks)&&(d=!0),!d)),d}get isBlock(){return this.type.isBlock}get isTextblock(){return this.type.isTextblock}get inlineContent(){return this.type.inlineContent}get isInline(){return this.type.isInline}get isText(){return this.type.isText}get isLeaf(){return this.type.isLeaf}get isAtom(){return this.type.isAtom}toString(){if(this.type.spec.toDebugString)return this.type.spec.toDebugString(this);let a=this.type.name;return this.content.size&&(a+="("+this.content.toStringInner()+")"),z(this.marks,a)}contentMatchAt(a){let b=this.type.contentMatch.matchFragment(this.content,0,a);if(!b)throw Error("Called contentMatchAt on a node with invalid content");return b}canReplace(a,b,c=e.empty,d=0,f=c.childCount){let g=this.contentMatchAt(a).matchFragment(c,d,f),h=g&&g.matchFragment(this.content,b);if(!h||!h.validEnd)return!1;for(let a=d;a<f;a++)if(!this.type.allowsMarks(c.child(a).marks))return!1;return!0}canReplaceWith(a,b,c,d){if(d&&!this.type.allowsMarks(d))return!1;let e=this.contentMatchAt(a).matchType(c),f=e&&e.matchFragment(this.content,b);return!!f&&f.validEnd}canAppend(a){return a.content.size?this.canReplace(this.childCount,this.childCount,a.content):this.type.compatibleContent(a.type)}check(){this.type.checkContent(this.content),this.type.checkAttrs(this.attrs);let a=i.none;for(let b=0;b<this.marks.length;b++){let c=this.marks[b];c.type.checkAttrs(c.attrs),a=c.addToSet(a)}if(!i.sameSet(a,this.marks))throw RangeError(`Invalid collection of marks for node ${this.type.name}: ${this.marks.map(a=>a.type.name)}`);this.content.forEach(a=>a.check())}toJSON(){let a={type:this.type.name};for(let b in this.attrs){a.attrs=this.attrs;break}return this.content.size&&(a.content=this.content.toJSON()),this.marks.length&&(a.marks=this.marks.map(a=>a.toJSON())),a}static fromJSON(a,b){let c;if(!b)throw RangeError("Invalid input for Node.fromJSON");if(b.marks){if(!Array.isArray(b.marks))throw RangeError("Invalid mark data for Node.fromJSON");c=b.marks.map(a.markFromJSON)}if("text"==b.type){if("string"!=typeof b.text)throw RangeError("Invalid text node in JSON");return a.text(b.text,c)}let d=e.fromJSON(a,b.content),f=a.nodeType(b.type).create(b.attrs,d,c);return f.type.checkAttrs(f.attrs),f}}x.prototype.text=void 0;class y extends x{constructor(a,b,c,d){if(super(a,b,null,d),!c)throw RangeError("Empty text nodes are not allowed");this.text=c}toString(){return this.type.spec.toDebugString?this.type.spec.toDebugString(this):z(this.marks,JSON.stringify(this.text))}get textContent(){return this.text}textBetween(a,b){return this.text.slice(a,b)}get nodeSize(){return this.text.length}mark(a){return a==this.marks?this:new y(this.type,this.attrs,this.text,a)}withText(a){return a==this.text?this:new y(this.type,this.attrs,a,this.marks)}cut(a=0,b=this.text.length){return 0==a&&b==this.text.length?this:this.withText(this.text.slice(a,b))}eq(a){return this.sameMarkup(a)&&this.text==a.text}toJSON(){let a=super.toJSON();return a.text=this.text,a}}function z(a,b){for(let c=a.length-1;c>=0;c--)b=a[c].type.name+"("+b+")";return b}class A{constructor(a){this.validEnd=a,this.next=[],this.wrapCache=[]}static parse(a,b){var c;let d,e=new B(a,b);if(null==e.next)return A.empty;let f=function a(b){let c=[];do c.push(function(b){let c=[];do c.push(function(b){let c=function(b){if(b.eat("(")){let c=a(b);return b.eat(")")||b.err("Missing closing paren"),c}if(/\W/.test(b.next))b.err("Unexpected token '"+b.next+"'");else{let a=(function(a,b){let c=a.nodeTypes,d=c[b];if(d)return[d];let e=[];for(let a in c){let d=c[a];d.isInGroup(b)&&e.push(d)}return 0==e.length&&a.err("No node type or group '"+b+"' found"),e})(b,b.next).map(a=>(null==b.inline?b.inline=a.isInline:b.inline!=a.isInline&&b.err("Mixing inline and block content"),{type:"name",value:a}));return b.pos++,1==a.length?a[0]:{type:"choice",exprs:a}}}(b);for(;;)if(b.eat("+"))c={type:"plus",expr:c};else if(b.eat("*"))c={type:"star",expr:c};else if(b.eat("?"))c={type:"opt",expr:c};else if(b.eat("{"))c=function(a,b){let c=C(a),d=c;return a.eat(",")&&(d="}"!=a.next?C(a):-1),a.eat("}")||a.err("Unclosed braced range"),{type:"range",min:c,max:d,expr:b}}(b,c);else break;return c}(b));while(b.next&&")"!=b.next&&"|"!=b.next);return 1==c.length?c[0]:{type:"seq",exprs:c}}(b));while(b.eat("|"));return 1==c.length?c[0]:{type:"choice",exprs:c}}(e);e.next&&e.err("Unexpected trailing text");let g=(c=function(a){let b=[[]];return e(function a(b,f){if("choice"==b.type)return b.exprs.reduce((b,c)=>b.concat(a(c,f)),[]);if("seq"==b.type)for(let d=0;;d++){let g=a(b.exprs[d],f);if(d==b.exprs.length-1)return g;e(g,f=c())}else if("star"==b.type){let g=c();return d(f,g),e(a(b.expr,g),g),[d(g)]}else if("plus"==b.type){let g=c();return e(a(b.expr,f),g),e(a(b.expr,g),g),[d(g)]}else if("opt"==b.type)return[d(f)].concat(a(b.expr,f));else if("range"==b.type){let g=f;for(let d=0;d<b.min;d++){let d=c();e(a(b.expr,g),d),g=d}if(-1==b.max)e(a(b.expr,g),g);else for(let f=b.min;f<b.max;f++){let f=c();d(g,f),e(a(b.expr,g),f),g=f}return[d(g)]}else if("name"==b.type)return[d(f,void 0,b.value)];else throw Error("Unknown expr type")}(a,0),c()),b;function c(){return b.push([])-1}function d(a,c,d){let e={term:d,to:c};return b[a].push(e),e}function e(a,b){a.forEach(a=>a.to=b)}}(f),d=Object.create(null),function a(b){let e=[];b.forEach(a=>{c[a].forEach(({term:a,to:b})=>{let d;if(a){for(let b=0;b<e.length;b++)e[b][0]==a&&(d=e[b][1]);E(c,b).forEach(b=>{d||e.push([a,d=[]]),-1==d.indexOf(b)&&d.push(b)})}})});let f=d[b.join(",")]=new A(b.indexOf(c.length-1)>-1);for(let b=0;b<e.length;b++){let c=e[b][1].sort(D);f.next.push({type:e[b][0],next:d[c.join(",")]||a(c)})}return f}(E(c,0)));return function(a,b){for(let c=0,d=[a];c<d.length;c++){let a=d[c],e=!a.validEnd,f=[];for(let b=0;b<a.next.length;b++){let{type:c,next:g}=a.next[b];f.push(c.name),e&&!(c.isText||c.hasRequiredAttrs())&&(e=!1),-1==d.indexOf(g)&&d.push(g)}e&&b.err("Only non-generatable nodes ("+f.join(", ")+") in a required position (see https://prosemirror.net/docs/guide/#generatable)")}}(g,e),g}matchType(a){for(let b=0;b<this.next.length;b++)if(this.next[b].type==a)return this.next[b].next;return null}matchFragment(a,b=0,c=a.childCount){let d=this;for(let e=b;d&&e<c;e++)d=d.matchType(a.child(e).type);return d}get inlineContent(){return 0!=this.next.length&&this.next[0].type.isInline}get defaultType(){for(let a=0;a<this.next.length;a++){let{type:b}=this.next[a];if(!(b.isText||b.hasRequiredAttrs()))return b}return null}compatible(a){for(let b=0;b<this.next.length;b++)for(let c=0;c<a.next.length;c++)if(this.next[b].type==a.next[c].type)return!0;return!1}fillBefore(a,b=!1,c=0){let d=[this];return function f(g,h){let i=g.matchFragment(a,c);if(i&&(!b||i.validEnd))return e.from(h.map(a=>a.createAndFill()));for(let a=0;a<g.next.length;a++){let{type:b,next:c}=g.next[a];if(!(b.isText||b.hasRequiredAttrs())&&-1==d.indexOf(c)){d.push(c);let a=f(c,h.concat(b));if(a)return a}}return null}(this,[])}findWrapping(a){for(let b=0;b<this.wrapCache.length;b+=2)if(this.wrapCache[b]==a)return this.wrapCache[b+1];let b=this.computeWrapping(a);return this.wrapCache.push(a,b),b}computeWrapping(a){let b=Object.create(null),c=[{match:this,type:null,via:null}];for(;c.length;){let d=c.shift(),e=d.match;if(e.matchType(a)){let a=[];for(let b=d;b.type;b=b.via)a.push(b.type);return a.reverse()}for(let a=0;a<e.next.length;a++){let{type:f,next:g}=e.next[a];f.isLeaf||f.hasRequiredAttrs()||f.name in b||d.type&&!g.validEnd||(c.push({match:f.contentMatch,type:f,via:d}),b[f.name]=!0)}}return null}get edgeCount(){return this.next.length}edge(a){if(a>=this.next.length)throw RangeError(`There's no ${a}th edge in this content match`);return this.next[a]}toString(){let a=[];return!function b(c){a.push(c);for(let d=0;d<c.next.length;d++)-1==a.indexOf(c.next[d].next)&&b(c.next[d].next)}(this),a.map((b,c)=>{let d=c+(b.validEnd?"*":" ")+" ";for(let c=0;c<b.next.length;c++)d+=(c?", ":"")+b.next[c].type.name+"->"+a.indexOf(b.next[c].next);return d}).join("\n")}}A.empty=new A(!0);class B{constructor(a,b){this.string=a,this.nodeTypes=b,this.inline=null,this.pos=0,this.tokens=a.split(/\s*(?=\b|\W|$)/),""==this.tokens[this.tokens.length-1]&&this.tokens.pop(),""==this.tokens[0]&&this.tokens.shift()}get next(){return this.tokens[this.pos]}eat(a){return this.next==a&&(this.pos++||!0)}err(a){throw SyntaxError(a+" (in content expression '"+this.string+"')")}}function C(a){/\D/.test(a.next)&&a.err("Expected number, got '"+a.next+"'");let b=Number(a.next);return a.pos++,b}function D(a,b){return b-a}function E(a,b){let c=[];return function b(d){let e=a[d];if(1==e.length&&!e[0].term)return b(e[0].to);c.push(d);for(let a=0;a<e.length;a++){let{term:d,to:f}=e[a];d||-1!=c.indexOf(f)||b(f)}}(b),c.sort(D)}function F(a){let b=Object.create(null);for(let c in a){let d=a[c];if(!d.hasDefault)return null;b[c]=d.default}return b}function G(a,b){let c=Object.create(null);for(let d in a){let e=b&&b[d];if(void 0===e){let b=a[d];if(b.hasDefault)e=b.default;else throw RangeError("No value supplied for attribute "+d)}c[d]=e}return c}function H(a,b,c,d){for(let d in b)if(!(d in a))throw RangeError(`Unsupported attribute ${d} for ${c} of type ${d}`);for(let c in a){let d=a[c];d.validate&&d.validate(b[c])}}function I(a,b){let c=Object.create(null);if(b)for(let d in b)c[d]=new K(a,d,b[d]);return c}class J{constructor(a,b,c){this.name=a,this.schema=b,this.spec=c,this.markSet=null,this.groups=c.group?c.group.split(" "):[],this.attrs=I(a,c.attrs),this.defaultAttrs=F(this.attrs),this.contentMatch=null,this.inlineContent=null,this.isBlock=!(c.inline||"text"==a),this.isText="text"==a}get isInline(){return!this.isBlock}get isTextblock(){return this.isBlock&&this.inlineContent}get isLeaf(){return this.contentMatch==A.empty}get isAtom(){return this.isLeaf||!!this.spec.atom}isInGroup(a){return this.groups.indexOf(a)>-1}get whitespace(){return this.spec.whitespace||(this.spec.code?"pre":"normal")}hasRequiredAttrs(){for(let a in this.attrs)if(this.attrs[a].isRequired)return!0;return!1}compatibleContent(a){return this==a||this.contentMatch.compatible(a.contentMatch)}computeAttrs(a){return!a&&this.defaultAttrs?this.defaultAttrs:G(this.attrs,a)}create(a=null,b,c){if(this.isText)throw Error("NodeType.create can't construct text nodes");return new x(this,this.computeAttrs(a),e.from(b),i.setFrom(c))}createChecked(a=null,b,c){return b=e.from(b),this.checkContent(b),new x(this,this.computeAttrs(a),b,i.setFrom(c))}createAndFill(a=null,b,c){if(a=this.computeAttrs(a),(b=e.from(b)).size){let a=this.contentMatch.fillBefore(b);if(!a)return null;b=a.append(b)}let d=this.contentMatch.matchFragment(b),f=d&&d.fillBefore(e.empty,!0);return f?new x(this,a,b.append(f),i.setFrom(c)):null}validContent(a){let b=this.contentMatch.matchFragment(a);if(!b||!b.validEnd)return!1;for(let b=0;b<a.childCount;b++)if(!this.allowsMarks(a.child(b).marks))return!1;return!0}checkContent(a){if(!this.validContent(a))throw RangeError(`Invalid content for node ${this.name}: ${a.toString().slice(0,50)}`)}checkAttrs(a){H(this.attrs,a,"node",this.name)}allowsMarkType(a){return null==this.markSet||this.markSet.indexOf(a)>-1}allowsMarks(a){if(null==this.markSet)return!0;for(let b=0;b<a.length;b++)if(!this.allowsMarkType(a[b].type))return!1;return!0}allowedMarks(a){let b;if(null==this.markSet)return a;for(let c=0;c<a.length;c++)this.allowsMarkType(a[c].type)?b&&b.push(a[c]):b||(b=a.slice(0,c));return b?b.length?b:i.none:a}static compile(a,b){let c=Object.create(null);a.forEach((a,d)=>c[a]=new J(a,b,d));let d=b.spec.topNode||"doc";if(!c[d])throw RangeError("Schema is missing its top node type ('"+d+"')");if(!c.text)throw RangeError("Every schema needs a 'text' type");for(let a in c.text.attrs)throw RangeError("The text node type should not have attributes");return c}}class K{constructor(a,b,c){this.hasDefault=Object.prototype.hasOwnProperty.call(c,"default"),this.default=c.default,this.validate="string"==typeof c.validate?function(a,b,c){let d=c.split("|");return c=>{let e=null===c?"null":typeof c;if(0>d.indexOf(e))throw RangeError(`Expected value of type ${d} for attribute ${b} on type ${a}, got ${e}`)}}(a,b,c.validate):c.validate}get isRequired(){return!this.hasDefault}}class L{constructor(a,b,c,d){this.name=a,this.rank=b,this.schema=c,this.spec=d,this.attrs=I(a,d.attrs),this.excluded=null;let e=F(this.attrs);this.instance=e?new i(this,e):null}create(a=null){return!a&&this.instance?this.instance:new i(this,G(this.attrs,a))}static compile(a,b){let c=Object.create(null),d=0;return a.forEach((a,e)=>c[a]=new L(a,d++,b,e)),c}removeFromSet(a){for(var b=0;b<a.length;b++)a[b].type==this&&(a=a.slice(0,b).concat(a.slice(b+1)),b--);return a}isInSet(a){for(let b=0;b<a.length;b++)if(a[b].type==this)return a[b]}checkAttrs(a){H(this.attrs,a,"mark",this.name)}excludes(a){return this.excluded.indexOf(a)>-1}}class M{constructor(a){this.linebreakReplacement=null,this.cached=Object.create(null);let b=this.spec={};for(let c in a)b[c]=a[c];b.nodes=d.from(a.nodes),b.marks=d.from(a.marks||{}),this.nodes=J.compile(this.spec.nodes,this),this.marks=L.compile(this.spec.marks,this);let c=Object.create(null);for(let a in this.nodes){if(a in this.marks)throw RangeError(a+" can not be both a node and a mark");let b=this.nodes[a],d=b.spec.content||"",e=b.spec.marks;if(b.contentMatch=c[d]||(c[d]=A.parse(d,this.nodes)),b.inlineContent=b.contentMatch.inlineContent,b.spec.linebreakReplacement){if(this.linebreakReplacement)throw RangeError("Multiple linebreak nodes defined");if(!b.isInline||!b.isLeaf)throw RangeError("Linebreak replacement nodes must be inline leaf nodes");this.linebreakReplacement=b}b.markSet="_"==e?null:e?N(this,e.split(" ")):""!=e&&b.inlineContent?null:[]}for(let a in this.marks){let b=this.marks[a],c=b.spec.excludes;b.excluded=null==c?[b]:""==c?[]:N(this,c.split(" "))}this.nodeFromJSON=a=>x.fromJSON(this,a),this.markFromJSON=a=>i.fromJSON(this,a),this.topNodeType=this.nodes[this.spec.topNode||"doc"],this.cached.wrappings=Object.create(null)}node(a,b=null,c,d){if("string"==typeof a)a=this.nodeType(a);else if(a instanceof J){if(a.schema!=this)throw RangeError("Node type from different schema used ("+a.name+")")}else throw RangeError("Invalid node type: "+a);return a.createChecked(b,c,d)}text(a,b){let c=this.nodes.text;return new y(c,c.defaultAttrs,a,i.setFrom(b))}mark(a,b){return"string"==typeof a&&(a=this.marks[a]),a.create(b)}nodeType(a){let b=this.nodes[a];if(!b)throw RangeError("Unknown node type: "+a);return b}}function N(a,b){let c=[];for(let d=0;d<b.length;d++){let e=b[d],f=a.marks[e],g=f;if(f)c.push(f);else for(let b in a.marks){let d=a.marks[b];("_"==e||d.spec.group&&d.spec.group.split(" ").indexOf(e)>-1)&&c.push(g=d)}if(!g)throw SyntaxError("Unknown mark type: '"+b[d]+"'")}return c}class O{constructor(a,b){this.schema=a,this.rules=b,this.tags=[],this.styles=[];let c=this.matchedStyles=[];b.forEach(a=>{if(null!=a.tag)this.tags.push(a);else if(null!=a.style){let b=/[^=]*/.exec(a.style)[0];0>c.indexOf(b)&&c.push(b),this.styles.push(a)}}),this.normalizeLists=!this.tags.some(b=>{if(!/^(ul|ol)\b/.test(b.tag)||!b.node)return!1;let c=a.nodes[b.node];return c.contentMatch.matchType(c)})}parse(a,b={}){let c=new U(this,b,!1);return c.addAll(a,i.none,b.from,b.to),c.finish()}parseSlice(a,b={}){let c=new U(this,b,!0);return c.addAll(a,i.none,b.from,b.to),k.maxOpen(c.finish())}matchTag(a,b,c){for(let f=c?this.tags.indexOf(c)+1:0;f<this.tags.length;f++){var d,e;let c=this.tags[f];if(d=a,e=c.tag,(d.matches||d.msMatchesSelector||d.webkitMatchesSelector||d.mozMatchesSelector).call(d,e)&&(void 0===c.namespace||a.namespaceURI==c.namespace)&&(!c.context||b.matchesContext(c.context))){if(c.getAttrs){let b=c.getAttrs(a);if(!1===b)continue;c.attrs=b||void 0}return c}}}matchStyle(a,b,c,d){for(let e=d?this.styles.indexOf(d)+1:0;e<this.styles.length;e++){let d=this.styles[e],f=d.style;if(0==f.indexOf(a)&&(!d.context||c.matchesContext(d.context))&&(!(f.length>a.length)||61==f.charCodeAt(a.length)&&f.slice(a.length+1)==b)){if(d.getAttrs){let a=d.getAttrs(b);if(!1===a)continue;d.attrs=a||void 0}return d}}}static schemaRules(a){let b=[];function c(a){let c=null==a.priority?50:a.priority,d=0;for(;d<b.length;d++){let a=b[d];if((null==a.priority?50:a.priority)<c)break}b.splice(d,0,a)}for(let b in a.marks){let d=a.marks[b].spec.parseDOM;d&&d.forEach(a=>{c(a=V(a)),a.mark||a.ignore||a.clearMark||(a.mark=b)})}for(let b in a.nodes){let d=a.nodes[b].spec.parseDOM;d&&d.forEach(a=>{c(a=V(a)),a.node||a.ignore||a.mark||(a.node=b)})}return b}static fromSchema(a){return a.cached.domParser||(a.cached.domParser=new O(a,O.schemaRules(a)))}}let P={address:!0,article:!0,aside:!0,blockquote:!0,canvas:!0,dd:!0,div:!0,dl:!0,fieldset:!0,figcaption:!0,figure:!0,footer:!0,form:!0,h1:!0,h2:!0,h3:!0,h4:!0,h5:!0,h6:!0,header:!0,hgroup:!0,hr:!0,li:!0,noscript:!0,ol:!0,output:!0,p:!0,pre:!0,section:!0,table:!0,tfoot:!0,ul:!0},Q={head:!0,noscript:!0,object:!0,script:!0,style:!0,title:!0},R={ol:!0,ul:!0};function S(a,b,c){return null!=b?!!b|2*("full"===b):a&&"pre"==a.whitespace?3:-5&c}class T{constructor(a,b,c,d,e,f){this.type=a,this.attrs=b,this.marks=c,this.solid=d,this.options=f,this.content=[],this.activeMarks=i.none,this.match=e||(4&f?null:a.contentMatch)}findWrapping(a){if(!this.match){if(!this.type)return[];let b=this.type.contentMatch.fillBefore(e.from(a));if(b)this.match=this.type.contentMatch.matchFragment(b);else{let b=this.type.contentMatch,c;return(c=b.findWrapping(a.type))?(this.match=b,c):null}}return this.match.findWrapping(a.type)}finish(a){if(!(1&this.options)){let a=this.content[this.content.length-1],b;a&&a.isText&&(b=/[ \t\r\n\u000c]+$/.exec(a.text))&&(a.text.length==b[0].length?this.content.pop():this.content[this.content.length-1]=a.withText(a.text.slice(0,a.text.length-b[0].length)))}let b=e.from(this.content);return!a&&this.match&&(b=b.append(this.match.fillBefore(e.empty,!0))),this.type?this.type.create(this.attrs,b,this.marks):b}inlineContext(a){return this.type?this.type.inlineContent:this.content.length?this.content[0].isInline:a.parentNode&&!P.hasOwnProperty(a.parentNode.nodeName.toLowerCase())}}class U{constructor(a,b,c){this.parser=a,this.options=b,this.isOpen=c,this.open=0,this.localPreserveWS=!1;let d=b.topNode,e,f=S(null,b.preserveWhitespace,0)|4*!!c;e=d?new T(d.type,d.attrs,i.none,!0,b.topMatch||d.type.contentMatch,f):c?new T(null,null,i.none,!0,null,f):new T(a.schema.topNodeType,null,i.none,!0,null,f),this.nodes=[e],this.find=b.findPositions,this.needsBlock=!1}get top(){return this.nodes[this.open]}addDOM(a,b){3==a.nodeType?this.addTextNode(a,b):1==a.nodeType&&this.addElement(a,b)}addTextNode(a,b){let c=a.nodeValue,d=this.top,e=2&d.options?"full":this.localPreserveWS||(1&d.options)>0;if("full"===e||d.inlineContext(a)||/[^ \t\r\n\u000c]/.test(c)){if(e)c="full"!==e?c.replace(/\r?\n|\r/g," "):c.replace(/\r\n?/g,"\n");else if(c=c.replace(/[ \t\r\n\u000c]+/g," "),/^[ \t\r\n\u000c]/.test(c)&&this.open==this.nodes.length-1){let b=d.content[d.content.length-1],e=a.previousSibling;(!b||e&&"BR"==e.nodeName||b.isText&&/[ \t\r\n\u000c]$/.test(b.text))&&(c=c.slice(1))}c&&this.insertNode(this.parser.schema.text(c),b,!/\S/.test(c)),this.findInText(a)}else this.findInside(a)}addElement(a,b,c){let d=this.localPreserveWS,e=this.top;("PRE"==a.tagName||/pre/.test(a.style&&a.style.whiteSpace))&&(this.localPreserveWS=!0);let f=a.nodeName.toLowerCase(),g;R.hasOwnProperty(f)&&this.parser.normalizeLists&&function(a){for(let b=a.firstChild,c=null;b;b=b.nextSibling){let a=1==b.nodeType?b.nodeName.toLowerCase():null;a&&R.hasOwnProperty(a)&&c?(c.appendChild(b),b=c):"li"==a?c=b:a&&(c=null)}}(a);let h=this.options.ruleFromNode&&this.options.ruleFromNode(a)||(g=this.parser.matchTag(a,this,c));c:if(h?h.ignore:Q.hasOwnProperty(f))this.findInside(a),this.ignoreFallback(a,b);else if(!h||h.skip||h.closeParent){h&&h.closeParent?this.open=Math.max(0,this.open-1):h&&h.skip.nodeType&&(a=h.skip);let c,d=this.needsBlock;if(P.hasOwnProperty(f))e.content.length&&e.content[0].isInline&&this.open&&(this.open--,e=this.top),c=!0,e.type||(this.needsBlock=!0);else if(!a.firstChild){this.leafFallback(a,b);break c}let g=h&&h.skip?b:this.readStyles(a,b);g&&this.addAll(a,g),c&&this.sync(e),this.needsBlock=d}else{let c=this.readStyles(a,b);c&&this.addElementByRule(a,h,c,!1===h.consuming?g:void 0)}this.localPreserveWS=d}leafFallback(a,b){"BR"==a.nodeName&&this.top.type&&this.top.type.inlineContent&&this.addTextNode(a.ownerDocument.createTextNode("\n"),b)}ignoreFallback(a,b){"BR"!=a.nodeName||this.top.type&&this.top.type.inlineContent||this.findPlace(this.parser.schema.text("-"),b,!0)}readStyles(a,b){let c=a.style;if(c&&c.length)for(let a=0;a<this.parser.matchedStyles.length;a++){let d=this.parser.matchedStyles[a],e=c.getPropertyValue(d);if(e)for(let a;;){let c=this.parser.matchStyle(d,e,this,a);if(!c)break;if(c.ignore)return null;if(b=c.clearMark?b.filter(a=>!c.clearMark(a)):b.concat(this.parser.schema.marks[c.mark].create(c.attrs)),!1===c.consuming)a=c;else break}}return b}addElementByRule(a,b,c,d){let e,f;if(b.node)if((f=this.parser.schema.nodes[b.node]).isLeaf)this.insertNode(f.create(b.attrs),c,"BR"==a.nodeName)||this.leafFallback(a,c);else{let a=this.enter(f,b.attrs||null,c,b.preserveWhitespace);a&&(e=!0,c=a)}else{let a=this.parser.schema.marks[b.mark];c=c.concat(a.create(b.attrs))}let g=this.top;if(f&&f.isLeaf)this.findInside(a);else if(d)this.addElement(a,c,d);else if(b.getContent)this.findInside(a),b.getContent(a,this.parser.schema).forEach(a=>this.insertNode(a,c,!1));else{let d=a;"string"==typeof b.contentElement?d=a.querySelector(b.contentElement):"function"==typeof b.contentElement?d=b.contentElement(a):b.contentElement&&(d=b.contentElement),this.findAround(a,d,!0),this.addAll(d,c),this.findAround(a,d,!1)}e&&this.sync(g)&&this.open--}addAll(a,b,c,d){let e=c||0;for(let f=c?a.childNodes[c]:a.firstChild,g=null==d?null:a.childNodes[d];f!=g;f=f.nextSibling,++e)this.findAtPoint(a,e),this.addDOM(f,b);this.findAtPoint(a,e)}findPlace(a,b,c){let d,e;for(let b=this.open,f=0;b>=0;b--){let g=this.nodes[b],h=g.findWrapping(a);if(h&&(!d||d.length>h.length+f)&&(d=h,e=g,!h.length))break;if(g.solid){if(c)break;f+=2}}if(!d)return null;this.sync(e);for(let a=0;a<d.length;a++)b=this.enterInner(d[a],null,b,!1);return b}insertNode(a,b,c){if(a.isInline&&this.needsBlock&&!this.top.type){let a=this.textblockFromContext();a&&(b=this.enterInner(a,null,b))}let d=this.findPlace(a,b,c);if(d){this.closeExtra();let b=this.top;b.match&&(b.match=b.match.matchType(a.type));let c=i.none;for(let e of d.concat(a.marks))(b.type?b.type.allowsMarkType(e.type):W(e.type,a.type))&&(c=e.addToSet(c));return b.content.push(a.mark(c)),!0}return!1}enter(a,b,c,d){let e=this.findPlace(a.create(b),c,!1);return e&&(e=this.enterInner(a,b,c,!0,d)),e}enterInner(a,b,c,d=!1,e){this.closeExtra();let f=this.top;f.match=f.match&&f.match.matchType(a);let g=S(a,e,f.options);4&f.options&&0==f.content.length&&(g|=4);let h=i.none;return c=c.filter(b=>(f.type?!f.type.allowsMarkType(b.type):!W(b.type,a))||(h=b.addToSet(h),!1)),this.nodes.push(new T(a,b,h,d,null,g)),this.open++,c}closeExtra(a=!1){let b=this.nodes.length-1;if(b>this.open){for(;b>this.open;b--)this.nodes[b-1].content.push(this.nodes[b].finish(a));this.nodes.length=this.open+1}}finish(){return this.open=0,this.closeExtra(this.isOpen),this.nodes[0].finish(!!(this.isOpen||this.options.topOpen))}sync(a){for(let b=this.open;b>=0;b--)if(this.nodes[b]==a)return this.open=b,!0;else this.localPreserveWS&&(this.nodes[b].options|=1);return!1}get currentPos(){this.closeExtra();let a=0;for(let b=this.open;b>=0;b--){let c=this.nodes[b].content;for(let b=c.length-1;b>=0;b--)a+=c[b].nodeSize;b&&a++}return a}findAtPoint(a,b){if(this.find)for(let c=0;c<this.find.length;c++)this.find[c].node==a&&this.find[c].offset==b&&(this.find[c].pos=this.currentPos)}findInside(a){if(this.find)for(let b=0;b<this.find.length;b++)null==this.find[b].pos&&1==a.nodeType&&a.contains(this.find[b].node)&&(this.find[b].pos=this.currentPos)}findAround(a,b,c){if(a!=b&&this.find)for(let d=0;d<this.find.length;d++)null==this.find[d].pos&&1==a.nodeType&&a.contains(this.find[d].node)&&b.compareDocumentPosition(this.find[d].node)&(c?2:4)&&(this.find[d].pos=this.currentPos)}findInText(a){if(this.find)for(let b=0;b<this.find.length;b++)this.find[b].node==a&&(this.find[b].pos=this.currentPos-(a.nodeValue.length-this.find[b].offset))}matchesContext(a){if(a.indexOf("|")>-1)return a.split(/\s*\|\s*/).some(this.matchesContext,this);let b=a.split("/"),c=this.options.context,d=!this.isOpen&&(!c||c.parent.type==this.nodes[0].type),e=-(c?c.depth+1:0)+ +!d,f=(a,g)=>{for(;a>=0;a--){let h=b[a];if(""==h){if(a==b.length-1||0==a)continue;for(;g>=e;g--)if(f(a-1,g))return!0;return!1}{let a=g>0||0==g&&d?this.nodes[g].type:c&&g>=e?c.node(g-e).type:null;if(!a||a.name!=h&&!a.isInGroup(h))return!1;g--}}return!0};return f(b.length-1,this.open)}textblockFromContext(){let a=this.options.context;if(a)for(let b=a.depth;b>=0;b--){let c=a.node(b).contentMatchAt(a.indexAfter(b)).defaultType;if(c&&c.isTextblock&&c.defaultAttrs)return c}for(let a in this.parser.schema.nodes){let b=this.parser.schema.nodes[a];if(b.isTextblock&&b.defaultAttrs)return b}}}function V(a){let b={};for(let c in a)b[c]=a[c];return b}function W(a,b){let c=b.schema.nodes;for(let d in c){let e=c[d];if(!e.allowsMarkType(a))continue;let f=[],g=a=>{f.push(a);for(let c=0;c<a.edgeCount;c++){let{type:d,next:e}=a.edge(c);if(d==b||0>f.indexOf(e)&&g(e))return!0}};if(g(e.contentMatch))return!0}}class X{constructor(a,b){this.nodes=a,this.marks=b}serializeFragment(a,b={},c){c||(c=Z(b).createDocumentFragment());let d=c,e=[];return a.forEach(a=>{if(e.length||a.marks.length){let c=0,f=0;for(;c<e.length&&f<a.marks.length;){let b=a.marks[f];if(!this.marks[b.type.name]){f++;continue}if(!b.eq(e[c][0])||!1===b.type.spec.spanning)break;c++,f++}for(;c<e.length;)d=e.pop()[1];for(;f<a.marks.length;){let c=a.marks[f++],g=this.serializeMark(c,a.isInline,b);g&&(e.push([c,d]),d.appendChild(g.dom),d=g.contentDOM||g.dom)}}d.appendChild(this.serializeNodeInner(a,b))}),c}serializeNodeInner(a,b){let{dom:c,contentDOM:d}=_(Z(b),this.nodes[a.type.name](a),null,a.attrs);if(d){if(a.isLeaf)throw RangeError("Content hole not allowed in a leaf node spec");this.serializeFragment(a.content,b,d)}return c}serializeNode(a,b={}){let c=this.serializeNodeInner(a,b);for(let d=a.marks.length-1;d>=0;d--){let e=this.serializeMark(a.marks[d],a.isInline,b);e&&((e.contentDOM||e.dom).appendChild(c),c=e.dom)}return c}serializeMark(a,b,c={}){let d=this.marks[a.type.name];return d&&_(Z(c),d(a,b),null,a.attrs)}static renderSpec(a,b,c=null,d){return _(a,b,c,d)}static fromSchema(a){return a.cached.domSerializer||(a.cached.domSerializer=new X(this.nodesFromSchema(a),this.marksFromSchema(a)))}static nodesFromSchema(a){let b=Y(a.nodes);return b.text||(b.text=a=>a.text),b}static marksFromSchema(a){return Y(a.marks)}}function Y(a){let b={};for(let c in a){let d=a[c].spec.toDOM;d&&(b[c]=d)}return b}function Z(a){return a.document||window.document}let $=new WeakMap;function _(a,b,c,d){let e,f,g;if("string"==typeof b)return{dom:a.createTextNode(b)};if(null!=b.nodeType)return{dom:b};if(b.dom&&null!=b.dom.nodeType)return b;let h=b[0],i;if("string"!=typeof h)throw RangeError("Invalid array passed to renderSpec");if(d&&(void 0===(f=$.get(d))&&$.set(d,(g=null,!function a(b){if(b&&"object"==typeof b)if(Array.isArray(b))if("string"==typeof b[0])g||(g=[]),g.push(b);else for(let c=0;c<b.length;c++)a(b[c]);else for(let c in b)a(b[c])}(d),f=g)),i=f)&&i.indexOf(b)>-1)throw RangeError("Using an array from an attribute object as a DOM spec. This may be an attempted cross site scripting attack.");let j=h.indexOf(" ");j>0&&(c=h.slice(0,j),h=h.slice(j+1));let k=c?a.createElementNS(c,h):a.createElement(h),l=b[1],m=1;if(l&&"object"==typeof l&&null==l.nodeType&&!Array.isArray(l)){for(let a in m=2,l)if(null!=l[a]){let b=a.indexOf(" ");b>0?k.setAttributeNS(a.slice(0,b),a.slice(b+1),l[a]):"style"==a&&k.style?k.style.cssText=l[a]:k.setAttribute(a,l[a])}}for(let f=m;f<b.length;f++){let g=b[f];if(0===g){if(f<b.length-1||f>m)throw RangeError("Content hole must be the only child of its parent node");return{dom:k,contentDOM:k}}{let{dom:b,contentDOM:f}=_(a,g,c,d);if(k.appendChild(b),f){if(e)throw RangeError("Multiple content holes");e=f}}}return{dom:k,contentDOM:e}}},98031:(a,b,c)=>{c.d(b,{Lz:()=>bQ,NZ:()=>bq,zF:()=>bt});var d=c(35857),e=c(81553),f=c(41662);let g=function(a){for(var b=0;;b++)if(!(a=a.previousSibling))return b},h=function(a){let b=a.assignedSlot||a.parentNode;return b&&11==b.nodeType?b.host:b},i=null,j=function(a,b,c){let d=i||(i=document.createRange());return d.setEnd(a,null==c?a.nodeValue.length:c),d.setStart(a,b||0),d},k=function(){i=null},l=function(a,b,c,d){return c&&(n(a,b,c,d,-1)||n(a,b,c,d,1))},m=/^(img|br|input|textarea|hr)$/i;function n(a,b,c,d,e){for(var f;;){if(a==c&&b==d)return!0;if(b==(e<0?0:o(a))){let c=a.parentNode;if(!c||1!=c.nodeType||p(a)||m.test(a.nodeName)||"false"==a.contentEditable)return!1;b=g(a)+(e<0?0:1),a=c}else{if(1!=a.nodeType)return!1;let c=a.childNodes[b+(e<0?-1:0)];if(1==c.nodeType&&"false"==c.contentEditable)if(null==(f=c.pmViewDesc)||!f.ignoreForSelection)return!1;else b+=e;else a=c,b=e<0?o(a):0}}}function o(a){return 3==a.nodeType?a.nodeValue.length:a.childNodes.length}function p(a){let b;for(let c=a;c&&!(b=c.pmViewDesc);c=c.parentNode);return b&&b.node&&b.node.isBlock&&(b.dom==a||b.contentDOM==a)}let q=function(a){return a.focusNode&&l(a.focusNode,a.focusOffset,a.anchorNode,a.anchorOffset)};function r(a,b){let c=document.createEvent("Event");return c.initEvent("keydown",!0,!0),c.keyCode=a,c.key=c.code=b,c}let s="undefined"!=typeof navigator?navigator:null,t="undefined"!=typeof document?document:null,u=s&&s.userAgent||"",v=/Edge\/(\d+)/.exec(u),w=/MSIE \d/.exec(u),x=/Trident\/(?:[7-9]|\d{2,})\..*rv:(\d+)/.exec(u),y=!!(w||x||v),z=w?document.documentMode:x?+x[1]:v?+v[1]:0,A=!y&&/gecko\/(\d+)/i.test(u);A&&(/Firefox\/(\d+)/.exec(u)||[0,0])[1];let B=!y&&/Chrome\/(\d+)/.exec(u),C=!!B,D=B?+B[1]:0,E=!y&&!!s&&/Apple Computer/.test(s.vendor),F=E&&(/Mobile\/\w+/.test(u)||!!s&&s.maxTouchPoints>2),G=F||!!s&&/Mac/.test(s.platform),H=!!s&&/Win/.test(s.platform),I=/Android \d/.test(u),J=!!t&&"webkitFontSmoothing"in t.documentElement.style,K=J?+(/\bAppleWebKit\/(\d+)/.exec(navigator.userAgent)||[0,0])[1]:0;function L(a,b){return"number"==typeof a?a:a[b]}function M(a,b,c){let d=a.someProp("scrollThreshold")||0,e=a.someProp("scrollMargin")||5,f=a.dom.ownerDocument;for(let g=c||a.dom;g;){if(1!=g.nodeType){g=h(g);continue}let a=g,c=a==f.body,i=c?function(a){let b=a.defaultView&&a.defaultView.visualViewport;return b?{left:0,right:b.width,top:0,bottom:b.height}:{left:0,right:a.documentElement.clientWidth,top:0,bottom:a.documentElement.clientHeight}}(f):function(a){let b=a.getBoundingClientRect(),c=b.width/a.offsetWidth||1,d=b.height/a.offsetHeight||1;return{left:b.left,right:b.left+a.clientWidth*c,top:b.top,bottom:b.top+a.clientHeight*d}}(a),j=0,k=0;if(b.top<i.top+L(d,"top")?k=-(i.top-b.top+L(e,"top")):b.bottom>i.bottom-L(d,"bottom")&&(k=b.bottom-b.top>i.bottom-i.top?b.top+L(e,"top")-i.top:b.bottom-i.bottom+L(e,"bottom")),b.left<i.left+L(d,"left")?j=-(i.left-b.left+L(e,"left")):b.right>i.right-L(d,"right")&&(j=b.right-i.right+L(e,"right")),j||k)if(c)f.defaultView.scrollBy(j,k);else{let c=a.scrollLeft,d=a.scrollTop;k&&(a.scrollTop+=k),j&&(a.scrollLeft+=j);let e=a.scrollLeft-c,f=a.scrollTop-d;b={left:b.left-e,top:b.top-f,right:b.right-e,bottom:b.bottom-f}}let l=c?"fixed":getComputedStyle(g).position;if(/^(fixed|sticky)$/.test(l))break;g="absolute"==l?g.offsetParent:h(g)}}function N(a){let b=[],c=a.ownerDocument;for(let d=a;d&&(b.push({dom:d,top:d.scrollTop,left:d.scrollLeft}),a!=c);d=h(d));return b}function O(a,b){for(let c=0;c<a.length;c++){let{dom:d,top:e,left:f}=a[c];d.scrollTop!=e+b&&(d.scrollTop=e+b),d.scrollLeft!=f&&(d.scrollLeft=f)}}let P=null;function Q(a,b){return a.left>=b.left-1&&a.left<=b.right+1&&a.top>=b.top-1&&a.top<=b.bottom+1}function R(a){return a.top<a.bottom||a.left<a.right}function S(a,b){let c=a.getClientRects();if(c.length){let a=c[b<0?0:c.length-1];if(R(a))return a}return Array.prototype.find.call(c,R)||a.getBoundingClientRect()}let T=/[\u0590-\u05f4\u0600-\u06ff\u0700-\u08ac]/;function U(a,b,c){let{node:d,offset:e,atom:f}=a.docView.domFromPos(b,c<0?-1:1),g=J||A;if(3==d.nodeType)if(g&&(T.test(d.nodeValue)||(c<0?!e:e==d.nodeValue.length))){let a=S(j(d,e,e),c);if(A&&e&&/\s/.test(d.nodeValue[e-1])&&e<d.nodeValue.length){let b=S(j(d,e-1,e-1),-1);if(b.top==a.top){let c=S(j(d,e,e+1),-1);if(c.top!=a.top)return V(c,c.left<b.left)}}return a}else{let a=e,b=e,f=c<0?1:-1;return c<0&&!e?(b++,f=-1):c>=0&&e==d.nodeValue.length?(a--,f=1):c<0?a--:b++,V(S(j(d,a,b),f),f<0)}if(!a.state.doc.resolve(b-(f||0)).parent.inlineContent){if(null==f&&e&&(c<0||e==o(d))){let a=d.childNodes[e-1];if(1==a.nodeType)return W(a.getBoundingClientRect(),!1)}if(null==f&&e<o(d)){let a=d.childNodes[e];if(1==a.nodeType)return W(a.getBoundingClientRect(),!0)}return W(d.getBoundingClientRect(),c>=0)}if(null==f&&e&&(c<0||e==o(d))){let a=d.childNodes[e-1],b=3==a.nodeType?j(a,o(a)-!g):1!=a.nodeType||"BR"==a.nodeName&&a.nextSibling?null:a;if(b)return V(S(b,1),!1)}if(null==f&&e<o(d)){let a=d.childNodes[e];for(;a.pmViewDesc&&a.pmViewDesc.ignoreForCoords;)a=a.nextSibling;let b=a?3==a.nodeType?j(a,0,+!g):1==a.nodeType?a:null:null;if(b)return V(S(b,-1),!0)}return V(S(3==d.nodeType?j(d):d,-c),c>=0)}function V(a,b){if(0==a.width)return a;let c=b?a.left:a.right;return{top:a.top,bottom:a.bottom,left:c,right:c}}function W(a,b){if(0==a.height)return a;let c=b?a.top:a.bottom;return{top:c,bottom:c,left:a.left,right:a.right}}function X(a,b,c){let d=a.state,e=a.root.activeElement;d!=b&&a.updateState(b),e!=a.dom&&a.focus();try{return c()}finally{d!=b&&a.updateState(d),e!=a.dom&&e&&e.focus()}}let Y=/[\u0590-\u08ac]/,Z=null,$=null,_=!1;class aa{constructor(a,b,c,d){this.parent=a,this.children=b,this.dom=c,this.contentDOM=d,this.dirty=0,c.pmViewDesc=this}matchesWidget(a){return!1}matchesMark(a){return!1}matchesNode(a,b,c){return!1}matchesHack(a){return!1}parseRule(){return null}stopEvent(a){return!1}get size(){let a=0;for(let b=0;b<this.children.length;b++)a+=this.children[b].size;return a}get border(){return 0}destroy(){this.parent=void 0,this.dom.pmViewDesc==this&&(this.dom.pmViewDesc=void 0);for(let a=0;a<this.children.length;a++)this.children[a].destroy()}posBeforeChild(a){for(let b=0,c=this.posAtStart;;b++){let d=this.children[b];if(d==a)return c;c+=d.size}}get posBefore(){return this.parent.posBeforeChild(this)}get posAtStart(){return this.parent?this.parent.posBeforeChild(this)+this.border:0}get posAfter(){return this.posBefore+this.size}get posAtEnd(){return this.posAtStart+this.size-2*this.border}localPosFromDOM(a,b,c){let d;if(this.contentDOM&&this.contentDOM.contains(1==a.nodeType?a:a.parentNode))if(c<0){let c,d;if(a==this.contentDOM)c=a.childNodes[b-1];else{for(;a.parentNode!=this.contentDOM;)a=a.parentNode;c=a.previousSibling}for(;c&&!((d=c.pmViewDesc)&&d.parent==this);)c=c.previousSibling;return c?this.posBeforeChild(d)+d.size:this.posAtStart}else{let c,d;if(a==this.contentDOM)c=a.childNodes[b];else{for(;a.parentNode!=this.contentDOM;)a=a.parentNode;c=a.nextSibling}for(;c&&!((d=c.pmViewDesc)&&d.parent==this);)c=c.nextSibling;return c?this.posBeforeChild(d):this.posAtEnd}if(a==this.dom&&this.contentDOM)d=b>g(this.contentDOM);else if(this.contentDOM&&this.contentDOM!=this.dom&&this.dom.contains(this.contentDOM))d=2&a.compareDocumentPosition(this.contentDOM);else if(this.dom.firstChild){if(0==b)for(let b=a;;b=b.parentNode){if(b==this.dom){d=!1;break}if(b.previousSibling)break}if(null==d&&b==a.childNodes.length)for(let b=a;;b=b.parentNode){if(b==this.dom){d=!0;break}if(b.nextSibling)break}}return(null==d?c>0:d)?this.posAtEnd:this.posAtStart}nearestDesc(a,b=!1){for(let c=!0,d=a;d;d=d.parentNode){let e=this.getDesc(d),f;if(e&&(!b||e.node))if(!c||!(f=e.nodeDOM)||(1==f.nodeType?f.contains(1==a.nodeType?a:a.parentNode):f==a))return e;else c=!1}}getDesc(a){let b=a.pmViewDesc;for(let a=b;a;a=a.parent)if(a==this)return b}posFromDOM(a,b,c){for(let d=a;d;d=d.parentNode){let e=this.getDesc(d);if(e)return e.localPosFromDOM(a,b,c)}return -1}descAt(a){for(let b=0,c=0;b<this.children.length;b++){let d=this.children[b],e=c+d.size;if(c==a&&e!=c){for(;!d.border&&d.children.length;)for(let a=0;a<d.children.length;a++){let b=d.children[a];if(b.size){d=b;break}}return d}if(a<e)return d.descAt(a-c-d.border);c=e}}domFromPos(a,b){if(!this.contentDOM)return{node:this.dom,offset:0,atom:a+1};let c=0,d=0;for(let b=0;c<this.children.length;c++){let e=this.children[c],f=b+e.size;if(f>a||e instanceof ah){d=a-b;break}b=f}if(d)return this.children[c].domFromPos(d-this.children[c].border,b);for(let a;c&&!(a=this.children[c-1]).size&&a instanceof ab&&a.side>=0;c--);if(b<=0){let a,d=!0;for(;(a=c?this.children[c-1]:null)&&a.dom.parentNode!=this.contentDOM;c--,d=!1);return a&&b&&d&&!a.border&&!a.domAtom?a.domFromPos(a.size,b):{node:this.contentDOM,offset:a?g(a.dom)+1:0}}{let a,d=!0;for(;(a=c<this.children.length?this.children[c]:null)&&a.dom.parentNode!=this.contentDOM;c++,d=!1);return a&&d&&!a.border&&!a.domAtom?a.domFromPos(0,b):{node:this.contentDOM,offset:a?g(a.dom):this.contentDOM.childNodes.length}}}parseRange(a,b,c=0){if(0==this.children.length)return{node:this.contentDOM,from:a,to:b,fromOffset:0,toOffset:this.contentDOM.childNodes.length};let d=-1,e=-1;for(let f=c,h=0;;h++){let c=this.children[h],i=f+c.size;if(-1==d&&a<=i){let e=f+c.border;if(a>=e&&b<=i-c.border&&c.node&&c.contentDOM&&this.contentDOM.contains(c.contentDOM))return c.parseRange(a,b,e);a=f;for(let b=h;b>0;b--){let c=this.children[b-1];if(c.size&&c.dom.parentNode==this.contentDOM&&!c.emptyChildAt(1)){d=g(c.dom)+1;break}a-=c.size}-1==d&&(d=0)}if(d>-1&&(i>b||h==this.children.length-1)){b=i;for(let a=h+1;a<this.children.length;a++){let c=this.children[a];if(c.size&&c.dom.parentNode==this.contentDOM&&!c.emptyChildAt(-1)){e=g(c.dom);break}b+=c.size}-1==e&&(e=this.contentDOM.childNodes.length);break}f=i}return{node:this.contentDOM,from:a,to:b,fromOffset:d,toOffset:e}}emptyChildAt(a){if(this.border||!this.contentDOM||!this.children.length)return!1;let b=this.children[a<0?0:this.children.length-1];return 0==b.size||b.emptyChildAt(a)}domAfterPos(a){let{node:b,offset:c}=this.domFromPos(a,0);if(1!=b.nodeType||c==b.childNodes.length)throw RangeError("No node after pos "+a);return b.childNodes[c]}setSelection(a,b,c,d=!1){let e=Math.min(a,b),f=Math.max(a,b);for(let g=0,h=0;g<this.children.length;g++){let i=this.children[g],j=h+i.size;if(e>h&&f<j)return i.setSelection(a-h-i.border,b-h-i.border,c,d);h=j}let h=this.domFromPos(a,a?-1:1),i=b==a?h:this.domFromPos(b,b?-1:1),j=c.root.getSelection(),k=c.domSelectionRange(),m=!1;if((A||E)&&a==b){let{node:a,offset:b}=h;if(3==a.nodeType){if((m=!!(b&&"\n"==a.nodeValue[b-1]))&&b==a.nodeValue.length)for(let b=a,c;b;b=b.parentNode){if(c=b.nextSibling){"BR"==c.nodeName&&(h=i={node:c.parentNode,offset:g(c)+1});break}let a=b.pmViewDesc;if(a&&a.node&&a.node.isBlock)break}}else{let c=a.childNodes[b-1];m=c&&("BR"==c.nodeName||"false"==c.contentEditable)}}if(A&&k.focusNode&&k.focusNode!=i.node&&1==k.focusNode.nodeType){let a=k.focusNode.childNodes[k.focusOffset];a&&"false"==a.contentEditable&&(d=!0)}if(!(d||m&&E)&&l(h.node,h.offset,k.anchorNode,k.anchorOffset)&&l(i.node,i.offset,k.focusNode,k.focusOffset))return;let n=!1;if((j.extend||a==b)&&!m){j.collapse(h.node,h.offset);try{a!=b&&j.extend(i.node,i.offset),n=!0}catch(a){}}if(!n){if(a>b){let a=h;h=i,i=a}let c=document.createRange();c.setEnd(i.node,i.offset),c.setStart(h.node,h.offset),j.removeAllRanges(),j.addRange(c)}}ignoreMutation(a){return!this.contentDOM&&"selection"!=a.type}get contentLost(){return this.contentDOM&&this.contentDOM!=this.dom&&!this.dom.contains(this.contentDOM)}markDirty(a,b){for(let c=0,d=0;d<this.children.length;d++){let e=this.children[d],f=c+e.size;if(c==f?a<=f&&b>=c:a<f&&b>c){let d=c+e.border,g=f-e.border;if(a>=d&&b<=g){this.dirty=a==c||b==f?2:1,a==d&&b==g&&(e.contentLost||e.dom.parentNode!=this.contentDOM)?e.dirty=3:e.markDirty(a-d,b-d);return}e.dirty=e.dom!=e.contentDOM||e.dom.parentNode!=this.contentDOM||e.children.length?3:2}c=f}this.dirty=2}markParentsDirty(){let a=1;for(let b=this.parent;b;b=b.parent,a++){let c=1==a?2:1;b.dirty<c&&(b.dirty=c)}}get domAtom(){return!1}get ignoreForCoords(){return!1}get ignoreForSelection(){return!1}isText(a){return!1}}class ab extends aa{constructor(a,b,c,d){let e,f=b.type.toDOM;if("function"==typeof f&&(f=f(c,()=>e?e.parent?e.parent.posBeforeChild(e):void 0:d)),!b.type.spec.raw){if(1!=f.nodeType){let a=document.createElement("span");a.appendChild(f),f=a}f.contentEditable="false",f.classList.add("ProseMirror-widget")}super(a,[],f,null),this.widget=b,this.widget=b,e=this}matchesWidget(a){return 0==this.dirty&&a.type.eq(this.widget.type)}parseRule(){return{ignore:!0}}stopEvent(a){let b=this.widget.spec.stopEvent;return!!b&&b(a)}ignoreMutation(a){return"selection"!=a.type||this.widget.spec.ignoreSelection}destroy(){this.widget.type.destroy(this.dom),super.destroy()}get domAtom(){return!0}get ignoreForSelection(){return!!this.widget.type.spec.relaxedSide}get side(){return this.widget.type.side}}class ac extends aa{constructor(a,b,c,d){super(a,[],b,null),this.textDOM=c,this.text=d}get size(){return this.text.length}localPosFromDOM(a,b){return a!=this.textDOM?this.posAtStart+(b?this.size:0):this.posAtStart+b}domFromPos(a){return{node:this.textDOM,offset:a}}ignoreMutation(a){return"characterData"===a.type&&a.target.nodeValue==a.oldValue}}class ad extends aa{constructor(a,b,c,d,e){super(a,[],c,d),this.mark=b,this.spec=e}static create(a,b,c,d){let f=d.nodeViews[b.type.name],g=f&&f(b,d,c);return g&&g.dom||(g=e.ZF.renderSpec(document,b.type.spec.toDOM(b,c),null,b.attrs)),new ad(a,b,g.dom,g.contentDOM||g.dom,g)}parseRule(){return 3&this.dirty||this.mark.type.spec.reparseInView?null:{mark:this.mark.type.name,attrs:this.mark.attrs,contentElement:this.contentDOM}}matchesMark(a){return 3!=this.dirty&&this.mark.eq(a)}markDirty(a,b){if(super.markDirty(a,b),0!=this.dirty){let a=this.parent;for(;!a.node;)a=a.parent;a.dirty<this.dirty&&(a.dirty=this.dirty),this.dirty=0}}slice(a,b,c){let d=ad.create(this.parent,this.mark,!0,c),e=this.children,f=this.size;b<f&&(e=as(e,b,f,c)),a>0&&(e=as(e,0,a,c));for(let a=0;a<e.length;a++)e[a].parent=d;return d.children=e,d}ignoreMutation(a){return this.spec.ignoreMutation?this.spec.ignoreMutation(a):super.ignoreMutation(a)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}}class ae extends aa{constructor(a,b,c,d,e,f,g,h,i){super(a,[],e,f),this.node=b,this.outerDeco=c,this.innerDeco=d,this.nodeDOM=g}static create(a,b,c,d,f,g){let h=f.nodeViews[b.type.name],i,j=h&&h(b,f,()=>i?i.parent?i.parent.posBeforeChild(i):void 0:g,c,d),k=j&&j.dom,l=j&&j.contentDOM;if(b.isText)if(k){if(3!=k.nodeType)throw RangeError("Text must be rendered as a DOM text node")}else k=document.createTextNode(b.text);else if(!k){let a=e.ZF.renderSpec(document,b.type.spec.toDOM(b),null,b.attrs);({dom:k,contentDOM:l}=a)}l||b.isText||"BR"==k.nodeName||(k.hasAttribute("contenteditable")||(k.contentEditable="false"),b.type.spec.draggable&&(k.draggable=!0));let m=k;return(k=an(k,c,b),j)?i=new ai(a,b,c,d,k,l||null,m,j,f,g+1):b.isText?new ag(a,b,c,d,k,m,f):new ae(a,b,c,d,k,l||null,m,f,g+1)}parseRule(){if(this.node.type.spec.reparseInView)return null;let a={node:this.node.type.name,attrs:this.node.attrs};if("pre"==this.node.type.whitespace&&(a.preserveWhitespace="full"),this.contentDOM)if(this.contentLost){for(let b=this.children.length-1;b>=0;b--){let c=this.children[b];if(this.dom.contains(c.dom.parentNode)){a.contentElement=c.dom.parentNode;break}}a.contentElement||(a.getContent=()=>e.FK.empty)}else a.contentElement=this.contentDOM;else a.getContent=()=>this.node.content;return a}matchesNode(a,b,c){return 0==this.dirty&&a.eq(this.node)&&ao(b,this.outerDeco)&&c.eq(this.innerDeco)}get size(){return this.node.nodeSize}get border(){return+!this.node.isLeaf}updateChildren(a,b){let c=this.node.inlineContent,d=b,f=a.composing?this.localCompositionInfo(a,b):null,g=f&&f.pos>-1?f:null,h=f&&f.pos<0,i=new aq(this,g&&g.node,a);(function(a,b,c,d){let e=b.locals(a),f=0;if(0==e.length){for(let c=0;c<a.childCount;c++){let g=a.child(c);d(g,e,b.forChild(f,g),c),f+=g.nodeSize}return}let g=0,h=[],i=null;for(let j=0;;){let k,l,m,n;for(;g<e.length&&e[g].to==f;){let a=e[g++];a.widget&&(k?(l||(l=[k])).push(a):k=a)}if(k)if(l){l.sort(ar);for(let a=0;a<l.length;a++)c(l[a],j,!!i)}else c(k,j,!!i);if(i)n=-1,m=i,i=null;else if(j<a.childCount)n=j,m=a.child(j++);else break;for(let a=0;a<h.length;a++)h[a].to<=f&&h.splice(a--,1);for(;g<e.length&&e[g].from<=f&&e[g].to>f;)h.push(e[g++]);let o=f+m.nodeSize;if(m.isText){let a=o;g<e.length&&e[g].from<a&&(a=e[g].from);for(let b=0;b<h.length;b++)h[b].to<a&&(a=h[b].to);a<o&&(i=m.cut(a-f),m=m.cut(0,a-f),o=a,n=-1)}else for(;g<e.length&&e[g].to<o;)g++;let p=m.isInline&&!m.isLeaf?h.filter(a=>!a.inline):h.slice();d(m,p,b.forChild(f,m),n),f=o}})(this.node,this.innerDeco,(b,f,g)=>{b.spec.marks?i.syncToMarks(b.spec.marks,c,a):b.type.side>=0&&!g&&i.syncToMarks(f==this.node.childCount?e.CU.none:this.node.child(f).marks,c,a),i.placeWidget(b,a,d)},(b,e,g,j)=>{let k;i.syncToMarks(b.marks,c,a),i.findNodeMatch(b,e,g,j)||h&&a.state.selection.from>d&&a.state.selection.to<d+b.nodeSize&&(k=i.findIndexWithChild(f.node))>-1&&i.updateNodeAt(b,e,g,k,a)||i.updateNextNode(b,e,g,a,j,d)||i.addNode(b,e,g,a,d),d+=b.nodeSize}),i.syncToMarks([],c,a),this.node.isTextblock&&i.addTextblockHacks(),i.destroyRest(),(i.changed||2==this.dirty)&&(g&&this.protectLocalComposition(a,g),function a(b,c,d){let e=b.firstChild,f=!1;for(let g=0;g<c.length;g++){let h=c[g],i=h.dom;if(i.parentNode==b){for(;i!=e;)e=ap(e),f=!0;e=e.nextSibling}else f=!0,b.insertBefore(i,e);if(h instanceof ad){let c=e?e.previousSibling:b.lastChild;a(h.contentDOM,h.children,d),e=c?c.nextSibling:b.firstChild}}for(;e;)e=ap(e),f=!0;f&&d.trackWrites==b&&(d.trackWrites=null)}(this.contentDOM,this.children,a),F&&function(a){if("UL"==a.nodeName||"OL"==a.nodeName){let b=a.style.cssText;a.style.cssText=b+"; list-style: square !important",window.getComputedStyle(a).listStyle,a.style.cssText=b}}(this.dom))}localCompositionInfo(a,b){let{from:c,to:e}=a.state.selection;if(!(a.state.selection instanceof d.U3)||c<b||e>b+this.node.content.size)return null;let f=a.input.compositionNode;if(!f||!this.dom.contains(f.parentNode))return null;if(!this.node.inlineContent)return{node:f,pos:-1,text:""};{let a=f.nodeValue,d=function(a,b,c,d){for(let e=0,f=0;e<a.childCount&&f<=d;){let g=a.child(e++),h=f;if(f+=g.nodeSize,!g.isText)continue;let i=g.text;for(;e<a.childCount;){let b=a.child(e++);if(f+=b.nodeSize,!b.isText)break;i+=b.text}if(f>=c){if(f>=d&&i.slice(d-b.length-h,d-h)==b)return d-b.length;let a=h<d?i.lastIndexOf(b,d-h-1):-1;if(a>=0&&a+b.length+h>=c)return h+a;if(c==d&&i.length>=d+b.length-h&&i.slice(d-h,d-h+b.length)==b)return d}}return -1}(this.node.content,a,c-b,e-b);return d<0?null:{node:f,pos:d,text:a}}}protectLocalComposition(a,{node:b,pos:c,text:d}){if(this.getDesc(b))return;let e=b;for(;e.parentNode!=this.contentDOM;e=e.parentNode){for(;e.previousSibling;)e.parentNode.removeChild(e.previousSibling);for(;e.nextSibling;)e.parentNode.removeChild(e.nextSibling);e.pmViewDesc&&(e.pmViewDesc=void 0)}let f=new ac(this,e,b,d);a.input.compositionNodes.push(f),this.children=as(this.children,c,c+d.length,a,f)}update(a,b,c,d){return 3!=this.dirty&&!!a.sameMarkup(this.node)&&(this.updateInner(a,b,c,d),!0)}updateInner(a,b,c,d){this.updateOuterDeco(b),this.node=a,this.innerDeco=c,this.contentDOM&&this.updateChildren(d,this.posAtStart),this.dirty=0}updateOuterDeco(a){if(ao(a,this.outerDeco))return;let b=1!=this.nodeDOM.nodeType,c=this.dom;this.dom=am(this.dom,this.nodeDOM,al(this.outerDeco,this.node,b),al(a,this.node,b)),this.dom!=c&&(c.pmViewDesc=void 0,this.dom.pmViewDesc=this),this.outerDeco=a}selectNode(){1==this.nodeDOM.nodeType&&this.nodeDOM.classList.add("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&(this.dom.draggable=!0)}deselectNode(){1==this.nodeDOM.nodeType&&(this.nodeDOM.classList.remove("ProseMirror-selectednode"),(this.contentDOM||!this.node.type.spec.draggable)&&this.dom.removeAttribute("draggable"))}get domAtom(){return this.node.isAtom}}function af(a,b,c,d,e){an(d,b,a);let f=new ae(void 0,a,b,c,d,d,d,e,0);return f.contentDOM&&f.updateChildren(e,0),f}class ag extends ae{constructor(a,b,c,d,e,f,g){super(a,b,c,d,e,null,f,g,0)}parseRule(){let a=this.nodeDOM.parentNode;for(;a&&a!=this.dom&&!a.pmIsDeco;)a=a.parentNode;return{skip:a||!0}}update(a,b,c,d){return 3!=this.dirty&&(0==this.dirty||!!this.inParent())&&!!a.sameMarkup(this.node)&&(this.updateOuterDeco(b),(0!=this.dirty||a.text!=this.node.text)&&a.text!=this.nodeDOM.nodeValue&&(this.nodeDOM.nodeValue=a.text,d.trackWrites==this.nodeDOM&&(d.trackWrites=null)),this.node=a,this.dirty=0,!0)}inParent(){let a=this.parent.contentDOM;for(let b=this.nodeDOM;b;b=b.parentNode)if(b==a)return!0;return!1}domFromPos(a){return{node:this.nodeDOM,offset:a}}localPosFromDOM(a,b,c){return a==this.nodeDOM?this.posAtStart+Math.min(b,this.node.text.length):super.localPosFromDOM(a,b,c)}ignoreMutation(a){return"characterData"!=a.type&&"selection"!=a.type}slice(a,b,c){let d=this.node.cut(a,b),e=document.createTextNode(d.text);return new ag(this.parent,d,this.outerDeco,this.innerDeco,e,e,c)}markDirty(a,b){super.markDirty(a,b),this.dom!=this.nodeDOM&&(0==a||b==this.nodeDOM.nodeValue.length)&&(this.dirty=3)}get domAtom(){return!1}isText(a){return this.node.text==a}}class ah extends aa{parseRule(){return{ignore:!0}}matchesHack(a){return 0==this.dirty&&this.dom.nodeName==a}get domAtom(){return!0}get ignoreForCoords(){return"IMG"==this.dom.nodeName}}class ai extends ae{constructor(a,b,c,d,e,f,g,h,i,j){super(a,b,c,d,e,f,g,i,j),this.spec=h}update(a,b,c,d){if(3==this.dirty)return!1;if(this.spec.update&&(this.node.type==a.type||this.spec.multiType)){let e=this.spec.update(a,b,c);return e&&this.updateInner(a,b,c,d),e}return(!!this.contentDOM||!!a.isLeaf)&&super.update(a,b,c,d)}selectNode(){this.spec.selectNode?this.spec.selectNode():super.selectNode()}deselectNode(){this.spec.deselectNode?this.spec.deselectNode():super.deselectNode()}setSelection(a,b,c,d){this.spec.setSelection?this.spec.setSelection(a,b,c.root):super.setSelection(a,b,c,d)}destroy(){this.spec.destroy&&this.spec.destroy(),super.destroy()}stopEvent(a){return!!this.spec.stopEvent&&this.spec.stopEvent(a)}ignoreMutation(a){return this.spec.ignoreMutation?this.spec.ignoreMutation(a):super.ignoreMutation(a)}}let aj=function(a){a&&(this.nodeName=a)};aj.prototype=Object.create(null);let ak=[new aj];function al(a,b,c){if(0==a.length)return ak;let d=c?ak[0]:new aj,e=[d];for(let f=0;f<a.length;f++){let g=a[f].type.attrs;if(g)for(let a in g.nodeName&&e.push(d=new aj(g.nodeName)),g){let f=g[a];null!=f&&(c&&1==e.length&&e.push(d=new aj(b.isInline?"span":"div")),"class"==a?d.class=(d.class?d.class+" ":"")+f:"style"==a?d.style=(d.style?d.style+";":"")+f:"nodeName"!=a&&(d[a]=f))}}return e}function am(a,b,c,d){if(c==ak&&d==ak)return b;let e=b;for(let b=0;b<d.length;b++){let f=d[b],g=c[b];if(b){let b;g&&g.nodeName==f.nodeName&&e!=a&&(b=e.parentNode)&&b.nodeName.toLowerCase()==f.nodeName||((b=document.createElement(f.nodeName)).pmIsDeco=!0,b.appendChild(e),g=ak[0]),e=b}!function(a,b,c){for(let d in b)"class"==d||"style"==d||"nodeName"==d||d in c||a.removeAttribute(d);for(let d in c)"class"!=d&&"style"!=d&&"nodeName"!=d&&c[d]!=b[d]&&a.setAttribute(d,c[d]);if(b.class!=c.class){let d=b.class?b.class.split(" ").filter(Boolean):[],e=c.class?c.class.split(" ").filter(Boolean):[];for(let b=0;b<d.length;b++)-1==e.indexOf(d[b])&&a.classList.remove(d[b]);for(let b=0;b<e.length;b++)-1==d.indexOf(e[b])&&a.classList.add(e[b]);0==a.classList.length&&a.removeAttribute("class")}if(b.style!=c.style){if(b.style){let c=/\s*([\w\-\xa1-\uffff]+)\s*:(?:"(?:\\.|[^"])*"|'(?:\\.|[^'])*'|\(.*?\)|[^;])*/g,d;for(;d=c.exec(b.style);)a.style.removeProperty(d[1])}c.style&&(a.style.cssText+=c.style)}}(e,g||ak[0],f)}return e}function an(a,b,c){return am(a,a,ak,al(b,c,1!=a.nodeType))}function ao(a,b){if(a.length!=b.length)return!1;for(let c=0;c<a.length;c++)if(!a[c].type.eq(b[c].type))return!1;return!0}function ap(a){let b=a.nextSibling;return a.parentNode.removeChild(a),b}class aq{constructor(a,b,c){this.lock=b,this.view=c,this.index=0,this.stack=[],this.changed=!1,this.top=a,this.preMatch=function(a,b){let c=b,d=c.children.length,e=a.childCount,f=new Map,g=[];d:for(;e>0;){let h;for(;;)if(d){let a=c.children[d-1];if(a instanceof ad)c=a,d=a.children.length;else{h=a,d--;break}}else if(c==b)break d;else d=c.parent.children.indexOf(c),c=c.parent;let i=h.node;if(i){if(i!=a.child(e-1))break;--e,f.set(h,e),g.push(h)}}return{index:e,matched:f,matches:g.reverse()}}(a.node.content,a)}destroyBetween(a,b){if(a!=b){for(let c=a;c<b;c++)this.top.children[c].destroy();this.top.children.splice(a,b-a),this.changed=!0}}destroyRest(){this.destroyBetween(this.index,this.top.children.length)}syncToMarks(a,b,c){let d=0,e=this.stack.length>>1,f=Math.min(e,a.length);for(;d<f&&(d==e-1?this.top:this.stack[d+1<<1]).matchesMark(a[d])&&!1!==a[d].type.spec.spanning;)d++;for(;d<e;)this.destroyRest(),this.top.dirty=0,this.index=this.stack.pop(),this.top=this.stack.pop(),e--;for(;e<a.length;){this.stack.push(this.top,this.index+1);let d=-1;for(let b=this.index;b<Math.min(this.index+3,this.top.children.length);b++){let c=this.top.children[b];if(c.matchesMark(a[e])&&!this.isLocked(c.dom)){d=b;break}}if(d>-1)d>this.index&&(this.changed=!0,this.destroyBetween(this.index,d)),this.top=this.top.children[this.index];else{let d=ad.create(this.top,a[e],b,c);this.top.children.splice(this.index,0,d),this.top=d,this.changed=!0}this.index=0,e++}}findNodeMatch(a,b,c,d){let e=-1,f;if(d>=this.preMatch.index&&(f=this.preMatch.matches[d-this.preMatch.index]).parent==this.top&&f.matchesNode(a,b,c))e=this.top.children.indexOf(f,this.index);else for(let d=this.index,f=Math.min(this.top.children.length,d+5);d<f;d++){let f=this.top.children[d];if(f.matchesNode(a,b,c)&&!this.preMatch.matched.has(f)){e=d;break}}return!(e<0)&&(this.destroyBetween(this.index,e),this.index++,!0)}updateNodeAt(a,b,c,d,e){let f=this.top.children[d];return 3==f.dirty&&f.dom==f.contentDOM&&(f.dirty=2),!!f.update(a,b,c,e)&&(this.destroyBetween(this.index,d),this.index++,!0)}findIndexWithChild(a){for(;;){let b=a.parentNode;if(!b)return -1;if(b==this.top.contentDOM){let b=a.pmViewDesc;if(b){for(let a=this.index;a<this.top.children.length;a++)if(this.top.children[a]==b)return a}return -1}a=b}}updateNextNode(a,b,c,d,e,f){for(let g=this.index;g<this.top.children.length;g++){let h=this.top.children[g];if(h instanceof ae){let i=this.preMatch.matched.get(h);if(null!=i&&i!=e)return!1;let j=h.dom,k,l=this.isLocked(j)&&!(a.isText&&h.node&&h.node.isText&&h.nodeDOM.nodeValue==a.text&&3!=h.dirty&&ao(b,h.outerDeco));if(!l&&h.update(a,b,c,d))return this.destroyBetween(this.index,g),h.dom!=j&&(this.changed=!0),this.index++,!0;if(!l&&(k=this.recreateWrapper(h,a,b,c,d,f)))return this.destroyBetween(this.index,g),this.top.children[this.index]=k,k.contentDOM&&(k.dirty=2,k.updateChildren(d,f+1),k.dirty=0),this.changed=!0,this.index++,!0;break}}return!1}recreateWrapper(a,b,c,d,e,f){if(a.dirty||b.isAtom||!a.children.length||!a.node.content.eq(b.content)||!ao(c,a.outerDeco)||!d.eq(a.innerDeco))return null;let g=ae.create(this.top,b,c,d,e,f);if(g.contentDOM)for(let b of(g.children=a.children,a.children=[],g.children))b.parent=g;return a.destroy(),g}addNode(a,b,c,d,e){let f=ae.create(this.top,a,b,c,d,e);f.contentDOM&&f.updateChildren(d,e+1),this.top.children.splice(this.index++,0,f),this.changed=!0}placeWidget(a,b,c){let d=this.index<this.top.children.length?this.top.children[this.index]:null;if(d&&d.matchesWidget(a)&&(a==d.widget||!d.widget.type.toDOM.parentNode))this.index++;else{let d=new ab(this.top,a,b,c);this.top.children.splice(this.index++,0,d),this.changed=!0}}addTextblockHacks(){let a=this.top.children[this.index-1],b=this.top;for(;a instanceof ad;)a=(b=a).children[b.children.length-1];(!a||!(a instanceof ag)||/\n$/.test(a.node.text)||this.view.requiresGeckoHackNode&&/\s$/.test(a.node.text))&&((E||C)&&a&&"false"==a.dom.contentEditable&&this.addHackNode("IMG",b),this.addHackNode("BR",this.top))}addHackNode(a,b){if(b==this.top&&this.index<b.children.length&&b.children[this.index].matchesHack(a))this.index++;else{let c=document.createElement(a);"IMG"==a&&(c.className="ProseMirror-separator",c.alt=""),"BR"==a&&(c.className="ProseMirror-trailingBreak");let d=new ah(this.top,[],c,null);b!=this.top?b.children.push(d):b.children.splice(this.index++,0,d),this.changed=!0}}isLocked(a){return this.lock&&(a==this.lock||1==a.nodeType&&a.contains(this.lock.parentNode))}}function ar(a,b){return a.type.side-b.type.side}function as(a,b,c,d,e){let f=[];for(let g=0,h=0;g<a.length;g++){let i=a[g],j=h,k=h+=i.size;j>=c||k<=b?f.push(i):(j<b&&f.push(i.slice(0,b-j,d)),e&&(f.push(e),e=void 0),k>c&&f.push(i.slice(c-j,i.size,d)))}return f}function at(a,b=null){let c=a.domSelectionRange(),e=a.state.doc;if(!c.focusNode)return null;let f=a.docView.nearestDesc(c.focusNode),h=f&&0==f.size,i=a.docView.posFromDOM(c.focusNode,c.focusOffset,1);if(i<0)return null;let j=e.resolve(i),k,l;if(q(c)){for(k=i;f&&!f.node;)f=f.parent;let a=f.node;if(f&&a.isAtom&&d.nh.isSelectable(a)&&f.parent&&!(a.isInline&&function(a,b,c){for(let d=0==b,e=b==o(a);d||e;){if(a==c)return!0;let b=g(a);if(!(a=a.parentNode))return!1;d=d&&0==b,e=e&&b==o(a)}}(c.focusNode,c.focusOffset,f.dom))){let a=f.posBefore;l=new d.nh(i==a?j:e.resolve(a))}}else{if(c instanceof a.dom.ownerDocument.defaultView.Selection&&c.rangeCount>1){let b=i,d=i;for(let e=0;e<c.rangeCount;e++){let f=c.getRangeAt(e);b=Math.min(b,a.docView.posFromDOM(f.startContainer,f.startOffset,1)),d=Math.max(d,a.docView.posFromDOM(f.endContainer,f.endOffset,-1))}if(b<0)return null;[k,i]=d==a.state.selection.anchor?[d,b]:[b,d],j=e.resolve(i)}else k=a.docView.posFromDOM(c.anchorNode,c.anchorOffset,1);if(k<0)return null}let m=e.resolve(k);if(!l){let c="pointer"==b||a.state.selection.head<j.pos&&!h?1:-1;l=aC(a,m,j,c)}return l}function au(a){return a.editable?a.hasFocus():aE(a)&&document.activeElement&&document.activeElement.contains(a.dom)}function av(a,b=!1){let c=a.state.selection;if(aA(a,c),au(a)){if(!b&&a.input.mouseDown&&a.input.mouseDown.allowDefault&&C){let b=a.domSelectionRange(),c=a.domObserver.currentSelection;if(b.anchorNode&&c.anchorNode&&l(b.anchorNode,b.anchorOffset,c.anchorNode,c.anchorOffset)){a.input.mouseDown.delayedSelectionSync=!0,a.domObserver.setCurSelection();return}}if(a.domObserver.disconnectSelection(),a.cursorWrapper)!function(a){let b=a.domSelection(),c=document.createRange();if(!b)return;let d=a.cursorWrapper.dom,e="IMG"==d.nodeName;e?c.setStart(d.parentNode,g(d)+1):c.setStart(d,0),c.collapse(!0),b.removeAllRanges(),b.addRange(c),!e&&!a.state.selection.visible&&y&&z<=11&&(d.disabled=!0,d.disabled=!1)}(a);else{var e;let f,g,h,i,{anchor:j,head:k}=c,l,m;aw&&!(c instanceof d.U3)&&(c.$from.parent.inlineContent||(l=ax(a,c.from)),c.empty||c.$from.parent.inlineContent||(m=ax(a,c.to))),a.docView.setSelection(j,k,a,b),aw&&(l&&az(l),m&&az(m)),c.visible?a.dom.classList.remove("ProseMirror-hideselection"):(a.dom.classList.add("ProseMirror-hideselection"),"onselectionchange"in document&&((f=(e=a).dom.ownerDocument).removeEventListener("selectionchange",e.input.hideSelectionGuard),h=(g=e.domSelectionRange()).anchorNode,i=g.anchorOffset,f.addEventListener("selectionchange",e.input.hideSelectionGuard=()=>{(g.anchorNode!=h||g.anchorOffset!=i)&&(f.removeEventListener("selectionchange",e.input.hideSelectionGuard),setTimeout(()=>{(!au(e)||e.state.selection.visible)&&e.dom.classList.remove("ProseMirror-hideselection")},20))})))}a.domObserver.setCurSelection(),a.domObserver.connectSelection()}}let aw=E||C&&D<63;function ax(a,b){let{node:c,offset:d}=a.docView.domFromPos(b,0),e=d<c.childNodes.length?c.childNodes[d]:null,f=d?c.childNodes[d-1]:null;if(E&&e&&"false"==e.contentEditable)return ay(e);if((!e||"false"==e.contentEditable)&&(!f||"false"==f.contentEditable)){if(e)return ay(e);else if(f)return ay(f)}}function ay(a){return a.contentEditable="true",E&&a.draggable&&(a.draggable=!1,a.wasDraggable=!0),a}function az(a){a.contentEditable="false",a.wasDraggable&&(a.draggable=!0,a.wasDraggable=null)}function aA(a,b){if(b instanceof d.nh){let c=a.docView.descAt(b.from);c!=a.lastSelectedViewDesc&&(aB(a),c&&c.selectNode(),a.lastSelectedViewDesc=c)}else aB(a)}function aB(a){a.lastSelectedViewDesc&&(a.lastSelectedViewDesc.parent&&a.lastSelectedViewDesc.deselectNode(),a.lastSelectedViewDesc=void 0)}function aC(a,b,c,e){return a.someProp("createSelectionBetween",d=>d(a,b,c))||d.U3.between(b,c,e)}function aD(a){return(!a.editable||!!a.hasFocus())&&aE(a)}function aE(a){let b=a.domSelectionRange();if(!b.anchorNode)return!1;try{return a.dom.contains(3==b.anchorNode.nodeType?b.anchorNode.parentNode:b.anchorNode)&&(a.editable||a.dom.contains(3==b.focusNode.nodeType?b.focusNode.parentNode:b.focusNode))}catch(a){return!1}}function aF(a,b){let{$anchor:c,$head:e}=a.selection,f=b>0?c.max(e):c.min(e),g=f.parent.inlineContent?f.depth?a.doc.resolve(b>0?f.after():f.before()):null:f;return g&&d.LN.findFrom(g,b)}function aG(a,b){return a.dispatch(a.state.tr.setSelection(b).scrollIntoView()),!0}function aH(a,b,c){let e=a.state.selection;if(e instanceof d.U3){if(c.indexOf("s")>-1){let{$head:c}=e,f=c.textOffset?null:b<0?c.nodeBefore:c.nodeAfter;if(!f||f.isText||!f.isLeaf)return!1;let g=a.state.doc.resolve(c.pos+f.nodeSize*(b<0?-1:1));return aG(a,new d.U3(e.$anchor,g))}else if(!e.empty)return!1;else if(a.endOfTextblock(b>0?"forward":"backward")){let c=aF(a.state,b);return!!c&&c instanceof d.nh&&aG(a,c)}else if(!(G&&c.indexOf("m")>-1)){let c=e.$head,f=c.textOffset?null:b<0?c.nodeBefore:c.nodeAfter,g;if(!f||f.isText)return!1;let h=b<0?c.pos-f.nodeSize:c.pos;return!!(f.isAtom||(g=a.docView.descAt(h))&&!g.contentDOM)&&(d.nh.isSelectable(f)?aG(a,new d.nh(b<0?a.state.doc.resolve(c.pos-f.nodeSize):c)):!!J&&aG(a,new d.U3(a.state.doc.resolve(b<0?h:h+f.nodeSize))))}}else{if(e instanceof d.nh&&e.node.isInline)return aG(a,new d.U3(b>0?e.$to:e.$from));let c=aF(a.state,b);return!!c&&aG(a,c)}}function aI(a){return 3==a.nodeType?a.nodeValue.length:a.childNodes.length}function aJ(a,b){let c=a.pmViewDesc;return c&&0==c.size&&(b<0||a.nextSibling||"BR"!=a.nodeName)}function aK(a,b){return b<0?function(a){let b=a.domSelectionRange(),c=b.focusNode,d=b.focusOffset;if(!c)return;let e,f,h=!1;for(A&&1==c.nodeType&&d<aI(c)&&aJ(c.childNodes[d],-1)&&(h=!0);;)if(d>0)if(1!=c.nodeType)break;else{let a=c.childNodes[d-1];if(aJ(a,-1))e=c,f=--d;else if(3==a.nodeType)d=(c=a).nodeValue.length;else break}else if(aL(c))break;else{let b=c.previousSibling;for(;b&&aJ(b,-1);)e=c.parentNode,f=g(b),b=b.previousSibling;if(b)d=aI(c=b);else{if((c=c.parentNode)==a.dom)break;d=0}}h?aM(a,c,d):e&&aM(a,e,f)}(a):function(a){let b,c,d=a.domSelectionRange(),e=d.focusNode,f=d.focusOffset;if(!e)return;let h=aI(e);for(;;)if(f<h){if(1!=e.nodeType)break;if(aJ(e.childNodes[f],1))b=e,c=++f;else break}else if(aL(e))break;else{let d=e.nextSibling;for(;d&&aJ(d,1);)b=d.parentNode,c=g(d)+1,d=d.nextSibling;if(d)f=0,h=aI(e=d);else{if((e=e.parentNode)==a.dom)break;f=h=0}}b&&aM(a,b,c)}(a)}function aL(a){let b=a.pmViewDesc;return b&&b.node&&b.node.isBlock}function aM(a,b,c){if(3!=b.nodeType){let a,d;(d=function(a,b){for(;a&&b==a.childNodes.length&&!p(a);)b=g(a)+1,a=a.parentNode;for(;a&&b<a.childNodes.length;){let c=a.childNodes[b];if(3==c.nodeType)return c;if(1==c.nodeType&&"false"==c.contentEditable)break;a=c,b=0}}(b,c))?(b=d,c=0):(a=function(a,b){for(;a&&!b&&!p(a);)b=g(a),a=a.parentNode;for(;a&&b;){let c=a.childNodes[b-1];if(3==c.nodeType)return c;if(1==c.nodeType&&"false"==c.contentEditable)break;b=(a=c).childNodes.length}}(b,c))&&(b=a,c=a.nodeValue.length)}let d=a.domSelection();if(!d)return;if(q(d)){let a=document.createRange();a.setEnd(b,c),a.setStart(b,c),d.removeAllRanges(),d.addRange(a)}else d.extend&&d.extend(b,c);a.domObserver.setCurSelection();let{state:e}=a;setTimeout(()=>{a.state==e&&av(a)},50)}function aN(a,b){let c=a.state.doc.resolve(b);if(!(C||H)&&c.parent.inlineContent){let d=a.coordsAtPos(b);if(b>c.start()){let c=a.coordsAtPos(b-1),e=(c.top+c.bottom)/2;if(e>d.top&&e<d.bottom&&Math.abs(c.left-d.left)>1)return c.left<d.left?"ltr":"rtl"}if(b<c.end()){let c=a.coordsAtPos(b+1),e=(c.top+c.bottom)/2;if(e>d.top&&e<d.bottom&&Math.abs(c.left-d.left)>1)return c.left>d.left?"ltr":"rtl"}}return"rtl"==getComputedStyle(a.dom).direction?"rtl":"ltr"}function aO(a,b,c){let e=a.state.selection;if(e instanceof d.U3&&!e.empty||c.indexOf("s")>-1||G&&c.indexOf("m")>-1)return!1;let{$from:f,$to:g}=e;if(!f.parent.inlineContent||a.endOfTextblock(b<0?"up":"down")){let c=aF(a.state,b);if(c&&c instanceof d.nh)return aG(a,c)}if(!f.parent.inlineContent){let c=b<0?f:g,h=e instanceof d.i5?d.LN.near(c,b):d.LN.findFrom(c,b);return!!h&&aG(a,h)}return!1}function aP(a,b){if(!(a.state.selection instanceof d.U3))return!0;let{$head:c,$anchor:e,empty:f}=a.state.selection;if(!c.sameParent(e))return!0;if(!f)return!1;if(a.endOfTextblock(b>0?"forward":"backward"))return!0;let g=!c.textOffset&&(b<0?c.nodeBefore:c.nodeAfter);if(g&&!g.isText){let d=a.state.tr;return b<0?d.delete(c.pos-g.nodeSize,c.pos):d.delete(c.pos,c.pos+g.nodeSize),a.dispatch(d),!0}return!1}function aQ(a,b,c){a.domObserver.stop(),b.contentEditable=c,a.domObserver.start()}function aR(a,b){a.someProp("transformCopied",c=>{b=c(b,a)});let c=[],{content:d,openStart:f,openEnd:g}=b;for(;f>1&&g>1&&1==d.childCount&&1==d.firstChild.childCount;){f--,g--;let a=d.firstChild;c.push(a.type.name,a.attrs!=a.type.defaultAttrs?a.attrs:null),d=a.content}let h=a.someProp("clipboardSerializer")||e.ZF.fromSchema(a.state.schema),i=aZ(),j=i.createElement("div");j.appendChild(h.serializeFragment(d,{document:i}));let k=j.firstChild,l,m=0;for(;k&&1==k.nodeType&&(l=aX[k.nodeName.toLowerCase()]);){for(let a=l.length-1;a>=0;a--){let b=i.createElement(l[a]);for(;j.firstChild;)b.appendChild(j.firstChild);j.appendChild(b),m++}k=j.firstChild}return k&&1==k.nodeType&&k.setAttribute("data-pm-slice",`${f} ${g}${m?` -${m}`:""} ${JSON.stringify(c)}`),{dom:j,text:a.someProp("clipboardTextSerializer",c=>c(b,a))||b.content.textBetween(0,b.content.size,"\n\n"),slice:b}}function aS(a,b,c,d,f){let g,h,i=f.parent.type.spec.code;if(!c&&!b)return null;let j=b&&(d||i||!c);if(j){if(a.someProp("transformPastedText",c=>{b=c(b,i||d,a)}),i)return b?new e.Ji(e.FK.from(a.state.schema.text(b.replace(/\r\n?/g,"\n"))),0,0):e.Ji.empty;let c=a.someProp("clipboardTextParser",c=>c(b,f,d,a));if(c)h=c;else{let c=f.marks(),{schema:d}=a.state,h=e.ZF.fromSchema(d);g=document.createElement("div"),b.split(/(?:\r\n?|\n)+/).forEach(a=>{let b=g.appendChild(document.createElement("p"));a&&b.appendChild(h.serializeNode(d.text(a,c)))})}}else a.someProp("transformPastedHTML",b=>{c=b(c,a)}),g=function(a){var b;let c,d=/^(\s*<meta [^>]*>)*/.exec(a);d&&(a=a.slice(d[0].length));let e=aZ().createElement("div"),f=/<([a-z][^>\s]+)/i.exec(a),g;if((g=f&&aX[f[1].toLowerCase()])&&(a=g.map(a=>"<"+a+">").join("")+a+g.map(a=>"</"+a+">").reverse().join("")),e.innerHTML=(b=a,(c=window.trustedTypes)?(a$||(a$=c.defaultPolicy||c.createPolicy("ProseMirrorClipboard",{createHTML:a=>a})),a$.createHTML(b)):b),g)for(let a=0;a<g.length;a++)e=e.querySelector(g[a])||e;return e}(c),J&&function(a){let b=a.querySelectorAll(C?"span:not([class]):not([style])":"span.Apple-converted-space");for(let c=0;c<b.length;c++){let d=b[c];1==d.childNodes.length&&"\xa0"==d.textContent&&d.parentNode&&d.parentNode.replaceChild(a.ownerDocument.createTextNode(" "),d)}}(g);let k=g&&g.querySelector("[data-pm-slice]"),l=k&&/^(\d+) (\d+)(?: -(\d+))? (.*)/.exec(k.getAttribute("data-pm-slice")||"");if(l&&l[3])for(let a=+l[3];a>0;a--){let a=g.firstChild;for(;a&&1!=a.nodeType;)a=a.nextSibling;if(!a)break;g=a}if(h||(h=(a.someProp("clipboardParser")||a.someProp("domParser")||e.S4.fromSchema(a.state.schema)).parseSlice(g,{preserveWhitespace:!!(j||l),context:f,ruleFromNode:a=>"BR"!=a.nodeName||a.nextSibling||!a.parentNode||aT.test(a.parentNode.nodeName)?null:{ignore:!0}})),l)h=function(a,b){if(!a.size)return a;let c=a.content.firstChild.type.schema,d;try{d=JSON.parse(b)}catch(b){return a}let{content:f,openStart:g,openEnd:h}=a;for(let a=d.length-2;a>=0;a-=2){let b=c.nodes[d[a]];if(!b||b.hasRequiredAttrs())break;f=e.FK.from(b.create(d[a+1],f)),g++,h++}return new e.Ji(f,g,h)}(aW(h,+l[1],+l[2]),l[4]);else if((h=e.Ji.maxOpen(function(a,b){if(a.childCount<2)return a;for(let c=b.depth;c>=0;c--){let d=b.node(c).contentMatchAt(b.index(c)),f,g=[];if(a.forEach(a=>{if(!g)return;let b=d.findWrapping(a.type),c;if(!b)return g=null;if(c=g.length&&f.length&&function a(b,c,d,f,g){if(g<b.length&&g<c.length&&b[g]==c[g]){let h=a(b,c,d,f.lastChild,g+1);if(h)return f.copy(f.content.replaceChild(f.childCount-1,h));if(f.contentMatchAt(f.childCount).matchType(g==b.length-1?d.type:b[g+1]))return f.copy(f.content.append(e.FK.from(aU(d,b,g+1))))}}(b,f,a,g[g.length-1],0))g[g.length-1]=c;else{g.length&&(g[g.length-1]=function a(b,c){if(0==c)return b;let d=b.content.replaceChild(b.childCount-1,a(b.lastChild,c-1)),f=b.contentMatchAt(b.childCount).fillBefore(e.FK.empty,!0);return b.copy(d.append(f))}(g[g.length-1],f.length));let c=aU(a,b);g.push(c),d=d.matchType(c.type),f=b}}),g)return e.FK.from(g)}return a}(h.content,f),!0)).openStart||h.openEnd){let a=0,b=0;for(let b=h.content.firstChild;a<h.openStart&&!b.type.spec.isolating;a++,b=b.firstChild);for(let a=h.content.lastChild;b<h.openEnd&&!a.type.spec.isolating;b++,a=a.lastChild);h=aW(h,a,b)}return a.someProp("transformPasted",b=>{h=b(h,a)}),h}let aT=/^(a|abbr|acronym|b|cite|code|del|em|i|ins|kbd|label|output|q|ruby|s|samp|span|strong|sub|sup|time|u|tt|var)$/i;function aU(a,b,c=0){for(let d=b.length-1;d>=c;d--)a=b[d].create(null,e.FK.from(a));return a}function aV(a,b,c,d,f,g){let h=b<0?a.firstChild:a.lastChild,i=h.content;return a.childCount>1&&(g=0),f<d-1&&(i=aV(i,b,c,d,f+1,g)),f>=c&&(i=b<0?h.contentMatchAt(0).fillBefore(i,g<=f).append(i):i.append(h.contentMatchAt(h.childCount).fillBefore(e.FK.empty,!0))),a.replaceChild(b<0?0:a.childCount-1,h.copy(i))}function aW(a,b,c){return b<a.openStart&&(a=new e.Ji(aV(a.content,-1,b,a.openStart,0,a.openEnd),b,a.openEnd)),c<a.openEnd&&(a=new e.Ji(aV(a.content,1,c,a.openEnd,0,0),a.openStart,c)),a}let aX={thead:["table"],tbody:["table"],tfoot:["table"],caption:["table"],colgroup:["table"],col:["table","colgroup"],tr:["table","tbody"],td:["table","tbody","tr"],th:["table","tbody","tr"]},aY=null;function aZ(){return aY||(aY=document.implementation.createHTMLDocument("title"))}let a$=null,a_={},a0={},a1={touchstart:!0,touchmove:!0};class a2{constructor(){this.shiftKey=!1,this.mouseDown=null,this.lastKeyCode=null,this.lastKeyCodeTime=0,this.lastClick={time:0,x:0,y:0,type:"",button:0},this.lastSelectionOrigin=null,this.lastSelectionTime=0,this.lastIOSEnter=0,this.lastIOSEnterFallbackTimeout=-1,this.lastFocus=0,this.lastTouch=0,this.lastChromeDelete=0,this.composing=!1,this.compositionNode=null,this.composingTimeout=-1,this.compositionNodes=[],this.compositionEndedAt=-2e8,this.compositionID=1,this.compositionPendingChanges=0,this.domChangeCount=0,this.eventHandlers=Object.create(null),this.hideSelectionGuard=null}}function a3(a,b){a.input.lastSelectionOrigin=b,a.input.lastSelectionTime=Date.now()}function a4(a){a.someProp("handleDOMEvents",b=>{for(let c in b)a.input.eventHandlers[c]||a.dom.addEventListener(c,a.input.eventHandlers[c]=b=>a5(a,b))})}function a5(a,b){return a.someProp("handleDOMEvents",c=>{let d=c[b.type];return!!d&&(d(a,b)||b.defaultPrevented)})}function a6(a){return{left:a.clientX,top:a.clientY}}function a7(a,b,c,d,e){if(-1==d)return!1;let f=a.state.doc.resolve(d);for(let d=f.depth+1;d>0;d--)if(a.someProp(b,b=>d>f.depth?b(a,c,f.nodeAfter,f.before(d),e,!0):b(a,c,f.node(d),f.before(d),e,!1)))return!0;return!1}function a8(a,b,c){if(a.focused||a.focus(),a.state.selection.eq(b))return;let d=a.state.tr.setSelection(b);"pointer"==c&&d.setMeta("pointer",!0),a.dispatch(d)}a0.keydown=(a,b)=>{if((a.input.shiftKey=16==b.keyCode||b.shiftKey,!bb(a,b))&&(a.input.lastKeyCode=b.keyCode,a.input.lastKeyCodeTime=Date.now(),!I||!C||13!=b.keyCode))if(229!=b.keyCode&&a.domObserver.forceFlush(),!F||13!=b.keyCode||b.ctrlKey||b.altKey||b.metaKey)a.someProp("handleKeyDown",c=>c(a,b))||function(a,b){let c,d=b.keyCode,e=(c="",b.ctrlKey&&(c+="c"),b.metaKey&&(c+="m"),b.altKey&&(c+="a"),b.shiftKey&&(c+="s"),c);if(8==d||G&&72==d&&"c"==e)return aP(a,-1)||aK(a,-1);if(46==d&&!b.shiftKey||G&&68==d&&"c"==e)return aP(a,1)||aK(a,1);if(13==d||27==d)return!0;if(37==d||G&&66==d&&"c"==e){let b=37==d?"ltr"==aN(a,a.state.selection.from)?-1:1:-1;return aH(a,b,e)||aK(a,b)}if(39==d||G&&70==d&&"c"==e){let b=39==d?"ltr"==aN(a,a.state.selection.from)?1:-1:1;return aH(a,b,e)||aK(a,b)}else if(38==d||G&&80==d&&"c"==e)return aO(a,-1,e)||aK(a,-1);else if(40==d||G&&78==d&&"c"==e)return function(a){if(!E||a.state.selection.$head.parentOffset>0)return!1;let{focusNode:b,focusOffset:c}=a.domSelectionRange();if(b&&1==b.nodeType&&0==c&&b.firstChild&&"false"==b.firstChild.contentEditable){let c=b.firstChild;aQ(a,c,"true"),setTimeout(()=>aQ(a,c,"false"),20)}return!1}(a)||aO(a,1,e)||aK(a,1);else if(e==(G?"m":"c")&&(66==d||73==d||89==d||90==d))return!0;return!1}(a,b)?b.preventDefault():a3(a,"key");else{let b=Date.now();a.input.lastIOSEnter=b,a.input.lastIOSEnterFallbackTimeout=setTimeout(()=>{a.input.lastIOSEnter==b&&(a.someProp("handleKeyDown",b=>b(a,r(13,"Enter"))),a.input.lastIOSEnter=0)},200)}},a0.keyup=(a,b)=>{16==b.keyCode&&(a.input.shiftKey=!1)},a0.keypress=(a,b)=>{if(bb(a,b)||!b.charCode||b.ctrlKey&&!b.altKey||G&&b.metaKey)return;if(a.someProp("handleKeyPress",c=>c(a,b)))return void b.preventDefault();let c=a.state.selection;if(!(c instanceof d.U3)||!c.$from.sameParent(c.$to)){let d=String.fromCharCode(b.charCode),e=()=>a.state.tr.insertText(d).scrollIntoView();/[\r\n]/.test(d)||a.someProp("handleTextInput",b=>b(a,c.$from.pos,c.$to.pos,d,e))||a.dispatch(e()),b.preventDefault()}};let a9=G?"metaKey":"ctrlKey";a_.mousedown=(a,b)=>{a.input.shiftKey=b.shiftKey;let c=bf(a),e=Date.now(),f="singleClick";e-a.input.lastClick.time<500&&function(a,b){let c=b.x-a.clientX,d=b.y-a.clientY;return c*c+d*d<100}(b,a.input.lastClick)&&!b[a9]&&a.input.lastClick.button==b.button&&("singleClick"==a.input.lastClick.type?f="doubleClick":"doubleClick"==a.input.lastClick.type&&(f="tripleClick")),a.input.lastClick={time:e,x:b.clientX,y:b.clientY,type:f,button:b.button};let g=a.posAtCoords(a6(b));g&&("singleClick"==f?(a.input.mouseDown&&a.input.mouseDown.done(),a.input.mouseDown=new ba(a,g,b,!!c)):("doubleClick"==f?function(a,b,c,d){return a7(a,"handleDoubleClickOn",b,c,d)||a.someProp("handleDoubleClick",c=>c(a,b,d))}:function(a,b,c,e){return a7(a,"handleTripleClickOn",b,c,e)||a.someProp("handleTripleClick",c=>c(a,b,e))||function(a,b,c){if(0!=c.button)return!1;let e=a.state.doc;if(-1==b)return!!e.inlineContent&&(a8(a,d.U3.create(e,0,e.content.size),"pointer"),!0);let f=e.resolve(b);for(let b=f.depth+1;b>0;b--){let c=b>f.depth?f.nodeAfter:f.node(b),g=f.before(b);if(c.inlineContent)a8(a,d.U3.create(e,g+1,g+1+c.content.size),"pointer");else{if(!d.nh.isSelectable(c))continue;a8(a,d.nh.create(e,g),"pointer")}return!0}}(a,c,e)})(a,g.pos,g.inside,b)?b.preventDefault():a3(a,"pointer"))};class ba{constructor(a,b,c,e){let f,g;if(this.view=a,this.pos=b,this.event=c,this.flushed=e,this.delayedSelectionSync=!1,this.mightDrag=null,this.startDoc=a.state.doc,this.selectNode=!!c[a9],this.allowDefault=c.shiftKey,b.inside>-1)f=a.state.doc.nodeAt(b.inside),g=b.inside;else{let c=a.state.doc.resolve(b.pos);f=c.parent,g=c.depth?c.before():0}let h=e?null:c.target,i=h?a.docView.nearestDesc(h,!0):null;this.target=i&&1==i.dom.nodeType?i.dom:null;let{selection:j}=a.state;(0==c.button&&f.type.spec.draggable&&!1!==f.type.spec.selectable||j instanceof d.nh&&j.from<=g&&j.to>g)&&(this.mightDrag={node:f,pos:g,addAttr:!!(this.target&&!this.target.draggable),setUneditable:!!(this.target&&A&&!this.target.hasAttribute("contentEditable"))}),this.target&&this.mightDrag&&(this.mightDrag.addAttr||this.mightDrag.setUneditable)&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&(this.target.draggable=!0),this.mightDrag.setUneditable&&setTimeout(()=>{this.view.input.mouseDown==this&&this.target.setAttribute("contentEditable","false")},20),this.view.domObserver.start()),a.root.addEventListener("mouseup",this.up=this.up.bind(this)),a.root.addEventListener("mousemove",this.move=this.move.bind(this)),a3(a,"pointer")}done(){this.view.root.removeEventListener("mouseup",this.up),this.view.root.removeEventListener("mousemove",this.move),this.mightDrag&&this.target&&(this.view.domObserver.stop(),this.mightDrag.addAttr&&this.target.removeAttribute("draggable"),this.mightDrag.setUneditable&&this.target.removeAttribute("contentEditable"),this.view.domObserver.start()),this.delayedSelectionSync&&setTimeout(()=>av(this.view)),this.view.input.mouseDown=null}up(a){if(this.done(),!this.view.dom.contains(a.target))return;let b=this.pos;if(this.view.state.doc!=this.startDoc&&(b=this.view.posAtCoords(a6(a))),this.updateAllowDefault(a),this.allowDefault||!b)a3(this.view,"pointer");else{var c,e,f,g;(c=this.view,e=b.pos,f=b.inside,g=this.selectNode,a7(c,"handleClickOn",e,f,a)||c.someProp("handleClick",b=>b(c,e,a))||(g?function(a,b){if(-1==b)return!1;let c=a.state.selection,e,f;c instanceof d.nh&&(e=c.node);let g=a.state.doc.resolve(b);for(let a=g.depth+1;a>0;a--){let b=a>g.depth?g.nodeAfter:g.node(a);if(d.nh.isSelectable(b)){f=e&&c.$from.depth>0&&a>=c.$from.depth&&g.before(c.$from.depth+1)==c.$from.pos?g.before(c.$from.depth):g.before(a);break}}return null!=f&&(a8(a,d.nh.create(a.state.doc,f),"pointer"),!0)}(c,f):function(a,b){if(-1==b)return!1;let c=a.state.doc.resolve(b),e=c.nodeAfter;return!!(e&&e.isAtom&&d.nh.isSelectable(e))&&(a8(a,new d.nh(c),"pointer"),!0)}(c,f)))?a.preventDefault():0==a.button&&(this.flushed||E&&this.mightDrag&&!this.mightDrag.node.isAtom||C&&!this.view.state.selection.visible&&2>=Math.min(Math.abs(b.pos-this.view.state.selection.from),Math.abs(b.pos-this.view.state.selection.to)))?(a8(this.view,d.LN.near(this.view.state.doc.resolve(b.pos)),"pointer"),a.preventDefault()):a3(this.view,"pointer")}}move(a){this.updateAllowDefault(a),a3(this.view,"pointer"),0==a.buttons&&this.done()}updateAllowDefault(a){!this.allowDefault&&(Math.abs(this.event.x-a.clientX)>4||Math.abs(this.event.y-a.clientY)>4)&&(this.allowDefault=!0)}}function bb(a,b){return!!a.composing||!!(E&&500>Math.abs(b.timeStamp-a.input.compositionEndedAt))&&(a.input.compositionEndedAt=-2e8,!0)}a_.touchstart=a=>{a.input.lastTouch=Date.now(),bf(a),a3(a,"pointer")},a_.touchmove=a=>{a.input.lastTouch=Date.now(),a3(a,"pointer")},a_.contextmenu=a=>bf(a);let bc=I?5e3:-1;function bd(a,b){clearTimeout(a.input.composingTimeout),b>-1&&(a.input.composingTimeout=setTimeout(()=>bf(a),b))}function be(a){let b;for(a.composing&&(a.input.composing=!1,a.input.compositionEndedAt=((b=document.createEvent("Event")).initEvent("event",!0,!0),b.timeStamp));a.input.compositionNodes.length>0;)a.input.compositionNodes.pop().markParentsDirty()}function bf(a,b=!1){if(!I||!(a.domObserver.flushingSoon>=0)){if(a.domObserver.forceFlush(),be(a),b||a.docView&&a.docView.dirty){let c=at(a),d=a.state.selection;return c&&!c.eq(d)?a.dispatch(a.state.tr.setSelection(c)):(a.markCursor||b)&&!d.$from.node(d.$from.sharedDepth(d.to)).inlineContent?a.dispatch(a.state.tr.deleteSelection()):a.updateState(a.state),!0}return!1}}a0.compositionstart=a0.compositionupdate=a=>{if(!a.composing){a.domObserver.flush();let{state:b}=a,c=b.selection.$to;if(b.selection instanceof d.U3&&(b.storedMarks||!c.textOffset&&c.parentOffset&&c.nodeBefore.marks.some(a=>!1===a.type.spec.inclusive)))a.markCursor=a.state.storedMarks||c.marks(),bf(a,!0),a.markCursor=null;else if(bf(a,!b.selection.empty),A&&b.selection.empty&&c.parentOffset&&!c.textOffset&&c.nodeBefore.marks.length){let b=a.domSelectionRange();for(let c=b.focusNode,d=b.focusOffset;c&&1==c.nodeType&&0!=d;){let b=d<0?c.lastChild:c.childNodes[d-1];if(!b)break;if(3==b.nodeType){let c=a.domSelection();c&&c.collapse(b,b.nodeValue.length);break}c=b,d=-1}}a.input.composing=!0}bd(a,bc)},a0.compositionend=(a,b)=>{a.composing&&(a.input.composing=!1,a.input.compositionEndedAt=b.timeStamp,a.input.compositionPendingChanges=a.domObserver.pendingRecords().length?a.input.compositionID:0,a.input.compositionNode=null,a.input.compositionPendingChanges&&Promise.resolve().then(()=>a.domObserver.flush()),a.input.compositionID++,bd(a,20))};let bg=y&&z<15||F&&K<604;function bh(a,b,c,d,f){let g=aS(a,b,c,d,a.state.selection.$from);if(a.someProp("handlePaste",b=>b(a,f,g||e.Ji.empty)))return!0;if(!g)return!1;let h=0==g.openStart&&0==g.openEnd&&1==g.content.childCount?g.content.firstChild:null,i=h?a.state.tr.replaceSelectionWith(h,d):a.state.tr.replaceSelection(g);return a.dispatch(i.scrollIntoView().setMeta("paste",!0).setMeta("uiEvent","paste")),!0}function bi(a){let b=a.getData("text/plain")||a.getData("Text");if(b)return b;let c=a.getData("text/uri-list");return c?c.replace(/\r?\n/g," "):""}a_.copy=a0.cut=(a,b)=>{let c=a.state.selection,d="cut"==b.type;if(c.empty)return;let e=bg?null:b.clipboardData,{dom:f,text:g}=aR(a,c.content());e?(b.preventDefault(),e.clearData(),e.setData("text/html",f.innerHTML),e.setData("text/plain",g)):function(a,b){if(!a.dom.parentNode)return;let c=a.dom.parentNode.appendChild(document.createElement("div"));c.appendChild(b),c.style.cssText="position: fixed; left: -10000px; top: 10px";let d=getSelection(),e=document.createRange();e.selectNodeContents(b),a.dom.blur(),d.removeAllRanges(),d.addRange(e),setTimeout(()=>{c.parentNode&&c.parentNode.removeChild(c),a.focus()},50)}(a,f),d&&a.dispatch(a.state.tr.deleteSelection().scrollIntoView().setMeta("uiEvent","cut"))},a0.paste=(a,b)=>{if(a.composing&&!I)return;let c=bg?null:b.clipboardData,d=a.input.shiftKey&&45!=a.input.lastKeyCode;c&&bh(a,bi(c),c.getData("text/html"),d,b)?b.preventDefault():function(a,b){if(!a.dom.parentNode)return;let c=a.input.shiftKey||a.state.selection.$from.parent.type.spec.code,d=a.dom.parentNode.appendChild(document.createElement(c?"textarea":"div"));c||(d.contentEditable="true"),d.style.cssText="position: fixed; left: -10000px; top: 10px",d.focus();let e=a.input.shiftKey&&45!=a.input.lastKeyCode;setTimeout(()=>{a.focus(),d.parentNode&&d.parentNode.removeChild(d),c?bh(a,d.value,null,e,b):bh(a,d.textContent,d.innerHTML,e,b)},50)}(a,b)};class bj{constructor(a,b,c){this.slice=a,this.move=b,this.node=c}}let bk=G?"altKey":"ctrlKey";function bl(a,b){let c=a.someProp("dragCopies",a=>!a(b));return null!=c?c:!b[bk]}for(let a in a_.dragstart=(a,b)=>{let c,e=a.input.mouseDown;if(e&&e.done(),!b.dataTransfer)return;let f=a.state.selection,g=f.empty?null:a.posAtCoords(a6(b));if(g&&g.pos>=f.from&&g.pos<=(f instanceof d.nh?f.to-1:f.to));else if(e&&e.mightDrag)c=d.nh.create(a.state.doc,e.mightDrag.pos);else if(b.target&&1==b.target.nodeType){let e=a.docView.nearestDesc(b.target,!0);e&&e.node.type.spec.draggable&&e!=a.docView&&(c=d.nh.create(a.state.doc,e.posBefore))}let h=(c||a.state.selection).content(),{dom:i,text:j,slice:k}=aR(a,h);b.dataTransfer.files.length&&C&&!(D>120)||b.dataTransfer.clearData(),b.dataTransfer.setData(bg?"Text":"text/html",i.innerHTML),b.dataTransfer.effectAllowed="copyMove",bg||b.dataTransfer.setData("text/plain",j),a.dragging=new bj(k,bl(a,b),c)},a_.dragend=a=>{let b=a.dragging;window.setTimeout(()=>{a.dragging==b&&(a.dragging=null)},50)},a0.dragover=a0.dragenter=(a,b)=>b.preventDefault(),a0.drop=(a,b)=>{let c=a.dragging;if(a.dragging=null,!b.dataTransfer)return;let g=a.posAtCoords(a6(b));if(!g)return;let h=a.state.doc.resolve(g.pos),i=c&&c.slice;i?a.someProp("transformPasted",b=>{i=b(i,a)}):i=aS(a,bi(b.dataTransfer),bg?null:b.dataTransfer.getData("text/html"),!1,h);let j=!!(c&&bl(a,b));if(a.someProp("handleDrop",c=>c(a,b,i||e.Ji.empty,j)))return void b.preventDefault();if(!i)return;b.preventDefault();let k=i?(0,f.Um)(a.state.doc,h.pos,i):h.pos;null==k&&(k=h.pos);let l=a.state.tr;if(j){let{node:a}=c;a?a.replace(l):l.deleteSelection()}let m=l.mapping.map(k),n=0==i.openStart&&0==i.openEnd&&1==i.content.childCount,o=l.doc;if(n?l.replaceRangeWith(m,m,i.content.firstChild):l.replaceRange(m,m,i),l.doc.eq(o))return;let p=l.doc.resolve(m);if(n&&d.nh.isSelectable(i.content.firstChild)&&p.nodeAfter&&p.nodeAfter.sameMarkup(i.content.firstChild))l.setSelection(new d.nh(p));else{let b=l.mapping.map(k);l.mapping.maps[l.mapping.maps.length-1].forEach((a,c,d,e)=>b=e),l.setSelection(aC(a,p,l.doc.resolve(b)))}a.focus(),a.dispatch(l.setMeta("uiEvent","drop"))},a_.focus=a=>{a.input.lastFocus=Date.now(),a.focused||(a.domObserver.stop(),a.dom.classList.add("ProseMirror-focused"),a.domObserver.start(),a.focused=!0,setTimeout(()=>{a.docView&&a.hasFocus()&&!a.domObserver.currentSelection.eq(a.domSelectionRange())&&av(a)},20))},a_.blur=(a,b)=>{a.focused&&(a.domObserver.stop(),a.dom.classList.remove("ProseMirror-focused"),a.domObserver.start(),b.relatedTarget&&a.dom.contains(b.relatedTarget)&&a.domObserver.currentSelection.clear(),a.focused=!1)},a_.beforeinput=(a,b)=>{if(C&&I&&"deleteContentBackward"==b.inputType){a.domObserver.flushSoon();let{domChangeCount:b}=a.input;setTimeout(()=>{if(a.input.domChangeCount!=b||(a.dom.blur(),a.focus(),a.someProp("handleKeyDown",b=>b(a,r(8,"Backspace")))))return;let{$cursor:c}=a.state.selection;c&&c.pos>0&&a.dispatch(a.state.tr.delete(c.pos-1,c.pos).scrollIntoView())},50)}},a0)a_[a]=a0[a];function bm(a,b){if(a==b)return!0;for(let c in a)if(a[c]!==b[c])return!1;for(let c in b)if(!(c in a))return!1;return!0}class bn{constructor(a,b){this.toDOM=a,this.spec=b||bs,this.side=this.spec.side||0}map(a,b,c,d){let{pos:e,deleted:f}=a.mapResult(b.from+d,this.side<0?-1:1);return f?null:new bq(e-c,e-c,this)}valid(){return!0}eq(a){return this==a||a instanceof bn&&(this.spec.key&&this.spec.key==a.spec.key||this.toDOM==a.toDOM&&bm(this.spec,a.spec))}destroy(a){this.spec.destroy&&this.spec.destroy(a)}}class bo{constructor(a,b){this.attrs=a,this.spec=b||bs}map(a,b,c,d){let e=a.map(b.from+d,this.spec.inclusiveStart?-1:1)-c,f=a.map(b.to+d,this.spec.inclusiveEnd?1:-1)-c;return e>=f?null:new bq(e,f,this)}valid(a,b){return b.from<b.to}eq(a){return this==a||a instanceof bo&&bm(this.attrs,a.attrs)&&bm(this.spec,a.spec)}static is(a){return a.type instanceof bo}destroy(){}}class bp{constructor(a,b){this.attrs=a,this.spec=b||bs}map(a,b,c,d){let e=a.mapResult(b.from+d,1);if(e.deleted)return null;let f=a.mapResult(b.to+d,-1);return f.deleted||f.pos<=e.pos?null:new bq(e.pos-c,f.pos-c,this)}valid(a,b){let{index:c,offset:d}=a.content.findIndex(b.from),e;return d==b.from&&!(e=a.child(c)).isText&&d+e.nodeSize==b.to}eq(a){return this==a||a instanceof bp&&bm(this.attrs,a.attrs)&&bm(this.spec,a.spec)}destroy(){}}class bq{constructor(a,b,c){this.from=a,this.to=b,this.type=c}copy(a,b){return new bq(a,b,this.type)}eq(a,b=0){return this.type.eq(a.type)&&this.from+b==a.from&&this.to+b==a.to}map(a,b,c){return this.type.map(a,this,b,c)}static widget(a,b,c){return new bq(a,a,new bn(b,c))}static inline(a,b,c,d){return new bq(a,b,new bo(c,d))}static node(a,b,c,d){return new bq(a,b,new bp(c,d))}get spec(){return this.type.spec}get inline(){return this.type instanceof bo}get widget(){return this.type instanceof bn}}let br=[],bs={};class bt{constructor(a,b){this.local=a.length?a:br,this.children=b.length?b:br}static create(a,b){return b.length?bz(b,a,0,bs):bu}find(a,b,c){let d=[];return this.findInner(null==a?0:a,null==b?1e9:b,d,0,c),d}findInner(a,b,c,d,e){for(let f=0;f<this.local.length;f++){let g=this.local[f];g.from<=b&&g.to>=a&&(!e||e(g.spec))&&c.push(g.copy(g.from+d,g.to+d))}for(let f=0;f<this.children.length;f+=3)if(this.children[f]<b&&this.children[f+1]>a){let g=this.children[f]+1;this.children[f+2].findInner(a-g,b-g,c,d+g,e)}}map(a,b,c){return this==bu||0==a.maps.length?this:this.mapInner(a,b,0,0,c||bs)}mapInner(a,b,c,d,e){let f;for(let g=0;g<this.local.length;g++){let h=this.local[g].map(a,c,d);h&&h.type.valid(b,h)?(f||(f=[])).push(h):e.onRemove&&e.onRemove(this.local[g].spec)}return this.children.length?function(a,b,c,d,e,f,g){let h=a.slice();for(let a=0,b=f;a<c.maps.length;a++){let d=0;c.maps[a].forEach((a,c,e,f)=>{let g=f-e-(c-a);for(let e=0;e<h.length;e+=3){let f=h[e+1];if(f<0||a>f+b-d)continue;let i=h[e]+b-d;c>=i?h[e+1]=a<=i?-2:-1:a>=b&&g&&(h[e]+=g,h[e+1]+=g)}d+=g}),b=c.maps[a].map(b,-1)}let i=!1;for(let b=0;b<h.length;b+=3)if(h[b+1]<0){if(-2==h[b+1]){i=!0,h[b+1]=-1;continue}let j=c.map(a[b]+f),k=j-e;if(k<0||k>=d.content.size){i=!0;continue}let l=c.map(a[b+1]+f,-1)-e,{index:m,offset:n}=d.content.findIndex(k),o=d.maybeChild(m);if(o&&n==k&&n+o.nodeSize==l){let d=h[b+2].mapInner(c,o,j+1,a[b]+f+1,g);d!=bu?(h[b]=k,h[b+1]=l,h[b+2]=d):(h[b+1]=-2,i=!0)}else i=!0}if(i){let i=bz(function(a,b,c,d,e,f,g){for(let h=0;h<a.length;h+=3)-1==a[h+1]&&function a(b,f){for(let a=0;a<b.local.length;a++){let h=b.local[a].map(d,e,f);h?c.push(h):g.onRemove&&g.onRemove(b.local[a].spec)}for(let c=0;c<b.children.length;c+=3)a(b.children[c+2],b.children[c]+f+1)}(a[h+2],b[h]+f+1);return c}(h,a,b,c,e,f,g),d,0,g);b=i.local;for(let a=0;a<h.length;a+=3)h[a+1]<0&&(h.splice(a,3),a-=3);for(let a=0,b=0;a<i.children.length;a+=3){let c=i.children[a];for(;b<h.length&&h[b]<c;)b+=3;h.splice(b,0,i.children[a],i.children[a+1],i.children[a+2])}}return new bt(b.sort(bA),h)}(this.children,f||[],a,b,c,d,e):f?new bt(f.sort(bA),br):bu}add(a,b){return b.length?this==bu?bt.create(a,b):this.addInner(a,b,0):this}addInner(a,b,c){let d,e=0;a.forEach((a,f)=>{let g=f+c,h;if(h=bx(b,a,g)){for(d||(d=this.children.slice());e<d.length&&d[e]<f;)e+=3;d[e]==f?d[e+2]=d[e+2].addInner(a,h,g+1):d.splice(e,0,f,f+a.nodeSize,bz(h,a,g+1,bs)),e+=3}});let f=bw(e?by(b):b,-c);for(let b=0;b<f.length;b++)f[b].type.valid(a,f[b])||f.splice(b--,1);return new bt(f.length?this.local.concat(f).sort(bA):this.local,d||this.children)}remove(a){return 0==a.length||this==bu?this:this.removeInner(a,0)}removeInner(a,b){let c=this.children,d=this.local;for(let d=0;d<c.length;d+=3){let e,f=c[d]+b,g=c[d+1]+b;for(let b=0,c;b<a.length;b++)(c=a[b])&&c.from>f&&c.to<g&&(a[b]=null,(e||(e=[])).push(c));if(!e)continue;c==this.children&&(c=this.children.slice());let h=c[d+2].removeInner(e,f+1);h!=bu?c[d+2]=h:(c.splice(d,3),d-=3)}if(d.length){for(let c=0,e;c<a.length;c++)if(e=a[c])for(let a=0;a<d.length;a++)d[a].eq(e,b)&&(d==this.local&&(d=this.local.slice()),d.splice(a--,1))}return c==this.children&&d==this.local?this:d.length||c.length?new bt(d,c):bu}forChild(a,b){let c,d;if(this==bu)return this;if(b.isLeaf)return bt.empty;for(let b=0;b<this.children.length;b+=3)if(this.children[b]>=a){this.children[b]==a&&(c=this.children[b+2]);break}let e=a+1,f=e+b.content.size;for(let a=0;a<this.local.length;a++){let b=this.local[a];if(b.from<f&&b.to>e&&b.type instanceof bo){let a=Math.max(e,b.from)-e,c=Math.min(f,b.to)-e;a<c&&(d||(d=[])).push(b.copy(a,c))}}if(d){let a=new bt(d.sort(bA),br);return c?new bv([a,c]):a}return c||bu}eq(a){if(this==a)return!0;if(!(a instanceof bt)||this.local.length!=a.local.length||this.children.length!=a.children.length)return!1;for(let b=0;b<this.local.length;b++)if(!this.local[b].eq(a.local[b]))return!1;for(let b=0;b<this.children.length;b+=3)if(this.children[b]!=a.children[b]||this.children[b+1]!=a.children[b+1]||!this.children[b+2].eq(a.children[b+2]))return!1;return!0}locals(a){return bB(this.localsInner(a))}localsInner(a){if(this==bu)return br;if(a.inlineContent||!this.local.some(bo.is))return this.local;let b=[];for(let a=0;a<this.local.length;a++)this.local[a].type instanceof bo||b.push(this.local[a]);return b}forEachSet(a){a(this)}}bt.empty=new bt([],[]),bt.removeOverlap=bB;let bu=bt.empty;class bv{constructor(a){this.members=a}map(a,b){let c=this.members.map(c=>c.map(a,b,bs));return bv.from(c)}forChild(a,b){if(b.isLeaf)return bt.empty;let c=[];for(let d=0;d<this.members.length;d++){let e=this.members[d].forChild(a,b);e!=bu&&(e instanceof bv?c=c.concat(e.members):c.push(e))}return bv.from(c)}eq(a){if(!(a instanceof bv)||a.members.length!=this.members.length)return!1;for(let b=0;b<this.members.length;b++)if(!this.members[b].eq(a.members[b]))return!1;return!0}locals(a){let b,c=!0;for(let d=0;d<this.members.length;d++){let e=this.members[d].localsInner(a);if(e.length)if(b){c&&(b=b.slice(),c=!1);for(let a=0;a<e.length;a++)b.push(e[a])}else b=e}return b?bB(c?b:b.sort(bA)):br}static from(a){switch(a.length){case 0:return bu;case 1:return a[0];default:return new bv(a.every(a=>a instanceof bt)?a:a.reduce((a,b)=>a.concat(b instanceof bt?b:b.members),[]))}}forEachSet(a){for(let b=0;b<this.members.length;b++)this.members[b].forEachSet(a)}}function bw(a,b){if(!b||!a.length)return a;let c=[];for(let d=0;d<a.length;d++){let e=a[d];c.push(new bq(e.from+b,e.to+b,e.type))}return c}function bx(a,b,c){if(b.isLeaf)return null;let d=c+b.nodeSize,e=null;for(let b=0,f;b<a.length;b++)(f=a[b])&&f.from>c&&f.to<d&&((e||(e=[])).push(f),a[b]=null);return e}function by(a){let b=[];for(let c=0;c<a.length;c++)null!=a[c]&&b.push(a[c]);return b}function bz(a,b,c,d){let e=[],f=!1;b.forEach((b,g)=>{let h=bx(a,b,g+c);if(h){f=!0;let a=bz(h,b,c+g+1,d);a!=bu&&e.push(g,g+b.nodeSize,a)}});let g=bw(f?by(a):a,-c).sort(bA);for(let a=0;a<g.length;a++)g[a].type.valid(b,g[a])||(d.onRemove&&d.onRemove(g[a].spec),g.splice(a--,1));return g.length||e.length?new bt(g,e):bu}function bA(a,b){return a.from-b.from||a.to-b.to}function bB(a){let b=a;for(let c=0;c<b.length-1;c++){let d=b[c];if(d.from!=d.to)for(let e=c+1;e<b.length;e++){let f=b[e];if(f.from==d.from){f.to!=d.to&&(b==a&&(b=a.slice()),b[e]=f.copy(f.from,d.to),bC(b,e+1,f.copy(d.to,f.to)));continue}f.from<d.to&&(b==a&&(b=a.slice()),b[c]=d.copy(d.from,f.from),bC(b,e,d.copy(f.from,d.to)));break}}return b}function bC(a,b,c){for(;b<a.length&&bA(c,a[b])>0;)b++;a.splice(b,0,c)}function bD(a){let b=[];return a.someProp("decorations",c=>{let d=c(a.state);d&&d!=bu&&b.push(d)}),a.cursorWrapper&&b.push(bt.create(a.state.doc,[a.cursorWrapper.deco])),bv.from(b)}let bE={childList:!0,characterData:!0,characterDataOldValue:!0,attributes:!0,attributeOldValue:!0,subtree:!0},bF=y&&z<=11;class bG{constructor(){this.anchorNode=null,this.anchorOffset=0,this.focusNode=null,this.focusOffset=0}set(a){this.anchorNode=a.anchorNode,this.anchorOffset=a.anchorOffset,this.focusNode=a.focusNode,this.focusOffset=a.focusOffset}clear(){this.anchorNode=this.focusNode=null}eq(a){return a.anchorNode==this.anchorNode&&a.anchorOffset==this.anchorOffset&&a.focusNode==this.focusNode&&a.focusOffset==this.focusOffset}}class bH{constructor(a,b){this.view=a,this.handleDOMChange=b,this.queue=[],this.flushingSoon=-1,this.observer=null,this.currentSelection=new bG,this.onCharData=null,this.suppressingSelectionUpdates=!1,this.lastChangedTextNode=null,this.observer=window.MutationObserver&&new window.MutationObserver(a=>{for(let b=0;b<a.length;b++)this.queue.push(a[b]);y&&z<=11&&a.some(a=>"childList"==a.type&&a.removedNodes.length||"characterData"==a.type&&a.oldValue.length>a.target.nodeValue.length)?this.flushSoon():this.flush()}),bF&&(this.onCharData=a=>{this.queue.push({target:a.target,type:"characterData",oldValue:a.prevValue}),this.flushSoon()}),this.onSelectionChange=this.onSelectionChange.bind(this)}flushSoon(){this.flushingSoon<0&&(this.flushingSoon=window.setTimeout(()=>{this.flushingSoon=-1,this.flush()},20))}forceFlush(){this.flushingSoon>-1&&(window.clearTimeout(this.flushingSoon),this.flushingSoon=-1,this.flush())}start(){this.observer&&(this.observer.takeRecords(),this.observer.observe(this.view.dom,bE)),this.onCharData&&this.view.dom.addEventListener("DOMCharacterDataModified",this.onCharData),this.connectSelection()}stop(){if(this.observer){let a=this.observer.takeRecords();if(a.length){for(let b=0;b<a.length;b++)this.queue.push(a[b]);window.setTimeout(()=>this.flush(),20)}this.observer.disconnect()}this.onCharData&&this.view.dom.removeEventListener("DOMCharacterDataModified",this.onCharData),this.disconnectSelection()}connectSelection(){this.view.dom.ownerDocument.addEventListener("selectionchange",this.onSelectionChange)}disconnectSelection(){this.view.dom.ownerDocument.removeEventListener("selectionchange",this.onSelectionChange)}suppressSelectionUpdates(){this.suppressingSelectionUpdates=!0,setTimeout(()=>this.suppressingSelectionUpdates=!1,50)}onSelectionChange(){if(aD(this.view)){if(this.suppressingSelectionUpdates)return av(this.view);if(y&&z<=11&&!this.view.state.selection.empty){let a=this.view.domSelectionRange();if(a.focusNode&&l(a.focusNode,a.focusOffset,a.anchorNode,a.anchorOffset))return this.flushSoon()}this.flush()}}setCurSelection(){this.currentSelection.set(this.view.domSelectionRange())}ignoreSelectionChange(a){if(!a.focusNode)return!0;let b=new Set,c;for(let c=a.focusNode;c;c=h(c))b.add(c);for(let d=a.anchorNode;d;d=h(d))if(b.has(d)){c=d;break}let d=c&&this.view.docView.nearestDesc(c);if(d&&d.ignoreMutation({type:"selection",target:3==c.nodeType?c.parentNode:c}))return this.setCurSelection(),!0}pendingRecords(){if(this.observer)for(let a of this.observer.takeRecords())this.queue.push(a);return this.queue}flush(){var a;let{view:b}=this;if(!b.docView||this.flushingSoon>-1)return;let c=this.pendingRecords();c.length&&(this.queue=[]);let e=b.domSelectionRange(),f=!this.suppressingSelectionUpdates&&!this.currentSelection.eq(e)&&aD(b)&&!this.ignoreSelectionChange(e),g=-1,h=-1,i=!1,j=[];if(b.editable)for(let a=0;a<c.length;a++){let b=this.registerMutation(c[a],j);b&&(g=g<0?b.from:Math.min(b.from,g),h=h<0?b.to:Math.max(b.to,h),b.typeOver&&(i=!0))}if(A&&j.length){let a=j.filter(a=>"BR"==a.nodeName);if(2==a.length){let[b,c]=a;b.parentNode&&b.parentNode.parentNode==c.parentNode?c.remove():b.remove()}else{let{focusNode:c}=this.currentSelection;for(let d of a){let a=d.parentNode;a&&"LI"==a.nodeName&&(!c||function(a,b){for(let c=b.parentNode;c&&c!=a.dom;c=c.parentNode){let b=a.docView.nearestDesc(c,!0);if(b&&b.node.isBlock)return c}return null}(b,c)!=a)&&d.remove()}}}let k=null;g<0&&f&&b.input.lastFocus>Date.now()-200&&Math.max(b.input.lastTouch,b.input.lastClick.time)<Date.now()-300&&q(e)&&(k=at(b))&&k.eq(d.LN.near(b.state.doc.resolve(0),1))?(b.input.lastFocus=0,av(b),this.currentSelection.set(e),b.scrollToSelection()):(g>-1||f)&&(g>-1&&(b.docView.markDirty(g,h),a=b,!bI.has(a)&&(bI.set(a,null),-1!==["normal","nowrap","pre-line"].indexOf(getComputedStyle(a.dom).whiteSpace))&&(a.requiresGeckoHackNode=A,bJ||(console.warn("ProseMirror expects the CSS white-space property to be set, preferably to 'pre-wrap'. It is recommended to load style/prosemirror.css from the prosemirror-view package."),bJ=!0))),this.handleDOMChange(g,h,i,j),b.docView&&b.docView.dirty?b.updateState(b.state):this.currentSelection.eq(e)||av(b),this.currentSelection.set(e))}registerMutation(a,b){if(b.indexOf(a.target)>-1)return null;let c=this.view.docView.nearestDesc(a.target);if("attributes"==a.type&&(c==this.view.docView||"contenteditable"==a.attributeName||"style"==a.attributeName&&!a.oldValue&&!a.target.getAttribute("style"))||!c||c.ignoreMutation(a))return null;if("childList"==a.type){for(let c=0;c<a.addedNodes.length;c++){let d=a.addedNodes[c];b.push(d),3==d.nodeType&&(this.lastChangedTextNode=d)}if(c.contentDOM&&c.contentDOM!=c.dom&&!c.contentDOM.contains(a.target))return{from:c.posBefore,to:c.posAfter};let d=a.previousSibling,e=a.nextSibling;if(y&&z<=11&&a.addedNodes.length)for(let b=0;b<a.addedNodes.length;b++){let{previousSibling:c,nextSibling:f}=a.addedNodes[b];(!c||0>Array.prototype.indexOf.call(a.addedNodes,c))&&(d=c),(!f||0>Array.prototype.indexOf.call(a.addedNodes,f))&&(e=f)}let f=d&&d.parentNode==a.target?g(d)+1:0,h=c.localPosFromDOM(a.target,f,-1),i=e&&e.parentNode==a.target?g(e):a.target.childNodes.length;return{from:h,to:c.localPosFromDOM(a.target,i,1)}}return"attributes"==a.type?{from:c.posAtStart-c.border,to:c.posAtEnd+c.border}:(this.lastChangedTextNode=a.target,{from:c.posAtStart,to:c.posAtEnd,typeOver:a.target.nodeValue==a.oldValue})}}let bI=new WeakMap,bJ=!1;function bK(a,b){let c=b.startContainer,d=b.startOffset,e=b.endContainer,f=b.endOffset,g=a.domAtPos(a.state.selection.anchor);return l(g.node,g.offset,e,f)&&([c,d,e,f]=[e,f,c,d]),{anchorNode:c,anchorOffset:d,focusNode:e,focusOffset:f}}function bL(a){let b=a.pmViewDesc;if(b)return b.parseRule();if("BR"==a.nodeName&&a.parentNode){if(E&&/^(ul|ol)$/i.test(a.parentNode.nodeName)){let a=document.createElement("div");return a.appendChild(document.createElement("li")),{skip:a}}else if(a.parentNode.lastChild==a||E&&/^(tr|table)$/i.test(a.parentNode.nodeName))return{ignore:!0}}else if("IMG"==a.nodeName&&a.getAttribute("mark-placeholder"))return{ignore:!0};return null}let bM=/^(a|abbr|acronym|b|bd[io]|big|br|button|cite|code|data(list)?|del|dfn|em|i|ins|kbd|label|map|mark|meter|output|q|ruby|s|samp|small|span|strong|su[bp]|time|u|tt|var)$/i;function bN(a,b,c){return Math.max(c.anchor,c.head)>b.content.size?null:aC(a,b.resolve(c.anchor),b.resolve(c.head))}function bO(a,b,c){let d=a.depth,e=b?a.end():a.pos;for(;d>0&&(b||a.indexAfter(d)==a.node(d).childCount);)d--,e++,b=!1;if(c){let b=a.node(d).maybeChild(a.indexAfter(d));for(;b&&!b.isLeaf;)b=b.firstChild,e++}return e}function bP(a){if(2!=a.length)return!1;let b=a.charCodeAt(0),c=a.charCodeAt(1);return b>=56320&&b<=57343&&c>=55296&&c<=56319}class bQ{constructor(a,b){this._root=null,this.focused=!1,this.trackWrites=null,this.mounted=!1,this.markCursor=null,this.cursorWrapper=null,this.lastSelectedViewDesc=void 0,this.input=new a2,this.prevDirectPlugins=[],this.pluginViews=[],this.requiresGeckoHackNode=!1,this.dragging=null,this._props=b,this.state=b.state,this.directPlugins=b.plugins||[],this.directPlugins.forEach(bV),this.dispatch=this.dispatch.bind(this),this.dom=a&&a.mount||document.createElement("div"),a&&(a.appendChild?a.appendChild(this.dom):"function"==typeof a?a(this.dom):a.mount&&(this.mounted=!0)),this.editable=bT(this),bS(this),this.nodeViews=bU(this),this.docView=af(this.state.doc,bR(this),bD(this),this.dom,this),this.domObserver=new bH(this,(a,b,c,f)=>(function(a,b,c,f,g){let h,i,j,k,l=a.input.compositionPendingChanges||(a.composing?a.input.compositionID:0);if(a.input.compositionPendingChanges=0,b<0){let b=a.input.lastSelectionTime>Date.now()-50?a.input.lastSelectionOrigin:null,c=at(a,b);if(c&&!a.state.selection.eq(c)){if(C&&I&&13===a.input.lastKeyCode&&Date.now()-100<a.input.lastKeyCodeTime&&a.someProp("handleKeyDown",b=>b(a,r(13,"Enter"))))return;let d=a.state.tr.setSelection(c);"pointer"==b?d.setMeta("pointer",!0):"key"==b&&d.scrollIntoView(),l&&d.setMeta("composition",l),a.dispatch(d)}return}let m=a.state.doc.resolve(b),n=m.sharedDepth(c);b=m.before(n+1),c=a.state.doc.resolve(c).after(n+1);let o=a.state.selection,p=function(a,b,c){let d,{node:f,fromOffset:g,toOffset:h,from:i,to:j}=a.docView.parseRange(b,c),k=a.domSelectionRange(),l=k.anchorNode;if(l&&a.dom.contains(1==l.nodeType?l:l.parentNode)&&(d=[{node:l,offset:k.anchorOffset}],q(k)||d.push({node:k.focusNode,offset:k.focusOffset})),C&&8===a.input.lastKeyCode)for(let a=h;a>g;a--){let b=f.childNodes[a-1],c=b.pmViewDesc;if("BR"==b.nodeName&&!c){h=a;break}if(!c||c.size)break}let m=a.state.doc,n=a.someProp("domParser")||e.S4.fromSchema(a.state.schema),o=m.resolve(i),p=null,r=n.parse(f,{topNode:o.parent,topMatch:o.parent.contentMatchAt(o.index()),topOpen:!0,from:g,to:h,preserveWhitespace:"pre"!=o.parent.type.whitespace||"full",findPositions:d,ruleFromNode:bL,context:o});if(d&&null!=d[0].pos){let a=d[0].pos,b=d[1]&&d[1].pos;null==b&&(b=a),p={anchor:a+i,head:b+i}}return{doc:r,sel:p,from:i,to:j}}(a,b,c),s=a.state.doc,t=s.slice(p.from,p.to);8===a.input.lastKeyCode&&Date.now()-100<a.input.lastKeyCodeTime?(h=a.state.selection.to,i="end"):(h=a.state.selection.from,i="start"),a.input.lastKeyCode=null;let u=function(a,b,c,d,e){let f=a.findDiffStart(b,c);if(null==f)return null;let{a:g,b:h}=a.findDiffEnd(b,c+a.size,c+b.size);if("end"==e){let a=Math.max(0,f-Math.min(g,h));d-=g+a-f}if(g<f&&a.size<b.size){let a=d<=f&&d>=g?f-d:0;(f-=a)&&f<b.size&&bP(b.textBetween(f-1,f+1))&&(f+=a?1:-1),h=f+(h-g),g=f}else if(h<f){let b=d<=f&&d>=h?f-d:0;(f-=b)&&f<a.size&&bP(a.textBetween(f-1,f+1))&&(f+=b?1:-1),g=f+(g-h),h=f}return{start:f,endA:g,endB:h}}(t.content,p.doc.content,p.from,h,i);if(u&&a.input.domChangeCount++,(F&&a.input.lastIOSEnter>Date.now()-225||I)&&g.some(a=>1==a.nodeType&&!bM.test(a.nodeName))&&(!u||u.endA>=u.endB)&&a.someProp("handleKeyDown",b=>b(a,r(13,"Enter")))){a.input.lastIOSEnter=0;return}if(!u)if(f&&o instanceof d.U3&&!o.empty&&o.$head.sameParent(o.$anchor)&&!a.composing&&!(p.sel&&p.sel.anchor!=p.sel.head))u={start:o.from,endA:o.to,endB:o.to};else{if(p.sel){let b=bN(a,a.state.doc,p.sel);if(b&&!b.eq(a.state.selection)){let c=a.state.tr.setSelection(b);l&&c.setMeta("composition",l),a.dispatch(c)}}return}a.state.selection.from<a.state.selection.to&&u.start==u.endB&&a.state.selection instanceof d.U3&&(u.start>a.state.selection.from&&u.start<=a.state.selection.from+2&&a.state.selection.from>=p.from?u.start=a.state.selection.from:u.endA<a.state.selection.to&&u.endA>=a.state.selection.to-2&&a.state.selection.to<=p.to&&(u.endB+=a.state.selection.to-u.endA,u.endA=a.state.selection.to)),y&&z<=11&&u.endB==u.start+1&&u.endA==u.start&&u.start>p.from&&" \xa0"==p.doc.textBetween(u.start-p.from-1,u.start-p.from+1)&&(u.start--,u.endA--,u.endB--);let v=p.doc.resolveNoCache(u.start-p.from),w=p.doc.resolveNoCache(u.endB-p.from),x=s.resolve(u.start),A=v.sameParent(w)&&v.parent.inlineContent&&x.end()>=u.endA;if((F&&a.input.lastIOSEnter>Date.now()-225&&(!A||g.some(a=>"DIV"==a.nodeName||"P"==a.nodeName))||!A&&v.pos<p.doc.content.size&&(!v.sameParent(w)||!v.parent.inlineContent)&&!/\S/.test(p.doc.textBetween(v.pos,w.pos,"",""))&&(j=d.LN.findFrom(p.doc.resolve(v.pos+1),1,!0))&&j.head>v.pos)&&a.someProp("handleKeyDown",b=>b(a,r(13,"Enter")))){a.input.lastIOSEnter=0;return}if(a.state.selection.anchor>u.start&&function(a,b,c,d,e){if(c-b<=e.pos-d.pos||bO(d,!0,!1)<e.pos)return!1;let f=a.resolve(b);if(!d.parent.isTextblock){let a=f.nodeAfter;return null!=a&&c==b+a.nodeSize}if(f.parentOffset<f.parent.content.size||!f.parent.isTextblock)return!1;let g=a.resolve(bO(f,!0,!0));return!(!g.parent.isTextblock||g.pos>c||bO(g,!0,!1)<c)&&d.parent.content.cut(d.parentOffset).eq(g.parent.content)}(s,u.start,u.endA,v,w)&&a.someProp("handleKeyDown",b=>b(a,r(8,"Backspace")))){I&&C&&a.domObserver.suppressSelectionUpdates();return}C&&u.endB==u.start&&(a.input.lastChromeDelete=Date.now()),I&&!A&&v.start()!=w.start()&&0==w.parentOffset&&v.depth==w.depth&&p.sel&&p.sel.anchor==p.sel.head&&p.sel.head==u.endA&&(u.endB-=2,w=p.doc.resolveNoCache(u.endB-p.from),setTimeout(()=>{a.someProp("handleKeyDown",function(b){return b(a,r(13,"Enter"))})},20));let B=u.start,D=u.endA,E=b=>{let c=b||a.state.tr.replace(B,D,p.doc.slice(u.start-p.from,u.endB-p.from));if(p.sel){let b=bN(a,c.doc,p.sel);b&&!(C&&a.composing&&b.empty&&(u.start!=u.endB||a.input.lastChromeDelete<Date.now()-100)&&(b.head==B||b.head==c.mapping.map(D)-1)||y&&b.empty&&b.head==B)&&c.setSelection(b)}return l&&c.setMeta("composition",l),c.scrollIntoView()};if(A){if(v.pos==w.pos){y&&z<=11&&0==v.parentOffset&&(a.domObserver.suppressSelectionUpdates(),setTimeout(()=>av(a),20));let b=E(a.state.tr.delete(B,D)),c=s.resolve(u.start).marksAcross(s.resolve(u.endA));c&&b.ensureMarks(c),a.dispatch(b)}else if(u.endA==u.endB&&(k=function(a,b){let c=a.firstChild.marks,d=b.firstChild.marks,f=c,g=d,h,i,j;for(let a=0;a<d.length;a++)f=d[a].removeFromSet(f);for(let a=0;a<c.length;a++)g=c[a].removeFromSet(g);if(1==f.length&&0==g.length)i=f[0],h="add",j=a=>a.mark(i.addToSet(a.marks));else{if(0!=f.length||1!=g.length)return null;i=g[0],h="remove",j=a=>a.mark(i.removeFromSet(a.marks))}let k=[];for(let a=0;a<b.childCount;a++)k.push(j(b.child(a)));if(e.FK.from(k).eq(a))return{mark:i,type:h}}(v.parent.content.cut(v.parentOffset,w.parentOffset),x.parent.content.cut(x.parentOffset,u.endA-x.start())))){let b=E(a.state.tr);"add"==k.type?b.addMark(B,D,k.mark):b.removeMark(B,D,k.mark),a.dispatch(b)}else if(v.parent.child(v.index()).isText&&v.index()==w.index()-!w.textOffset){let b=v.parent.textBetween(v.parentOffset,w.parentOffset),c=()=>E(a.state.tr.insertText(b,B,D));a.someProp("handleTextInput",d=>d(a,B,D,b,c))||a.dispatch(c())}}else a.dispatch(E())})(this,a,b,c,f)),this.domObserver.start(),function(a){for(let b in a_){let c=a_[b];a.dom.addEventListener(b,a.input.eventHandlers[b]=b=>{!function(a,b){if(!b.bubbles)return!0;if(b.defaultPrevented)return!1;for(let c=b.target;c!=a.dom;c=c.parentNode)if(!c||11==c.nodeType||c.pmViewDesc&&c.pmViewDesc.stopEvent(b))return!1;return!0}(a,b)||a5(a,b)||!a.editable&&b.type in a0||c(a,b)},a1[b]?{passive:!0}:void 0)}E&&a.dom.addEventListener("input",()=>null),a4(a)}(this),this.updatePluginViews()}get composing(){return this.input.composing}get props(){if(this._props.state!=this.state){let a=this._props;for(let b in this._props={},a)this._props[b]=a[b];this._props.state=this.state}return this._props}update(a){a.handleDOMEvents!=this._props.handleDOMEvents&&a4(this);let b=this._props;this._props=a,a.plugins&&(a.plugins.forEach(bV),this.directPlugins=a.plugins),this.updateStateInner(a.state,b)}setProps(a){let b={};for(let a in this._props)b[a]=this._props[a];for(let c in b.state=this.state,a)b[c]=a[c];this.update(b)}updateState(a){this.updateStateInner(a,this._props)}updateStateInner(a,b){var c,d,e;let f=this.state,h=!1,i=!1;a.storedMarks&&this.composing&&(be(this),i=!0),this.state=a;let j=f.plugins!=a.plugins||this._props.plugins!=b.plugins;if(j||this._props.plugins!=b.plugins||this._props.nodeViews!=b.nodeViews){let a=bU(this);(function(a,b){let c=0,d=0;for(let d in a){if(a[d]!=b[d])return!0;c++}for(let a in b)d++;return c!=d})(a,this.nodeViews)&&(this.nodeViews=a,h=!0)}(j||b.handleDOMEvents!=this._props.handleDOMEvents)&&a4(this),this.editable=bT(this),bS(this);let k=bD(this),m=bR(this),n=f.plugins==a.plugins||f.doc.eq(a.doc)?a.scrollToSelection>f.scrollToSelection?"to selection":"preserve":"reset",q=h||!this.docView.matchesNode(a.doc,m,k);(q||!a.selection.eq(f.selection))&&(i=!0);let r="preserve"==n&&i&&null==this.dom.style.overflowAnchor&&function(a){let b,c,d=a.dom.getBoundingClientRect(),e=Math.max(0,d.top);for(let f=(d.left+d.right)/2,g=e+1;g<Math.min(innerHeight,d.bottom);g+=5){let d=a.root.elementFromPoint(f,g);if(!d||d==a.dom||!a.dom.contains(d))continue;let h=d.getBoundingClientRect();if(h.top>=e-20){b=d,c=h.top;break}}return{refDOM:b,refTop:c,stack:N(a.dom)}}(this);if(i){let b,c,i;this.domObserver.stop();let j=q&&(y||C)&&!this.composing&&!f.selection.empty&&!a.selection.empty&&(d=f.selection,e=a.selection,i=Math.min(d.$anchor.sharedDepth(d.head),e.$anchor.sharedDepth(e.head)),d.$anchor.start(i)!=e.$anchor.start(i));if(q){let b=C?this.trackWrites=this.domSelectionRange().focusNode:null;this.composing&&(this.input.compositionNode=function(a){let b=a.domSelectionRange();if(!b.focusNode)return null;let c=function(a,b){for(;;){if(3==a.nodeType&&b)return a;if(1==a.nodeType&&b>0){if("false"==a.contentEditable)return null;b=o(a=a.childNodes[b-1])}else{if(!a.parentNode||p(a))return null;b=g(a),a=a.parentNode}}}(b.focusNode,b.focusOffset),d=function(a,b){for(;;){if(3==a.nodeType&&b<a.nodeValue.length)return a;if(1==a.nodeType&&b<a.childNodes.length){if("false"==a.contentEditable)return null;a=a.childNodes[b],b=0}else{if(!a.parentNode||p(a))return null;b=g(a)+1,a=a.parentNode}}}(b.focusNode,b.focusOffset);if(c&&d&&c!=d){let b=d.pmViewDesc,e=a.domObserver.lastChangedTextNode;if(c==e||d==e)return e;if(!b||!b.isText(d.nodeValue))return d;if(a.input.compositionNode==d){let a=c.pmViewDesc;if(!(!a||!a.isText(c.nodeValue)))return d}}return c||d}(this)),(h||!this.docView.update(a.doc,m,k,this))&&(this.docView.updateOuterDeco(m),this.docView.destroy(),this.docView=af(a.doc,m,k,this.dom,this)),b&&!this.trackWrites&&(j=!0)}j||!(this.input.mouseDown&&this.domObserver.currentSelection.eq(this.domSelectionRange())&&(b=this.docView.domFromPos(this.state.selection.anchor,0),c=this.domSelectionRange(),l(b.node,b.offset,c.anchorNode,c.anchorOffset)))?av(this,j):(aA(this,a.selection),this.domObserver.setCurSelection()),this.domObserver.start()}this.updatePluginViews(f),(null==(c=this.dragging)?void 0:c.node)&&!f.doc.eq(a.doc)&&this.updateDraggedNode(this.dragging,f),"reset"==n?this.dom.scrollTop=0:"to selection"==n?this.scrollToSelection():r&&function({refDOM:a,refTop:b,stack:c}){let d=a?a.getBoundingClientRect().top:0;O(c,0==d?0:d-b)}(r)}scrollToSelection(){let a=this.domSelectionRange().focusNode;if(a&&this.dom.contains(1==a.nodeType?a:a.parentNode))if(this.someProp("handleScrollToSelection",a=>a(this)));else if(this.state.selection instanceof d.nh){let b=this.docView.domAfterPos(this.state.selection.from);1==b.nodeType&&M(this,b.getBoundingClientRect(),a)}else M(this,this.coordsAtPos(this.state.selection.head,1),a)}destroyPluginViews(){let a;for(;a=this.pluginViews.pop();)a.destroy&&a.destroy()}updatePluginViews(a){if(a&&a.plugins==this.state.plugins&&this.directPlugins==this.prevDirectPlugins)for(let b=0;b<this.pluginViews.length;b++){let c=this.pluginViews[b];c.update&&c.update(this,a)}else{this.prevDirectPlugins=this.directPlugins,this.destroyPluginViews();for(let a=0;a<this.directPlugins.length;a++){let b=this.directPlugins[a];b.spec.view&&this.pluginViews.push(b.spec.view(this))}for(let a=0;a<this.state.plugins.length;a++){let b=this.state.plugins[a];b.spec.view&&this.pluginViews.push(b.spec.view(this))}}}updateDraggedNode(a,b){let c=a.node,e=-1;if(this.state.doc.nodeAt(c.from)==c.node)e=c.from;else{let a=c.from+(this.state.doc.content.size-b.doc.content.size);(a>0&&this.state.doc.nodeAt(a))==c.node&&(e=a)}this.dragging=new bj(a.slice,a.move,e<0?void 0:d.nh.create(this.state.doc,e))}someProp(a,b){let c=this._props&&this._props[a],d;if(null!=c&&(d=b?b(c):c))return d;for(let c=0;c<this.directPlugins.length;c++){let e=this.directPlugins[c].props[a];if(null!=e&&(d=b?b(e):e))return d}let e=this.state.plugins;if(e)for(let c=0;c<e.length;c++){let f=e[c].props[a];if(null!=f&&(d=b?b(f):f))return d}}hasFocus(){if(y){let a=this.root.activeElement;if(a==this.dom)return!0;if(!a||!this.dom.contains(a))return!1;for(;a&&this.dom!=a&&this.dom.contains(a);){if("false"==a.contentEditable)return!1;a=a.parentElement}return!0}return this.root.activeElement==this.dom}focus(){this.domObserver.stop(),this.editable&&function(a){if(a.setActive)return a.setActive();if(P)return a.focus(P);let b=N(a);a.focus(null==P?{get preventScroll(){return P={preventScroll:!0},!0}}:void 0),P||(P=!1,O(b,0))}(this.dom),av(this),this.domObserver.start()}get root(){let a=this._root;if(null==a){for(let a=this.dom.parentNode;a;a=a.parentNode)if(9==a.nodeType||11==a.nodeType&&a.host)return a.getSelection||(Object.getPrototypeOf(a).getSelection=()=>a.ownerDocument.getSelection()),this._root=a}return a||document}updateRoot(){this._root=null}posAtCoords(a){return function(a,b){var c;let d,e,f=a.dom.ownerDocument,g,i=0,k=function(a,b,c){if(a.caretPositionFromPoint)try{let d=a.caretPositionFromPoint(b,c);if(d)return{node:d.offsetNode,offset:Math.min(o(d.offsetNode),d.offset)}}catch(a){}if(a.caretRangeFromPoint){let d=a.caretRangeFromPoint(b,c);if(d)return{node:d.startContainer,offset:Math.min(o(d.startContainer),d.startOffset)}}}(f,b.left,b.top);k&&({node:g,offset:i}=k);let l=(a.root.elementFromPoint?a.root:f).elementFromPoint(b.left,b.top);if(!l||!a.dom.contains(1!=l.nodeType?l.parentNode:l)){let c=a.dom.getBoundingClientRect();if(!Q(b,c)||!(l=function a(b,c,d){let e=b.childNodes.length;if(e&&d.top<d.bottom)for(let f=Math.max(0,Math.min(e-1,Math.floor(e*(c.top-d.top)/(d.bottom-d.top))-2)),g=f;;){let d=b.childNodes[g];if(1==d.nodeType){let b=d.getClientRects();for(let e=0;e<b.length;e++){let f=b[e];if(Q(c,f))return a(d,c,f)}}if((g=(g+1)%e)==f)break}return b}(a.dom,b,c)))return null}if(E)for(let a=l;g&&a;a=h(a))a.draggable&&(g=void 0);if(l=(d=(c=l).parentNode)&&/^li$/i.test(d.nodeName)&&b.left<c.getBoundingClientRect().left?d:c,g){let c;if(A&&1==g.nodeType&&(i=Math.min(i,g.childNodes.length))<g.childNodes.length){let a=g.childNodes[i],c;"IMG"==a.nodeName&&(c=a.getBoundingClientRect()).right<=b.left&&c.bottom>b.top&&i++}J&&i&&1==g.nodeType&&1==(c=g.childNodes[i-1]).nodeType&&"false"==c.contentEditable&&c.getBoundingClientRect().top>=b.top&&i--,g==a.dom&&i==g.childNodes.length-1&&1==g.lastChild.nodeType&&b.top>g.lastChild.getBoundingClientRect().bottom?e=a.state.doc.content.size:(0==i||1!=g.nodeType||"BR"!=g.childNodes[i-1].nodeName)&&(e=function(a,b,c,d){let e=-1;for(let c=b,f=!1;c!=a.dom;){let b=a.docView.nearestDesc(c,!0),g;if(!b)return null;if(1==b.dom.nodeType&&(b.node.isBlock&&b.parent||!b.contentDOM)&&((g=b.dom.getBoundingClientRect()).width||g.height)&&(b.node.isBlock&&b.parent&&(!f&&g.left>d.left||g.top>d.top?e=b.posBefore:(!f&&g.right<d.left||g.bottom<d.top)&&(e=b.posAfter),f=!0),!b.contentDOM&&e<0&&!b.node.isText))return(b.node.isBlock?d.top<(g.top+g.bottom)/2:d.left<(g.left+g.right)/2)?b.posBefore:b.posAfter;c=b.dom.parentNode}return e>-1?e:a.docView.posFromDOM(b,c,-1)}(a,g,i,b))}null==e&&(e=function(a,b,c){let{node:d,offset:e}=function a(b,c){let d,e,f,g=2e8,h,i=0,k=c.top,l=c.top;for(let a=b.firstChild,m=0;a;a=a.nextSibling,m++){let b;if(1==a.nodeType)b=a.getClientRects();else{if(3!=a.nodeType)continue;b=j(a).getClientRects()}for(let j=0;j<b.length;j++){let n=b[j];if(n.top<=k&&n.bottom>=l){k=Math.max(n.bottom,k),l=Math.min(n.top,l);let b=n.left>c.left?n.left-c.left:n.right<c.left?c.left-n.right:0;if(b<g){f=a,g=b,h=b&&3==f.nodeType?{left:n.right<c.left?n.right:n.left,top:c.top}:c,1==a.nodeType&&b&&(i=m+ +(c.left>=(n.left+n.right)/2));continue}}else n.top>c.top&&!d&&n.left<=c.left&&n.right>=c.left&&(d=a,e={left:Math.max(n.left,Math.min(n.right,c.left)),top:n.top});!f&&(c.left>=n.right&&c.top>=n.top||c.left>=n.left&&c.top>=n.bottom)&&(i=m+1)}}return(!f&&d&&(f=d,h=e,g=0),f&&3==f.nodeType)?function(a,b){let c=a.nodeValue.length,d=document.createRange();for(let e=0;e<c;e++){d.setEnd(a,e+1),d.setStart(a,e);let c=S(d,1);if(c.top!=c.bottom&&Q(b,c))return{node:a,offset:e+ +(b.left>=(c.left+c.right)/2)}}return{node:a,offset:0}}(f,h):!f||g&&1==f.nodeType?{node:b,offset:i}:a(f,h)}(b,c),f=-1;if(1==d.nodeType&&!d.firstChild){let a=d.getBoundingClientRect();f=a.left!=a.right&&c.left>(a.left+a.right)/2?1:-1}return a.docView.posFromDOM(d,e,f)}(a,l,b));let m=a.docView.nearestDesc(l,!0);return{pos:e,inside:m?m.posAtStart-m.border:-1}}(this,a)}coordsAtPos(a,b=1){return U(this,a,b)}domAtPos(a,b=0){return this.docView.domFromPos(a,b)}nodeDOM(a){let b=this.docView.descAt(a);return b?b.nodeDOM:null}posAtDOM(a,b,c=-1){let d=this.docView.posFromDOM(a,b,c);if(null==d)throw RangeError("DOM position not inside the editor");return d}endOfTextblock(a,b){return function(a,b,c){let d,e;return Z==b&&$==c?_:(Z=b,$=c,_="up"==c||"down"==c?(d=b.selection,e="up"==c?d.$from:d.$to,X(a,b,()=>{let{node:b}=a.docView.domFromPos(e.pos,"up"==c?-1:1);for(;;){let c=a.docView.nearestDesc(b,!0);if(!c)break;if(c.node.isBlock){b=c.contentDOM||c.dom;break}b=c.dom.parentNode}let d=U(a,e.pos,1);for(let a=b.firstChild;a;a=a.nextSibling){let b;if(1==a.nodeType)b=a.getClientRects();else{if(3!=a.nodeType)continue;b=j(a,0,a.nodeValue.length).getClientRects()}for(let a=0;a<b.length;a++){let e=b[a];if(e.bottom>e.top+1&&("up"==c?d.top-e.top>(e.bottom-d.top)*2:e.bottom-d.bottom>(d.bottom-e.top)*2))return!1}}return!0})):function(a,b,c){let{$head:d}=b.selection;if(!d.parent.isTextblock)return!1;let e=d.parentOffset,f=e==d.parent.content.size,g=a.domSelection();return g?Y.test(d.parent.textContent)&&g.modify?X(a,b,()=>{let{focusNode:b,focusOffset:e,anchorNode:f,anchorOffset:h}=a.domSelectionRange(),i=g.caretBidiLevel;g.modify("move",c,"character");let j=d.depth?a.docView.domAfterPos(d.before()):a.dom,{focusNode:k,focusOffset:l}=a.domSelectionRange(),m=k&&!j.contains(1==k.nodeType?k:k.parentNode)||b==k&&e==l;try{g.collapse(f,h),b&&(b!=f||e!=h)&&g.extend&&g.extend(b,e)}catch(a){}return null!=i&&(g.caretBidiLevel=i),m}):"left"==c||"backward"==c?!e:f:d.pos==d.start()||d.pos==d.end()}(a,b,c))}(this,b||this.state,a)}pasteHTML(a,b){return bh(this,"",a,!1,b||new ClipboardEvent("paste"))}pasteText(a,b){return bh(this,a,null,!0,b||new ClipboardEvent("paste"))}serializeForClipboard(a){return aR(this,a)}destroy(){if(this.docView){for(let a in this.domObserver.stop(),this.input.eventHandlers)this.dom.removeEventListener(a,this.input.eventHandlers[a]);clearTimeout(this.input.composingTimeout),clearTimeout(this.input.lastIOSEnterFallbackTimeout),this.destroyPluginViews(),this.mounted?(this.docView.update(this.state.doc,[],bD(this),this),this.dom.textContent=""):this.dom.parentNode&&this.dom.parentNode.removeChild(this.dom),this.docView.destroy(),this.docView=null,k()}}get isDestroyed(){return null==this.docView}dispatchEvent(a){!a5(this,a)&&a_[a.type]&&(this.editable||!(a.type in a0))&&a_[a.type](this,a)}domSelectionRange(){let a=this.domSelection();return a?E&&11===this.root.nodeType&&function(a){let b=a.activeElement;for(;b&&b.shadowRoot;)b=b.shadowRoot.activeElement;return b}(this.dom.ownerDocument)==this.dom&&function(a,b){let c;if(b.getComposedRanges){let c=b.getComposedRanges(a.root)[0];if(c)return bK(a,c)}function d(a){a.preventDefault(),a.stopImmediatePropagation(),c=a.getTargetRanges()[0]}return a.dom.addEventListener("beforeinput",d,!0),document.execCommand("indent"),a.dom.removeEventListener("beforeinput",d,!0),c?bK(a,c):null}(this,a)||a:{focusNode:null,focusOffset:0,anchorNode:null,anchorOffset:0}}domSelection(){return this.root.getSelection()}}function bR(a){let b=Object.create(null);return b.class="ProseMirror",b.contenteditable=String(a.editable),a.someProp("attributes",c=>{if("function"==typeof c&&(c=c(a.state)),c)for(let a in c)"class"==a?b.class+=" "+c[a]:"style"==a?b.style=(b.style?b.style+";":"")+c[a]:b[a]||"contenteditable"==a||"nodeName"==a||(b[a]=String(c[a]))}),b.translate||(b.translate="no"),[bq.node(0,a.state.doc.content.size,b)]}function bS(a){if(a.markCursor){let b=document.createElement("img");b.className="ProseMirror-separator",b.setAttribute("mark-placeholder","true"),b.setAttribute("alt",""),a.cursorWrapper={dom:b,deco:bq.widget(a.state.selection.from,b,{raw:!0,marks:a.markCursor})}}else a.cursorWrapper=null}function bT(a){return!a.someProp("editable",b=>!1===b(a.state))}function bU(a){let b=Object.create(null);function c(a){for(let c in a)Object.prototype.hasOwnProperty.call(b,c)||(b[c]=a[c])}return a.someProp("nodeViews",c),a.someProp("markViews",c),b}function bV(a){if(a.spec.state||a.spec.filterTransaction||a.spec.appendTransaction)throw RangeError("Plugins passed directly to the view must not have a state component")}bQ.prototype.dispatch=function(a){let b=this._props.dispatchTransaction;b?b.call(this,a):this.updateState(this.state.apply(a))}},98916:(a,b,c)=>{c.d(b,{A:()=>d});let d=(0,c(62688).A)("quote",[["path",{d:"M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"rib7q0"}],["path",{d:"M5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2 1 1 0 0 1 1 1v1a2 2 0 0 1-2 2 1 1 0 0 0-1 1v2a1 1 0 0 0 1 1 6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z",key:"1ymkrd"}]])}};